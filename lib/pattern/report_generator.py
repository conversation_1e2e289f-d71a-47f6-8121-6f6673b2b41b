"""
Module to combine multiple HTML validation reports into a single consolidated HTML report.
"""

from bs4 import BeautifulSoup
import os
import re
import logging
from typing import Optional
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.chart_dict import VALIDATION_CHARTS
from lib.std.universal.logger import log_error,log_info,log_warn

# logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
sub_folder="Individual_Reports"
final_output_folder="Final_Consolidated_Report"
final_report="Consolidated_Report.html"

def make_ids_unique(soup: BeautifulSoup, prefix: str) -> BeautifulSoup:
    """
    Prefixes all 'id', 'href', and 'data-bs-target' attributes in the soup with the given prefix.
    This avoids conflicts between multiple reports.
    """
    # Update IDs
    for tag in soup.find_all(attrs={"id": True}):
        old_id = tag["id"]
        new_id = f"{prefix}-{old_id}"
        tag["id"] = new_id

        # Update all matching anchor href="#old_id"
        for ref_tag in soup.find_all(href=f"#{old_id}"):
            ref_tag["href"] = f"#{new_id}"

        # Update Bootstrap-specific targets
        for ref_tag in soup.find_all(attrs={"data-bs-target": f"#{old_id}"}):
            ref_tag["data-bs-target"] = f"#{new_id}"

        for ref_tag in soup.find_all(attrs={"aria-controls": old_id}):
            ref_tag["aria-controls"] = new_id

        for ref_tag in soup.find_all(attrs={"aria-labelledby": old_id}):
            ref_tag["aria-labelledby"] = new_id

    return soup

def combine_all_reports(include_charts: list[str] | None = None) -> None:
    """
    Combines individual HTML reports into a single consolidated report.
    Only includes charts in `include_charts` (by chart key) if provided.
    """
    # Output path for combined report
    _, output_file = create_folder_file_path(
        subfolder=final_output_folder, 
        output_file=final_report
    )

    # Folder where individual reports are saved
    report_folder = create_folder_file_path(subfolder=sub_folder)

    combined_sections = []

    for key, chart_info in VALIDATION_CHARTS.items():
        if include_charts and key not in include_charts:
            continue  # Skip failed or excluded charts

        html_filename = chart_info.get("html")
        if not html_filename:
            log_warn(f"No HTML file defined for chart '{key}'")
            continue

        filepath = os.path.join(report_folder, html_filename)
        if not os.path.exists(filepath):
            log_warn(f"HTML file not found for chart '{key}': {filepath}")
            continue

        try:
            with open(filepath, "r", encoding="utf-8") as f:
                soup = BeautifulSoup(f, "html.parser")
                container_div = soup.find("div", class_="container")
                if container_div:
                    # Make IDs unique based on chart key
                    unique_prefix = key.replace(" ", "_")
                    updated_container = make_ids_unique(container_div, unique_prefix)

                    section_html = f"""
                    <div class="report-section border rounded p-3 mb-5">
                        {str(updated_container)}
                    </div>
                    """
                    combined_sections.append(section_html)
        except Exception as e:
            log_warn(f"Failed to process HTML for chart '{key}': {e}")

    # Generate the combined HTML
    full_html = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Combined Validation Report</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; }}
            .badge-fail {{ background-color: #dc3545; }}
            .card-header {{ cursor: pointer; }}
            .report-section h2 {{ margin-bottom: 1rem; border-bottom: 1px solid #ccc; padding-bottom: 0.5rem; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="mb-5">Combined Report</h1>
            {"".join(combined_sections)}
        </div>
    </body>
    </html>
    """

    try:
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(full_html)
        log_info(f"Combined HTML report created at: {output_file}")
    except Exception as e:
        log_error(f"Failed to write combined report: {e}")

"""
Main script to run all validation scripts for FOPC QA automation.
Parses command-line arguments, validates configuration, and executes all validation modules.
"""

import os
import time
import importlib.util
import sys
import argparse
import logging
from dotenv import load_dotenv
from lib.pattern.report_generator import combine_all_reports
from lib.pattern.config import config
from datetime import datetime
import re
from lib.pattern.qa_auto_core.db_handler.data_loader import RobustDataLoader
from dateutil.relativedelta import relativedelta
from datetime import timedelta
from lib.std.universal.logger import log_info,log_error,log_warn
from lib.std.universal.chart_dict import VALIDATION_CHARTS
from dateutil.relativedelta import relativedelta

# Configure logging
# logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")

load_dotenv()

VALIDATION_FOLDER = os.path.join(os.path.dirname(__file__), "qa_auto_core")
# VALIDATION_SEQUENCE = [  
#         # "validate_kpi_scorecard.py",
#         # "validate_kpi_advisor.py",  
#         # "validate_client_report_card.py",
#         # "validate_client_report_card_3_months.py",   
#         "validate_cp_summary_overview.py", 
#     ]


def run_validation_script(filepath: str) -> bool:
    """
    Dynamically imports and runs the 'run_validation' function from the given script.
    Returns True if the script completes successfully, False otherwise.
    """
    module_name = os.path.splitext(os.path.basename(filepath))[0]
    spec = importlib.util.spec_from_file_location(module_name, filepath)
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module

    try:
        spec.loader.exec_module(module)
        if hasattr(module, "run_validation"):
            start_time = time.time()
            log_info(f"Started running: {module_name}")
            try:
                # The chart can internally return True/False
                success = module.run_validation()
                if success is None:
                    # If nothing returned, assume True (success)
                    success = True
            except Exception as e:
                log_error(f"Runtime error inside {module_name}: {e}")
                success = False
            end_time = time.time()
            log_info(f"Completed: {module_name} | Time taken: {end_time - start_time:.2f} seconds")
            return success
        else:
            log_warn(f"No 'run_validation' found in {module_name}")
            return False
    except Exception as e:
        log_error(f"Failed to load or execute {module_name}: {e}")
        return False

def get_months_between(start_date_str, end_date_str):
    """Return (end_month, start_month) as tuple in 'Mon-YY' format."""
    start = datetime.strptime(start_date_str, "%Y-%m-%d")
    end = datetime.strptime(end_date_str, "%Y-%m-%d")

    return start.strftime("%b-%y"), end.strftime("%b-%y")

def run_all_validations(args_dict: dict) -> tuple[list[str], bool]:
    """
    Sets global config, runs all validation scripts, and returns:
      - failed_charts: List of validation scripts that failed
      - db_success: Boolean indicating if database/data load was successful
    """
    # Set global config attributes
    config.store_id = args_dict["store_id"]
    config.store_name = args_dict["store_name"]
    config.start_date = args_dict["start_date"]
    config.end_date = args_dict["end_date"]
    config.fopc_month = args_dict["fopc_month"]
    config.pre_fopc_month = args_dict["pre_fopc_month"]
    config.database_name = args_dict["database_name"]
    config.working_days = float(args_dict["working_days"])
    config.advisor = args_dict["advisor"]
    config.technician = args_dict["technician"]
    config.site_url = args_dict["site_url"]
    config.last_month = args_dict["last_month"]
    config.role = args_dict["role"]
    config.target_month_year = args_dict["target_month_year"]
    config.realm = args_dict["realm"]
    # In your config (runtime)
    config.charts_to_run = args_dict.get("charts", "all")
    config.month1, config.month2 = get_months_between(config.start_date, config.end_date)
    
    log_info(f"Selected months for comparison: {config.month1}, {config.month2}")

    # Load database/data
    loader = RobustDataLoader(max_workers=5, max_retries=2)
    results, db_success, failed_dfs = loader.load_all_data_with_retry()

    if not db_success:
        log_error(f"Aborting further processing. Failed to load: {failed_dfs}")
        return [], False  # DB load failed, no chart validations run

    # Continue only if DB/data loaded successfully
    timings = loader.get_task_timings()
    if timings:
        log_info(f"Slowest task: {max(timings.items(), key=lambda x: x[1])}")

    log_info(f"Scanning folder: {VALIDATION_FOLDER}")    

    failed_charts: list[str] = []

    # Resolve charts_to_run from config
    if config.charts_to_run == "all":
        charts_to_run = list(VALIDATION_CHARTS.keys())
    else:
        # split CLI string into list if needed
        charts_to_run = (
            config.charts_to_run
            if isinstance(config.charts_to_run, list)
            else [c.strip() for c in config.charts_to_run.split(",")]
        )

    for key in charts_to_run:
        chart_info = VALIDATION_CHARTS.get(key)
        if not chart_info:
            log_warn(f"Unknown chart key: {key}")
            failed_charts.append(key)
            continue

        script_file = chart_info["script"]
        filepath = os.path.join(VALIDATION_FOLDER, script_file)

        if os.path.exists(filepath):
            log_info(f"Running {script_file} with args: {args_dict}")
            try:
                success = run_validation_script(filepath)
                if not success:
                    failed_charts.append(key)
            except Exception as e:
                log_error(f"Validation script {script_file} failed with exception: {e}")
                failed_charts.append(key)
        else:
            log_warn(f"Expected validation file not found: {script_file}")
            failed_charts.append(key)

    if failed_charts:
        log_warn(f"The following validation scripts failed: {failed_charts}")
    else:
        log_info("All validation scripts completed successfully.")

    return failed_charts, True  # DB loaded successfully, return chart failures

def validate_config(config_obj) -> None:
    """
    Validates the configuration object for required formats and values.
    Exits the program if validation fails.
    """
    errors = []

    # Validate date formats
    try:
        start = datetime.strptime(config_obj.start_date, "%Y-%m-%d")
    except ValueError:
        errors.append(
            f"Invalid start_date '{config_obj.start_date}'. Expected format: YYYY-MM-DD and valid calendar date."
        )

    try:
        end = datetime.strptime(config_obj.end_date, "%Y-%m-%d")
    except ValueError:
        errors.append(
            f"Invalid end_date '{config_obj.end_date}'. Expected format: YYYY-MM-DD and valid calendar date."
        )

    if "start" in locals() and "end" in locals() and start > end:
        errors.append("start_date cannot be after end_date.")

    for field_name in ["fopc_month", "pre_fopc_month", "last_month"]:
        value = getattr(config_obj, field_name)
        try:
            datetime.strptime(value, "%Y-%m")
        except ValueError:
            errors.append(f"Invalid {field_name} format. Expected YYYY-MM.")

    # Validate site_url format
    if not re.match(r"^https?://", config_obj.site_url):
        errors.append("site_url must start with http:// or https://")

    if errors:
        for error in errors:
            log_error(error)
        sys.exit(1)


def main():
    """
    Main entry point for running all validations.
    """
    parser = argparse.ArgumentParser(
        description="Run all validation scripts with store context."
    )

    parser.add_argument("--store_id", required=True, help="Store ID")
    parser.add_argument("--store_name", required=True, help="Store Name")
    parser.add_argument("--start_date", required=True, help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end_date", required=True, help="End date (YYYY-MM-DD)")
    parser.add_argument(
        "--fopc_month",
        required=True,
        help="Current FOPC month being evaluated (format: YYYY-MM)",
    )
    parser.add_argument(
        "--pre_fopc_month",
        required=True,
        help="Previous FOPC month for comparison (format: YYYY-MM)",
    )
    parser.add_argument(
        "--database_name",
        required=True,
        help="Name of the database to connect (e.g., sampackag)",
    )
    parser.add_argument(
        "--working_days",
        required=True,
        help="Number of working days in the selected date range (e.g., 46.74)",
    )
    parser.add_argument(
        "--advisor", required=True, help="Advisor name or 'all' to include all advisors"
    )
    parser.add_argument(
        "--technician",
        required=True,
        help="Technician name or 'all' to include all technicians",
    )
    parser.add_argument(
        "--site_url",
        required=True,
        help="Base URL of the site (e.g., https://sampackag.fixedops.cc/)",
    )
    parser.add_argument(
        "--last_month",
        required=True,
        help="Last month for client report card three months (format: YYYY-MM)",
    )
    parser.add_argument("--role", required=True, help="Name of role")
    
    parser.add_argument(
    "--target_month_year",
    required=True,
    nargs="+",
    help="Target month/year(s), e.g. --target_month_year 2023-11-01 2023-12-01"
    )
    parser.add_argument(
        "--realm",
        required=True,
        type=str.strip,   # trims leading/trailing spaces automatically
        help="Realm name (e.g., carriageag, sampackag)"
    )

    parser.add_argument(
    "--charts",
    type=str.strip,
    default="all",
    help="Comma-separated list of chart keys to run, or 'all' to run all charts"
)

    args = parser.parse_args()
    args_dict = vars(args)
    
    validate_config(args)
    total_start = time.time()
    failed_charts, db_success = run_all_validations(args_dict)
    total_end = time.time()
    log_info(f"All validations completed in {total_end - total_start:.2f} seconds")

    # Convert charts argument to list of keys
    
    if args.charts.lower() == "all":
        charts_to_run = list(VALIDATION_CHARTS.keys())
    else:
        # Convert to a proper list of charts
        if isinstance(config.charts_to_run, str):
            charts_to_run = [chart.strip() for chart in config.charts_to_run.split(",")]
        elif isinstance(config.charts_to_run, (list, tuple)):
            charts_to_run = list(config.charts_to_run)
        else:
            charts_to_run = [config.charts_to_run]

    # Remove failed charts
    successful_charts = [chart for chart in charts_to_run if chart not in failed_charts]

    # Combine reports if DB load succeeded or if some charts succeeded
    if db_success and successful_charts:
        combine_all_reports(include_charts=successful_charts)
    elif not db_success:
        logging.error("Database load failed. Skipping report generation.")
    elif not successful_charts:
        logging.warning("No validation charts succeeded. Nothing to combine.")


if __name__ == "__main__":
    main()

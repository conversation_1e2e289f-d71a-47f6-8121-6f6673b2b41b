"""
Validation script for Client Report Card (3 months).
Automates UI, extracts data, compares with DB, and generates validation reports.
"""

import os
import json
import logging
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime
from typing import Any, Dict
import pandas as pd
from dotenv import load_dotenv
from playwright.sync_api import sync_playwright
from dateutil.relativedelta import relativedelta
import asyncio
import time

from lib.std.universal.extract_image_data import extract_image_data_split
from lib.pattern.qa_auto_core.compare_client_report_card_3_months import compare_client_report_card_3_months
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.std.universal.authmanager import AuthManager
from lib.std.universal.chart_dict import VALIDATION_CHARTS
from lib.std.universal.extract_image_data import extract_image_data

from lib.std.universal.constants import ClientReportCard3Month,client_report_three_month_prompt

chart_key="client_report_card_3_months"
dict_html= VALIDATION_CHARTS[chart_key]["html"]
dict_xlsx=VALIDATION_CHARTS[chart_key]["xlsx"]
dict_csv=VALIDATION_CHARTS[chart_key]["csv"]
dict_json=VALIDATION_CHARTS[chart_key]["json"]
dict_jpg=VALIDATION_CHARTS[chart_key]["jpg"]
dict_md=VALIDATION_CHARTS[chart_key]["md"]

ind_folder="Individual_Reports"
omni_folder="Omni_Results"
load_dotenv()


def round_off(n: float, decimals: int = 0) -> float:
    """Round a number to a given number of decimal places using ROUND_HALF_UP."""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def is_empty(value: Any) -> bool:
    """Check if a value is considered empty for merging."""
    return value in [None, "", 'value', 'value%']

def merge_missing_values(base: Any, update: Any) -> Any:
    """Recursively merge missing values from update into base."""
    if isinstance(base, dict) and isinstance(update, dict):
        for key in update:
            if key in base:
                base[key] = merge_missing_values(base[key], update[key])
            else:
                base[key] = update[key]
    elif isinstance(base, list) and isinstance(update, list):
        return [merge_missing_values(b, u) for b, u in zip(base, update)]
    else:
        return update if is_empty(base) else base
    return base


def automate_site(config):
    """
    Synchronous wrapper to run Client Report Card automation safely in Python 3.12+
    """
    async def runner():
        auth = AuthManager(config)
        success = await auth.start()
        if not success:
            log_error("❌ Authentication failed. Exiting KPI capture.")
            return False

        page = auth.page
        try:
            # Get base URL from config
            base_url = config.site_url
            path = "ThreeMonthReport"
            site_url = f"{base_url.rstrip('/')}/{path}"

            # Create result folder with tenant name explicitly
            # result_folder = create_folder_file_path(
            #     base_folder_name="Omni_Results",
            #     tenant_name=config.database_name
            # )
            # os.makedirs(result_folder, exist_ok=True)

            result_folder= create_folder_file_path(subfolder="Omni_Results") 

            start_date = datetime.strptime(config.start_date, "%Y-%m-%d")
            end_date = datetime.strptime(config.end_date, "%Y-%m-%d")
            date_range = f"{start_date.strftime('%m/%d/%y')} - {end_date.strftime('%m/%d/%y')}"

            last_month_date = datetime.strptime(config.last_month, "%Y-%m")
            month_year = last_month_date.strftime("%b-%y")

            # Playwright navigation
            await page.goto(site_url)
            await page.click("input#picker")
            await page.fill("input[name='picker']", date_range)
            await page.keyboard.press("Enter")

            # Wait for datepicker to hide
            try:
                await page.wait_for_selector(".daterangepicker", state="hidden", timeout=5000)
            except Exception:
                await page.wait_for_selector(".daterangepicker", state="detached", timeout=2000)

            # Select month
            await page.click("#mui-component-select-month-1")
            await page.get_by_role("option", name=month_year).click()

            # Click checkboxes
            await page.get_by_label("Competitive").click()
            await page.get_by_label("Maintenance").click()
            await page.get_by_label("Repair").click()
            await page.wait_for_timeout(5000)

            # # Hide sticky header
            # await page.evaluate("""
            #     const header = document.querySelector('.sticky-header-class');
            #     if(header) header.style.display = 'none';
            # """)

            # # Make table header sticky
            # await page.evaluate("""
            #     const thead = document.querySelector('table.client-report-second.datatable thead');
            #     if (thead) {
            #         thead.style.position = 'sticky';
            #         thead.style.top = '0px';
            #         thead.style.backgroundColor = '#fff';
            #         thead.style.zIndex = '9999';
            #     }
            #     let parent = thead?.parentElement;
            #     while (parent) {
            #         parent.style.overflow = 'visible';
            #         parent = parent.parentElement;
            #     }
            # """)

            # # Screenshot scrolling
            # total_height = await page.evaluate("document.body.scrollHeight")
            # viewport_height = await page.evaluate("window.innerHeight")
            # overlap = 100
            # scroll_step = viewport_height - overlap
            # num_screenshots = (total_height + scroll_step - 1) // scroll_step

            # for i in range(num_screenshots):
            #     scroll_y = i * scroll_step
            #     if scroll_y + viewport_height > total_height:
            #         scroll_y = total_height - viewport_height
            #     await page.evaluate(f"window.scrollTo(0, {scroll_y})")
            #     await page.wait_for_timeout(1000)
            #     await page.evaluate("window.dispatchEvent(new Event('scroll'))")
            #     await page.wait_for_timeout(500)

            #     filename = os.path.join(result_folder, f"{os.path.splitext(dict_jpg)[0]}{i + 1}.jpg")
            #     await page.screenshot(path=filename, full_page=False)
            #     log_info(f"Saved: {filename}")


            # Hide sticky header (as per existing code)
            await page.evaluate("""
                const header = document.querySelector('.sticky-header-class');
                if(header) header.style.display = 'none';
            """)

            # Make table header sticky (as per existing code) - NOTE: This block is generally unnecessary for a FULL screenshot
            # but is left here as per your original code.
            await page.evaluate("""
                const thead = document.querySelector('table.client-report-second.datatable thead');
                if (thead) {
                    thead.style.position = 'sticky';
                    thead.style.top = '0px';
                    thead.style.backgroundColor = '#fff';
                    thead.style.zIndex = '9999';
                }
                let parent = thead?.parentElement;
                while (parent) {
                    parent.style.overflow = 'visible';
                    parent = parent.parentElement;
                }
            """)
            
            # ------------------------------------------------------------------
            # --- START: Full Screenshot Capture (SINGLE IMAGE STRATEGY) ---
            # ------------------------------------------------------------------

            # Calculate base name and file path for the full screenshot
            base_name = os.path.splitext(dict_jpg)[0]
            temp_full_path = os.path.join(result_folder, f"{base_name}.jpg")

            # 1. Scroll to the very top to ensure full capture starts correctly
            await page.evaluate("window.scrollTo(0, 0)")
            await page.wait_for_timeout(1000) # Wait for any last-second rendering/stabilization

            # 2. Take the single, full-page screenshot
            await page.screenshot(path=temp_full_path, full_page=True)
            log_info(f"✅ Saved full page screenshot for extraction: {temp_full_path}")


            return True

        except Exception as e:
            log_error(f"❌ Failed to capture Client Report Card: {e}")
            return False
        finally:
            await auth.stop()

    # Run async inside sync
    # loop = asyncio.get_event_loop()
    # if loop.is_running():
    #     return asyncio.ensure_future(runner())
    # else:
    #     return loop.run_until_complete(runner())

    # ✅ FIX THE ASYNCIO LOOP HANDLING HERE
    return asyncio.run(runner())


# --------------------------------------------------------------------------------------------------
## 1. Helper Functions for Category KPIs (COMPETITIVE, REPAIR, MAINTENANCE)
# --------------------------------------------------------------------------------------------------

def calculate_opcategory_kpis(op_category_name, total_df_3_month, total_df_L_month, total_df_L_3_month, total_shop_hours_3_month, total_shop_hours_L_month, round_off):
    """
    Calculates all 11 KPIs for a single operation category (e.g., 'REPAIR').
    CRITICAL FIX: Uses total_df_L_3_month for the Current Annual Pace calculation.
    """
    
    df_3_month = total_df_3_month[total_df_3_month['opcategory'] == op_category_name]
    df_L_month = total_df_L_month[total_df_L_month['opcategory'] == op_category_name]
    df_L_3_month = total_df_L_3_month[total_df_L_3_month['opcategory'] == op_category_name] # NEW: Filter L3M data
    
    category_results = []
    
    # --- Extract sums for all periods ---
    
    # 3-Month Period (3M)
    lbr_sale_3_month = float(df_3_month['lbrsale'].sum())
    lbr_cost_3_month = float(df_3_month['lbrcost'].sum())
    pts_sale_3_month = float(df_3_month['prtextendedsale'].sum())
    pts_cost_3_month = float(df_3_month['prtextendedcost'].sum())
    lbr_hours_3_month = df_3_month['lbrsoldhours'].sum()
    lbr_GP_3_month = lbr_sale_3_month - lbr_cost_3_month
    pts_GP_3_month = pts_sale_3_month - pts_cost_3_month
    
    # Last Month Period (LM)
    lbr_sale_L_month = float(df_L_month['lbrsale'].sum())
    lbr_cost_L_month = float(df_L_month['lbrcost'].sum())
    pts_sale_L_month = float(df_L_month['prtextendedsale'].sum())
    lbr_hours_L_month = df_L_month['lbrsoldhours'].sum()
    lbr_GP_L_month = lbr_sale_L_month - lbr_cost_L_month
    pts_GP_L_month = pts_sale_L_month - float(df_L_month['prtextendedcost'].sum())
    
    # Last 3-Month Period (L3M) - Used for Current Annual Pace
    lbr_sale_L_3_month = float(df_L_3_month['lbrsale'].sum())
    lbr_cost_L_3_month = float(df_L_3_month['lbrcost'].sum())
    pts_sale_L_3_month = float(df_L_3_month['prtextendedsale'].sum())
    pts_cost_L_3_month = float(df_L_3_month['prtextendedcost'].sum())
    lbr_hours_L_3_month = df_L_3_month['lbrsoldhours'].sum()
    
    # --- Annual Pace Calculations (Using L3M Averages) ---
    
    # Labor/Parts Sales & GP (L3M Avg * 12)
    lbr_sale_L_3_month_pace = round_off((lbr_sale_L_3_month / 3) * 12)
    lbr_GP_L_3_month_pace = round_off(((lbr_sale_L_3_month - lbr_cost_L_3_month) / 3) * 12)
    pts_sale_L_3_month_pace = round_off((pts_sale_L_3_month / 3) * 12)
    pts_GP_L_3_month_pace = round_off(((pts_sale_L_3_month - pts_cost_L_3_month) / 3) * 12)
    total_sale_L_3_month_pace = round_off(((lbr_sale_L_3_month + pts_sale_L_3_month) / 3) * 12)
    total_GP_L_3_month_pace = round_off(((lbr_sale_L_3_month + pts_sale_L_3_month - lbr_cost_L_3_month - pts_cost_L_3_month) / 3) * 12)
    hours_L_3_month_pace = round_off((lbr_hours_L_3_month / 3) * 12)
    
    # Labor/Parts GP % (L3M GP / L3M Sale * 100) - No annualization factor
    lbr_GP_perc_L_3_month = round_off(((lbr_sale_L_3_month - lbr_cost_L_3_month) / lbr_sale_L_3_month) * 100) if lbr_sale_L_3_month != 0 else 0
    pts_GP_perc_L_3_month = round_off(((pts_sale_L_3_month - pts_cost_L_3_month) / pts_sale_L_3_month) * 100) if pts_sale_L_3_month != 0 else 0
    
    # --- Recalculate KPI Rows ---

    # 1. Hours Sold
    hours_3_month_avg = round_off(lbr_hours_3_month / 3)
    hours_L_month = round_off(lbr_hours_L_month)
    hours_annual_3_month = round_off((lbr_hours_3_month / 3) * 12)
    
    category_results.append([
        "Hours Sold", hours_3_month_avg, hours_L_month, round_off(hours_L_month - hours_3_month_avg), 
        hours_annual_3_month, hours_L_3_month_pace, round_off(hours_L_3_month_pace - hours_annual_3_month)
    ])

    # 2. ELR (Rate KPIs are NOT annualized)
    ELR_3_month = round_off(lbr_sale_3_month / lbr_hours_3_month, 2) if lbr_hours_3_month != 0 else 0
    ELR_L_month = round_off(lbr_sale_L_month / lbr_hours_L_month, 2) if lbr_hours_L_month != 0 else 0
    
    category_results.append([
        "ELR", ELR_3_month, ELR_L_month, round_off(ELR_L_month - ELR_3_month, 2), 
        ELR_3_month, ELR_3_month, round_off(ELR_3_month - ELR_3_month, 2) # Annual pace is the same as 3M avg
    ])

    # 3. % of Total Shop Hours (Percentage KPIs are NOT annualized)
    perc_hours_3_month = round_off((lbr_hours_3_month / total_shop_hours_3_month) * 100) if total_shop_hours_3_month != 0 else 0
    perc_hours_L_month = round_off((lbr_hours_L_month / total_shop_hours_L_month) * 100) if total_shop_hours_L_month != 0 else 0

    category_results.append([
        "% of Total Shop Hours", perc_hours_3_month, perc_hours_L_month, round_off(perc_hours_L_month - perc_hours_3_month), 
        perc_hours_3_month, perc_hours_L_month, round_off(perc_hours_L_month - perc_hours_3_month) # Annual pace is L3M avg
    ])
    
    # 4. Total Labor Sold
    lbr_sale_3_month_avg = round_off(lbr_sale_3_month / 3)
    lbr_sale_annual_3_month = round_off((lbr_sale_3_month / 3) * 12)
    
    category_results.append([
        "Total Labor Sold", lbr_sale_3_month_avg, round_off(lbr_sale_L_month), round_off(lbr_sale_L_month - lbr_sale_3_month_avg), 
        lbr_sale_annual_3_month, lbr_sale_L_3_month_pace, round_off(lbr_sale_L_3_month_pace - lbr_sale_annual_3_month)
    ])

    # 5. Total Labor GP
    lbr_GP_3_month_avg = round_off(lbr_GP_3_month / 3)
    lbr_GP_annual_3_month = round_off((lbr_GP_3_month / 3) * 12)
    
    category_results.append([
        "Total Labor GP", lbr_GP_3_month_avg, round_off(lbr_GP_L_month), round_off(lbr_GP_L_month - lbr_GP_3_month_avg), 
        lbr_GP_annual_3_month, lbr_GP_L_3_month_pace, round_off(lbr_GP_L_3_month_pace - lbr_GP_annual_3_month)
    ])
    
    # 6. Total Labor GP% (Percentage KPIs are NOT annualized)
    lbr_GP_perc_3_month = round_off((lbr_GP_3_month / lbr_sale_3_month) * 100) if lbr_sale_3_month != 0 else 0
    lbr_GP_perc_L_month = round_off((lbr_GP_L_month / lbr_sale_L_month) * 100) if lbr_sale_L_month != 0 else 0

    category_results.append([
        "Total Labor GP%", lbr_GP_perc_3_month, lbr_GP_perc_L_month, round_off(lbr_GP_perc_L_month - lbr_GP_perc_3_month), 
        lbr_GP_perc_3_month, lbr_GP_perc_L_month, round_off(lbr_GP_perc_L_month - lbr_GP_perc_3_month) # Annual pace is L3M avg
    ])
    
    # 7. Total Parts Sale
    pts_sale_3_month_avg = round_off(pts_sale_3_month / 3)
    pts_sale_annual_3_month = round_off((pts_sale_3_month / 3) * 12)
    
    category_results.append([
        "Total Parts Sale", pts_sale_3_month_avg, round_off(pts_sale_L_month), round_off(pts_sale_L_month - pts_sale_3_month_avg), 
        pts_sale_annual_3_month, pts_sale_L_3_month_pace, round_off(pts_sale_L_3_month_pace - pts_sale_annual_3_month)
    ])
    
    # 8. Total Parts GP
    pts_GP_3_month_avg = round_off(pts_GP_3_month / 3)
    pts_GP_annual_3_month = round_off((pts_GP_3_month / 3) * 12)
    
    category_results.append([
        "Total Parts GP", pts_GP_3_month_avg, round_off(pts_GP_L_month), round_off(pts_GP_L_month - pts_GP_3_month_avg), 
        pts_GP_annual_3_month, pts_GP_L_3_month_pace, round_off(pts_GP_L_3_month_pace - pts_GP_annual_3_month)
    ])

    # 9. Total Parts GP% (Percentage KPIs are NOT annualized)
    pts_GP_perc_3_month = round_off((pts_GP_3_month / pts_sale_3_month) * 100) if pts_sale_3_month != 0 else 0
    pts_GP_perc_L_month = round_off((pts_GP_L_month / pts_sale_L_month) * 100) if pts_sale_L_month != 0 else 0

    category_results.append([
        "Total Parts GP%", pts_GP_perc_3_month, pts_GP_perc_L_month, round_off(pts_GP_perc_L_month - pts_GP_perc_3_month), 
        pts_GP_perc_3_month, pts_GP_perc_L_month, round_off(pts_GP_perc_L_month - pts_GP_perc_3_month) # Annual pace is L3M avg
    ])

    # 10. Total Lbr & Pts Sales
    total_sale_3_month = lbr_sale_3_month + pts_sale_3_month
    total_sale_L_month = lbr_sale_L_month + pts_sale_L_month
    total_sale_3_month_avg = round_off(total_sale_3_month / 3)
    total_sale_annual_3_month = round_off((total_sale_3_month / 3) * 12)
    
    category_results.append([
        "Total Lbr & Pts Sales", total_sale_3_month_avg, round_off(total_sale_L_month), round_off(total_sale_L_month - total_sale_3_month_avg), 
        total_sale_annual_3_month, total_sale_L_3_month_pace, round_off(total_sale_L_3_month_pace - total_sale_annual_3_month)
    ])
    
    # 11. Lbr & Pts GP
    total_GP_3_month = total_sale_3_month - (lbr_cost_3_month + pts_cost_3_month)
    total_GP_L_month = total_sale_L_month - (lbr_cost_L_month + float(df_L_month['prtextendedcost'].sum()))
    total_GP_3_month_avg = round_off(total_GP_3_month / 3)
    total_GP_annual_3_month = round_off((total_GP_3_month / 3) * 12)
    
    category_results.append([
        "Lbr & Pts GP", total_GP_3_month_avg, round_off(total_GP_L_month), round_off(total_GP_L_month - total_GP_3_month_avg), 
        total_GP_annual_3_month, total_GP_L_3_month_pace, round_off(total_GP_L_3_month_pace - total_GP_annual_3_month)
    ])

    return category_results


# In your calculation script (before writing to JSON)

def map_kpis_to_dict(kpi_list, ClientReportCard3Month):
    """
    Converts the list of KPI rows into the required dictionary format,
    where each KPI contains a dictionary of the 6 metric headers mapped to their values.
    """
    kpi_dict = {}
    
    KPI_HEADERS = [
        "3 MTH Avg (Baseline)", 
        "Last Month", 
        "Variance", 
        "Prior Annual Pace", 
        "Annual Pace", 
        "Variance Annualized"
    ]
    
    # You would need the key_mapping dictionary here from your existing code
    # e.g., key_mapping = {'Hours Sold': ClientReportCard3Month.hours_sold, ...}
    
    for row in kpi_list:
        kpi_name = row[0]
        kpi_values = row[1:] # This is the list of 6 raw numbers
        
        # This assumes your calculated JSON structure uses the KPI name string as the key
        # If it uses the ClientReportCard3Month constant as the key, use kpi_key = key_mapping[kpi_name]
        kpi_key = kpi_name 
        
        if len(kpi_values) == len(KPI_HEADERS):
            # Map the list of values to the dictionary of headers
            inner_metrics_dict = dict(zip(KPI_HEADERS, kpi_values))
        else:
            inner_metrics_dict = {} 
            
        kpi_dict[kpi_key] = inner_metrics_dict

    return kpi_dict

# --------------------------------------------------------------------------------------------------
## 2. Function for Overall Shop KPIs and Top-Level Metrics
# --------------------------------------------------------------------------------------------------

def calculate_top_level_kpis(df_3m, df_Lm, df_L3m, dms_fees, fopc_fees, round_off, ClientReportCard3Month):
    """
    Calculates all overall shop KPIs (kpi_section) and top-level ROI/Fees metrics.
    Returns: A tuple (kpi_section_dict, top_level_dict, Sold_hours_3_month, Sold_hours_L_month_value)
    """

    # --- Initialization ---
    (Sold_hours_3_month, Sold_hours_L_month_value) = (0, 0)
    (RO_count_3_month_avg, RO_count_L_month, RO_count_prior_annual_pace, RO_count_current_annual_pace) = (0,) * 4
    (Sold_hours_3_month_avg, Sold_hours_L_month, Sold_hours_prior_annual_pace, Sold_hours_current_annual_pace) = (0,) * 4
    (Hours_per_RO_3_month_avg, Hours_per_RO_L_month, Hours_per_RO_prior_annual_pace, Hours_per_RO_current_annual_pace) = (0,) * 4
    (Total_ELR_3_month, Total_ELR_L_month, Total_ELR_prior_annual_pace, Total_ELR_current_annual_pace) = (0,) * 4
    (Repair_ELR_3_month_avg, Repair_ELR_L_month, Repair_ELR_current_annual_pace) = (0,) * 3
    (Labor_GP_perc_3_month_avg, Labor_GP_perc_L_month, Labor_GP_perc_prior_annual_pace, Labor_GP_perc_current_annual_pace) = (0,) * 4
    (Parts_GP_perc_3_month_avg, Parts_GP_perc_L_month, Parts_GP_perc_prior_annual_pace, Parts_GP_perc_current_annual_pace) = (0,) * 4
    (Labor_sale_3_month_avg, Labor_sale_L_month, Labor_sale_prior_annual_pace, Labor_sale_current_annual_pace) = (0,) * 4
    (Labor_GP_3_month_avg, Labor_GP_L_month, Labor_GP_prior_annual_pace, Labor_GP_current_annual_pace) = (0,) * 4
    (Parts_sale_3_month_avg, Parts_sale_L_month, Parts_sale_prior_annual_pace, Parts_sale_current_annual_pace) = (0,) * 4
    (Parts_GP_3_month_avg, Parts_GP_L_month, Parts_GP_prior_annual_pace, Parts_GP_current_annual_pace) = (0,) * 4
    (Labor_Parts_sale_3_month_avg, Labor_Parts_sale_L_month, Labor_Parts_sale_prior_annual_pace, Labor_Parts_sale_current_annual_pace) = (0,) * 4
    (Labor_Parts_GP_3_month_avg, Labor_Parts_GP_L_month, Labor_Parts_GP_prior_annual_pace, Labor_Parts_GP_current_annual_pace) = (0,) * 4
    
    (Labor_sale_value_3_month, Parts_sale_value_3_month, Labor_cost_value_3_month, Parts_cost_value_3_month) = (0,) * 4
    (Labor_sale_value_L_month, Parts_sale_value_L_month, Labor_cost_value_L_month, Parts_cost_value_L_month) = (0,) * 4
    (Labor_sale_value_L_3_month, Parts_sale_value_L_3_month, Labor_cost_value_L_3_month, Parts_cost_value_L_3_month) = (0,) * 4
    
    
    # --- 3-Month Period (df_3m) ---
    if not df_3m.empty:
        RO_count_3_month = df_3m['unique_ro_number'].nunique()
        RO_count_3_month_avg = round_off(RO_count_3_month / 3)
        RO_count_prior_annual_pace = round_off(RO_count_3_month_avg * 12)
        
        Sold_hours_3_month = df_3m[(df_3m['opcategory'] != 'N/A') & (df_3m['opcategory'] != 'SHOP SUPPLIES')]['lbrsoldhours'].sum()
        Sold_hours_3_month_avg = round_off(Sold_hours_3_month / 3)
        Sold_hours_prior_annual_pace = round_off(Sold_hours_3_month_avg * 12)
        
        if RO_count_3_month != 0:
            Hours_per_RO_3_month_avg = round_off(Sold_hours_3_month / RO_count_3_month)
        if Sold_hours_3_month != 0:
            Total_ELR_3_month = round_off(df_3m['lbrsale'].sum() / Sold_hours_3_month, 2)
        Total_ELR_prior_annual_pace = Total_ELR_3_month
        
        total_CP_revenue_3_month_REP = df_3m[df_3m['opcategory'] == 'REPAIR']
        if not total_CP_revenue_3_month_REP.empty and total_CP_revenue_3_month_REP['lbrsoldhours'].sum() != 0:
            Repair_ELR_3_month_avg = round_off(total_CP_revenue_3_month_REP['lbrsale'].sum() / total_CP_revenue_3_month_REP['lbrsoldhours'].sum(), 2)
        
        Labor_sale_value_3_month = float(df_3m['lbrsale'].sum())
        Labor_cost_value_3_month = float(df_3m['lbrcost'].sum())
        Parts_sale_value_3_month = float(df_3m['prtextendedsale'].sum())
        Parts_cost_value_3_month = float(df_3m['prtextendedcost'].sum())

        if Labor_sale_value_3_month != 0:
            Labor_GP_perc_3_month_avg = round_off(((Labor_sale_value_3_month - Labor_cost_value_3_month) / Labor_sale_value_3_month) * 100)
        if Parts_sale_value_3_month != 0:
            Parts_GP_perc_3_month_avg = round_off(((Parts_sale_value_3_month - Parts_cost_value_3_month) / Parts_sale_value_3_month) * 100)

        Labor_sale_3_month_avg = round_off(Labor_sale_value_3_month / 3)
        Labor_GP_3_month_avg = round_off((Labor_sale_value_3_month - Labor_cost_value_3_month) / 3)
        Parts_sale_3_month_avg = round_off(Parts_sale_value_3_month / 3)
        Parts_GP_3_month_avg = round_off((Parts_sale_value_3_month - Parts_cost_value_3_month) / 3)
        Labor_Parts_sale_3_month_avg = round_off((Labor_sale_value_3_month + Parts_sale_value_3_month) / 3)
        Labor_Parts_GP_3_month_avg = round_off(((Labor_sale_value_3_month + Parts_sale_value_3_month) - (Labor_cost_value_3_month + Parts_cost_value_3_month)) / 3)
        
        Labor_GP_perc_prior_annual_pace = Labor_GP_perc_3_month_avg
        Parts_GP_perc_prior_annual_pace = Parts_GP_perc_3_month_avg
        Labor_sale_prior_annual_pace = round_off(Labor_sale_3_month_avg * 12)
        Labor_GP_prior_annual_pace = round_off(Labor_GP_3_month_avg * 12)
        Parts_sale_prior_annual_pace = round_off(Parts_sale_3_month_avg * 12)
        Parts_GP_prior_annual_pace = round_off(Parts_GP_3_month_avg * 12)
        Labor_Parts_sale_prior_annual_pace = round_off(Labor_Parts_sale_3_month_avg * 12)
        Labor_Parts_GP_prior_annual_pace = round_off(Labor_Parts_GP_3_month_avg * 12)
        Hours_per_RO_prior_annual_pace = Hours_per_RO_3_month_avg # Simplified

    # --- Last Month Period (df_Lm) ---
    if not df_Lm.empty:
        RO_count_L_month = df_Lm['unique_ro_number'].nunique()
        Sold_hours_L_month_value = df_Lm['lbrsoldhours'].sum()
        Sold_hours_L_month = round_off(Sold_hours_L_month_value)
        
        if RO_count_L_month != 0:
            Hours_per_RO_L_month = round_off(Sold_hours_L_month_value / RO_count_L_month)
        if Sold_hours_L_month_value != 0:
            Total_ELR_L_month = round_off(df_Lm['lbrsale'].sum() / Sold_hours_L_month_value, 2)
            
        total_CP_revenue_L_month_REP = df_Lm[df_Lm['opcategory'] == 'REPAIR']
        if not total_CP_revenue_L_month_REP.empty and total_CP_revenue_L_month_REP['lbrsoldhours'].sum() != 0:
            Repair_ELR_L_month = round_off(total_CP_revenue_L_month_REP['lbrsale'].sum() / total_CP_revenue_L_month_REP['lbrsoldhours'].sum(), 2)

        Labor_sale_value_L_month = float(df_Lm['lbrsale'].sum())
        Labor_cost_value_L_month = float(df_Lm['lbrcost'].sum())
        Parts_sale_value_L_month = float(df_Lm['prtextendedsale'].sum())
        Parts_cost_value_L_month = float(df_Lm['prtextendedcost'].sum())

        if Labor_sale_value_L_month != 0:
            Labor_GP_perc_L_month = round_off(((Labor_sale_value_L_month - Labor_cost_value_L_month) / Labor_sale_value_L_month) * 100)
        if Parts_sale_value_L_month != 0:
            Parts_GP_perc_L_month = round_off(((Parts_sale_value_L_month - Parts_cost_value_L_month) / Parts_sale_value_L_month) * 100)
            
        Labor_sale_L_month = round_off(Labor_sale_value_L_month)
        Labor_GP_L_month = round_off(Labor_sale_value_L_month - Labor_cost_value_L_month)
        Parts_sale_L_month = round_off(Parts_sale_value_L_month)
        Parts_GP_L_month = round_off(Parts_sale_value_L_month - Parts_cost_value_L_month)
        Labor_Parts_sale_L_month = round_off(Labor_sale_value_L_month + Parts_sale_value_L_month)
        Labor_Parts_GP_L_month = round_off((Labor_sale_value_L_month + Parts_sale_value_L_month) - (Labor_cost_value_L_month + Parts_cost_value_L_month))

    # --- Last 3-Month Period (df_L3m - for Current Annual Pace) ---
    if not df_L3m.empty:
        RO_count_L_3_month = df_L3m['unique_ro_number'].nunique()
        Sold_hours_L_3_month = df_L3m['lbrsoldhours'].sum()
        Sold_hours_L_3_month_avg = Sold_hours_L_3_month / 3
        
        RO_count_current_annual_pace = round_off((RO_count_L_3_month / 3) * 12)
        Sold_hours_current_annual_pace = round_off(Sold_hours_L_3_month_avg * 12)
        
        if RO_count_L_3_month != 0:
            Hours_per_RO_current_annual_pace = round_off(Sold_hours_L_3_month / RO_count_L_3_month)
        if Sold_hours_L_3_month != 0:
            Total_ELR_current_annual_pace = round_off(df_L3m['lbrsale'].sum() / Sold_hours_L_3_month, 2)
            
        Labor_sale_value_L_3_month = float(df_L3m['lbrsale'].sum())
        Labor_cost_value_L_3_month = float(df_L3m['lbrcost'].sum())
        Parts_sale_value_L_3_month = float(df_L3m['prtextendedsale'].sum())
        Parts_cost_value_L_3_month = float(df_L3m['prtextendedcost'].sum())
        
        if Labor_sale_value_L_3_month != 0:
            Labor_GP_perc_current_annual_pace = round_off(((Labor_sale_value_L_3_month - Labor_cost_value_L_3_month) / Labor_sale_value_L_3_month) * 100)
        if Parts_sale_value_L_3_month != 0:
            Parts_GP_perc_current_annual_pace = round_off(((Parts_sale_value_L_3_month - Parts_cost_value_L_3_month) / Parts_sale_value_L_3_month) * 100)
        
        Labor_sale_current_annual_pace = round_off((Labor_sale_value_L_3_month / 3) * 12)
        Labor_GP_current_annual_pace = round_off(((Labor_sale_value_L_3_month - Labor_cost_value_L_3_month) / 3) * 12)
        Parts_sale_current_annual_pace = round_off((Parts_sale_value_L_3_month / 3) * 12)
        Parts_GP_current_annual_pace = round_off(((Parts_sale_value_L_3_month - Parts_cost_value_L_3_month) / 3) * 12)
        Labor_Parts_sale_current_annual_pace = round_off(((Labor_sale_value_L_3_month + Parts_sale_value_L_3_month) / 3) * 12)
        Labor_Parts_GP_current_annual_pace = round_off((((Labor_sale_value_L_3_month + Parts_sale_value_L_3_month) - (Labor_cost_value_L_3_month + Parts_cost_value_L_3_month)) / 3) * 12)
        
        total_CP_revenue_L_3_month_REP = df_L3m[df_L3m['opcategory'] == 'REPAIR']
        if not total_CP_revenue_L_3_month_REP.empty and total_CP_revenue_L_3_month_REP['lbrsoldhours'].sum() != 0:
            Repair_ELR_current_annual_pace = round_off((total_CP_revenue_L_3_month_REP['lbrsale'].sum() / total_CP_revenue_L_3_month_REP['lbrsoldhours'].sum()), 2)

    # --- Variance Calculations ---
    RO_count_1_month_variance = RO_count_L_month - RO_count_3_month_avg
    RO_count_annual_variance = RO_count_current_annual_pace - RO_count_prior_annual_pace
    Sold_hours_1_month_variance = round_off(Sold_hours_L_month - Sold_hours_3_month_avg)
    Sold_hours_annual_variance = round_off(Sold_hours_current_annual_pace - Sold_hours_prior_annual_pace)
    Hours_per_RO_1_month_variance = round_off(Hours_per_RO_L_month - Hours_per_RO_3_month_avg)
    Hours_per_RO_annual_variance = round_off(Hours_per_RO_current_annual_pace - Hours_per_RO_prior_annual_pace)
    Total_ELR_1_month_variance = round_off(Total_ELR_L_month - Total_ELR_3_month, 2)
    Total_ELR_annual_variance = round_off(Total_ELR_current_annual_pace - Total_ELR_prior_annual_pace, 2)
    Repair_ELR_1_month_variance = Repair_ELR_L_month - Repair_ELR_3_month_avg
    Repair_ELR_annual_variance = round_off(Repair_ELR_current_annual_pace - Repair_ELR_3_month_avg, 2)
    Labor_GP_perc_1_month_variance = round_off(Labor_GP_perc_L_month - Labor_GP_perc_3_month_avg)
    Labor_GP_perc_annual_variance = round_off(Labor_GP_perc_current_annual_pace - Labor_GP_perc_prior_annual_pace)
    Parts_GP_perc_1_month_variance = round_off(Parts_GP_perc_L_month - Parts_GP_perc_3_month_avg)
    Parts_GP_perc_annual_variance = round_off(Parts_GP_perc_current_annual_pace - Parts_GP_perc_prior_annual_pace)
    Labor_sale_1_month_variance = round_off(Labor_sale_L_month - Labor_sale_3_month_avg)
    Labor_sale_annual_variance = round_off(Labor_sale_current_annual_pace - Labor_sale_prior_annual_pace)
    Labor_GP_1_month_variance = round_off(Labor_GP_L_month - Labor_GP_3_month_avg)
    Labor_GP_annual_variance = round_off(Labor_GP_current_annual_pace - Labor_GP_prior_annual_pace)
    Parts_sale_1_month_variance = round_off(Parts_sale_L_month - Parts_sale_3_month_avg)
    Parts_sale_annual_variance = round_off(Parts_sale_current_annual_pace - Parts_sale_prior_annual_pace)
    Parts_GP_1_month_variance = round_off(Parts_GP_L_month - Parts_GP_3_month_avg)
    Parts_GP_annual_variance = round_off(Parts_GP_current_annual_pace - Parts_GP_prior_annual_pace)
    Labor_Parts_sale_1_month_variance = round_off(Labor_Parts_sale_L_month - Labor_Parts_sale_3_month_avg)
    Labor_Parts_sale_annual_variance = round_off(Labor_Parts_sale_current_annual_pace - Labor_Parts_sale_prior_annual_pace)
    Labor_Parts_GP_1_month_variance = round_off(Labor_Parts_GP_L_month - Labor_Parts_GP_3_month_avg)
    Labor_Parts_GP_annual_variance = round_off(Labor_Parts_GP_current_annual_pace - Labor_Parts_GP_prior_annual_pace)

    # --- ROI Calculations (Top-Level Items) ---
    Monthly_fees = float(dms_fees + fopc_fees)
    Lbr_Pts_GP_1_month_ROI = 0
    Sold_hour_L_month_REP = round_off(df_Lm[df_Lm['opcategory'] == 'REPAIR']['lbrsoldhours'].sum())
    Sold_hour_L_3_month_REP = ((df_L3m[df_L3m['opcategory'] == 'REPAIR']['lbrsoldhours'].sum()) / 3) * 12
    
    if Monthly_fees != 0:
        Lbr_Pts_GP_1_month_ROI = round_off(((float(Labor_Parts_GP_1_month_variance) - float(Monthly_fees)) / float(Monthly_fees)) * 100)
        Rep_ELR_1_month_ROI = round_off((round_off((float(Repair_ELR_1_month_variance) * float(Sold_hour_L_month_REP)) - float(Monthly_fees)) / float(Monthly_fees)) * 100)
        # Lbr_Pts_GP_annual_ROI is not a required top-level key but we include the logic for completeness
        # Lbr_Pts_GP_annual_ROI = round_off(((float(Labor_Parts_GP_annual_variance) - (float(Monthly_fees) * 12)) / (float(Monthly_fees) * 12)) * 100) 
        # Rep_ELR_annual_ROI is not a required top-level key but we include the logic for completeness
        # Rep_ELR_annual_ROI = round_off(((round_off(float(Repair_ELR_annual_variance) * round_off(Sold_hour_L_3_month_REP)) - (float(Monthly_fees)) * 12) / (float(Monthly_fees) * 12)) * 100)


    # --- Construct Top-Level Dictionary ---
    top_level_dict = {
        ClientReportCard3Month.monthly_fopc: float(fopc_fees),
        ClientReportCard3Month.monthly_dms: float(dms_fees),
        ClientReportCard3Month.total: Monthly_fees,
        ClientReportCard3Month.roi: Lbr_Pts_GP_1_month_ROI,
        ClientReportCard3Month.total_gp_change: Labor_Parts_GP_1_month_variance,
        ClientReportCard3Month.repair_elr_change: round_off((float(Repair_ELR_1_month_variance)) * round_off(Sold_hour_L_month_REP)),
        ClientReportCard3Month.total_gp_change_annualized: Labor_Parts_GP_annual_variance,
        ClientReportCard3Month.repair_elr_change_annualized: round_off(float(Repair_ELR_annual_variance) * round_off(Sold_hour_L_3_month_REP)),
    }

    # --- Construct KPI Section Dictionary ---
    kpi_section_dict = {
        ClientReportCard3Month.ro_count: [RO_count_3_month_avg, RO_count_L_month, RO_count_1_month_variance, RO_count_prior_annual_pace, RO_count_current_annual_pace, RO_count_annual_variance],
        ClientReportCard3Month.hours_sold: [Sold_hours_3_month_avg, Sold_hours_L_month, Sold_hours_1_month_variance, Sold_hours_prior_annual_pace, Sold_hours_current_annual_pace, Sold_hours_annual_variance],
        ClientReportCard3Month.cust_pay_hrs_per_ro: [Hours_per_RO_3_month_avg, Hours_per_RO_L_month, Hours_per_RO_1_month_variance, Hours_per_RO_prior_annual_pace, Hours_per_RO_current_annual_pace, Hours_per_RO_annual_variance],
        ClientReportCard3Month.total_shop_elr: [Total_ELR_3_month, Total_ELR_L_month, Total_ELR_1_month_variance, Total_ELR_prior_annual_pace, Total_ELR_current_annual_pace, Total_ELR_annual_variance],
        ClientReportCard3Month.total_labor_gp_percent:[Labor_GP_perc_3_month_avg, Labor_GP_perc_L_month, Labor_GP_perc_1_month_variance, Labor_GP_perc_prior_annual_pace, Labor_GP_perc_current_annual_pace, Labor_GP_perc_annual_variance],
        ClientReportCard3Month.total_parts_gp_percent:[Parts_GP_perc_3_month_avg, Parts_GP_perc_L_month, Parts_GP_perc_1_month_variance, Parts_GP_perc_prior_annual_pace, Parts_GP_perc_current_annual_pace, Parts_GP_perc_annual_variance],
        ClientReportCard3Month.total_labor_sold:[Labor_sale_3_month_avg, Labor_sale_L_month, Labor_sale_1_month_variance, Labor_sale_prior_annual_pace, Labor_sale_current_annual_pace, Labor_sale_annual_variance],
        ClientReportCard3Month.total_labor_gp:[Labor_GP_3_month_avg, Labor_GP_L_month, Labor_GP_1_month_variance, Labor_GP_prior_annual_pace, Labor_GP_current_annual_pace, Labor_GP_annual_variance],
        ClientReportCard3Month.total_parts_sale:[Parts_sale_3_month_avg, Parts_sale_L_month, Parts_sale_1_month_variance, Parts_sale_prior_annual_pace, Parts_sale_current_annual_pace, Parts_sale_annual_variance],
        ClientReportCard3Month.total_parts_gp:[Parts_GP_3_month_avg, Parts_GP_L_month, Parts_GP_1_month_variance, Parts_GP_prior_annual_pace, Parts_GP_current_annual_pace, Parts_GP_annual_variance],
        ClientReportCard3Month.total_lbr_pts_sales:[Labor_Parts_sale_3_month_avg, Labor_Parts_sale_L_month, Labor_Parts_sale_1_month_variance, Labor_Parts_sale_prior_annual_pace, Labor_Parts_sale_current_annual_pace, Labor_Parts_sale_annual_variance],
        ClientReportCard3Month.total_lbr_pts_gp_kpi:[Labor_Parts_GP_3_month_avg, Labor_Parts_GP_L_month, Labor_Parts_GP_1_month_variance, Labor_Parts_GP_prior_annual_pace, Labor_Parts_GP_current_annual_pace, Labor_Parts_GP_annual_variance]
    }
    
    return kpi_section_dict, top_level_dict, Sold_hours_3_month, Sold_hours_L_month_value

  

def run_validation():

    
    start_time = time.time()
    log_info(f"Start Time: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    # Initialize components and capture UI
    ui_capture_success = automate_site(config)

    # Check if UI capture failed before proceeding
    if not ui_capture_success:
        log_warn("UI extraction failed. Skipping database validation and comparison.")
        return
  
              

    # Get the required environment variables
    storeid = config.store_id
    
    dms_fees = Decimal(os.environ.get('dms_fees', "0"))
    fopc_fees = Decimal(os.environ.get('fopc_fees',"0"))

    s_date_env = config.start_date
    e_date_env = config.end_date

    # Convert to pandas Timestamp (ensures proper handling of date formats)
    s_date = pd.Timestamp(s_date_env)
    e_date = pd.Timestamp(e_date_env)

    # Calculate required date range (3-month prior)
    required_date_start = pd.date_range(start=s_date, end=e_date, freq='MS').strftime('%Y-%m').tolist()
    start_dt = datetime.strptime(required_date_start[0], "%Y-%m")
    required_date_range = [(start_dt + relativedelta(months=i)).strftime("%Y-%m") for i in range(3)]
    
    # calculate date ranges (Last 3 months)
    last_month = config.last_month
    last_month_date = datetime.strptime(last_month, "%Y-%m")
    date_ranges = [(last_month_date - relativedelta(months=i)).strftime("%Y-%m") for i in range(3)]

    # Fetching data from DB
    all_revenue_details_df = config.all_revenue_details_for_client_report_card_3_month
    retail_flag_all = config.retail_flag_all
    retail_flag = set(retail_flag_all['source_paytype'])

    # Initializing new data frame to filter only required advisor and technician
    filtered_df = all_revenue_details_df[
        (all_revenue_details_df['department'] == 'Service') &
        (all_revenue_details_df['opcategory'] != 'N/A') &
        (all_revenue_details_df['opcategory'].isin(['REPAIR','COMPETITIVE','MAINTENANCE'])) &
        (all_revenue_details_df['hide_ro'] != True)
        & (all_revenue_details_df['store_id'].astype(str).str.strip() == storeid)
    ]

    merged_df = filtered_df.merge(
        retail_flag_all,
        left_on=['paytypegroup', 'store_id'],
        right_on=['source_paytype', 'store_id'],
        how='left'
    )

    merged_df = merged_df.copy()
    merged_df['unique_ro_number'] = merged_df['ronumber'].astype(str) + '_' + merged_df['closeddate'].astype(str)

    # Define customer and warranty pay types dynamically
    if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
        customer_pay_types = {'C'}
    elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
        customer_pay_types = {'C', 'M'}
    elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
        customer_pay_types = {'C', 'E'}
    elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
        customer_pay_types = {'C', 'E', 'M'}
    else:
        customer_pay_types = {} # Default to empty if logic is not met

    total_CP_revenue_details_df = merged_df[merged_df['paytypegroup'].isin(customer_pay_types)]
    total_CP_revenue_details_df = total_CP_revenue_details_df.copy()

    # Convert to numeric and filter records where all values are 0
    total_CP_revenue_details_df.loc[:, 'lbrsale'] = pd.to_numeric(total_CP_revenue_details_df['lbrsale'], errors='coerce').fillna(0)
    total_CP_revenue_details_df.loc[:, 'lbrsoldhours'] = pd.to_numeric(total_CP_revenue_details_df['lbrsoldhours'], errors='coerce').fillna(0)
    total_CP_revenue_details_df.loc[:, 'prtextendedsale'] = pd.to_numeric(total_CP_revenue_details_df['prtextendedsale'], errors='coerce').fillna(0)
    total_CP_revenue_details_df.loc[:, 'prtextendedcost'] = pd.to_numeric(total_CP_revenue_details_df['prtextendedcost'], errors='coerce').fillna(0)

    total_CP_revenue_details_df = total_CP_revenue_details_df[
        ~((total_CP_revenue_details_df['lbrsale'] == 0) &
        (total_CP_revenue_details_df['lbrsoldhours'] == 0) &
        (total_CP_revenue_details_df['prtextendedsale'] == 0) &
        (total_CP_revenue_details_df['prtextendedcost'] == 0))
    ]

    # Split DataFrames
    total_CP_revenue_L_month = total_CP_revenue_details_df[total_CP_revenue_details_df['month_year'] == last_month]
    total_CP_revenue_L_3_month = total_CP_revenue_details_df[total_CP_revenue_details_df['month_year'].isin(date_ranges)]
    total_CP_revenue_3_month = total_CP_revenue_details_df[total_CP_revenue_details_df['month_year'].isin(required_date_range)]


    # --- 1. Calculate Top-Level/KPI Section ---
    # This function contains all the logic from lines 351 to 506 in the original snippet.
    (
        kpi_section_data, 
        top_level_metrics, 
        Sold_hours_3_month_total, 
        Sold_hours_L_month_total
    ) = calculate_top_level_kpis(
        total_CP_revenue_3_month, 
        total_CP_revenue_L_month, 
        total_CP_revenue_L_3_month, 
        dms_fees, 
        fopc_fees, 
        round_off,
        ClientReportCard3Month
    )

    # Log the Top-Level Metrics (Fees/ROI)
    log_info("--- CALCULATED TOP-LEVEL METRICS (FEES/ROI) ---")
    log_info(str(top_level_metrics))

    # Log the Overall KPI Section Data
    log_info("--- CALCULATED OVERALL KPI SECTION DATA ---")
    log_info(str(kpi_section_data))

    # --- 2. Calculate OpCategory Sections (Competitive, Maintenance, Repair) ---
    
    # Competitive
    competitive_kpis_list = calculate_opcategory_kpis(
        'COMPETITIVE', 
        total_CP_revenue_3_month, 
        total_CP_revenue_L_month, 
        total_CP_revenue_L_3_month,  # <-- NEW ARGUMENT
        Sold_hours_3_month_total, 
        Sold_hours_L_month_total,
        round_off
    )
    competitive_section_data = map_kpis_to_dict(competitive_kpis_list, ClientReportCard3Month)
    

    # Log Competitive Section Data
    log_info("--- CALCULATED COMPETITIVE SECTION DATA ---")
    log_info(str(competitive_section_data))

    # Maintenance
    maintenance_kpis_list = calculate_opcategory_kpis(
        'MAINTENANCE', 
        total_CP_revenue_3_month, 
        total_CP_revenue_L_month, 
        total_CP_revenue_L_3_month,  # <-- NEW ARGUMENT
        Sold_hours_3_month_total, 
        Sold_hours_L_month_total,
        round_off
    )
    maintenance_section_data = map_kpis_to_dict(maintenance_kpis_list, ClientReportCard3Month)
    

    # Log Maintenance Section Data
    log_info("--- CALCULATED MAINTENANCE SECTION DATA ---")
    log_info(str(maintenance_section_data))

    # Repair
    repair_kpis_list = calculate_opcategory_kpis(
        'REPAIR', 
        total_CP_revenue_3_month, 
        total_CP_revenue_L_month, 
        total_CP_revenue_L_3_month,  # <-- NEW ARGUMENT
        Sold_hours_3_month_total, 
        Sold_hours_L_month_total,
        round_off
    )
    repair_section_data = map_kpis_to_dict(repair_kpis_list, ClientReportCard3Month)
    
    # Log Repair Section Data
    log_info("--- CALCULATED REPAIR SECTION DATA ---")
    log_info(str(repair_section_data))
    
    # --- 3. Construct Final Dictionary (Merging Top-Level and Sections) ---
    Client_Report_Card = top_level_metrics
    Client_Report_Card[ClientReportCard3Month.kpi_section] = kpi_section_data
    Client_Report_Card[ClientReportCard3Month.competitive_section] = competitive_section_data
    Client_Report_Card[ClientReportCard3Month.maintenance_section] = maintenance_section_data
    Client_Report_Card[ClientReportCard3Month.repair_section] = repair_section_data

    

    # --- 4. Log the Final Calculated Data Structure ---
    
    log_info("--- FINAL CALCULATED CLIENT REPORT CARD DATA START ---")
    log_info(str(Client_Report_Card))    
    log_info("--- FINAL CALCULATED CLIENT REPORT CARD DATA END ---")


    # Build the output path

    log_info("Writing the calculation results to JSON for Client Report Card 3 months")
    # Build the output path
    # json_output_path is the path to your calculated data
    _, json_output_path = create_folder_file_path(
        subfolder=omni_folder,
        output_file=dict_json, # This is the Calculated Data path (e.g., client_report_card_3_months.json)
    )

    # Save the Client_Report_Card data to the JSON file
    with open(json_output_path, "w") as json_file:
        json.dump(Client_Report_Card, json_file, indent=4)
   
    log_info("Starting UI image data extraction for  Client Report Card 3 months")
    _, full_image_path = create_folder_file_path(subfolder=omni_folder, output_file=dict_jpg)

    # Extract data from image to md and pdf
    extract_image_data(client_report_three_month_prompt, full_image_path)
    


    # 2. CRITICAL FIX: REMOVE OLD/INCORRECT UI DATA LOADING LOGIC
    # The following lines are now redundant and should be REMOVED or commented out,
    # as the merged JSON is already in 'ui_data_path'.

    # # File paths (REMOVE)
    # file1_path = os.path.join(result_folder, f"{os.path.splitext(dict_jpg)[0]}1.md")
    # file2_path = os.path.join(result_folder, f"{os.path.splitext(dict_jpg)[0]}2.md")
    # # Load JSON from primary and secondary files (REMOVE)
    # with open(file1_path, "r") as f:
    #     primary_data = json.load(f)
    # with open(file2_path, "r") as f:
    #     secondary_data = json.load(f)
    # filled_screen1 = merge_missing_values(primary_data, secondary_data)
    # # Save updated data back to file1 (REMOVE)
    # with open(file1_path, "w") as f:
    #     json.dump(filled_screen1, f, indent=2)
    # # Set the output path for comparison (REMOVE)
    # output_md_file_path = file1_path


    # 3. CRITICAL FIX: PASS THE CORRECT PATHS TO THE COMPARISON FUNCTION
    # Pass the calculated data path and the NEWLY MERGED UI data path.
    # Order is typically (Calculated, UI) or (Primary, Secondary). Based on your previous logic,
    # we assume compare_client_report_card_3_months expects (UI, Calculated).

    _, output_md_file_path = create_folder_file_path(subfolder=omni_folder, output_file=dict_md)
    # Assuming compare_client_report_card_3_months(file1_UI, file2_CALCULATED)
    compare_client_report_card_3_months(output_md_file_path, json_output_path)
    log_info("Completed the validation of Client Report Card 3 months")

    return True
import asyncio
import json
import os
import time
import glob
import traceback
import pandas as pd
import numpy as np
from datetime import datetime
from math import isfinite, isnan
from playwright.async_api import async_playwright
from concurrent.futures import ThreadPoolExecutor
import threading
from datetime import datetime
from collections import defaultdict
import re
from decimal import Decimal, ROUND_HALF_UP
import openpyxl
import openpyxl.cell
from openpyxl.styles import <PERSON><PERSON><PERSON>ill, Alignment, Font
from openpyxl.utils import get_column_letter
import csv
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.pattern.config import config
from lib.pattern.qa_auto_core.compare_cp_parts_overview import compare_cp_parts_overview_results
from lib.pattern.qa_auto_core.db_handler.db_connector import allRevenueDetailsCPOverview
from dateutil.relativedelta import relativedelta
from datetime import timedelta

from lib.std.universal.authmanager import AuthManager
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.chart_dict import VALIDATION_CHARTS


chart_key="cp_parts_overview"
chart_process_json = VALIDATION_CHARTS[chart_key]["chart_process_json"]
db_json = VALIDATION_CHARTS[chart_key]["db_json"]


#  Target months-years for drilling down (modify as needed)
TARGET_MONTHS_YEARS = config.target_month_year
# Configuration constants
MAX_CONCURRENT_BROWSERS = 3
BROWSER_TIMEOUT = 30000
namespace = {
    'ns': 'http://www.dmotorworks.com/service-repair-order-history'
}

def round_off(n, decimals=0):
    """Round off numbers with proper decimal handling"""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def zero_sales_check(df, columns):
    total_sum = df[columns].sum().sum()
    return total_sum == 0

def get_month_date_range_from_target(target_date_str):
    """Get the start and end date for the target month"""
    # Parse the target date string
    target_date = datetime.strptime(target_date_str, "%Y-%m-%d")    
    # Get first day of the target month
    month_start = target_date.replace(day=1)    
    # Get last day of the target month
    month_end = month_start + relativedelta(months=1) - timedelta(days=1)    
    return month_start, month_end

def process_target_month_data(all_revenue_details_df, month_start, month_end, advisor, tech, retail_flag, customer_pay_types, warranty_pay_types, columns_to_check):
    """Process data for the target month and return results"""
    month_start = month_start.date()
    month_end = month_end.date()   
    # Filter data for the specific target month
    month_data = all_revenue_details_df[
        (all_revenue_details_df['closeddate'] >= month_start) &
        (all_revenue_details_df['closeddate'] <= month_end)
    ]    
    log_info(f"Target month data shape: {month_data.shape}")    
    if month_data.empty:
        log_info("No data found for the target month")
        return None    
    # Apply existing filtering logic
    filtered_df = month_data[
        (month_data['department'] == 'Service') & 
        (month_data['hide_ro'] != True)
    ]   
    log_info(f"month_data::filtered_df: {filtered_df}")
    if filtered_df.empty:
        log_info("No service department data found for the target month")
        return None    
    filtered_df = filtered_df.copy()
    filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)
    log_info(f"filtered_df: {filtered_df}")
    # Initialize variables
    CP_parts_revenue = 0
    parts_revenue_combined = 0
    parts_revenue_C = 0
    parts_revenue_W = 0
    parts_revenue_I = 0
    parts_revenue_M = 0
    parts_revenue_E = 0
    parts_revenue_F = 0
    parts_gross_profit = 0
    parts_gross_profit_combined = 0
    parts_gross_profit_C = 0
    parts_gross_profit_W = 0
    parts_gross_profit_I = 0
    parts_gross_profit_M = 0
    parts_gross_profit_E = 0
    parts_gross_profit_F = 0
    CP_parts_gross_profit_percentage = 0
    parts_revenue_per_ro = 0
    parts_revenue_per_ro_combined = 0
    parts_revenue_per_ro_C = 0
    parts_revenue_per_ro_W = 0
    parts_revenue_per_ro_I = 0
    parts_revenue_per_ro_M = 0
    parts_revenue_per_ro_E = 0
    parts_revenue_per_ro_F = 0
    parts_markup = 0
    parts_markup_combined = 0
    parts_markup_C = 0
    parts_markup_W = 0
    parts_markup_I = 0
    parts_markup_M = 0
    parts_markup_E = 0
    parts_markup_F = 0
    ro_count_CP = 0
    ro_count_combined = 0
    ro_count_C = 0
    ro_count_W = 0
    ro_count_I = 0
    ro_count_M = 0
    ro_count_E = 0
    ro_count_F = 0
    hours_per_ro_parts_only_CP = 0
    hours_per_ro_parts_only_combined = 0
    hours_per_ro_parts_only_C = 0
    hours_per_ro_parts_only_W = 0
    hours_per_ro_parts_only_I = 0
    hours_per_ro_parts_only_M = 0
    hours_per_ro_parts_only_E = 0
    hours_per_ro_parts_only_F = 0
    ro_count_parts_only = 0
    ro_count_parts_only_combined = 0
    ro_count_parts_only_C = 0
    ro_count_parts_only_W = 0
    ro_count_parts_only_I = 0
    ro_count_parts_only_M = 0
    ro_count_parts_only_E = 0
    ro_count_parts_only_F = 0
    cp_parts_markup_repair_competitive = 0
    # NEW VARIABLES - Adding the missing calculations
    total_labor_cost = 0
    total_parts_cost = 0
    labor_sale_customer_pay = 0    
    # ... [existing group assignment logic remains the same] ...    
    combined_revenue_details = filtered_df.copy()
    combined_revenue_details['group'] = pd.Series(dtype="string")    
    # Create a temporary version for zero check without modifying the original data
    # Set specific columns to 0 if opcategory is 'N/A'
    combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    # Iterate through each unique RO number for group assignment
    for ro_number in combined_revenue_details['unique_ro_number'].unique():
        ro_specific_rows = combined_revenue_details[combined_revenue_details['unique_ro_number'] == ro_number]
        ro_specific_rows_C = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(customer_pay_types)]
        ro_specific_rows_W = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(warranty_pay_types)]

        zero_sales_C = 0
        zero_sales_W = 0

        zero_sales_C = zero_sales_check(ro_specific_rows_C, columns_to_check)
        zero_sales_W = zero_sales_check(ro_specific_rows_W, columns_to_check)        
        if not ro_specific_rows_C.empty and not zero_sales_C:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'C'
        elif not ro_specific_rows_W.empty and not zero_sales_W:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'W'
        else:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'I'
    # Apply filters based on advisor and tech conditions
    if advisor == {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details['unique_ro_number'].unique()
    elif advisor != {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['serviceadvisor'].astype(str).isin(advisor), 'unique_ro_number'].unique()
    elif advisor == {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['lbrtechno'].astype(str).isin(tech), 'unique_ro_number'].unique()
    elif advisor != {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[(combined_revenue_details['serviceadvisor'].astype(str).isin(advisor)) & 
            (combined_revenue_details['lbrtechno'].astype(str).isin(tech)), 'unique_ro_number'].unique()
    # Applying the Advisor and tech filter conditions
    combined_revenue_details = combined_revenue_details[combined_revenue_details['unique_ro_number'].isin(matching_ro_numbers)]
    combined_revenue_details = combined_revenue_details.reset_index(drop=True)
    combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    
    # RO Counts
    Scorecard_10_CP = combined_revenue_details.loc[combined_revenue_details['group'] == 'C', 'unique_ro_number'].nunique()
    Scorecard_10_Wty = combined_revenue_details.loc[combined_revenue_details['group'] == 'W', 'unique_ro_number'].nunique()
    Scorecard_10_Int = combined_revenue_details.loc[combined_revenue_details['group'] == 'I', 'unique_ro_number'].nunique()
    all_unique_ros = Scorecard_10_CP + Scorecard_10_Wty + Scorecard_10_Int 
    # ENHANCED CP CALCULATIONS - Following the second code pattern
    # Pre-compute zero-value mask for efficient filtering
    zero_values_mask = (
        (combined_revenue_details['lbrsale'].fillna(0) == 0) &
        (combined_revenue_details['lbrsoldhours'].fillna(0) == 0) &
        (combined_revenue_details['prtextendedsale'].fillna(0) == 0) &
        (combined_revenue_details['prtextendedcost'].fillna(0) == 0)
    )    
    # Create masks for customer pay and group C
    customer_pay_mask = combined_revenue_details['paytypegroup'].isin(customer_pay_types)
    group_C_mask = combined_revenue_details['group'] == 'C'
    cp_and_customer_mask = customer_pay_mask & group_C_mask    
    # Filter CP job details
    list_of_paytypegroup_C = combined_revenue_details[cp_and_customer_mask]
    total_CP_revenue_details_df = pd.DataFrame(list_of_paytypegroup_C)
    total_CP_revenue_details_df = total_CP_revenue_details_df[~zero_values_mask[cp_and_customer_mask]]    
    # Apply tech filter if needed
    if tech != {'all'}:
        tech_mask = total_CP_revenue_details_df['lbrtechno'].astype(str).isin(tech)
        total_CP_revenue_details_df = total_CP_revenue_details_df[tech_mask]    
    # CALCULATE THE REQUIRED METRICS
    effective_labor_rate_CP=0
    cp_parts_markup_CP=0
    if not total_CP_revenue_details_df.empty:
        # Convert columns to numeric once and store for reuse
        numeric_columns = {}
        columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']        
        for col in columns_to_convert:
            numeric_columns[col] = pd.to_numeric(total_CP_revenue_details_df[col], errors='coerce').fillna(0)        
        # 1. Labor Sale - Customer Pay
        labor_sale_customer_pay = numeric_columns['lbrsale'].sum()        
        # 2. Total Labor Cost
        total_labor_cost = numeric_columns['lbrcost'].sum()        
        # 3. Total Parts Sale
        total_parts_sale = numeric_columns['prtextendedsale'].sum()    
        # 4. Total Parts Cost
        total_parts_cost = numeric_columns['prtextendedcost'].sum()        
        # Additional calculations (existing logic)
        labor_sold_hours = numeric_columns['lbrsoldhours'].sum()        
        # Calculate gross profits
        labor_gross_profit = labor_sale_customer_pay - total_labor_cost
        parts_gross_profit = total_parts_sale - total_parts_cost
        combined_gross_profit = labor_gross_profit + parts_gross_profit        
        # Calculate percentages
        labor_gross_profit_percentage = round_off((labor_gross_profit / labor_sale_customer_pay) * 100, 1) if labor_sale_customer_pay != 0 else 0
        parts_gross_profit_percentage = round_off((parts_gross_profit / total_parts_sale) * 100, 1) if total_parts_sale != 0 else 0
        combined_revenue = labor_sale_customer_pay + total_parts_sale
        combined_gross_profit_percentage = round_off((combined_gross_profit / combined_revenue) * 100, 1) if combined_revenue != 0 else 0        
        # Effective labor rate
        effective_labor_rate_CP = round_off(labor_sale_customer_pay / labor_sold_hours, 2) if labor_sold_hours != 0 else 0        
        # Parts markup
        cp_parts_markup_CP = round_off(total_parts_sale / total_parts_cost, 4) if total_parts_cost != 0 else 0 
        # Round the main values
        parts_revenue = 0
        labor_revenue = round_off(labor_sale_customer_pay, 2)
        parts_revenue = round_off(total_parts_sale, 2)
        labor_sold_hours = round_off(labor_sold_hours, 2)    
        # 953 - CP Parts Revenue Per RO - FOPC_DV_0068 and FOPC_DV_0069
        parts_revenue_per_ro = 0
        if parts_revenue != 0 and Scorecard_10_CP != 0:
            parts_revenue_per_ro = round_off(parts_revenue / Scorecard_10_CP, 2)

        # 1318 - CP Hours Per RO Parts Only - FOPC_DV_0074 and FOPC_DV_0075

        total_revenue_CP_with_parts_sale = total_CP_revenue_details_df[total_CP_revenue_details_df['prtextendedsale'] != 0]
        parts_only_ro_count_CP = total_revenue_CP_with_parts_sale['unique_ro_number'].nunique()
        total_labor_sold_hours = pd.to_numeric(total_revenue_CP_with_parts_sale['lbrsoldhours']).fillna(0).sum()

        hours_per_ro_parts_only_CP = 0
        if total_labor_sold_hours != 0 and parts_only_ro_count_CP != 0:
            hours_per_ro_parts_only_CP = round_off((total_labor_sold_hours / parts_only_ro_count_CP), 2) 
        # 1334 - CP Parts Markup - Repair & Competitive - FOPC_DV_0078
        CP_revenue_details_comp_and_rep = total_CP_revenue_details_df[total_CP_revenue_details_df['opcategory'].isin(['COMPETITIVE', 'REPAIR'])]
        CP_parts_revenue_Comp_and_Rep = 0
        CP_parts_cost_Comp_and_Rep = 0
        if not CP_revenue_details_comp_and_rep.empty:
            CP_parts_revenue_Comp_and_Rep = pd.to_numeric(CP_revenue_details_comp_and_rep['prtextendedsale'], errors='coerce').fillna(0).sum()
            CP_parts_cost_Comp_and_Rep = pd.to_numeric(CP_revenue_details_comp_and_rep['prtextendedcost'], errors='coerce').fillna(0).sum()

        cp_parts_markup_repair_competitive = 0
        if CP_parts_revenue_Comp_and_Rep != 0 and CP_parts_cost_Comp_and_Rep != 0:
            cp_parts_markup_repair_competitive = round_off((CP_parts_revenue_Comp_and_Rep / CP_parts_cost_Comp_and_Rep),4)

    else:
        log_info("No Customer Pay data available for calculations")    
    # Continue with combined calculations for all pay types
    if not combined_revenue_details.empty:
        labor_sold_hours_combined_value = pd.to_numeric(combined_revenue_details['lbrsoldhours']).fillna(0).sum()
        labor_sold_hours_combined = round_off(labor_sold_hours_combined_value, 2)        
        all_labor_sale = pd.to_numeric(combined_revenue_details['lbrsale']).fillna(0).sum()
        all_sold_hours = pd.to_numeric(combined_revenue_details['lbrsoldhours']).fillna(0).sum()
        effective_labor_rate_combined = round_off(all_labor_sale / all_sold_hours, 2) if all_sold_hours != 0 else 0        
        part_extended_sale_combined = pd.to_numeric(combined_revenue_details['prtextendedsale']).fillna(0).sum()
        part_extended_cost_combined = pd.to_numeric(combined_revenue_details['prtextendedcost']).fillna(0).sum()
        cp_parts_markup_combined = round_off(part_extended_sale_combined / part_extended_cost_combined, 4) if part_extended_cost_combined != 0 else 0  

    
    log_info(f"target_month : {month_start.strftime('%Y-%m')}")
    log_info(f"parts_revenue : {parts_revenue}")
    log_info(f"combined_revenue : {round_off(combined_revenue, 2)}")
    log_info(f"customer_pay_ros: {Scorecard_10_CP}")
    log_info(f"total_parts_sale: {round_off(total_parts_sale, 2)}")
    log_info(f"total_parts_cost: {round_off(total_parts_cost, 2)}")
    log_info(f"parts_revenue_per_ro: {parts_revenue_per_ro}")
    log_info(f"hours_per_ro_parts_only_CP: {hours_per_ro_parts_only_CP}")
    log_info(f"parts_only_ro_count_CP: {parts_only_ro_count_CP}")
    log_info(f"total_labor_sold_hours: {total_labor_sold_hours}")
    # Return enhanced result set
    return {
        "target_month": month_start.strftime('%Y-%m'),
        "target_month_name": month_start.strftime('%B %Y'),       
        "total_ros": all_unique_ros,
        "ro_counts": {
            "customer_pay_ros": Scorecard_10_CP,
            "warranty_ros": Scorecard_10_Wty,
            "internal_ros": Scorecard_10_Int
        },
        # ENHANCED CUSTOMER PAY METRICS
        "customer_pay_metrics": {
            "labor_sale_customer_pay": round_off(labor_sale_customer_pay, 2),
            "total_labor_cost": round_off(total_labor_cost, 2),
            "total_parts_sale": round_off(total_parts_sale, 2),
            "total_parts_cost": round_off(total_parts_cost, 2),
            "labor_gross_profit": round_off(labor_gross_profit, 2),
            "parts_gross_profit": round_off(parts_gross_profit, 2),
            "combined_gross_profit": round_off(combined_gross_profit, 2),
            "labor_gross_profit_percentage": labor_gross_profit_percentage,
            "parts_gross_profit_percentage": parts_gross_profit_percentage,
            "combined_gross_profit_percentage": combined_gross_profit_percentage,
            "effective_labor_rate": effective_labor_rate_CP,
            "elr": effective_labor_rate_CP,
            "parts_markup": cp_parts_markup_CP,
            "labor_sold_hours": round_off(labor_sold_hours, 2),
            "parts_to_labor_ratio": round_off(total_parts_sale / labor_sale_customer_pay, 2) if labor_sale_customer_pay != 0 else 0,
            "hours_per_ro_parts_only_CP": hours_per_ro_parts_only_CP,
            "parts_only_ro_count_CP": parts_only_ro_count_CP,
            "total_labor_sold_hours": total_labor_sold_hours,
            "cp_parts_markup_repair_competitive":cp_parts_markup_repair_competitive,
            "CP_parts_revenue_Comp_and_Rep": CP_parts_revenue_Comp_and_Rep,
            "CP_parts_cost_Comp_and_Rep": CP_parts_cost_Comp_and_Rep
            
        },
        # Existing metrics (keeping for compatibility)
        "labor_revenue": labor_revenue,
        "parts_revenue": parts_revenue,
        "combined_revenue": round_off(combined_revenue, 2),
        "labor_gross_profit": round_off(labor_gross_profit, 2),
        "parts_gross_profit": round_off(parts_gross_profit, 2),
        "combined_gross_profit": round_off(combined_gross_profit, 2),
        "labor_gross_profit_percentage": labor_gross_profit_percentage,
        "parts_gross_profit_percentage": parts_gross_profit_percentage,
        "combined_gross_profit_percentage": combined_gross_profit_percentage,
        "labor_sold_hours": labor_sold_hours,
        "labor_sold_hours_combined": labor_sold_hours_combined,
        "parts_revenue_per_ro":parts_revenue_per_ro,
        "hours_per_ro_parts_only_CP": hours_per_ro_parts_only_CP,
        "parts_only_ro_count_CP": parts_only_ro_count_CP,
        "total_labor_sold_hours": total_labor_sold_hours,
        "cp_parts_markup_repair_competitive":cp_parts_markup_repair_competitive,
        "CP_parts_revenue_Comp_and_Rep": CP_parts_revenue_Comp_and_Rep,
        "CP_parts_cost_Comp_and_Rep": CP_parts_cost_Comp_and_Rep
    }

def db_execution(target_date_str, advisor, tech, retail_flag, columns_to_check):
    """
    Handle database operations and execute month processing
    """      
    try:
        customer_pay_types={},
        warranty_pay_types={}
        # Get target month date range
        month_start, month_end = get_month_date_range_from_target(target_date_str)        
        log_info(f"Target month range: {month_start.date()} to {month_end.date()}")       
        # Fetch all data from database
        log_info("Fetching data from database...")
        all_revenue_details_table_db_connect = allRevenueDetailsCPOverview()
        all_revenue_details_df = all_revenue_details_table_db_connect.getTableResult()    
        if all_revenue_details_df.empty:
            log_error("ERROR: No data retrieved from database!")
            return False
        # Convert date column and other preprocessing
        columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce')) 
        # Define customer and warranty pay types based on retail_flag
        if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C'}
            warranty_pay_types = {'W', 'F', 'M', 'E'}
        elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'M'}
            warranty_pay_types = {'W', 'F', 'E'}
        elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C', 'E'}
            warranty_pay_types = {'W', 'F', 'M'}
        elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'E', 'M'}
            warranty_pay_types = {'W', 'F'}   
        target_month_result = process_target_month_data(
            all_revenue_details_df, 
            month_start, 
            month_end,            
            advisor, 
            tech, 
            retail_flag, 
            customer_pay_types, 
            warranty_pay_types, 
            columns_to_check
        ) 
        return target_month_result, customer_pay_types, warranty_pay_types        
    except Exception as e:
        log_error(f"ERROR in db_execution: {str(e)}")
        log_error("=" * 60)
        log_error("DATABASE EXECUTION FAILED")
        log_error("=" * 60)
        return None, None, None

def db_calculation():
    """
    Main execution function for db calculation
    """    
    # Configuration variables
    columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
    retail_flag = {'C','M'}    
    # retail_flag = config.retail_flag_all
    storeid = config.store_id
    realm = config.database_name   
    advisor_set = config.advisor
    tech_set = config.technician    
    # Process advisor configuration
    if isinstance(advisor_set, str):
        if advisor_set.lower() == 'all':
            advisor = {'all'}
        elif ',' in advisor_set:
            advisor = {x.strip() for x in advisor_set.split(',')}
        else:
            advisor = {advisor_set.strip()}
    else:
        advisor = {'all'}
    
    if advisor != {'all'}:
        advisor_id = next(iter(advisor))   
    else:
        advisor_id = 'all'    
    # Process technician configuration
    if isinstance(tech_set, str):
        if tech_set.lower() == 'all':
            tech = {'all'}
        elif ',' in tech_set:
            tech = {x.strip() for x in tech_set.split(',')}
        else:
            tech = {tech_set.strip()}
    else:
        tech = {'all'}   
    
    # Execute database operations and processing
    target_date_str = TARGET_MONTHS_YEARS[0]
    target_month_result, customer_pay_types, warranty_pay_types = db_execution(
        target_date_str, advisor, tech, retail_flag, columns_to_check
    )    
    # Process results
    if target_month_result:
        log_info("\n" + "=" * 80)
        log_info("RESULTS PROCESSING")
        log_info("=" * 80)        
        # Create the final result set for the target month only
        final_result_set = {
            "analysis_info": {
                "target_month": target_date_str,
                "analysis_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "store_id": storeid,
                "realm": realm,
                "advisor_filter": list(advisor),
                "technician_filter": list(tech),
                "customer_pay_types": list(customer_pay_types),
                "warranty_pay_types": list(warranty_pay_types)
            },
            "target_month_results": target_month_result
        }     
        # Write results to JSON file
        result_dir,output_filename = create_folder_file_path(subfolder="chart_processing_results", output_file=db_json) 
        with open(output_filename, 'w', encoding='utf-8') as json_file:
            json.dump(final_result_set, json_file, indent=4, ensure_ascii=False)        
        log_info(f"\nTarget month CP Parts overview data written successfully to {output_filename}")        
        # Display summary
        log_info(f"\nTarget Month Summary for {target_month_result['target_month_name']}:")
        log_info(f"  Total Revenue: ${target_month_result['combined_revenue']:,.2f}")
        log_info(f"  Total Gross Profit: ${target_month_result['combined_gross_profit']:,.2f}")
        log_info(f"  GP Percentage: {target_month_result['combined_gross_profit_percentage']}%")
        log_info(f"  Total ROs: {target_month_result['total_ros']}")
        log_info(f"    - Customer Pay ROs: {target_month_result['ro_counts']['customer_pay_ros']}")
        log_info(f"    - Warranty ROs: {target_month_result['ro_counts']['warranty_ros']}")
        log_info(f"    - Internal ROs: {target_month_result['ro_counts']['internal_ros']}")
        log_info(f"  Labor sold hours: {target_month_result['labor_sold_hours']}")        
    else:
        log_info("\n" + "=" * 80)
        log_info("NO DATA RESULTS PROCESSING")
        log_info("=" * 80)        
        log_info(f"No data available for target month {target_date_str}")   
    
    log_info("\n" + "=" * 80)
    log_info("CP OVERVIEW ANALYSIS - MAIN EXECUTION COMPLETED")
    log_info("=" * 80)
    
class MultiChartParallelProcessor:
    """Process multiple charts in parallel with separate browsers"""
    
    def __init__(self, max_browsers=MAX_CONCURRENT_BROWSERS, auth_manager=None):
        self.max_browsers = max_browsers
        self.auth_manager = auth_manager or AuthManager(config)
        self.charts_info = None

    async def discover_charts(self):
        """Discover all charts on the CPPartsOverview page"""
        log_info("Discovering charts on CPPartsOverview page...")

        # This ensures a valid session before a new page is even created.

        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []
        # Get a NEW PAGE from the existing session.
        page = await self.auth_manager.new_page() 
        try:
            await page.goto(f"{config.site_url.rstrip('/')}/CPPartsOverview", timeout=50000)
            chart_found = False
            selectors_to_try = [
                'canvas.chartjs-render-monitor',
                'canvas[class*="chartjs"]',
                'canvas',
                '[id*="chart"]',
                '.react-grid-item canvas',
                '[id*="chartContainterId"]'
            ]
            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    chart_found = True
                    log_info(f" Found charts using selector: {selector}")
                    break
                except Exception:
                    continue
            if not chart_found:
                log_error("No chart elements found with any selector")
                return False            
            await asyncio.sleep(2)                
            charts_info = await page.evaluate("""
                () => {
                    const canvases = document.querySelectorAll('canvas');
                    const chartsInfo = [];
                        
                    for (let i = 0; i < canvases.length; i++) {
                        const canvas = canvases[i];
                        const rect = canvas.getBoundingClientRect();
                            
                        // Try to find chart title - look in the card header
                        let chartTitle = `Chart ${i + 1}`;
                            
                        // Look for parent container with chart ID
                        let parentContainer = canvas.closest('[id*="chartContainterId"]');
                        if (parentContainer) {
                            // Extract chart ID from container ID
                            const containerId = parentContainer.id;
                            const chartId = containerId.replace('chartContainterId-', '');
                                
                            // Look for the card header with matching ID
                            const cardHeader = parentContainer.querySelector(`#card-header-${chartId}`);
                            if (cardHeader) {
                                const titleElement = cardHeader.querySelector('.MuiCardHeader-title');
                                if (titleElement && titleElement.textContent.trim()) {
                                    chartTitle = titleElement.textContent.trim();
                                }
                            }
                        }
                            
                        // If no specific container found, try general approach
                        if (chartTitle === `Chart ${i + 1}`) {
                            let container = canvas.closest('.MuiCard-root, [class*="card"], [class*="chart"], [class*="widget"]');
                            if (container) {
                                const titleElement = container.querySelector('h1, h2, h3, h4, h5, h6, .title, [class*="title"], .MuiCardHeader-title');
                                if (titleElement && titleElement.textContent.trim()) {
                                    chartTitle = titleElement.textContent.trim();
                                }
                            }
                        }
                            
                        // Check if canvas is a valid chart
                        let hasChart = false;
                            
                        // Check for Chart.js specific indicators
                        if (canvas.classList.contains('chartjs-render-monitor') || 
                            canvas.classList.contains('chartjs-render') ||
                            canvas.width > 0 && canvas.height > 0) {
                            hasChart = true;
                        }
                            
                        // Try to detect Chart.js instance
                        try {
                            if (typeof Chart !== 'undefined' && Chart.getChart) {
                                const chart = Chart.getChart(canvas);
                                if (chart) {
                                    hasChart = true;
                                }
                            }
                        } catch (e) {
                            // Continue with other checks
                        }
                            
                        // Check for canvas with actual content (non-zero dimensions)
                        if (!hasChart && rect.width > 0 && rect.height > 0) {
                            // Additional check for canvas context
                            try {
                                const ctx = canvas.getContext('2d');
                                if (ctx) {
                                    hasChart = true;
                                }
                            } catch (e) {
                                // Fallback to dimension check
                                hasChart = true;
                            }
                        }
                            
                        // Only include canvases that appear to be charts and are visible
                        if (hasChart && rect.width > 0 && rect.height > 0) {
                            chartsInfo.push({
                                canvasIndex: i,
                                chartTitle: chartTitle,
                                canvasId: canvas.id || `canvas-${i}`,
                                canvasClass: canvas.className,
                                containerId: parentContainer ? parentContainer.id : null,
                                position: {
                                    x: Math.round(rect.left),
                                    y: Math.round(rect.top),
                                    width: Math.round(rect.width),
                                    height: Math.round(rect.height)
                                },
                                visible: rect.width > 0 && rect.height > 0,
                                isChartJs: canvas.classList.contains('chartjs-render-monitor')
                            });
                        }
                    }
                        
                    console.log(`Found ${chartsInfo.length} charts with data`);
                    return chartsInfo;
                }
            """)                
            log_info(f"Found {len(charts_info)} charts on CPPartsOverview page")
            log_info(f"Found ==== {charts_info} ====")
            for chart in charts_info:
                log_info(f"  - Chart {chart['canvasIndex']}: {chart['chartTitle']} (Container: {chart['containerId']})")                
            return charts_info                
        finally:
            log_info("Finished discovering chart-point combinations")
            await page.close()

    async def find_matching_points_in_chart(self, page, chart_index, target_month_year,chart_id):
        """Find matching data points for a specific chart and target month-year"""
        try:
            log_info(f"Finding points in chart {chart_index} for target: {target_month_year}")            
            matching_points = await page.evaluate(r"""
                (args) => {
                    const { chartIndex, targetMonthYear } = args;
                    const matchingPoints = [];
                    
                    try {
                        // Find all canvas elements
                        const canvases = document.querySelectorAll('canvas');
                        console.log(`Found ${canvases.length} canvas elements`);
                        
                        // Check if the requested chart index exists
                        if (chartIndex >= canvases.length) {
                            console.log(`Chart index ${chartIndex} out of range (max: ${canvases.length - 1})`);
                            return [];
                        }
                        
                        const canvas = canvases[chartIndex];
                        let chart = null;
                        
                        console.log(`Processing canvas ${chartIndex} for target: ${targetMonthYear}`);
                        
                        // Multiple methods to get chart instance
                        try {
                            // Method 1: Chart.getChart (Chart.js v3+)
                            if (typeof Chart !== 'undefined' && Chart.getChart) {
                                chart = Chart.getChart(canvas);
                                console.log(`Method 1 - Chart.getChart: ${chart ? 'Found' : 'Not found'}`);
                            }
                            
                            // Method 2: Chart.instances (Chart.js v2)
                            if (!chart && typeof Chart !== 'undefined' && Chart.instances) {
                                const instances = Object.values(Chart.instances);
                                chart = instances.find(instance => instance.canvas === canvas);
                                console.log(`Method 2 - Chart.instances: ${chart ? 'Found' : 'Not found'}`);
                            }
                            
                            // Method 3: Canvas._chart property (older versions)
                            if (!chart && canvas._chart) {
                                chart = canvas._chart;
                                console.log(`Method 3 - canvas._chart: Found`);
                            }
                            
                            // Method 4: Check for chart instance in canvas properties
                            if (!chart) {
                                const keys = Object.keys(canvas);
                                for (const key of keys) {
                                    if (key.includes('chart') || key.includes('Chart')) {
                                        chart = canvas[key];
                                        if (chart && chart.data) {
                                            console.log(`Method 4 - Found chart via property: ${key}`);
                                            break;
                                        }
                                    }
                                }
                            }
                            
                        } catch (e) {
                            console.warn(`Error getting chart instance for canvas ${chartIndex}:`, e);
                        }
                        
                        if (!chart) {
                            console.log(`No chart found for canvas ${chartIndex}`);
                            return [];
                        }
                        
                        // Validate chart structure
                        if (!chart.data || !chart.data.datasets) {
                            console.log(`Invalid chart data structure for canvas ${chartIndex}`);
                            return [];
                        }
                        
                        console.log(`Processing chart with ${chart.data.datasets.length} datasets`);
                        
                        const canvasRect = canvas.getBoundingClientRect();
                        console.log(`Canvas rect:`, canvasRect);
                        
                        // Get x-axis labels
                        const xLabels = chart.data.labels || [];
                        console.log(`X-axis labels:`, xLabels);
                        
                        // Helper function to check if label matches target
                        const isLabelMatch = (label, target) => {
                            if (!label || !target) return false;
                            
                            const labelStr = String(label).toLowerCase().trim();
                            const targetStr = String(target).toLowerCase().trim();
                            
                            // Direct match
                            if (labelStr === targetStr) return true;
                            
                            // Contains match
                            if (labelStr.includes(targetStr) || targetStr.includes(labelStr)) return true;
                            
                            // Month-year pattern matching (e.g., "Jan 2024", "January 2024", "01/2024")
                            const monthYearRegex = /(\w+)[\/\-\s]+(\d{4})/;
                            const labelMatch = labelStr.match(monthYearRegex);
                            const targetMatch = targetStr.match(monthYearRegex);
                            
                            if (labelMatch && targetMatch) {
                                const labelMonth = labelMatch[1];
                                const labelYear = labelMatch[2];
                                const targetMonth = targetMatch[1];
                                const targetYear = targetMatch[2];
                                
                                // Check if years match and months match (partial match allowed)
                                if (labelYear === targetYear && 
                                    (labelMonth.includes(targetMonth) || targetMonth.includes(labelMonth))) {
                                    return true;
                                }
                            }
                            
                            return false;
                        };
                        
                        // Process each dataset
                        for (let datasetIndex = 0; datasetIndex < chart.data.datasets.length; datasetIndex++) {
                            const dataset = chart.data.datasets[datasetIndex];
                            
                            if (!dataset.data || !Array.isArray(dataset.data)) {
                                console.log(`No data in dataset ${datasetIndex}`);
                                continue;
                            }
                            
                            console.log(`Processing dataset ${datasetIndex} with ${dataset.data.length} points`);
                            
                            // Process each data point
                            for (let pointIndex = 0; pointIndex < dataset.data.length; pointIndex++) {
                                const value = dataset.data[pointIndex];
                                const xLabel = xLabels[pointIndex] || `Point ${pointIndex}`;
                                
                                // Only process points that match the target month-year
                                if (!isLabelMatch(xLabel, targetMonthYear)) {
                                    continue;
                                }
                                
                                console.log(`Found matching point: ${xLabel} matches ${targetMonthYear}`);
                                
                                try {
                                    let screenX = null;
                                    let screenY = null;
                                    let canvasX = null;
                                    let canvasY = null;
                                    
                                    // Enhanced coordinate extraction
                                    try {
                                        const meta = chart.getDatasetMeta(datasetIndex);
                                        if (meta && meta.data && meta.data[pointIndex]) {
                                            const element = meta.data[pointIndex];
                                            
                                            // Check if coordinates are valid numbers
                                            if (typeof element.x === 'number' && !isNaN(element.x) && 
                                                typeof element.y === 'number' && !isNaN(element.y)) {
                                                canvasX = element.x;
                                                canvasY = element.y;
                                                screenX = canvasRect.left + element.x;
                                                screenY = canvasRect.top + element.y;
                                                console.log(`Element coordinates: canvas(${canvasX}, ${canvasY}) -> screen(${screenX}, ${screenY})`);
                                            }
                                        }
                                    } catch (e) {
                                        console.warn(`Could not get element position for point ${pointIndex}:`, e);
                                    }
                                    
                                    // Fallback: Use chart scales to calculate coordinates
                                    if ((canvasX === null || isNaN(canvasX)) && chart.scales) {
                                        try {
                                            // Find x and y scales
                                            const xScale = chart.scales.x || chart.scales['x-axis-0'] || chart.scales.xAxes?.[0] ||
                                                        Object.values(chart.scales).find(s => s.axis === 'x' || s.type === 'category' || s.type === 'time');
                                            const yScale = chart.scales.y || chart.scales['y-axis-0'] || chart.scales.yAxes?.[0] ||
                                                        Object.values(chart.scales).find(s => s.axis === 'y' || s.position === 'left');
                                            
                                            if (xScale && yScale && xScale.getPixelForValue && yScale.getPixelForValue) {
                                                // Get the actual y value
                                                let yValue = value;
                                                if (typeof value === 'object' && value !== null) {
                                                    yValue = value.y || value.value || value.data;
                                                }
                                                if (typeof yValue === 'string') {
                                                    yValue = parseFloat(yValue);
                                                }
                                                
                                                if (!isNaN(yValue)) {
                                                    canvasX = xScale.getPixelForValue(pointIndex);
                                                    canvasY = yScale.getPixelForValue(yValue);
                                                    
                                                    if (!isNaN(canvasX) && !isNaN(canvasY)) {
                                                        screenX = canvasRect.left + canvasX;
                                                        screenY = canvasRect.top + canvasY;
                                                        console.log(`Scale-based coordinates: canvas(${canvasX}, ${canvasY}) -> screen(${screenX}, ${screenY})`);
                                                    }
                                                }
                                            }
                                        } catch (e) {
                                            console.warn(`Error in scale-based calculation:`, e);
                                        }
                                    }
                                    
                                    // Final validation of coordinates
                                    const coordsValid = screenX !== null && screenY !== null && 
                                                    !isNaN(screenX) && !isNaN(screenY) &&
                                                    isFinite(screenX) && isFinite(screenY);
                                    
                                    // Handle different value formats
                                    let displayValue = value;
                                    if (typeof value === 'object' && value !== null) {
                                        displayValue = value.y || value.value || value.data || JSON.stringify(value);
                                    }
                                    
                                    const pointData = {
                                        canvasIndex: chartIndex,
                                        datasetIndex: datasetIndex,
                                        pointIndex: pointIndex,
                                        value: displayValue,
                                        xLabel: xLabel,
                                        screenX: screenX,
                                        screenY: screenY,
                                        canvasX: canvasX,
                                        canvasY: canvasY,
                                        datasetLabel: dataset.label || `Dataset ${datasetIndex}`,
                                        chartType: chart.config ? chart.config.type : 'unknown',
                                        coordinatesValid: coordsValid,
                                        targetMonthYear: targetMonthYear
                                    };
                                    
                                    matchingPoints.push(pointData);
                                    console.log(`Added matching point:`, pointData);
                                    
                                } catch (e) {
                                    console.warn(`Error processing matching point ${pointIndex}:`, e);
                                }
                            }
                        }
                        
                    } catch (e) {
                        console.error('Error in chart point extraction:', e);
                        return [];
                    }
                    
                    console.log(`Found ${matchingPoints.length} matching points for chart ${chartIndex} and target ${targetMonthYear}`);
                    return matchingPoints;
                }
            """, {'chartIndex': chart_index, 'targetMonthYear': target_month_year})
            
            log_info(f" Found {len(matching_points)} matching points in chart {chart_index} for {target_month_year}")
            log_info(f"Matching points: {matching_points}")
            return matching_points            
        except Exception as e:
            log_error(f"Error finding matching points in chart {chart_index}: {str(e)}")
            return []      
    
    async def create_chart_point_combinations(self, target_months_years):
        """Create combinations of charts and their matching points"""
        log_info("Creating chart-point combinations...")

        # Use the dedicated parallel function to check and re-login the session.
        # This ensures a valid session before a new page is even created.
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []
        # Get a NEW PAGE from the existing session.
        page = await self.auth_manager.new_page()
        try:
            
            # Navigate to the CPOverview page using this new page
            await page.goto(f"{config.site_url.rstrip('/')}/CPPartsOverview", timeout=30000) 
            await asyncio.sleep(2)                
            # Discover all charts
            charts_info = await self.discover_charts()
            if not charts_info:
                log_warn(" No charts found")
                # Delete auth_state.json file when no charts are found
                auth_state_path = "auth_state.json"
                try:
                    if os.path.exists(auth_state_path):
                        os.remove(auth_state_path)
                        log_warn(f"Deleted {auth_state_path} due to no charts found")
                    else:
                        log_info(f"{auth_state_path} not found to delete")
                except Exception as e:
                    log_error(f"Error deleting {auth_state_path}: {e}")
                return []
                
            chart_point_combinations = []
            charts_with_points = []
                
            # For each chart, find matching points for each target month-year
            for chart_info in charts_info:
                chart_index = chart_info['canvasIndex']
                container_id = chart_info.get('containerId', '')
                chart_id = container_id.split('-')[-1] if container_id else None
                chart_title = chart_info.get('chartTitle', f'Chart {chart_id}')
                log_info(f"Processing Chart {chart_id}: {chart_title}")                    
                chart_total_points = 0
                chart_combinations = [] 
                log_info(f"   Added combination: chart_info::: {chart_info} -----)")                   
                # Process each target month-year for this chart
                for target_month_year in target_months_years:
                    log_info(f"Looking for data points matching: {target_month_year}")
                    # Find matching points for this chart and target month-year
                    matching_points = await self.find_matching_points_in_chart(
                        page, chart_index, target_month_year,chart_id)                        
                    log_info(f"Found {len(matching_points) if matching_points else 0} matching points for {target_month_year}")
                    if matching_points:
                        # Create combination for this chart and target month-year
                        combination = {
                            'chart_index': f"chart_{chart_index}",
                            'chart_id': chart_id,
                            'chart_info': chart_info,
                            'target_month_year': target_month_year,
                            'matching_points': matching_points,
                            'processing_status': 'pending',
                            'points_count': len(matching_points)
                        }
                        chart_combinations.append(combination)
                        chart_total_points += len(matching_points)                            
                        log_info(f"   Added combination: Chart {chart_index} - {target_month_year} ({len(matching_points)} points)")
                        log_info(f"   chart_combinations::: {chart_combinations} === points)")
                    else:
                        log_info(f"   No matching points found for Chart {chart_index} - {target_month_year}")                    
                # Track charts with their point counts
                if chart_combinations:
                    charts_with_points.append({
                        'chart_index': chart_index,
                        'chart_id': chart_id,
                        'chart_title': chart_title,
                        'total_points': chart_total_points,
                        'combinations': chart_combinations
                    })                
            # Sort charts by total points (descending) to get charts with most points first
            charts_with_points.sort(key=lambda x: x['total_points'], reverse=True)             # Take all charts and their combinations 
            for chart_data in charts_with_points:
                chart_point_combinations.extend(chart_data['combinations'])
                log_info(f"  Added Chart {chart_data['chart_index']}: {chart_data['chart_title']} ({chart_data['total_points']} points)")
            log_info(f"\nSummary: Created {len(chart_point_combinations)} chart-point combinations from {len(charts_with_points)} charts")              
            # log_info summary by chart
            chart_summary = {}
            for combo in chart_point_combinations:
                chart_id = combo['chart_id']
                if chart_id not in chart_summary:
                    chart_summary[chart_id] = 0
                chart_summary[chart_id] += 1                
            for chart_id, count in chart_summary.items():
                log_info(f"  {chart_id}: {count} combinations")                
            return chart_point_combinations                
        except Exception as e:
            log_error(f" Error creating chart-point combinations: {str(e)}")
            return []  
        finally:
            log_info("Finished creating chart-point combinations")
            await page.close() 
   
    async def apply_enhanced_legend_control(self, page):
        """Apply the enhanced legend control script to the page with robust chart detection"""
        try:
            # Wait for charts to be loaded with multiple attempts
            log_info("Waiting for charts to load...")

            # Try multiple selectors to find charts
            chart_found = False
            selectors_to_try = [
                'canvas.chartjs-render-monitor',
                'canvas[class*="chartjs"]',
                'canvas',
                '[id*="chart"]',
                '.react-grid-item canvas',
                '[id*="chartContainterId"]'
            ]
            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    chart_found = True
                    log_info(f" Found charts using selector: {selector}")
                    break
                except Exception:
                    continue

            if not chart_found:
                log_warn("No chart elements found with any selector")
                return False
            await asyncio.sleep(3)  # Give charts time to fully initialize
            # Execute the comprehensive chart detection script
            result = await page.evaluate("""
                (function() {
                    console.log('=== COMPREHENSIVE CHART DETECTION ===');

                    // Check Chart.js availability
                    const chartJsAvailable = typeof Chart !== 'undefined';
                    console.log('Chart.js available:', chartJsAvailable);

                    if (!chartJsAvailable) {
                        // Try to find Chart.js in window object
                        const chartKeys = Object.keys(window).filter(key => key.toLowerCase().includes('chart'));
                        console.log('Potential chart objects:', chartKeys);
                        return false;
                    }

                    // Initialize chart instances map
                    window.legendControlInitialized = true;
                    window.chartInstances = new Map();

                    // Find all possible canvas elements
                    const allCanvases = document.querySelectorAll('canvas');
                    const chartCanvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                    const anyChartCanvas = document.querySelectorAll('canvas[class*="chart"]');

                    console.log(`Canvas elements found:`);
                    console.log(`  - Total canvas: ${allCanvases.length}`);
                    console.log(`  - chartjs-render-monitor: ${chartCanvases.length}`);
                    console.log(`  - chart-related: ${anyChartCanvas.length}`);

                    // Use the most specific selector that found canvases
                    let canvasesToProcess = chartCanvases.length > 0 ? chartCanvases :
                                          anyChartCanvas.length > 0 ? anyChartCanvas : allCanvases;

                    console.log(`Processing ${canvasesToProcess.length} canvas elements`);

                    let successfullyRegistered = 0;

                    canvasesToProcess.forEach((canvas, index) => {
                        console.log(`\\n--- Processing Canvas ${index} ---`);

                        let chartInstance = null;
                        let detectionMethod = 'none';

                        // Method 1: Chart.getChart (Chart.js v3+)
                        try {
                            if (Chart.getChart) {
                                chartInstance = Chart.getChart(canvas);
                                if (chartInstance) {
                                    detectionMethod = 'Chart.getChart';
                                    console.log(`✓ Found via Chart.getChart`);
                                }
                            }
                        } catch (e) {
                            console.log(`✗ Chart.getChart failed:`, e.message);
                        }

                        // Method 2: Canvas chart property
                        if (!chartInstance) {
                            try {
                                if (canvas.chart) {
                                    chartInstance = canvas.chart;
                                    detectionMethod = 'canvas.chart';
                                    console.log(`✓ Found via canvas.chart`);
                                }
                            } catch (e) {
                                console.log(`✗ canvas.chart failed:`, e.message);
                            }
                        }

                        // Method 3: Canvas _chart property
                        if (!chartInstance) {
                            try {
                                if (canvas._chart) {
                                    chartInstance = canvas._chart;
                                    detectionMethod = 'canvas._chart';
                                    console.log(`✓ Found via canvas._chart`);
                                }
                            } catch (e) {
                                console.log(`✗ canvas._chart failed:`, e.message);
                            }
                        }

                        // Method 4: Search Chart.instances
                        if (!chartInstance) {
                            try {
                                if (Chart.instances) {
                                    Object.values(Chart.instances).forEach(instance => {
                                        if (instance && instance.canvas === canvas) {
                                            chartInstance = instance;
                                            detectionMethod = 'Chart.instances';
                                            console.log(`✓ Found via Chart.instances`);
                                        }
                                    });
                                }
                            } catch (e) {
                                console.log(`✗ Chart.instances search failed:`, e.message);
                            }
                        }

                        // Method 5: Check canvas data attributes or properties
                        if (!chartInstance) {
                            try {
                                // Look for any property that might contain a chart instance
                                const props = Object.getOwnPropertyNames(canvas);
                                for (const prop of props) {
                                    if (prop.includes('chart') || prop.includes('Chart')) {
                                        const value = canvas[prop];
                                        if (value && typeof value === 'object' && value.data && value.data.datasets) {
                                            chartInstance = value;
                                            detectionMethod = `canvas.${prop}`;
                                            console.log(`✓ Found via canvas.${prop}`);
                                            break;
                                        }
                                    }
                                }
                            } catch (e) {
                                console.log(`✗ Property search failed:`, e.message);
                            }
                        }

                        // Validate chart instance
                        if (chartInstance) {
                            console.log(`Chart instance found via: ${detectionMethod}`);

                            if (chartInstance.data && chartInstance.data.datasets && chartInstance.data.datasets.length > 0) {
                                // Use canvas ID or create consistent ID mapping
                                let chartId = canvas.id || `canvas-${index}`;

                                // Also register with alternative IDs for compatibility
                                const alternativeIds = [`chart_${index}`, `chart-${index}`, `canvas_${index}`];

                                window.chartInstances.set(chartId, {
                                    instance: chartInstance,
                                    canvas: canvas,
                                    detectionMethod: detectionMethod,
                                    originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                                });

                                // Register with alternative IDs
                                alternativeIds.forEach(altId => {
                                    window.chartInstances.set(altId, {
                                        instance: chartInstance,
                                        canvas: canvas,
                                        detectionMethod: detectionMethod,
                                        originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                                    });
                                });

                                console.log(` Registered ${chartId} (and alternatives: ${alternativeIds.join(', ')}):`);
                                console.log(`   - Datasets: ${chartInstance.data.datasets.length}`);
                                console.log(`   - Detection: ${detectionMethod}`);

                                chartInstance.data.datasets.forEach((dataset, dsIndex) => {
                                    console.log(`   - Dataset ${dsIndex}: "${dataset.label || 'Unnamed'}"`);
                                });

                                successfullyRegistered++;
                            } else {
                                console.log(`✗ Chart instance invalid (no datasets)`);
                            }
                        } else {
                            console.log(`✗ No chart instance found for canvas ${index}`);
                        }
                    });

                    console.log(`\\n=== DETECTION COMPLETE ===`);
                    console.log(`Successfully registered: ${successfullyRegistered} charts`);
                    console.log(`Chart IDs: [${Array.from(window.chartInstances.keys()).join(', ')}]`);

                    return successfullyRegistered > 0;
                })()
            """)

            if result:
                log_info(" Enhanced legend control applied successfully with comprehensive detection")
                return True
            else:
                log_info(" No chart instances found even with comprehensive detection")
                return False

        except Exception as e:
            log_error(f" Failed to apply enhanced legend control: {str(e)}")
            return False
    async def disable_all_legends(self, page):
        """Disable legends for all charts"""
        try:
            await page.evaluate("""
                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        
                        // Disable legend
                        if (!chart.options.plugins) chart.options.plugins = {};
                        if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                        chart.options.plugins.legend.display = false;
                        
                        // Update chart
                        chart.update('none');
                        
                        console.log(`Legend disabled for ${chartId}`);
                    });
                }
            """)
            
            log_info(" All legends disabled")
            return True
            
        except Exception as e:
            log_error(f" Failed to disable legends: {str(e)}")
            return False
     
    async def enable_only_target_legend(self, page, chart_id, target_dataset_label):
        """Enable only the target dataset legend and disable all others"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to enable legend for chart: {chart_id}, dataset: {target_dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found, attempting to reinitialize...');
                    return false;
                }}

                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;
                let actualChartId = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        actualChartId = id;
                        console.log(`Found chart with ID: ${{id}}`);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found with any ID variation');
                    console.log('Available charts:', Array.from(window.chartInstances.keys()));
                    return false;
                }}

                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure for', actualChartId);
                    return false;
                }}

                // Find target dataset index with fuzzy matching
                let targetDatasetIndex = -1;
                chart.data.datasets.forEach((dataset, index) => {{
                    const datasetLabel = dataset.label || '';
                    const targetLabel = '{target_dataset_label}';

                    // Exact match first
                    if (datasetLabel === targetLabel) {{
                        targetDatasetIndex = index;
                        return;
                    }}

                    // Fuzzy match (contains or similar)
                    if (datasetLabel.includes(targetLabel) || targetLabel.includes(datasetLabel)) {{
                        targetDatasetIndex = index;
                        console.log(`Fuzzy match found: "${{datasetLabel}}" matches "${{targetLabel}}"`);
                    }}
                }});

                if (targetDatasetIndex === -1) {{
                    console.log('Target dataset not found: {target_dataset_label}');
                    console.log('Available datasets:', chart.data.datasets.map(d => d.label));

                    // Try to find any dataset and use the first one as fallback
                    if (chart.data.datasets.length > 0) {{
                        targetDatasetIndex = 0;
                        console.log('Using first dataset as fallback:', chart.data.datasets[0].label);
                    }} else {{
                        return false;
                    }}
                }}

                try {{
                    // Show only the target dataset
                    chart.data.datasets.forEach((dataset, index) => {{
                        const meta = chart.getDatasetMeta(index);
                        if (meta) {{
                            meta.hidden = (index !== targetDatasetIndex);
                        }}
                    }});

                    // Enable legend but filter to show only target dataset
                    if (!chart.options.plugins) chart.options.plugins = {{}};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {{}};

                    chart.options.plugins.legend.display = true;
                    chart.options.plugins.legend.labels = {{
                        filter: function(legendItem) {{
                            return legendItem.datasetIndex === targetDatasetIndex;
                        }}
                    }};

                    // Update chart
                    chart.update('none');

                    const actualDatasetLabel = chart.data.datasets[targetDatasetIndex].label;
                    console.log('Successfully enabled legend for:', actualDatasetLabel, '(index:', targetDatasetIndex, ')');
                    return true;
                }} catch (error) {{
                    console.error('Error updating chart:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                log_info(f" Enabled only legend for {target_dataset_label} in chart {chart_id}")
                return True
            else:
                log_error(f"Failed to enable legend for {target_dataset_label} in chart {chart_id}")
                return False

        except Exception as e:
            log_error(f" Error enabling legend for {target_dataset_label}: {str(e)}")
            return False

    async def debug_legend_control(self, page):
        """Debug function to check legend control setup"""
        try:
            debug_info = await page.evaluate("""
            (function() {
                const info = {
                    chartInstancesExists: !!window.chartInstances,
                    chartInstancesSize: window.chartInstances ? window.chartInstances.size : 0,
                    chartIds: window.chartInstances ? Array.from(window.chartInstances.keys()) : [],
                    chartDetails: []
                };

                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        info.chartDetails.push({
                            id: chartId,
                            hasInstance: !!chart,
                            hasData: !!(chart && chart.data),
                            datasetCount: chart && chart.data && chart.data.datasets ? chart.data.datasets.length : 0,
                            datasetLabels: chart && chart.data && chart.data.datasets ? chart.data.datasets.map(d => d.label) : []
                        });
                    });
                }

                return info;
            })()
            """)
            
            for chart_detail in debug_info.get('chartDetails', []):
                chart_id = chart_detail.get('id', 'Unknown')
                dataset_count = chart_detail.get('datasetCount', 0)
                dataset_labels = chart_detail.get('datasetLabels', [])                
            return debug_info

        except Exception as e:
            log_error(f" Error debugging legend control: {str(e)}")
            return None

    async def click_data_point_for_drilldown(self, page, chart_id, point_data):
        """Click on a specific data point to trigger drilldown navigation"""
        try:
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')

            # First try using the pre-calculated screen coordinates
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    log_info(f"Clicking at pre-calculated coordinates: ({screen_x}, {screen_y})")
                    await page.mouse.click(screen_x, screen_y)
                    await asyncio.sleep(1)
                    return {
                        'success': True,
                        'method': 'pre_calculated_coordinates',
                        'position': {'x': screen_x, 'y': screen_y},
                        'dataset_label': dataset_label,
                        'x_label': x_label
                    }
                except Exception as coord_error:
                    log_error(f"Pre-calculated coordinates failed: {coord_error}")

            # Fallback to JavaScript-based clicking
            click_result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to click data point for chart: {chart_id}, dataset: {dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found');
                    return {{ success: false, error: 'Chart instances not initialized' }};
                }}

                if (!window.chartInstances.has('{chart_id}')) {{
                    console.log('Chart {chart_id} not found');
                    console.log('Available charts:', Array.from(window.chartInstances.keys()));
                    return {{ success: false, error: 'Chart instance not found' }};
                }}

                const chartData = window.chartInstances.get('{chart_id}');
                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure');
                    return {{ success: false, error: 'Invalid chart data' }};
                }}

                try {{
                    // Find target dataset index by label
                    let targetDatasetIndex = {dataset_index};
                    chart.data.datasets.forEach((dataset, index) => {{
                        if (dataset.label === '{dataset_label}') {{
                            targetDatasetIndex = index;
                        }}
                    }});

                    // Get dataset meta
                    const meta = chart.getDatasetMeta(targetDatasetIndex);
                    if (!meta || !meta.data || !meta.data[{point_index}]) {{
                        console.log('Data point not found at index: {point_index}');
                        return {{ success: false, error: 'Data point not found' }};
                    }}

                    // Get point element and position
                    const pointElement = meta.data[{point_index}];
                    const pointPosition = pointElement.getCenterPoint();
                    const canvas = chart.canvas;

                    // Create click event
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {{
                        clientX: rect.left + pointPosition.x,
                        clientY: rect.top + pointPosition.y,
                        bubbles: true,
                        cancelable: true
                    }});

                    // Dispatch click event
                    canvas.dispatchEvent(clickEvent);

                    console.log('Successfully clicked data point: {x_label} from {dataset_label}');
                    return {{
                        success: true,
                        method: 'javascript_click',
                        position: pointPosition,
                        dataset_label: '{dataset_label}',
                        x_label: '{x_label}'
                    }};

                }} catch (error) {{
                    console.error('Error clicking data point:', error);
                    return {{ success: false, error: error.message }};
                }}
            }})()
            """)

            if click_result.get('success', False):
                log_info(f" Data point clicked for drilldown: {x_label} from {dataset_label}")
                return click_result
            else:
                log_error(f"Failed to click data point: {click_result.get('error', 'Unknown error')}")
                return click_result

        except Exception as e:
            log_error(f" Error clicking data point for drilldown: {str(e)}")
            return {'success': False, 'error': str(e)}

 
    async def process_single_point_task_with_selective_legend(self, page, task, target_month_year):
        """Process a single point task with selective legend control and drilldown"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        try:
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')         
            # Step 1: Enable only the target legend, disable all others
            legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
            if not legend_enabled:
                log_error(f"{task_id}: Legend control failed, continuing anyway")
            # Wait for legend update
            await asyncio.sleep(1)
            # Debug: Check if the chart area is visible and clickable
            await self.debug_chart_clickability(page, chart_id, point_data)
            # Step 2: Click data point and wait for drilldown navigation
            click_result = None
            navigation_result = None
            # Get current URL before clicking
            initial_url = page.url
            log_info(f"{task_id}: Current URL before click: {initial_url}")
            # Method 1: Use pre-calculated coordinates (most reliable)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    await page.mouse.click(screen_x, screen_y)                   
                    await asyncio.sleep(3)  # Give time for navigation to start
                    # Check if URL changed
                    current_url = page.url
                    if current_url != initial_url:
                        log_info(f" {task_id}: Navigation detected to: {current_url}")
                        # Wait for page to fully load
                        try:
                            # await page.wait_for_load_state("networkidle", timeout=10000)
                            await asyncio.sleep(2)
                        except Exception:
                            pass  # Continue even if load state fails

                        click_result = {
                            'success': True,
                            'method': 'pre_calculated_coordinates',
                            'coordinates': {'x': screen_x, 'y': screen_y}
                        }

                        navigation_result = {
                            'success': True,
                            'url': current_url,
                            'navigation_completed': True,
                            'initial_url': initial_url
                        }

                    else:
                        log_info(f"{task_id}: No navigation detected after coordinate click")
                        # Wait a bit more and check again
                        await asyncio.sleep(2)
                        current_url = page.url

                        if current_url != initial_url:
                            log_info(f" {task_id}: Delayed navigation detected to: {current_url}")
                            click_result = {'success': True, 'method': 'pre_calculated_coordinates'}
                            navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                        else:
                            log_info(f" {task_id}: No navigation after coordinate click")
                            click_result = {'success': False, 'error': 'No navigation detected after coordinate click'}
                            navigation_result = {'success': False, 'error': 'No URL change detected'}

                except Exception as coord_error:
                    log_error(f"{task_id}: Coordinate clicking failed: {coord_error}")
                    click_result = {'success': False, 'error': str(coord_error)}

            # Method 2: Fallback to JavaScript-based clicking
            if not click_result or not click_result.get('success', False):
                log_info(f"{task_id}: Trying JavaScript-based clicking...")

                try:
                    # Try JavaScript clicking
                    js_click_result = await self.click_data_point_for_drilldown(page, chart_id, point_data)

                    if js_click_result.get('success', False):
                        log_info(f"{task_id}: JavaScript click executed, waiting for navigation...")

                        # Wait for potential navigation
                        await asyncio.sleep(3)
                        # Check if URL changed
                        current_url = page.url
                        if current_url != initial_url:                           
                            # Wait for page to fully load
                            try:
                                # await page.wait_for_load_state("networkidle", timeout=10000)
                                await asyncio.sleep(2)
                            except Exception:
                                pass
                            click_result = js_click_result
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                        else:                           
                            # Wait a bit more and check again
                            await asyncio.sleep(2)
                            current_url = page.url
                            if current_url != initial_url:
                                log_info(f" {task_id}: Delayed JavaScript navigation detected to: {current_url}")
                                click_result = js_click_result
                                navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                            else:
                                click_result = {'success': False, 'error': 'JavaScript click no navigation'}
                                navigation_result = {'success': False, 'error': 'No URL change after JavaScript click'}
                    else:
                        click_result = js_click_result
                        navigation_result = {'success': False, 'error': 'JavaScript click failed'}

                except Exception as js_error:
                    log_error(f" {task_id}: JavaScript clicking failed: {js_error}")
                    click_result = {'success': False, 'error': str(js_error)}
                    navigation_result = {'success': False, 'error': str(js_error)}

            # Method 3: Fallback to simple click with longer wait
            if not click_result or not click_result.get('success', False):
                log_info(f"{task_id}: Trying simple click with extended wait...")
                try:
                    # Simple click without complex navigation detection
                    await page.mouse.click(screen_x, screen_y)
                    log_info(f"{task_id}: Simple click executed, waiting longer for navigation...")
                    # Wait longer for navigation
                    await asyncio.sleep(5)
                    # Check URL multiple times
                    for attempt in range(3):
                        current_url = page.url
                        if current_url != initial_url:
                            click_result = {
                                'success': True,
                                'method': 'simple_click_extended_wait',
                                'coordinates': {'x': screen_x, 'y': screen_y}
                            }
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                            break

                        if attempt < 2:  # Don't wait after last attempt
                            await asyncio.sleep(2)

                    if not click_result or not click_result.get('success', False):
                        log_info(f" {task_id}: Simple click also failed to trigger navigation")
                        click_result = {'success': False, 'error': 'Simple click no navigation'}
                        navigation_result = {'success': False, 'error': 'No URL change after simple click'}
                except Exception as simple_error:
                    log_error(f" {task_id}: Simple clicking failed: {simple_error}")
                    click_result = {'success': False, 'error': str(simple_error)}
                    navigation_result = {'success': False, 'error': str(simple_error)}

            # Check if any clicking method succeeded
            if not click_result or not click_result.get('success', False):
                error_msg = click_result.get('error', 'All clicking methods failed') if click_result else 'No click result'
                log_error(f" {task_id}: Failed to click data point: {error_msg}")
                return {
                    'task_id': task_id,
                    'chart_id': chart_id,
                    'error': f"Failed to click data point: {error_msg}",
                    'success': False,
                    'legend_controlled': legend_enabled
                }
            # Check navigation result
            if not navigation_result or not navigation_result.get('success', False):
                log_info(f"{task_id}: Navigation failed, but click was successful - attempting data extraction anyway")
                # Try to extract data from current page
                current_url = page.url
                navigation_result = {
                    'success': False,
                    'url': current_url,
                    'navigation_completed': False,
                    'error': 'Navigation failed but click succeeded'
                }

            # Step 3: Extract data from the drilldown page
            extracted_data = None
            extraction_success = False
            # Only attempt extraction if navigation was successful
            if navigation_result and navigation_result.get('success', False):
                current_url = page.url
                # Check if we're on the expected drilldown page
                if "AnalyzeData" in current_url and "chartId=drillDown" in current_url:
                    log_info(f" {task_id}: On correct drilldown page, extracting data...")
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year)
                    # Check extraction success from the nested structure
                    extraction_success = False
                    if extracted_data and extracted_data.get('extraction_data'):
                        extraction_success = extracted_data['extraction_data'].get('success', False)
                    if extraction_success:
                        log_info(f" {task_id}: Data extraction successful")
                    else:
                        log_error(f"{task_id}: Data extraction failed or incomplete")
                else:
                    log_info(f"{task_id}: Not on expected drilldown page. URL: {current_url}")
                    extracted_data = {
                        'extraction_data': {
                            'success': False,
                            'error': f'Unexpected page URL: {current_url}',
                            'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
                        }
                    }
                    extraction_success = False
            else:
                log_error(f" {task_id}: Skipping data extraction due to navigation failure")
                extracted_data = {
                    'extraction_data': {
                        'success': False,
                        'error': 'Navigation failed, cannot extract data',
                        'navigation_error': navigation_result.get('error', 'Unknown navigation error') if navigation_result else 'No navigation result'
                    }
                }
                extraction_success = False
            # Determine overall success
            overall_success = (
                click_result.get('success', False) and
                navigation_result.get('success', False) and
                extraction_success
            )
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'click_result': click_result,
                'navigation_result': navigation_result,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': overall_success,
                'legend_controlled': legend_enabled,
                'drilldown_url': navigation_result.get('url', '') if navigation_result else '',
                'click_success': click_result.get('success', False) if click_result else False,
                'navigation_success': navigation_result.get('success', False) if navigation_result else False,
                'extraction_success': extraction_success
            }
            log_info(f" {task_id}: Successfully processed {point_label} from {dataset_label}")
            return result
        except Exception as e:
            log_info(f" {task_id}: Error processing point: {e}")
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': False
            }
            return error_result

    async def extract_data_from_drilldown_page(self, page, point_data, target_month_year):
        """Extract MUI Grid data from drilldown page focusing on h5 and h6 tags only"""
        max_retries = 3
        retry_delay = 2  # seconds between retries
        
        for attempt in range(max_retries):
            try:
                log_info(f"Extracting drill-down page data... (Attempt {attempt + 1}/{max_retries})")

                # Wait for page to load completely
                # await page.wait_for_load_state("networkidle", timeout=10000)
                await asyncio.sleep(3)
                extraction_data = {
                    "extraction_timestamp": datetime.now().isoformat(),
                    "page_url": page.url,
                    "mui_grid_data": [],
                    "all_text_content": [],
                    "raw_html_sections": [],
                    "monetary_data": [],
                    "success": False,
                    "error": None,
                    "attempt": attempt + 1
                }
                # Method 1: Look for MUI Grid containers with the specific structure
                mui_grid_selectors = [
                    '.MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3',
                    '.MuiGrid-root.MuiGrid-container',
                    '[class*="MuiGrid-container"]'
                ]
                for selector in mui_grid_selectors:
                    try:
                        grid_containers = await page.query_selector_all(selector)
                        log_info(f"Found {len(grid_containers)} grid containers with selector: {selector}")
                        for container_index, container in enumerate(grid_containers):
                            if await container.is_visible():
                                # Extract the specific structure we're looking for
                                grid_items = await container.query_selector_all('.MuiGrid-item')
                                log_info(f"Container {container_index} has {len(grid_items)} grid items")
                                container_data = {
                                    "container_index": container_index,
                                    "selector_used": selector,
                                    "items": []
                                }
                                for item_index, item in enumerate(grid_items):
                                    # Look for h5 (title) and h6 (value) elements
                                    h5_element = await item.query_selector('h5.MuiTypography-root.MuiTypography-h5')
                                    h6_element = await item.query_selector('h6.MuiTypography-root.MuiTypography-subtitle1')
                                    if h5_element and h6_element:
                                        title = (await h5_element.text_content()).strip()
                                        value = (await h6_element.text_content()).strip()
                                        item_data = {
                                            "item_index": item_index,
                                            "title": title,
                                            "value": value,
                                            "html_structure": {
                                                "h5_html": await h5_element.inner_html(),
                                                "h6_html": await h6_element.inner_html()
                                            }
                                        }
                                        container_data["items"].append(item_data)
                                        # log_info(f"Extracted: {title} - {value}")
                                if container_data["items"]:
                                    extraction_data["mui_grid_data"].append(container_data)
                                    # log_info(f"Added container {container_index} with {len(container_data['items'])} items")

                    except Exception as selector_error:
                        log_error(f"Error with selector {selector}: {selector_error}")
                        continue
                # Determine success
                extraction_data["success"] = len(extraction_data["mui_grid_data"]) > 0
                log_info(f"Extraction success: {extraction_data['success']}")
                log_info(f"MUI Grid data items: {len(extraction_data['mui_grid_data'])}")
                # If successful, return the result immediately
                if extraction_data["success"]:
                    log_info(f" Data extraction successful on attempt {attempt + 1}")
                    result = {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': extraction_data,
                        'error': None,
                        'processing_time': 0,
                        'screenshot_path': None
                    }
                    log_info(f" result==== {result}")
                    return result

                # If not successful and not the last attempt, wait and retry
                if attempt < max_retries - 1:
                    log_error(f"Attempt {attempt + 1} failed, retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)                   
                else:
                    log_error(f" All {max_retries} attempts failed")
                    # Return failure result after all attempts exhausted
                    result = {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': extraction_data,
                        'error': f"Data extraction failed after {max_retries} attempts",
                        'processing_time': 0,
                        'screenshot_path': None
                    }
                    return result

            except Exception as e:
                log_error(f" Error extracting drill-down page data on attempt {attempt + 1}: {e}")
              
                # If not the last attempt, wait and retry
                if attempt < max_retries - 1:
                    log_error(f"Attempt {attempt + 1} failed with error, retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                else:
                    log_error(f" All {max_retries} attempts failed with errors")
                    # Return error result after all attempts exhausted
                    return {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': {
                            "extraction_timestamp": datetime.now().isoformat(),
                            "success": False,
                            "error": str(e),
                            "attempt": attempt + 1
                        },
                        'error': str(e),
                        'processing_time': 0,
                        'screenshot_path': None
                    }
        return {
            'target_month_year': target_month_year,
            'point_data': point_data,
            'click_success': True,
            'navigation_success': True,
            'extraction_data': {
                "extraction_timestamp": datetime.now().isoformat(),
                "success": False,
                "error": "Maximum retries exceeded",
                "attempt": max_retries
            },
            'error': "Maximum retries exceeded",
            'processing_time': 0,
            'screenshot_path': None
        }
   
    async def debug_and_setup_charts(self, page):
        """Debug and manually setup chart instances"""
        try:
            result = await page.evaluate("""
                (function() {
                    console.log('=== CHART DEBUG AND SETUP ===');

                    // Check if Chart.js is available
                    console.log('Chart.js available:', typeof Chart !== 'undefined');

                    // Find all canvas elements
                    const allCanvases = document.querySelectorAll('canvas');
                    console.log(`Total canvas elements found: ${allCanvases.length}`);

                    const chartCanvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                    console.log(`Chart.js canvas elements found: ${chartCanvases.length}`);

                    // Initialize chart instances map
                    if (!window.chartInstances) {
                        window.chartInstances = new Map();
                    }

                    let foundCharts = 0;

                    // Try multiple methods to find charts
                    chartCanvases.forEach((canvas, index) => {
                        let chartInstance = null;

                        console.log(`Processing canvas ${index}:`);

                        // Method 1: Chart.getChart
                        if (typeof Chart !== 'undefined' && Chart.getChart) {
                            try {
                                chartInstance = Chart.getChart(canvas);
                                if (chartInstance) {
                                    console.log(`  - Found via Chart.getChart`);
                                }
                            } catch (e) {
                                console.log(`  - Chart.getChart failed:`, e.message);
                            }
                        }

                        // Method 2: Canvas chart property
                        if (!chartInstance && canvas.chart) {
                            chartInstance = canvas.chart;
                            console.log(`  - Found via canvas.chart`);
                        }

                        // Method 3: Canvas _chart property
                        if (!chartInstance && canvas._chart) {
                            chartInstance = canvas._chart;
                            console.log(`  - Found via canvas._chart`);
                        }

                        // Method 4: Search Chart.instances
                        if (!chartInstance && typeof Chart !== 'undefined' && Chart.instances) {
                            Object.values(Chart.instances).forEach(instance => {
                                if (instance.canvas === canvas) {
                                    chartInstance = instance;
                                    console.log(`  - Found via Chart.instances`);
                                }
                            });
                        }

                        if (chartInstance && chartInstance.data && chartInstance.data.datasets) {
                            const chartId = `chart_${index}`;
                            window.chartInstances.set(chartId, {
                                instance: chartInstance,
                                canvas: canvas,
                                originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                            });

                            console.log(`  - Registered as ${chartId} with ${chartInstance.data.datasets.length} datasets`);
                            chartInstance.data.datasets.forEach((dataset, dsIndex) => {
                                console.log(`    Dataset ${dsIndex}: ${dataset.label || 'Unnamed'}`);
                            });

                            foundCharts++;
                        } else {
                            console.log(`  - No valid chart instance found`);
                        }
                    });

                    console.log(`=== SETUP COMPLETE: ${foundCharts} charts registered ===`);
                    return foundCharts;
                })()
            """)

            log_info(f"Debug setup completed: {result} charts found and registered")
            return result > 0

        except Exception as e:
            log_error(f" Debug setup failed: {str(e)}")
            return False
   
    async def click_chart_data_point(self, page, chart_id, point_data):
        """Click on a specific chart data point directly"""
        try:
            # Extract point information
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)            
            # Click on the specific data point
            point_clicked = await page.evaluate(f"""
            (function() {{
                if (window.chartInstances && window.chartInstances.has('{chart_id}')) {{
                    const chartData = window.chartInstances.get('{chart_id}');
                    const chart = chartData.instance;
                    
                    try {{
                        // Find the dataset by label if datasetIndex is not reliable
                        let targetDatasetIndex = {dataset_index};
                        
                        // Verify dataset index by label
                        chart.data.datasets.forEach((dataset, index) => {{
                            if (dataset.label === '{dataset_label}') {{
                                targetDatasetIndex = index;
                            }}
                        }});
                        
                        // Get the dataset meta
                        const meta = chart.getDatasetMeta(targetDatasetIndex);
                        if (!meta || !meta.data || !meta.data[{point_index}]) {{
                            console.log('Data point not found at index: ' + {point_index});
                            return false;
                        }}
                        
                        // Get the data point element
                        const pointElement = meta.data[{point_index}];
                        if (!pointElement) {{
                            console.log('Point element not found');
                            return false;
                        }}                        
                        // Get the chart canvas
                        const canvas = chart.canvas;
                        if (!canvas) {{
                            console.log('Canvas not found');
                            return false;
                        }}                        
                        // Get point position
                        const pointPosition = pointElement.getCenterPoint();                        
                        // Create and dispatch click event
                        const rect = canvas.getBoundingClientRect();
                        const clickEvent = new MouseEvent('click', {{
                            clientX: rect.left + pointPosition.x,
                            clientY: rect.top + pointPosition.y,
                            bubbles: true,
                            cancelable: true
                        }});                        
                        // Dispatch the click event
                        canvas.dispatchEvent(clickEvent);                        
                        console.log('Clicked data point: ' + '{x_label}' + ' from dataset: ' + '{dataset_label}');
                        console.log('Point position:', pointPosition);                        
                        return true;                        
                    }} catch (error) {{
                        console.error('Error clicking data point:', error);
                        return false;
                    }}
                }}
                console.log('Chart instance not found: {chart_id}');
                return false;
            }})()
            """)
            
            if point_clicked:
                log_info(f" Data point clicked for {chart_id} - {x_label} from {dataset_label}")
                return True
            else:
                log_error(f"Could not click data point for {chart_id} - {x_label} from {dataset_label}")
                return False               
        except Exception as e:
            log_error(f"Failed to click data point for {chart_id} - {x_label}: {str(e)}")
            return False
   
    async def process_all_combinations_parallel(self, combinations):
        """Process charts in parallel with multiple browsers, each handling different charts"""

        log_info(f"🚀 Starting parallel processing of {len(combinations)} charts with 3 browsers")       
        # Organize combinations by chart for easier processing
        chart_combinations = {}
        for combo_idx, combination in enumerate(combinations):
            chart_id = combination.get('chart_id', f'chart_{combo_idx}')
            target_month = combination.get('target_month_year', 'unknown')
            matching_points = combination.get('matching_points', [])

            chart_combinations[chart_id] = {
                'combination_index': combo_idx,
                'chart_info': combination['chart_info'],
                'target_month_year': target_month,
                'matching_points': matching_points,
                'current_point_index': 0
            }
            log_info(f"**Chart {chart_id}: {target_month} ({(len(matching_points))} points)")

        all_results = []
        target_month_year = TARGET_MONTHS_YEARS
        max_browsers = 3

        # Group charts for parallel processing (3 charts at a time)
        chart_items = list(chart_combinations.items())
        chart_batches = []

        # Create batches of 3 charts each
        for i in range(0, len(chart_items), max_browsers):
            batch = chart_items[i:i + max_browsers]
            chart_batches.append(batch)

        log_info(f"Processing {len(chart_batches)} batches with up to {max_browsers} browsers per batch")
        # Process each batch of charts in parallel
        for batch_index, chart_batch in enumerate(chart_batches, 1):
            log_info(f"\n🚀 Starting Batch {batch_index}/{len(chart_batches)} with {len(chart_batch)} charts")

            # Create parallel tasks for this batch
            batch_tasks = []
            for browser_index, (chart_id, chart_data) in enumerate(chart_batch):
                browser_id = f"Browser_{browser_index + 1}"
                log_info(f"   📋 {browser_id}: Will process Chart {chart_id} - {chart_data['chart_info'].get('chartTitle', 'Unknown')}")
               
                task = asyncio.create_task(
                    self.process_single_chart_parallel(chart_data, target_month_year, browser_id,chart_id)
                )
                batch_tasks.append((browser_id, chart_id, task))
            # Wait for all browsers in this batch to complete
            log_info(f"Waiting for all {len(batch_tasks)} browsers in batch {batch_index} to complete...")
            for browser_id, chart_id, task in batch_tasks:
                try:
                    result = await task
                    if isinstance(result, list):
                        all_results.extend(result)
                        log_info(f" {browser_id} completed Chart {chart_id}: {len(result)} results")
                    else:
                        log_info(f"{browser_id} returned unexpected result type for Chart {chart_id}")
                except Exception as e:
                    log_error(f" {browser_id} failed processing Chart {chart_id}: {str(e)}")
                    continue
            log_info(f" Batch {batch_index} completed")
            # Add delay between batches to avoid overwhelming the system
            if batch_index < len(chart_batches):
                log_info("Waiting before next batch...")
                await asyncio.sleep(3)

        # Process final results
        successful_results = []
        failed_results = []

        for result in all_results:
            if isinstance(result, dict) and result.get('success', False):
                successful_results.append(result)
            else:
                failed_results.append(result)

        log_info(f"\n🎉 Parallel processing with {max_browsers} browsers completed!")
        log_info("Summary:")
        log_info(f"   - Total charts processed: {len(chart_combinations)}")
        log_info(f"   - Total batches processed: {len(chart_batches)}")
        log_info(f"   - Total point tasks processed: {len(all_results)}")
        log_info(f"   - Successful: {len(successful_results)}")
        log_info(f"   - Failed: {len(failed_results)}")
        log_info(f"   - Success rate: {(len(successful_results) / len(all_results) * 100):.1f}%" if all_results else "0%")
        log_info(all_results,"all_results+++++++++++")
        return {
            'successful': successful_results,
            'failed': failed_results,
            'all_results': all_results,
            'total_processed': len(all_results),
            'total_charts': len(chart_combinations),
            'batches_processed': len(chart_batches),
            'success_rate': (len(successful_results) / len(all_results) * 100) if all_results else 0
        }

    async def process_single_chart_parallel(self, chart_data, target_month_year, browser_id,chart_id):
        """Process all points in a single chart in parallel (for use with multiple browsers)"""
        
        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
        matching_points = chart_data.get('matching_points', [])
        log_info(f"{browser_id}: Processing chart: {chart_title} :({chart_id}) with {len(matching_points)} points")
        chart_results = []
        # Use the dedicated parallel function to check and re-login the session. 
        # This ensures a valid session before a new page is even created.
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []
        
        # Get a NEW PAGE from the existing session.
        page = await self.auth_manager.new_page()
        try:
            # Navigate to CPOverview
            log_info(f"{browser_id}: Navigating to CPPartsOverview for {chart_id}")
            await page.goto(f"{config.site_url.rstrip('/')}/CPPartsOverview", timeout=30000)
            # await page.wait_for_load_state("networkidle", timeout=15000)
            await asyncio.sleep(2)

            # Apply enhanced legend control
            legend_setup_success = await self.apply_enhanced_legend_control(page)
            await asyncio.sleep(2)

            if not legend_setup_success:
                log_info(f"{browser_id}: Legend control setup failed for {chart_id}, attempting manual setup...")
                await self.debug_and_setup_charts(page)
            # Debug legend control setup
            await self.debug_legend_control(page)
            # Process each point in this chart sequentially within this browser
            for point_idx, point_data in enumerate(matching_points):
                point_label = point_data.get('xLabel', f'Point_{point_idx}')
                dataset_label = point_data.get('datasetLabel', 'Unknown Dataset') 
                try:
                    # Step 1: Disable ALL legends first                        
                    await self.disable_all_legends(page)
                    await asyncio.sleep(1)
                    # Step 2: Enable ONLY the legend for current chart/dataset                        
                    legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
                    await asyncio.sleep(2)  # Give more time for chart to update
                    if legend_enabled:
                        log_info(f" {browser_id}: Legend control successful - ONLY {chart_id} legend is active")
                    else:
                        log_error(f"{browser_id}: Legend control failed, but continuing with processing")
                    # Step 2.5: Ensure chart is interactive and data points are clickable
                    log_info(f"{browser_id}: Ensuring chart {chart_id} is interactive after legend control...")
                    await self.ensure_chart_interactivity(page, chart_id)
                    await asyncio.sleep(1)
                    # Step 3: Create task for this point
                    task = {
                        'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_info': chart_data['chart_info'],
                        'target_month_year': chart_data['target_month_year'],
                        'point_data': point_data,
                        'point_index': point_idx
                    }

                    # Step 4: Process this point with enhanced clicking
                    result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year)
                    if isinstance(result, dict):
                        result['chart_title'] = chart_title
                        result['point_sequence'] = point_idx + 1
                        result['method'] = 'parallel_processing'
                        result['browser_id'] = browser_id
                        result['chart_id'] = chart_id

                    chart_results.append(result)

                    # Log detailed result
                    if result.get('success', False):
                        click_success = result.get('click_success', False)
                        nav_success = result.get('navigation_success', False)
                        extract_success = result.get('extraction_success', False)                           
                        if nav_success:
                            drilldown_url = result.get('drilldown_url', 'Unknown')
                            log_info(f"   🔗 Drilldown URL: {drilldown_url}")
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        log_error(f" {browser_id}: Failed point {point_idx + 1}: {point_label} - {error_msg}")

                    # Step 5: Navigate back to CPPartsOverview for next point (if not last point)
                    if point_idx < len(matching_points) - 1:
                        log_info(f"{browser_id}: Navigating back to CPPartsOverview for next point")
                        try:
                            await page.goto(f"{config.site_url.rstrip('/')}/CPPartsOverview", timeout=30000)
                            # await page.wait_for_load_state("networkidle", timeout=15000)
                            await asyncio.sleep(2)

                            # Re-apply legend control
                            await self.apply_enhanced_legend_control(page)
                            await asyncio.sleep(1)
                            log_info(f" {browser_id}: Successfully navigated back to CPPartsOverview")
                        except Exception as nav_back_error:
                            log_error(f" {browser_id}: Failed to navigate back to CPPartsOverview: {nav_back_error}")
                            # Try to continue anyway
                            pass

                except Exception as e:
                    log_error(f" {browser_id}: Error processing point {point_idx + 1} - {point_label}: {str(e)}")
                    error_result = {
                        'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_title': chart_title,
                        'point_label': point_label,
                        'error': str(e),
                        'success': False,
                        'method': 'parallel_processing',
                        'browser_id': browser_id,
                        'point_sequence': point_idx + 1
                    }
                    chart_results.append(error_result)

            log_info(f" {browser_id}: Completed all points for chart: {chart_title}")

        except Exception as e:
            log_error(f" {browser_id}: Error setting up chart {chart_id}: {str(e)}")
            error_result = {
                'chart_id': chart_id,
                'chart_title': chart_title,
                'error': f"Chart setup failed: {str(e)}",
                'success': False,
                'method': 'parallel_processing',
                'browser_id': browser_id
            }
            chart_results.append(error_result)

        finally:
            try:
                # await context.close()
                # await browser.close()
                await page.close()
                log_info(f"🔒 {browser_id}: Browser closed for chart {chart_id}")
            except Exception as cleanup_error:
                    log_error(f"{browser_id}: Cleanup error for {chart_id}: {cleanup_error}")

        return chart_results

    async def process_single_chart_sequential(self, chart_data, target_month_year):
        """Process all points in a single chart sequentially"""
        chart_id = chart_data.get('chart_info', {}).get('canvasId', 'unknown_chart')
        chart_ids =  chart_data.get('chart_id')
        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
        matching_points = chart_data.get('matching_points', [])

        log_info(f"🎯 Processing chart::: {chart_title} ({chart_id}) with {len(matching_points)} points:: {chart_data}")

        chart_results = []
            
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []
        # Get a NEW PAGE from the existing session.
        page = await self.auth_manager.new_page()

        try:
            # Navigate to CPPartsOverview
            log_info(f"Navigating to CPPartsOverview for {chart_id}")
            await page.goto("https://carriageag-simt.fixedopspc.com/CPPartsOverview", timeout=30000)
            await asyncio.sleep(2)

            # Apply enhanced legend control
            legend_setup_success = await self.apply_enhanced_legend_control(page)
            await asyncio.sleep(2)
            log_info(f"legend_setup_success= {legend_setup_success}")
            if not legend_setup_success:
                log_error(f"Legend control setup failed for {chart_id}, attempting manual setup...")
                await self.debug_and_setup_charts(page)

            # Debug legend control setup
            await self.debug_legend_control(page)

            log_info(f" Page setup completed for {chart_id}")

            # Process each point in this chart sequentially
            for point_idx, point_data in enumerate(matching_points):
                point_label = point_data.get('xLabel', f'Point_{point_idx}')
                dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')

                log_info(f"\nProcessing point {point_idx + 1}/{len(matching_points)}: {point_label} ({dataset_label})")

                try:
                    # Step 1: Disable ALL legends first
                    log_info(f"🔒 Disabling all legends before processing {chart_id}")
                    await self.disable_all_legends(page)
                    await asyncio.sleep(1)

                    # Step 2: Enable ONLY the legend for current chart/dataset
                    log_info(f"🔓 Enabling ONLY legend for {chart_id} - {dataset_label}")
                    legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
                    await asyncio.sleep(2)  # Give more time for chart to update

                    if legend_enabled:
                        log_info(f" Legend control successful - ONLY {chart_id} legend is active")
                    else:
                        log_error(f"Legend control failed, but continuing with processing")

                    # Step 2.5: Ensure chart is interactive and data points are clickable
                    log_info(f"Ensuring chart {chart_id} is interactive after legend control...")
                    await self.ensure_chart_interactivity(page, chart_id)
                    await asyncio.sleep(1)

                    # Step 3: Create task for this point
                    task = {
                        'task_id': f"{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_info': chart_data['chart_info'],
                        'target_month_year': chart_data['target_month_year'],
                        'point_data': point_data,
                        'point_index': point_idx
                    }

                    # Step 4: Process this point with enhanced clicking
                    result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year, chart_ids)

                    if isinstance(result, dict):
                        result['chart_title'] = chart_title
                        result['point_sequence'] = point_idx + 1
                        result['method'] = 'sequential_processing'

                    chart_results.append(result)

                    # Log detailed result
                    if result.get('success', False):
                        click_success = result.get('click_success', False)
                        nav_success = result.get('navigation_success', False)
                        extract_success = result.get('extraction_success', False)
                        log_info(f" Completed point {point_idx + 1}: {point_label}")
                        log_info(f"   Click: {'' if click_success else ''} | Navigation: {'' if nav_success else ''} | Extraction: {'' if extract_success else ''}")
                        if nav_success:
                            drilldown_url = result.get('drilldown_url', 'Unknown')
                            log_info(f"   🔗 Drilldown URL: {drilldown_url}")
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        log_error(f" Failed point {point_idx + 1}: {point_label} - {error_msg}")

                    # Step 5: Navigate back to CPPartsOverview for next point (if not last point)
                    if point_idx < len(matching_points) - 1:
                        log_info(f"Navigating back to CPPartsOverview for next point")
                        try:
                            await page.goto("https://carriageag-simt.fixedopspc.com/CPPartsOverview", timeout=30000)
                            # await page.wait_for_load_state("networkidle", timeout=15000)
                            await asyncio.sleep(2)

                            # Re-apply legend control
                            await self.apply_enhanced_legend_control(page)
                            await asyncio.sleep(1)
                            log_info(f" Successfully navigated back to CPPartsOverview")
                        except Exception as nav_back_error:
                            log_error(f" Failed to navigate back to CPPartsOverview: {nav_back_error}")
                            # Try to continue anyway
                            pass

                except Exception as e:
                    log_error(f" Error processing point {point_idx + 1} - {point_label}: {str(e)}")
                    error_result = {
                        'task_id': f"{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_title': chart_title,
                        'point_label': point_label,
                        'error': str(e),
                        'success': False,
                        'method': 'sequential_processing',
                        'point_sequence': point_idx + 1
                    }
                    chart_results.append(error_result)

            log_info(f" Completed all points for chart: {chart_title}")

        except Exception as e:
            log_error(f" Error setting up chart {chart_id}: {str(e)}")
            error_result = {
                'chart_id': chart_id,
                'chart_title': chart_title,
                'error': f"Chart setup failed: {str(e)}",
                'success': False,
                'method': 'sequential_processing'
            }
            chart_results.append(error_result)

        finally:
            try:
                await page.close()
            except Exception as cleanup_error:
                log_error(f"Cleanup error for {chart_id}: {cleanup_error}")

        return chart_results    
  
    async def ensure_chart_interactivity(self, page, chart_id):
        """Ensure chart is interactive and data points are clickable after legend control"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Ensuring chart interactivity for: {chart_id}');

                if (!window.chartInstances) {{
                    console.log('No chart instances found');
                    return false;
                }}

                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;
                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found for interactivity check');
                    return false;
                }}

                const chart = chartData.instance;

                try {{
                    // Ensure chart is responsive to events
                    if (chart.options) {{
                        if (!chart.options.interaction) chart.options.interaction = {{}};
                        chart.options.interaction.intersect = false;
                        chart.options.interaction.mode = 'point';

                        if (!chart.options.onHover) {{
                            chart.options.onHover = function(event, elements) {{
                                console.log('Chart hover detected:', elements.length, 'elements');
                            }};
                        }}

                        if (!chart.options.onClick) {{
                            chart.options.onClick = function(event, elements) {{
                                console.log('Chart click detected:', elements.length, 'elements');
                                if (elements.length > 0) {{
                                    const element = elements[0];
                                    console.log('Clicked element:', element);
                                }}
                            }};
                        }}
                    }}

                    // Force chart update to apply interaction settings
                    chart.update('none');

                    console.log('Chart interactivity ensured for: {chart_id}');
                    return true;
                }} catch (error) {{
                    console.error('Error ensuring chart interactivity:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                log_info(f" Chart {chart_id} interactivity ensured")
            else:
                log_error(f"Failed to ensure chart {chart_id} interactivity")

            return result

        except Exception as e:
            log_error(f" Error ensuring chart interactivity: {str(e)}")
            return False

    async def process_single_point_task_with_enhanced_clicking(self, page, task, target_month_year, chart_ids):
        """Process a single point task with enhanced clicking methods for drilldown"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        try:
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            # Get current URL before clicking
            initial_url = page.url
            log_info(f"{task_id}: Current URL before click: {initial_url}")

            # Method 1: Try Chart.js event-based clicking first
            click_result = await self.try_chartjs_event_click(page, chart_id, point_data)
            navigation_result = None

            if click_result.get('success', False):
                log_info(f" {task_id}: Chart.js event click successful, checking for navigation...")

                # Wait for navigation
                await asyncio.sleep(3)
                current_url = page.url

                if current_url != initial_url:
                    log_info(f" {task_id}: Navigation successful to: {current_url}")
                    navigation_result = {'success': True, 'url': current_url, 'method': 'chartjs_event'}
                else:
                    log_error(f"{task_id}: Chart.js event click didn't trigger navigation")
                    click_result = {'success': False, 'error': 'No navigation after Chart.js event click'}

            # Method 2: Fallback to coordinate clicking if Chart.js event didn't work
            if not click_result or not click_result.get('success', False):
                log_info(f"{task_id}: Trying coordinate-based clicking...")

                screen_x = point_data.get('screenX')
                screen_y = point_data.get('screenY')

                if screen_x and screen_y:
                    # Try multiple click methods
                    for click_method in ['single', 'double', 'with_delay']:
                        log_info(f"{task_id}: Trying {click_method} click at ({screen_x}, {screen_y})")

                        if click_method == 'single':
                            await page.mouse.click(screen_x, screen_y)
                        elif click_method == 'double':
                            await page.mouse.click(screen_x, screen_y, click_count=2)
                        elif click_method == 'with_delay':
                            await page.mouse.move(screen_x, screen_y)
                            await asyncio.sleep(0.5)
                            await page.mouse.down()
                            await asyncio.sleep(0.1)
                            await page.mouse.up()

                        # Check for navigation after each method
                        await asyncio.sleep(3)
                        current_url = page.url

                        if current_url != initial_url:
                            log_info(f" {task_id}: {click_method} click triggered navigation to: {current_url}")
                            click_result = {'success': True, 'method': f'coordinate_{click_method}'}
                            navigation_result = {'success': True, 'url': current_url, 'method': f'coordinate_{click_method}'}
                            break
                        else:
                            log_error(f"{task_id}: {click_method} click didn't trigger navigation")

                    if not navigation_result or not navigation_result.get('success', False):
                        click_result = {'success': False, 'error': 'All coordinate click methods failed'}
                        navigation_result = {'success': False, 'error': 'No navigation with any click method'}
                else:
                    click_result = {'success': False, 'error': 'No coordinates available'}
                    navigation_result = {'success': False, 'error': 'No coordinates for clicking'}

            # Continue with data extraction if navigation was successful
            if navigation_result and navigation_result.get('success', False):
                current_url = page.url
                log_info(f"{task_id}: Attempting data extraction from: {current_url}")

                if "AnalyzeData" in current_url and "chartId=drillDown" in current_url:
                    log_info(f" {task_id}: On correct drilldown page, extracting data...")
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year)
                    extraction_success = False
                    if extracted_data and extracted_data.get('extraction_data'):
                        extraction_success = extracted_data['extraction_data'].get('success', False)

                    if extraction_success:
                        log_info(f" {task_id}: Data extraction successful")
                    else:
                        log_error(f"{task_id}: Data extraction failed or incomplete")
                else:
                    log_error(f"{task_id}: Not on expected drilldown page. URL: {current_url}")
                    extracted_data = {
                        'extraction_data': {
                            'success': False,
                            'error': f'Unexpected page URL: {current_url}',
                            'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
                        }
                    }
                    extraction_success = False
            else:
                log_error(f" {task_id}: Skipping data extraction due to navigation failure")
                extracted_data = {
                    'extraction_data': {
                        'success': False,
                        'error': 'Navigation failed, cannot extract data',
                        'navigation_error': navigation_result.get('error', 'Unknown navigation error') if navigation_result else 'No navigation result'
                    }
                }
                extraction_success = False
            # Determine overall success
            overall_success = (
                click_result.get('success', False) and
                navigation_result.get('success', False) and
                extraction_success
            )
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_ids': chart_ids,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'click_result': click_result,
                'navigation_result': navigation_result,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': overall_success,
                'legend_controlled': True,  # We know legend control was attempted
                'drilldown_url': navigation_result.get('url', '') if navigation_result else '',
                'click_success': click_result.get('success', False) if click_result else False,
                'navigation_success': navigation_result.get('success', False) if navigation_result else False,
                'extraction_success': extraction_success
            }
            log_info(f" {task_id}: Enhanced processing completed for {point_label} from {dataset_label}")
            return result

        except Exception as e:
            log_error(f" {task_id}: Error in enhanced processing: {e}")
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': True
            }
            return error_result

    async def try_chartjs_event_click(self, page, chart_id, point_data):
        """Try to trigger Chart.js click event programmatically"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting Chart.js event click for chart: {chart_id}');

                if (!window.chartInstances) {{
                    console.log('No chart instances found');
                    return {{ success: false, error: 'No chart instances' }};
                }}
                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        break;
                    }}
                }}
                if (!chartData) {{
                    console.log('Chart not found for event click');
                    return {{ success: false, error: 'Chart not found' }};
                }}

                const chart = chartData.instance;
                const canvas = chartData.canvas;

                try {{
                    // Get point data
                    const pointIndex = {point_data.get('pointIndex', 0)};
                    const datasetIndex = {point_data.get('datasetIndex', 0)};

                    // Get the data point element
                    const meta = chart.getDatasetMeta(datasetIndex);
                    if (!meta || !meta.data || !meta.data[pointIndex]) {{
                        console.log('Data point not found');
                        return {{ success: false, error: 'Data point not found' }};
                    }}

                    const pointElement = meta.data[pointIndex];
                    const pointPosition = pointElement.getCenterPoint();

                    // Create a synthetic click event
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {{
                        clientX: rect.left + pointPosition.x,
                        clientY: rect.top + pointPosition.y,
                        bubbles: true,
                        cancelable: true,
                        view: window
                    }});

                    // Trigger the click event
                    canvas.dispatchEvent(clickEvent);

                    // Also try Chart.js onClick if available
                    if (chart.options && chart.options.onClick) {{
                        const elements = chart.getElementsAtEventForMode(clickEvent, 'nearest', {{ intersect: true }}, false);
                        chart.options.onClick(clickEvent, elements, chart);
                    }}

                    console.log('Chart.js event click executed successfully');
                    return {{ success: true, method: 'chartjs_event', position: pointPosition }};

                }} catch (error) {{
                    console.error('Error in Chart.js event click:', error);
                    return {{ success: false, error: error.message }};
                }}
            }})()
            """)
            return result
        except Exception as e:
            log_error(f" Error in Chart.js event click: {str(e)}")
            return {'success': False, 'error': str(e)}

    async def debug_chart_clickability(self, page, chart_id, point_data):
        """Debug function to check if chart area is clickable"""
        try:
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')

            if screen_x and screen_y:
                # Check what element is at the click coordinates
                element_info = await page.evaluate(f"""
                    () => {{
                        const element = document.elementFromPoint({screen_x}, {screen_y});
                        if (element) {{
                            return {{
                                tagName: element.tagName,
                                className: element.className,
                                id: element.id,
                                isCanvas: element.tagName === 'CANVAS',
                                boundingRect: element.getBoundingClientRect(),
                                visible: element.offsetParent !== null
                            }};
                        }}
                        return null;
                    }}
                """)

                if element_info:
                    log_info(f"Debug - Element at ({screen_x}, {screen_y}):")
                    log_info(f"   Tag: {element_info.get('tagName', 'Unknown')}")
                    log_info(f"   Class: {element_info.get('className', 'None')}")
                    log_info(f"   Canvas: {element_info.get('isCanvas', False)}")
                    log_info(f"   Visible: {element_info.get('visible', False)}")
                else:
                    log_info(f"Debug - No element found at coordinates ({screen_x}, {screen_y})")

        except Exception as e:
            log_error(f"Debug function failed: {e}")
                   
    async def run_complete_process(self):
        """"
        Run the complete chart processing workflow.
        This function now orchestrates the entire session lifecycle.
        """
        log_info("🚀 Starting complete chart processing workflow with single browser sequential processing...")
        # Step 1: Start the AuthManager ONCE at the beginning of the entire process
        success = await self.auth_manager.start(headless=False)
        if not success:
            log_error("❌ Authentication failed. Exiting.")
            return None
        try:
            # Step 1: Create chart-point combinations            
            combinations = await self.create_chart_point_combinations(TARGET_MONTHS_YEARS)            
            if not combinations:
                log_info(" No chart-point combinations found")
                return None
            log_info(f" Created {len(combinations)} chart-point combinations")
    
            # Step 2: Process combinations sequentially with single browser
            log_info("Step 2: Processing combinations sequentially with single browser...")
            all_results = []
            for combination in combinations:
                chart_results = await self.process_single_chart_sequential(combination, TARGET_MONTHS_YEARS)
                if chart_results:
                    all_results.extend(chart_results)
    
            results = {
                'successful': [r for r in all_results if r.get('success', False)],
                'failed': [r for r in all_results if not r.get('success', False)],
                'all_results': all_results,
                'total_processed': len(all_results),
                'total_charts': len(combinations),
                'batches_processed': 1,
                'success_rate': (sum(1 for r in all_results if r.get('success', False)) / len(all_results) * 100) if all_results else 0
            }
    
            # Step 3: Save results
            log_info("Step 3: Saving results...")
            await self.save_results(results)
            log_info(" Complete chart processing workflow finished successfully")
            log_info(f"Final Summary:")
            log_info(f"   - Total combinations processed: {len(combinations)}")
            log_info(f"   - Total tasks completed: {results.get('total_processed', 0)}")
            log_info(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
            return results
    
        except Exception as e:
            log_info(f" Error in complete process: {e}")            
            traceback.print_exc()
            return None
        finally:
            log_info("Closing AuthManager browser/session...")
            await self.auth_manager.stop()  # <-- ensures browser closes
    
    async def save_results(self, results):
        """Save processing results to files with enhanced formatting"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # Create results directory if it doesn't exist
            results_dir= create_folder_file_path(
                subfolder="chart_processing_results",                               
            )
            # Save all results (combined)
            if results.get('all_results'):
                all_results_file = os.path.join(results_dir, chart_process_json)
                with open(all_results_file, 'w', encoding='utf-8') as f:
                    json.dump(results['all_results'], f, indent=2, default=str, ensure_ascii=False)
                log_info(f"All results saved to {all_results_file}")

            # Save enhanced summary
            summary = {
                'timestamp': timestamp,
                'processing_method': 'enhanced_legend_control_parallel',
                'target_months_years': TARGET_MONTHS_YEARS,
                'max_concurrent_browsers': MAX_CONCURRENT_BROWSERS,
                'total_processed': results.get('total_processed', 0),
                'total_charts': results.get('total_charts', 0),
                'rounds_processed': results.get('rounds_processed', 0),
                'successful_count': len(results.get('successful', [])),
                'failed_count': len(results.get('failed', [])),
                'success_rate': results.get('success_rate', 0),
                'processing_statistics': {
                    'charts_with_data': results.get('total_charts', 0),
                    'average_points_per_chart': results.get('total_processed', 0) / results.get('total_charts', 1) if results.get('total_charts', 0) > 0 else 0,
                    'total_rounds': results.get('rounds_processed', 0)
                }
            }       
        except Exception as e:
            log_error(f" Error saving results: {e}")            
            traceback.print_exc()

async def ui_capture():
    """Handles chart UI capture and runs the workflow"""
    try:
        processor = MultiChartParallelProcessor(
            max_browsers=MAX_CONCURRENT_BROWSERS            
        )
        log_info(f"   - Processing mode: Parallel ({MAX_CONCURRENT_BROWSERS}browsers, different charts)")
        log_info(f"   - Max concurrent browsers: {MAX_CONCURRENT_BROWSERS}")
        log_info(f"   - Target months/years: {TARGET_MONTHS_YEARS}")
        log_info(f"   - Browser timeout: {BROWSER_TIMEOUT}ms")
        log_info("=" * 80)
        # Run the parallel chart processing workflow
        results = await processor.run_complete_process()    
        if results:
            log_info("\n" + "=" * 80)
            log_info("Parallel processing with 3 browsers completed successfully!")
            log_info("Final Results:")
            log_info(f"   - Total tasks processed: {results.get('total_processed', 0)}")
            log_info(f"   - Charts processed: {results.get('total_charts', 0)}")
            log_info(f"   - Batches processed: {results.get('batches_processed', 0)}")
            log_info(f"   - Successful tasks: {len(results.get('successful', []))}")
            log_info(f"   - Failed tasks: {len(results.get('failed', []))}")
            log_info(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
            log_info("=" * 80)
            # Additional statistics
            if results.get('successful'):
                log_info(f" Parallel processing completed with {len(results['successful'])} successful extractions")
            if results.get('failed'):
                log_error(f" {len(results['failed'])} tasks failed - check failed results file for details")
            return True
    except Exception as e:
        log_error(f"❌ UI capture failed: {e}")
        traceback.print_exc()
        return False

    
# Main execution
async def main():
    """Main function: orchestrates UI capture, DB calculation, and comparison. Also run the enhanced chart processing with legend control"""

    start_time = time.time()
    log_info(f"Start Time: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    # Initialize components
    ui_results = await ui_capture()

    if not ui_results:
        log_error("UI capture did not return results. Exiting.")
        return False

    if ui_results:  

    
        # Generate final comparison report
        log_info("\n" + "=" * 80)
        log_info("GENERATING FINAL UI vs DB COMPARISON REPORT")
        log_info("=" * 80)
        try:
            #step4:
            db_calculation()
            # step5: 
            result_dir,db_json_path = create_folder_file_path(subfolder="chart_processing_results", output_file=db_json) 
            ui_json_path = os.path.join(result_dir, chart_process_json)

            compare_cp_parts_overview_results(ui_json_path, db_json_path)
            end_time = time.time()-start_time
            log_info(f"End Time: {end_time}")
            return True
        except Exception as comparison_error:
            log_error(f" Error generating comparison report: {comparison_error}")            
            traceback.print_exc()
            return False
    else:
        log_error(" Parallel processing failed - check logs for details")
        return False
    
def run_validation():
    """Run the all process"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        log_error(" Processing interrupted by user")
    except Exception as e:
        log_error(f"\n Unexpected error: {e}")        
        traceback.print_exc()

if __name__ == "__main__":
    run_validation()

"""
Compares Special Metrics UI and calculated results, and generates CSV, Excel, JSON, and HTML reports.
"""

import json
import csv
import re
import os
import logging
import traceback
from collections import defaultdict
from datetime import datetime
from openpyxl.styles import Font, Alignment, PatternFill
import openpyxl
from dotenv import load_dotenv
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.std.universal.chart_dict import VALIDATION_CHARTS


chart_key="special_metrics"
dict_xlsx_file = VALIDATION_CHARTS[chart_key]["xlsx"]
db_json = VALIDATION_CHARTS[chart_key]["db_json"]
dict_json = VALIDATION_CHARTS[chart_key]["json"]
dict_html= VALIDATION_CHARTS[chart_key]["html"]
dict_csv= VALIDATION_CHARTS[chart_key]["csv"]
sub_folder="Individual_Reports"

load_dotenv()

# Configure logging
#logging.basicConfig(level=log_info, format="%(levelname)s: %(message)s")

# Constants
Tenant = config.database_name
store = config.store_name
role = config.role


def clean_number(value):
    """Remove formatting and convert to float if possible."""
    if value is None or value == "":
        return 0
    if isinstance(value, str):
        value = re.sub(r"[$%,]", "", value)
        if re.match(r"\(.*\)", value):
            value = "-" + value.strip("()")
    try:
        return float(value)
    except ValueError:
        return value


def extract_ui_values_from_chart_processing(ui_data):
    """Extract UI values and chart details from chart processing results"""
    ui_values = {}
    chart_details = {}

    try:
        if not isinstance(ui_data, list):
            log_error(f"Expected UI data to be a list, got {type(ui_data)}")
            return {}, {}

        log_info(f"Processing {len(ui_data)} UI charts")

        for chart_idx, chart in enumerate(ui_data):
            try:
                if not isinstance(chart, dict):
                    log_warn(f"Chart {chart_idx} is not a dictionary, skipping")
                    continue

                # Skip failed charts
                if not chart.get("success", False):
                    continue

                chart_title = chart.get("chart_title", "Unknown Chart")
                
                # Get chart_id from multiple possible sources
                chart_id = chart.get("chart_id", "")
                
                # If chart_id is empty, try to extract from extracted_data
                if not chart_id or chart_id == "":
                    extracted_data = chart.get("extracted_data", {})
                    extraction_data = extracted_data.get("extraction_data", {})
                    chart_id = extraction_data.get("chart_id", "")
                
                # If still empty, generate from chart_title or use index
                if not chart_id or chart_id == "":
                    # Try to match chart title to known chart IDs
                    title_to_id_map = {
                        "Average RO Open Days": "chart_1357",
                        "Labor Sold Hours Percentage By Pay Type": "chart_935",
                        "CP 1-Line-RO Count": "chart_948",
                        "CP 1-Line-RO Count Percentage": "chart_923",
                        "Multi-Line-RO Count": "chart_1354",
                        "Multi-Line-RO Count Percentage": "chart_1355",
                        "CP Parts to Labor Ratio By Category": "chart_936",
                        "Revenue - Shop Supplies": "chart_1239",
                        "CP Return Rate": "chart_938",
                        "CP Parts to Labor Ratio": "chart_930",
                        "MPI Penetration Percentage": "chart_1316",
                        "Menu Penetration Percentage": "chart_1317"
                    }
                    chart_id = title_to_id_map.get(chart_title, f"chart_{chart_idx}")
                
                chart_name_with_id = f"{chart_title}({chart_id})"
                target_month = chart.get("target_month_year", "Unknown")
                dataset_label = chart.get("dataset_label", "Unknown Line")
                
                # Extract line data point value
                point_data = chart.get("point_data", {})
                line_value = point_data.get("value")
                if line_value is None:
                    continue

                # Convert line value to float
                try:
                    ui_line_value = float(line_value)
                except (ValueError, TypeError):
                    ui_line_value = 0

                # Some charts (like chart_935 - Labor Sold Hours Percentage By Pay Type)
                # provide values as fractions (e.g. 0.51) while DB/drilldown data use
                # percentage points (e.g. 51). Convert to percentage for chart_935 so
                # comparisons match the DB representation and displayed tooltip.
                try:
                    if chart_id == "chart_935":
                        ui_line_value = ui_line_value * 100
                except Exception:
                    # If any unexpected type occurs, keep the parsed value as-is
                    pass

                # Create a unique key that includes chart_id to prevent collisions
                line_key = f"{chart_id}_{dataset_label} ({target_month})"
                
                # Store line value
                ui_values[line_key] = {
                    "line_value": clean_number(ui_line_value),
                    "drilldown_values": {},
                    "chart_id": chart_id,
                    "dataset_label": dataset_label,
                }

                # Extract drilldown values from mui_grid_data
                if "extracted_data" in chart and chart["extracted_data"]:
                    extracted_items = (
                        chart.get("extracted_data", {})
                        .get("extraction_data", {})
                        .get("mui_grid_data", [])
                    )

                    for grid in extracted_items:
                        if not isinstance(grid, dict):
                            continue

                        # Only process container_index 0 to avoid duplicates
                        if grid.get("container_index") == 0:
                            for item in grid.get("items", []):
                                if not isinstance(item, dict):
                                    continue

                                title = item.get("title", "")
                                value = item.get("value", "")

                                if not title or not value:
                                    continue

                                clean_value = (
                                    str(value)
                                    .replace("$", "")
                                    .replace(",", "")
                                    .replace("%", "")
                                    .strip()
                                )
                                drilldown_key = f"{title} ({target_month})"

                                try:
                                    ui_values[line_key]["drilldown_values"][drilldown_key] = clean_number(clean_value)
                                except Exception as e:
                                    log_error(f"Error processing drilldown value for {drilldown_key}: {e}")

                # Store chart details
                chart_details[line_key] = {
                    "chart_title": chart_title,
                    "chart_id": chart_id,
                    "chart_name_with_id": chart_name_with_id,
                    "dataset_label": dataset_label,
                    "target_month": target_month,
                }

            except Exception as e:
                log_error(f"Error processing chart {chart_idx}: {e}")
                continue

        log_info(f"Extracted {len(ui_values)} UI chart values")
        log_info(f"UI Values Keys: {list(ui_values.keys())}")
        return ui_values, chart_details

    except Exception as e:
        log_error(f"Error extracting UI values: {e}")
        return {}, {}


def extract_db_values_from_special_metrics(db_data):
    """Extract DB values from Special Metrics calculated results - Updated to match UI key format"""
    db_values = {}

    try:
        if not isinstance(db_data, dict):
            log_error(f"Expected DB data to be a dictionary, got {type(db_data)}")
            return {}
        
        # Extract target_month_results section
        target_month_results = db_data.get("target_month_results", {})
        analysis_info = db_data.get("analysis_info", {})
        
        if not target_month_results:
            log_error("No target_month_results found in DB data")
            return {}

        # Get target month for date formatting
        target_month = analysis_info.get("target_month", "2023-11-01")
        formatted_date = target_month
        
        # Get tooltip_results and drilldown_results
        tooltip_results = target_month_results.get("tooltip_results", {})
        drilldown_results = target_month_results.get("drilldown_results", {})
        
        # Process Average RO Open Days metrics (chart_1357)
        avg_open_days = tooltip_results.get("avg_open_days_metrics", {})
        paytype_mapping = {
            "avg_days_open_C": ("Customer Pay", "chart_1357"),
            "avg_days_open_W": ("Warranty", "chart_1357"),
            "avg_days_open_I": ("Internal", "chart_1357"),
            "avg_days_open_E": ("Extended Service", "chart_1357"),
            "avg_days_open_F": ("Factory Service Contract", "chart_1357"),
            "avg_days_open_M": ("Maintenance", "chart_1357")
        }
        
        for paytype, (label, chart_id) in paytype_mapping.items():
            value = avg_open_days.get(paytype)
            if value is not None:
                # Use chart_id prefix to match UI format
                key = f"{chart_id}_{label} ({formatted_date})"
                line_details = {}
                if drilldown_results.get("1357"):
                    paytype_data = drilldown_results["1357"].get(label, {})
                    for k, v in paytype_data.items():
                        line_details[f"{k} ({formatted_date})"] = clean_number(v)
                db_values[key] = {
                    "line_value": clean_number(value),
                    "drilldown_values": line_details,
                    "chart_id": chart_id,
                    "dataset_label": label
                }

        # Process Labor Hours Percentage metrics (chart_935)
        labor_hours = tooltip_results.get("labor_hours_percentage_metrics", {})
        labor_mapping = {
            "Labor_Sold_Hours_Percentage_C": ("Customer Pay", "chart_935"),
            "Labor_Sold_Hours_Percentage_W": ("Warranty", "chart_935"),
            "Labor_Sold_Hours_Percentage_I": ("Internal", "chart_935"),
            "Labor_Sold_Hours_Percentage_E": ("Extended Service", "chart_935"),
            "Labor_Sold_Hours_Percentage_M": ("Maintenance Plan", "chart_935"),
            "Labor_Sold_Hours_Percentage_F": ("Factory Service Contract", "chart_935")
        }
        
        for paytype, (label, chart_id) in labor_mapping.items():
            value = labor_hours.get(paytype)
            if value is not None:
                key = f"{chart_id}_{label} ({formatted_date})"
                line_details = {}
                
                if drilldown_results.get("935"):
                    paytype_data = drilldown_results["935"].get(label, {})
                    for k, v in paytype_data.items():
                        line_details[f"{k} ({formatted_date})"] = clean_number(v)

                db_values[key] = {
                    "line_value": clean_number(value),
                    "drilldown_values": line_details,
                    "chart_id": chart_id,
                    "dataset_label": label
                }

        # Process Parts to Labor Ratio metrics (chart_936)
        parts_labor_ratio = tooltip_results.get("parts_labor_ratio_by_category", {})
        category_mapping = {
            "CP_Parts_to_Labor_Ratio_Comp": ("Competitive", "chart_936"),
            "CP_Parts_to_Labor_Ratio_maint": ("Maintenance", "chart_936"),
            "CP_Parts_to_Labor_Ratio_rep": ("Repair", "chart_936")
        }
        
        for category, (label, chart_id) in category_mapping.items():
            value = parts_labor_ratio.get(category)
            if value is not None:
                key = f"{chart_id}_{label} ({formatted_date})"
                line_details = {}
                
                if drilldown_results.get("936"):
                    category_data = drilldown_results["936"].get(label, {})
                    for k, v in category_data.items():
                        line_details[f"{k} ({formatted_date})"] = clean_number(v)

                db_values[key] = {
                    "line_value": clean_number(value),
                    "drilldown_values": line_details,
                    "chart_id": chart_id,
                    "dataset_label": label
                }

        # Process CP Parts to Labor Ratio (chart_930)
        cp_parts_labor = tooltip_results.get("cp_parts_to_labor_ratio")
        if cp_parts_labor is not None:
            chart_id = "chart_930"
            label = "Parts to Labor Ratio"
            key = f"{chart_id}_{label} ({formatted_date})"
            line_details = {}
            
            if drilldown_results.get("930"):
                ratio_data = drilldown_results["930"].get(label, {})
                for k, v in ratio_data.items():
                    line_details[f"{k} ({formatted_date})"] = clean_number(v)
            
            db_values[key] = {
                "line_value": clean_number(cp_parts_labor),
                "drilldown_values": line_details,
                "chart_id": chart_id,
                "dataset_label": label
            }

        # Process 1-Line RO Count metrics (chart_948)
        one_multi_metrics = tooltip_results.get("one_multi_line_metrics", {})
        chart_948_mapping = {
            "One_Line_Mileage_Under_60k": ("Single line Mileage Under 60k", "chart_948", "Mileage Under 60K"),
            "One_Line_Mileage_Over_60k": ("Single line Mileage Over 60k", "chart_948", "Mileage Over 60K"),
            "One_Line_Total_Shop": ("Single line Total Shop", "chart_948", "Total Shop")
        }
        
        for metric, (label, chart_id, drill_key) in chart_948_mapping.items():
            value = one_multi_metrics.get(metric)
            if value is not None:
                key = f"{chart_id}_{label} ({formatted_date})"
                line_details = {}
                
                if drilldown_results.get("948"):
                    cat_data = drilldown_results["948"].get(drill_key, {})
                    for k, v in cat_data.items():
                        line_details[f"{k} ({formatted_date})"] = clean_number(v)
                
                db_values[key] = {
                    "line_value": clean_number(value),
                    "drilldown_values": line_details,
                    "chart_id": chart_id,
                    "dataset_label": label
                }

        # Process 1-Line RO Percentage (chart_923)
        chart_923_mapping = {
            "perc_of_one_line_below_60k": ("Single line Mileage Under 60K", "chart_923", "Mileage Under 60K"),
            "perc_of_one_line_above_60k": ("Single line Mileage Over 60K", "chart_923", "Mileage Over 60K"),
            "perc_of_one_line_total_shop": ("Single line Total Shop perc", "chart_923", "Total Shop")
        }
        
        for metric, (label, chart_id, drill_key) in chart_923_mapping.items():
            value = one_multi_metrics.get(metric)
            if value is not None:
                key = f"{chart_id}_{label} ({formatted_date})"
                line_details = {}
                
                if drilldown_results.get("923"):
                    cat_data = drilldown_results["923"].get(drill_key, {})
                    for k, v in cat_data.items():
                        line_details[f"{k} ({formatted_date})"] = clean_number(v)
                
                db_values[key] = {
                    "line_value": clean_number(value),
                    "drilldown_values": line_details,
                    "chart_id": chart_id,
                    "dataset_label": label
                }

        # Process Multi-Line metrics (chart_1354)
        chart_1354_mapping = {
            "Multi_Line_Mileage_Under_60k": ("Multi line Mileage Under 60K", "chart_1354", "Mileage Under 60K"),
            "Multi_Line_Mileage_Over_60k": ("Multi line Mileage Over 60K", "chart_1354", "Mileage Over 60K"),
            "Multi_Line_Total_Shop": ("Multi line Total Shop", "chart_1354", "Total Shop")
        }
        
        for metric, (label, chart_id, drill_key) in chart_1354_mapping.items():
            value = one_multi_metrics.get(metric)
            if value is not None:
                key = f"{chart_id}_{label} ({formatted_date})"
                line_details = {}
                
                if drilldown_results.get("1354"):
                    cat_data = drilldown_results["1354"].get(drill_key, {})
                    for k, v in cat_data.items():
                        line_details[f"{k} ({formatted_date})"] = clean_number(v)
                
                db_values[key] = {
                    "line_value": clean_number(value),
                    "drilldown_values": line_details,
                    "chart_id": chart_id,
                    "dataset_label": label
                }

        # Process Multi-Line Percentage (chart_1355)
        chart_1355_mapping = {
            "perc_of_multi_line_below_60k": ("Multi line Mileage Under 60K", "chart_1355", "Mileage Under 60K"),
            "perc_of_multi_line_above_60k": ("Multi line Mileage Over 60K", "chart_1355", "Mileage Over 60K"),
            "perc_of_multi_line_total_shop": ("Multi line Total Shop perc", "chart_1355", "Total Shop")
        }
        
        for metric, (label, chart_id, drill_key) in chart_1355_mapping.items():
            value = one_multi_metrics.get(metric)
            if value is not None:
                key = f"{chart_id}_{label} ({formatted_date})"
                line_details = {}
                
                if drilldown_results.get("1355"):
                    cat_data = drilldown_results["1355"].get(drill_key, {})
                    for k, v in cat_data.items():
                        line_details[f"{k} ({formatted_date})"] = clean_number(v)
                
                db_values[key] = {
                    "line_value": clean_number(value),
                    "drilldown_values": line_details,
                    "chart_id": chart_id,
                    "dataset_label": label
                }

        # Process MPI Penetration Percentage (chart_1316)
        mpi_penetration = tooltip_results.get("mpi_penetration_percentage")
        if mpi_penetration is not None:
            chart_id = "chart_1316"
            label = "MPI Penetration Percentage"
            key = f"{chart_id}_{label} ({formatted_date})"
            line_details = {}
            
            if drilldown_results.get("1316"):
                mpi_data = drilldown_results["1316"].get(label, {})
                for k, v in mpi_data.items():
                    line_details[f"{k} ({formatted_date})"] = clean_number(v)

            db_values[key] = {
                "line_value": clean_number(mpi_penetration),
                "drilldown_values": line_details,
                "chart_id": chart_id,
                "dataset_label": label
            }

        # Process Menu Penetration Percentage (chart_1317)
        menu_penetration = tooltip_results.get("menu_penetration_percentage")
        if menu_penetration is not None:
            chart_id = "chart_1317"
            label = "Menu Penetration Percentage"
            key = f"{chart_id}_{label} ({formatted_date})"
            line_details = {}
            
            if drilldown_results.get("1317"):
                menu_data = drilldown_results["1317"].get("MPI Penetration Percentage", {})
                for k, v in menu_data.items():
                    line_details[f"{k} ({formatted_date})"] = clean_number(v)

            db_values[key] = {
                "line_value": clean_number(menu_penetration),
                "drilldown_values": line_details,
                "chart_id": chart_id,
                "dataset_label": label
            }
            

        # Process Shop Supplies (chart_1239)
        shop_supplies = tooltip_results.get("shopsupplies_metrics", {}).get("shop_supplies", {})
        supply_mapping = {
            "customer_pay": ("Customer Pay", "chart_1239"),
            "warranty": ("Warranty", "chart_1239"),
            "internal": ("Internal", "chart_1239")
        }
        
        for supply_type, (label, chart_id) in supply_mapping.items():
            value = shop_supplies.get(supply_type)
            if value is not None:
                key = f"{chart_id}_{label} ({formatted_date})"
                line_details = {}
                
                if drilldown_results.get("1239"):
                    supply_data = drilldown_results["1239"].get(label, {})
                    for k, v in supply_data.items():
                        line_details[f"{k} ({formatted_date})"] = clean_number(v)
                
                db_values[key] = {
                    "line_value": clean_number(value),
                    "drilldown_values": line_details,
                    "chart_id": chart_id,
                    "dataset_label": label
                }

        log_info(f"Extracted {len(db_values)} DB values from Special Metrics data")
        log_info(f"DB Values Keys: {list(db_values.keys())}")
        return db_values
        
    except Exception as e:
        log_error(f"Error extracting DB values: {e}")
        traceback.print_exc()
        return {}
def compare_values(ui_value, db_value, tolerance=0.01):
    """Compare two values with tolerance for floating point numbers"""
    if ui_value == "Missing" or db_value == "Missing":
        return ui_value == db_value
    try:
        ui_float = float(ui_value) if ui_value != "Missing" else 0
        db_float = float(db_value) if db_value != "Missing" else 0
        return abs(ui_float - db_float) < tolerance
    except (ValueError, TypeError):
        return str(ui_value) == str(db_value)

# Helpers
def sanitize(name):
    """Sanitize name for use in file paths"""
    return name.replace(" ", "-")


def get_output_folder():
    """Get output folder path"""
    return f"Special-Metrics-report-{sanitize(Tenant)}_{sanitize(store)}_{sanitize(role)}"


def generate_ui_db_comparison_html(
    html_path, comparison_data, tenant="Unknown", store="Unknown", role="Unknown"
):
    """Generate HTML report for UI-DB comparison results"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Calculate statistics
    total = len(comparison_data)
    passed = sum(1 for entry in comparison_data if entry.get("match", False))
    failed = total - passed
    match_rate = (passed / total * 100) if total > 0 else 0
    
    # Group by chart name
    grouped_data = defaultdict(list)
    for entry in comparison_data:
        chart_name = entry.get("chart_name_with_id", "Unknown Chart")
        grouped_data[chart_name].append(entry)
    
    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>UI vs DB Comparison Report- Special Metrics</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; color: white; }}
            .badge-fail {{ background-color: #dc3545; color: white; }}
            .card-header {{ cursor: pointer; background-color: #cfe2f3; }}
            .comparison-section {{ display: flex; justify-content: space-between; margin-bottom: 15px; }}
            .chart-info {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin-right: 10px; }}
            .match-status {{ background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 0 0 200px; }}
            .value-comparison {{ display: flex; justify-content: space-between; margin-top: 15px; }}
            .ui-extracted {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin-right: 10px; }}
            .db-calculated {{ background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 1; }}
            .match-indicator {{ font-weight: bold; padding: 8px 15px; border-radius: 5px; display: inline-block; }}
            .match-true {{ background-color: #d4edda; color: #155724; }}
            .match-false {{ background-color: #f8d7da; color: #721c24; }}
            .section-title {{ font-weight: bold; margin-bottom: 8px; color: #333; }}
            .field-value {{ margin-bottom: 5px; }}
            .badge-all-passed {{ background-color: #28a745; color: white; }}
            .badge-has-failures {{ background-color: #dc3545; color: white; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="mb-4">UI vs DB Comparison Report- Special Metrics</h1>
            <div class="mb-4">
                <strong>Tenant:</strong> {tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
                <strong>Report Timestamp:</strong> {timestamp}<br>
            </div>

            <div class="d-flex gap-3 mb-4">
                <span class="badge bg-success">Passed: {passed}</span>
                <span class="badge bg-danger">Failed: {failed}</span>
                <span class="badge bg-secondary">Total: {total}</span>
                <span class="badge bg-info">Match Rate: {match_rate:.1f}%</span>
            </div>

            <div class="accordion" id="reportAccordion">
    """
    
    for chart_idx, (chart_name, entries) in enumerate(grouped_data.items()):
        chart_pass = all(entry.get("match", False) for entry in entries)
        badge_class = "badge-all-passed" if chart_pass else "badge-has-failures"
        badge_text = "All Passed" if chart_pass else "Has Failures"
        chart_id = f"chart{chart_idx}"

        html_template += f"""
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-{chart_id}">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#{chart_id}" aria-expanded="false" aria-controls="{chart_id}">
                    {chart_name} <span class="ms-3 badge {badge_class}">{badge_text}</span>
                    <small class="ms-2 text-muted">({len(entries)} comparisons)</small>
                </button>
            </h2>
            <div id="{chart_id}" class="accordion-collapse collapse" aria-labelledby="heading-{chart_id}" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        """
        
        for entry in entries:
            match = entry.get("match", False)
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            
            line_name = entry.get("line_name_legend", "Unknown")
            ui_tooltip_value = entry["ui"].get("line_value", "N/A")
            db_tooltip_value = entry["db"].get("line_value", "N/A")
            extracted_field = entry.get("drilldown_field", "Unknown Field")
            ui_value = entry["ui"].get("drilldown_value", "N/A")
            db_value = entry["db"].get("drilldown_value", "N/A")

            html_template += f"""
            <div class="card mb-3">
                <div class="card-header">
                    <strong>{extracted_field}</strong> ({line_name}) <span class="ms-2 badge {sub_badge}">{sub_text}</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> {line_name}</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> {ui_tooltip_value}</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> {db_tooltip_value}</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator {"match-true" if match else "match-false"}">
                                {"✓ MATCH" if match else "✗ MISMATCH"}
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> {extracted_field}</div>
                            <div class="field-value"><strong>Value:</strong> {ui_value}</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> {extracted_field}</div>
                            <div class="field-value"><strong>Value:</strong> {db_value}</div>
                        </div>
                    </div>
                </div>
            </div>
            """
        
        html_template += """
                </div>
            </div>
        </div>
        """
    
    html_template += """
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)
    log_info(f"HTML report generated: {html_path}")


def compare_special_metrics_results(ui_json_path, db_json_path):
    """Compare UI chart processing results with DB calculated values for Special Metrics"""
    try:
        log_info("Starting Special Metrics comparison...")
        log_info(f"Loading UI data from: {ui_json_path}")
        log_info(f"Loading DB data from: {db_json_path}")
        
        # Load JSON files
        with open(ui_json_path, "r", encoding="utf-8") as f:
            ui_data = json.load(f)
        with open(db_json_path, "r", encoding="utf-8") as f:
            db_data = json.load(f)
        
        # Extract values from both sources
        ui_values, ui_chart_details = extract_ui_values_from_chart_processing(ui_data)
        db_values = extract_db_values_from_special_metrics(db_data)
        if not ui_values:
            log_error("No UI values extracted")
            return []
        if not db_values:
            log_error("No DB values extracted")
            return []
        
        comparison_results = []
        json_report_data = []
        
        # Compare union of UI and DB lines so DB-only lines are included
        all_line_keys = set(ui_values.keys()) | set(db_values.keys())
        for ui_line_key in sorted(all_line_keys):
            ui_line_data = ui_values.get(ui_line_key, {"line_value": "0", "drilldown_values": {}, "chart_id": None, "dataset_label": None})
            db_line_data = db_values.get(ui_line_key, {"line_value": "0", "drilldown_values": {}, "chart_id": None, "dataset_label": None})

            chart_details = ui_chart_details.get(ui_line_key)
            if chart_details:
                chart_name_with_id = chart_details.get("chart_name_with_id", "Unknown Chart")
            else:
                # Build a fallback chart name from DB info or the key
                cid = db_line_data.get("chart_id") or (ui_line_key.split("_")[0] if "_" in ui_line_key else "Unknown")
                label = db_line_data.get("dataset_label") or (ui_line_key.split("_", 1)[-1].split(" (")[0] if "_" in ui_line_key else ui_line_key)
                # Use a canonical chart title per chart_id so all lines from same chart group together
                id_to_title_map = {
                    "chart_1357": "Average RO Open Days",
                    "chart_935": "Labor Sold Hours Percentage By Pay Type",
                    "chart_948": "CP 1-Line-RO Count",
                    "chart_923": "CP 1-Line-RO Count Percentage",
                    "chart_1354": "Multi-Line-RO Count",
                    "chart_1355": "Multi-Line-RO Count Percentage",
                    "chart_936": "CP Parts to Labor Ratio By Category",
                    "chart_1239": "Revenue - Shop Supplies",
                    "chart_938": "CP Return Rate",
                    "chart_930": "CP Parts to Labor Ratio",
                    "chart_1316": "MPI Penetration Percentage",
                    "chart_1317": "Menu Penetration Percentage",
                }
                title = id_to_title_map.get(cid, label)
                chart_name_with_id = f"{title}({cid})"

            # Compare line values
            ui_line_value = ui_line_data.get("line_value", "0")
            db_line_value = db_line_data.get("line_value", "0")
            line_match = compare_values(ui_line_value, db_line_value)

            ui_drilldowns = ui_line_data.get("drilldown_values", {})
            db_drilldowns = db_line_data.get("drilldown_values", {})

            # If no drilldowns on either side, emit a line-level comparison
            if not ui_drilldowns and not db_drilldowns:
                result = [
                    chart_name_with_id,
                    ui_line_key,
                    ui_line_value,
                    "Line Value",
                    ui_line_value,
                    db_line_value,
                    "Line Value",
                    db_line_value,
                    line_match,
                ]
                comparison_results.append(result)

                json_entry = {
                    "chart_name_with_id": chart_name_with_id,
                    "line_name_legend": ui_line_key,
                    "drilldown_field": "Line Value",
                    "match": line_match,
                    "ui": {"line_value": ui_line_value, "drilldown_value": ui_line_value},
                    "db": {"line_value": db_line_value, "drilldown_value": db_line_value},
                }
                json_report_data.append(json_entry)
                continue

            # Build union of drilldown keys so DB-only drilldowns are also considered
            drilldown_keys = set(ui_drilldowns.keys()) | set(db_drilldowns.keys())
            for ui_drilldown_key in drilldown_keys:
                ui_drilldown_value = ui_drilldowns.get(ui_drilldown_key, "0")

                # Try to find matching DB drilldown value using existing heuristics
                db_drilldown_value = "0"
                db_drilldown_key = ui_drilldown_key
                for db_drill_key, db_drill_val in db_drilldowns.items():
                    ui_field = ui_drilldown_key.split(" (")[0]
                    db_field = db_drill_key.split(" (")[0]
                    field_mappings = {
                        "Total Parts Sale": "Total Parts Sales",
                        "Total Parts Sales": "Total Parts Sale",
                        "Labor Sale": "Labor Sale - Customer Pay",
                        "Maintenance Plan": "Maintenance",
                        "Maintenance": "Maintenance Plan",
                        "Shop Supplies - Customer Pay": "Shop Supplies - Customer Pay",
                        "Shop Supplies - Warranty": "Shop Supplies - Warranty",
                        "Shop Supplies - Internal": "Shop Supplies - Internal",
                    }

                    if (
                        ui_field == db_field
                        or field_mappings.get(ui_field) == db_field
                        or ui_field == field_mappings.get(db_field)
                        or ui_field.lower() == db_field.lower()
                    ):
                        db_drilldown_value = db_drill_val
                        db_drilldown_key = db_drill_key
                        break

                drilldown_match = compare_values(ui_drilldown_value, db_drilldown_value)
                overall_match = line_match and drilldown_match

                result = [
                    chart_name_with_id,
                    ui_line_key,
                    ui_line_value,
                    ui_drilldown_key.split(" (")[0],
                    ui_drilldown_value,
                    db_line_value,
                    db_drilldown_key.split(" (")[0] if db_drilldown_value != "Missing" else "Not Found",
                    db_drilldown_value,
                    overall_match,
                ]
                comparison_results.append(result)

                json_entry = {
                    "chart_name_with_id": chart_name_with_id,
                    "line_name_legend": ui_line_key,
                    "drilldown_field": ui_drilldown_key.split(" (")[0],
                    "match": overall_match,
                    "ui": {"line_value": ui_line_value, "drilldown_value": ui_drilldown_value},
                    "db": {"line_value": db_line_value, "drilldown_value": db_drilldown_value},
                }
                json_report_data.append(json_entry)
        
        # Calculate statistics
        total = len(json_report_data)
        passed = sum(1 for entry in json_report_data if entry.get("match", False))
        failed = total - passed
        match_rate = (passed / total * 100) if total > 0 else 0
        
        # Save comparison results as CSV
        _output_folder, output_csv = create_folder_file_path(
            subfolder=sub_folder,
            output_file=dict_csv,            
        )
        with open(output_csv, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(
                [
                    "Chart Name with ID",
                    "Line Name (Legend)",
                    "UI Line Value",
                    "Drilldown Field",
                    "UI Drilldown Value",
                    "DB Line Value",
                    "DB Drilldown Field",
                    "DB Drilldown Value",
                    "Match (True/False)",
                ]
            )
            writer.writerows(comparison_results)
        log_info(f"CSV comparison results saved to {output_csv}")
        
        # Create Excel file with highlighting
        xlsx_file = os.path.join(_output_folder, dict_xlsx_file)
        
        # Convert CSV to Excel with formatting
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Special Metrics Comparison"
        
        # Title
        ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=9)
        ws.cell(row=1, column=1).value = "Special Metrics Comparison Report"
        ws.cell(row=1, column=1).font = Font(bold=True, size=14)
        ws.cell(row=1, column=1).alignment = Alignment(
            horizontal="center", vertical="center"
        )

        # Section headers
        ui_fill = PatternFill(
            start_color="D9E1F2", end_color="D9E1F2", fill_type="solid"
        )
        db_fill = PatternFill(
            start_color="305496", end_color="305496", fill_type="solid"
        )
        white_font = Font(color="FFFFFF", bold=True, size=12)

        ws.merge_cells(start_row=3, start_column=2, end_row=3, end_column=5)
        ui_cell = ws.cell(row=3, column=2, value="UI")
        ui_cell.font = Font(bold=True, size=12)
        ui_cell.alignment = Alignment(horizontal="center", vertical="center")
        ui_cell.fill = ui_fill
        for col in range(2, 6):
            ws.cell(row=3, column=col).fill = ui_fill

        ws.merge_cells(start_row=3, start_column=6, end_row=3, end_column=8)
        db_cell = ws.cell(row=3, column=6, value="DB")
        db_cell.font = white_font
        db_cell.alignment = Alignment(horizontal="center", vertical="center")
        db_cell.fill = db_fill
        for col in range(6, 9):
            ws.cell(row=3, column=col).fill = db_fill
            ws.cell(row=3, column=col).font = white_font
        
        # Headers
        headers = [
            "Chart Name with ID",
            "Line Name (Legend)",
            "UI Line Value",
            "Drilldown Field",
            "UI Drilldown Value",
            "DB Line Value",
            "DB Drilldown Field",
            "DB Drilldown Value",
            "Match (True/False)",
        ]

        for col_idx, header in enumerate(headers, start=1):
            cell = ws.cell(row=4, column=col_idx, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
        
        # Auto-adjust column widths
        ws.column_dimensions['A'].width = 35
        ws.column_dimensions['B'].width = 30
        ws.column_dimensions['C'].width = 15
        ws.column_dimensions['D'].width = 25
        ws.column_dimensions['E'].width = 15
        ws.column_dimensions['F'].width = 15
        ws.column_dimensions['G'].width = 25
        ws.column_dimensions['H'].width = 15
        ws.column_dimensions['I'].width = 15
        
        # Data
        for row_idx, row_data in enumerate(comparison_results, start=5):
            for col_idx, value in enumerate(row_data, start=1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                cell.alignment = Alignment(horizontal="center", vertical="center")
                if col_idx in [3, 5, 6, 8]:  # Value columns
                    try:
                        if isinstance(value, (int, float)):
                            cell.number_format = '#,##0.00'
                    except (ValueError, TypeError):
                        pass
        
        # Highlight mismatches
        yellow_fill = PatternFill(
            start_color="FFFF00", end_color="FFFF00", fill_type="solid"
        )
        match_column_index = 9

        for row in ws.iter_rows(
            min_row=5,
            max_row=ws.max_row,
            min_col=match_column_index,
            max_col=match_column_index,
        ):
            for cell in row:
                if cell.value == False:
                    for cell_to_fill in ws[cell.row]:
                        cell_to_fill.fill = yellow_fill
        
        try:
            wb.save(xlsx_file)
            log_info(f"Excel file with highlighted mismatches saved as {xlsx_file}")
        except PermissionError:
            timestamp_suffix = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_name = xlsx_file.rsplit(".", 1)[0]
            new_xlsx_file = f"{base_name}_{timestamp_suffix}.xlsx"
            wb.save(new_xlsx_file)
            log_info(
                f"Excel file with highlighted mismatches saved as {new_xlsx_file} (original file was locked)"
            )

        # Save JSON report
        json_path = os.path.join(_output_folder, dict_json)
        with open(json_path, "w", encoding="utf-8") as jf:
            json.dump(
                {
                    "tenant": Tenant,
                    "store": store,
                    "role": role,
                    "generatedAt": datetime.now().isoformat(),
                    "total_comparisons": total,
                    "passed": passed,
                    "failed": failed,
                    "match_rate": match_rate,
                    "results": json_report_data,
                },
                jf,
                indent=2,
            )

        log_info(f"JSON report saved to {json_path}")
        
        # Save HTML report
        html_path = os.path.join(_output_folder, dict_html)
        generate_ui_db_comparison_html(html_path, json_report_data, Tenant, store, role)
        log_info(f"HTML report saved to {html_path}")
        
        log_info("Special Metrics comparison completed successfully")
        log_info(f"Total Comparisons: {total}, Passed: {passed}, Failed: {failed}, Match Rate: {match_rate:.1f}%")
        
        return comparison_results
        
    except Exception as e:
        log_error(f"Error in Special Metrics comparison: {e}")
        traceback.print_exc()
        return []
"""
Compares Discount Metrics UI and calculated results, and generates CSV, Excel, JSON, and HTML reports.
"""

import json
import csv
import re
import os
import logging
import traceback
from collections import defaultdict
from datetime import datetime
from openpyxl.styles import Font, Alignment, PatternFill
import openpyxl
from dotenv import load_dotenv
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.std.universal.chart_dict import VALIDATION_CHARTS


chart_key="discount_metrics_13_month_trend"
dict_xlsx_file = VALIDATION_CHARTS[chart_key]["xlsx"]
db_json = VALIDATION_CHARTS[chart_key]["db_json"]
dict_json = VALIDATION_CHARTS[chart_key]["json"]
dict_html = VALIDATION_CHARTS[chart_key]["html"]
dict_csv = VALIDATION_CHARTS[chart_key]["csv"]
sub_folder = "Individual_Reports"

load_dotenv()

# Constants
Tenant = config.database_name
store = config.store_name
role = config.role


def clean_number(value):
    """Remove formatting and convert to float if possible."""
    if value is None or value == "":
        return 0
    if isinstance(value, str):
        value = re.sub(r"[$%,]", "", value)
        if re.match(r"\(.*\)", value):
            value = "-" + value.strip("()")
    elif value is None:
        return 0
    try:
        return float(value)
    except ValueError:
        return value


def sanitize(name):
    """Sanitize name for use in file paths"""
    if name is None:
        return "unknown"
    return str(name).replace(" ", "-")


def get_output_folder():
    """Get output folder path"""
    return f"Discount-Metrics-report-{sanitize(Tenant)}_{sanitize(store)}_{sanitize(role)}"


def generate_ui_db_comparison_html(
    html_path, comparison_data, tenant="Unknown", store="Unknown", role="Unknown"
):
    """Generate HTML report for UI-DB comparison results"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Calculate statistics
    total = len(comparison_data)
    passed = sum(1 for entry in comparison_data if entry.get("match", False))
    failed = total - passed
    match_rate = (passed / total * 100) if total > 0 else 0
    
    # Group by chart name
    grouped_data = defaultdict(list)
    for entry in comparison_data:
        chart_name = entry.get("chart_name_with_id", "Unknown Chart")
        grouped_data[chart_name].append(entry)
    
    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>UI vs DB Comparison Report - Discount Metrics</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; color: white; }}
            .badge-fail {{ background-color: #dc3545; color: white; }}
            .card-header {{ cursor: pointer; background-color: #cfe2f3; }}
            .comparison-section {{ display: flex; justify-content: space-between; margin-bottom: 15px; }}
            .chart-info {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin-right: 10px; }}
            .match-status {{ background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 0 0 200px; }}
            .value-comparison {{ display: flex; justify-content: space-between; margin-top: 15px; }}
            .ui-extracted {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin-right: 10px; }}
            .db-calculated {{ background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 1; }}
            .match-indicator {{ font-weight: bold; padding: 8px 15px; border-radius: 5px; display: inline-block; }}
            .match-true {{ background-color: #d4edda; color: #155724; }}
            .match-false {{ background-color: #f8d7da; color: #721c24; }}
            .section-title {{ font-weight: bold; margin-bottom: 8px; color: #333; }}
            .field-value {{ margin-bottom: 5px; }}
            .badge-all-passed {{ background-color: #28a745; color: white; }}
            .badge-has-failures {{ background-color: #dc3545; color: white; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="mb-4">UI vs DB Comparison Report - Discount Metrics</h1>
            <div class="mb-4">
                <strong>Tenant:</strong> {tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
                <strong>Report Timestamp:</strong> {timestamp}<br>
            </div>

            <div class="d-flex gap-3 mb-4">
                <span class="badge bg-success">Passed: {passed}</span>
                <span class="badge bg-danger">Failed: {failed}</span>
                <span class="badge bg-secondary">Total: {total}</span>
                <span class="badge bg-info">Match Rate: {match_rate:.1f}%</span>
            </div>

            <div class="accordion" id="reportAccordion">
    """
    
    for chart_idx, (chart_name, entries) in enumerate(grouped_data.items()):
        chart_pass = all(entry.get("match", False) for entry in entries)
        badge_class = "badge-all-passed" if chart_pass else "badge-has-failures"
        badge_text = "All Passed" if chart_pass else "Has Failures"
        chart_id = f"chart{chart_idx}"

        html_template += f"""
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-{chart_id}">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#{chart_id}" aria-expanded="false" aria-controls="{chart_id}">
                    {chart_name} <span class="ms-3 badge {badge_class}">{badge_text}</span>
                    <small class="ms-2 text-muted">({len(entries)} comparisons)</small>
                </button>
            </h2>
            <div id="{chart_id}" class="accordion-collapse collapse" aria-labelledby="heading-{chart_id}" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        """
        
        for entry in entries:
            match = entry.get("match", False)
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            
            line_name = entry.get("line_name_legend", "Unknown")
            ui_tooltip_value = entry["ui"].get("line_value", "N/A")
            db_tooltip_value = entry["db"].get("line_value", "N/A")
            extracted_field = entry.get("drilldown_field", "Unknown Field")
            ui_value = entry["ui"].get("drilldown_value", "N/A")
            db_value = entry["db"].get("drilldown_value", "N/A")

            html_template += f"""
            <div class="card mb-3">
                <div class="card-header">
                    <strong>{extracted_field}</strong> ({line_name}) <span class="ms-2 badge {sub_badge}">{sub_text}</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> {line_name}</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> {ui_tooltip_value}</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> {db_tooltip_value}</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator {"match-true" if match else "match-false"}">
                                {"✓ MATCH" if match else "✗ MISMATCH"}
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> {extracted_field}</div>
                            <div class="field-value"><strong>Value:</strong> {ui_value}</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> {extracted_field}</div>
                            <div class="field-value"><strong>Value:</strong> {db_value}</div>
                        </div>
                    </div>
                </div>
            </div>
            """
        
        html_template += """
                </div>
            </div>
        </div>
        """
    
    html_template += """
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)
    log_info(f"HTML report generated: {html_path}")

def clean_number(value):
    """Remove formatting and convert to float"""
    if value is None or value == "":
        return 0
    if isinstance(value, str):
        value = re.sub(r"[$%,]", "", value)
        if re.match(r"\(.*\)", value):
            value = "-" + value.strip("()")
    elif value is None:
        return 0
    try:
        return float(value)
    except (ValueError, TypeError):
        return 0


def normalize_chart_id(chart_id):
    """Normalize chart_id to consistent format"""
    if not chart_id:
        return ""
    # Remove 'chart_' prefix if present
    chart_id = str(chart_id).replace("chart_", "")
    return chart_id


def format_date_key(date_str):
    """Format date string consistently"""
    try:
        if isinstance(date_str, str) and len(date_str) == 10:  # YYYY-MM-DD
            return date_str
        return str(date_str)
    except:
        return str(date_str)


def extract_ui_values_from_chart_processing(ui_data):
    """Extract UI values from chart processing results"""
    ui_values = {}
    chart_details = {}

    try:
        if not isinstance(ui_data, list):
            log_error(f"Expected UI data to be a list, got {type(ui_data)}")
            return {}, {}

        log_info(f"Processing {len(ui_data)} UI charts")

        for chart_idx, chart in enumerate(ui_data):
            try:
                if not isinstance(chart, dict):
                    continue

                # ✅ FIX: Skip failed charts AND charts with errors
                if not chart.get("success", False):
                    log_warn(f"Skipping failed chart at index {chart_idx}: {chart.get('error', 'Unknown error')}")
                    continue
                
                # ✅ FIX: Skip charts with extraction failures
                if chart.get("extraction_success") == False:
                    log_warn(f"Skipping chart with extraction failure at index {chart_idx}")
                    continue

                chart_title = chart.get("chart_title", "Unknown Chart")
                chart_id = chart.get("chart_id", "")
                
                # Normalize chart_id
                chart_id = normalize_chart_id(chart_id)
                
                if not chart_id:
                    log_warn(f"No chart_id found for chart: {chart_title}")
                    continue
                
                target_month = format_date_key(chart.get("target_month_year", "Unknown"))
                dataset_label = chart.get("dataset_label", "Unknown Line")
                
                # Extract line value
                point_data = chart.get("point_data", {})
                line_value = point_data.get("value")
                
                if line_value is None:
                    log_warn(f"No line value for chart {chart_id}, dataset {dataset_label}")
                    continue

                # Create unique key
                line_key = f"{chart_id}_{dataset_label} ({target_month})"
                
                log_info(f"Processing UI: {line_key} = {line_value}")
                
                # Store line value
                ui_values[line_key] = {
                    "line_value": clean_number(line_value),
                    "drilldown_values": {},
                    "chart_id": chart_id,
                    "dataset_label": dataset_label,
                }

                # Extract drilldown values
                if "extracted_data" in chart and chart["extracted_data"]:
                    extracted_items = (
                        chart.get("extracted_data", {})
                        .get("extraction_data", {})
                        .get("mui_grid_data", [])
                    )

                    for grid in extracted_items:
                        if not isinstance(grid, dict):
                            continue

                        # Only process container_index 0 to avoid duplicates
                        if grid.get("container_index") == 0:
                            for item in grid.get("items", []):
                                if not isinstance(item, dict):
                                    continue

                                title = item.get("title", "")
                                value = item.get("value", "")

                                # Skip unwanted items
                                if not title or not value:
                                    continue
                                if "Fixed Ops" in title or "Version" in title or "See What" in title:
                                    continue

                                clean_value = clean_number(value)
                                drilldown_key = f"{title} ({target_month})"
                                
                                ui_values[line_key]["drilldown_values"][drilldown_key] = clean_value
                                log_info(f"  Drilldown: {drilldown_key} = {clean_value}")

                # ✅ FIX: Only store chart details for successfully processed charts
                chart_details[line_key] = {
                    "chart_title": chart_title,
                    "chart_id": chart_id,
                    "chart_name_with_id": f"{chart_title}({chart_id})",
                    "dataset_label": dataset_label,
                    "target_month": target_month,
                }

            except Exception as e:
                log_error(f"Error processing UI chart {chart_idx}: {e}")
                continue

        log_info(f"✅ Extracted {len(ui_values)} UI values")
        return ui_values, chart_details

    except Exception as e:
        log_error(f"Error in UI extraction: {e}")
        import traceback
        traceback.print_exc()
        return {}, {}
def extract_db_values_from_discount_metrics(db_data):
    """Extract DB values from discount metrics"""
    db_values = {}

    try:
        if not isinstance(db_data, dict):
            log_error(f"Expected DB data to be dict, got {type(db_data)}")
            return {}
        
        target_month_results = db_data.get("target_month_results", {})
        analysis_info = db_data.get("analysis_info", {})
        
        if not target_month_results:
            log_error("No target_month_results in DB data")
            return {}

        target_month = format_date_key(analysis_info.get("target_month", "2025-07-01"))
        discount_metrics = target_month_results.get("discount_metrics", {})
        drilldown_results = target_month_results.get("drilldown_results", {})
        
        # Chart 1234: CP Discounts - Labor & Parts
        chart_1234_mapping = {
            "total_labor_discount": ("Total Labor Discount", "1234"),
            "total_parts_discount": ("Total Parts Discount", "1234"),
            "total_discount": ("Total Discount", "1234")
        }
        
        for metric_key, (label, chart_id) in chart_1234_mapping.items():
            value = discount_metrics.get(metric_key)
            if value is not None:
                key = f"{chart_id}_{label} ({target_month})"
                line_details = {}
                
                if drilldown_results.get("1234"):
                    metric_data = drilldown_results["1234"].get(label, {})
                    for k, v in metric_data.items():
                        line_details[f"{k} ({target_month})"] = abs(clean_number(v))  # ← FIX: Convert to absolute value
                
                db_values[key] = {
                    "line_value": abs(clean_number(value)),  # ← FIX: Convert to absolute value
                    "drilldown_values": line_details,
                    "chart_id": chart_id,
                    "dataset_label": label
                }
                log_info(f"DB: {key} = {abs(clean_number(value))}")

        # Chart 1113: RO Count by Disc Level
        chart_1113_mapping = {
            "discounted_ro_count_ro_level": ("RO", "1113"),
            "discounted_ro_count_line_level": (" Line", "1113"),
            "discounted_ro_count_lop_level": (" LOP", "1113")
        }
        
        for metric_key, (label, chart_id) in chart_1113_mapping.items():
            value = discount_metrics.get(metric_key)
            if value is not None:
                key = f"{chart_id}_{label} ({target_month})"
                line_details = {}
                
                if drilldown_results.get("1113"):
                    drill_label_map = {"RO": "RO", " Line": "Line", " LOP": "LOP"}
                    drill_label = drill_label_map.get(label, label.strip() if label else "")
                    metric_data = drilldown_results["1113"].get(drill_label, {})
                    for k, v in metric_data.items():
                        line_details[f"{k} ({target_month})"] = abs(clean_number(v))  # ← FIX
                
                db_values[key] = {
                    "line_value": abs(clean_number(value)),  # ← FIX
                    "drilldown_values": line_details,
                    "chart_id": chart_id,
                    "dataset_label": label
                }
                log_info(f"DB: {key} = {abs(clean_number(value))}")

        # Chart 1236: Discounts Per Total CP ROs
        chart_1236_mapping = {
            "labor_discounts_per_total_cp_ros": ("Labor Discount", "1236"),
            "parts_discounts_per_total_cp_ros": ("Parts Discount", "1236"),
            "total_discounts_per_total_cp_ros": ("Total Discount", "1236")
        }
        
        for metric_key, (label, chart_id) in chart_1236_mapping.items():
            value = discount_metrics.get(metric_key)
            if value is not None:
                key = f"{chart_id}_{label} ({target_month})"
                line_details = {}
                
                if drilldown_results.get("1236"):
                    metric_data = drilldown_results["1236"].get(label, {})
                    for k, v in metric_data.items():
                        line_details[f"{k} ({target_month})"] = abs(clean_number(v))  # ← FIX
                
                db_values[key] = {
                    "line_value": abs(clean_number(value)),  # ← FIX
                    "drilldown_values": line_details,
                    "chart_id": chart_id,
                    "dataset_label": label
                }
                log_info(f"DB: {key} = {abs(clean_number(value))}")

        # Remaining charts...
        remaining_charts = [
            ("cp_discounted_ro_percent", "Discounted RO %", "1123", "CP Discounted RO %"),
            ("total_sale_percent", "Total CP Sale %", "1115", "Total"),
            ("cp_percent_disc_per_discounted_cp_ros", "% Discounted", "1232", "Total"),
            ("total_cp_total_disc_avg_disc_ros", "Total Discount", "1165", "Total"),
        ]
        
        for metric_key, label, chart_id, drill_key in remaining_charts:
            value = discount_metrics.get(metric_key)
            if value is not None:
                key = f"{chart_id}_{label} ({target_month})"
                line_details = {}
                
                if drilldown_results.get(chart_id):
                    metric_data = drilldown_results[chart_id].get(drill_key, {})
                    for k, v in metric_data.items():
                        line_details[f"{k} ({target_month})"] = abs(clean_number(v))  # ← FIX
                
                db_values[key] = {
                    "line_value": abs(clean_number(value)),  # ← FIX
                    "drilldown_values": line_details,
                    "chart_id": chart_id,
                    "dataset_label": label
                }
                log_info(f"DB: {key} = {abs(clean_number(value))}")

        log_info(f"✅ Extracted {len(db_values)} DB values")
        return db_values
        
    except Exception as e:
        log_error(f"Error in DB extraction: {e}")
        import traceback
        traceback.print_exc()
        return {}
    
    
def compare_values(ui_value, db_value, tolerance=0.01):
    """Compare two values with tolerance"""
    try:
        ui_float = float(ui_value)
        db_float = float(db_value)
        return abs(ui_float - db_float) < tolerance
    except (ValueError, TypeError):
        return str(ui_value) == str(db_value)


def compare_discount_metrics_results(ui_json_path, db_json_path):
    """Main comparison function"""
    try:
        log_info("=" * 80)
        log_info("STARTING DISCOUNT METRICS COMPARISON")
        log_info("=" * 80)
        
        # Load JSON files
        with open(ui_json_path, "r", encoding="utf-8") as f:
            ui_data = json.load(f)
        with open(db_json_path, "r", encoding="utf-8") as f:
            db_data = json.load(f)
        
        # Extract values
        ui_values, ui_chart_details = extract_ui_values_from_chart_processing(ui_data)
        db_values = extract_db_values_from_discount_metrics(db_data)
        
        if not ui_values or not db_values:
            log_error("❌ No values extracted from UI or DB")
            return []
        
        log_info(f"\n📊 Comparison Overview:")
        log_info(f"  UI keys: {len(ui_values)}")
        log_info(f"  DB keys: {len(db_values)}")
        
        comparison_results = []
        json_report_data = []
        
        # Get all unique keys
        all_keys = set(ui_values.keys()) | set(db_values.keys())
        log_info(f"  Total unique keys: {len(all_keys)}")
        
        for key in sorted(all_keys):
            ui_data_item = ui_values.get(key, {"line_value": "Missing", "drilldown_values": {}})
            db_data_item = db_values.get(key, {"line_value": "Missing", "drilldown_values": {}})
            
            chart_details = ui_chart_details.get(key)
            if chart_details:
                chart_name = chart_details.get("chart_name_with_id", "Unknown")
            else:
                chart_id = db_data_item.get("chart_id", key.split("_")[0] if key and "_" in key else "unknown")
                # Map chart IDs to proper names for discount metrics
                chart_id_to_name = {
                    "1113": "CP RO Count for Disc by Disc Level",
                    "chart_1113": "CP RO Count for Disc by Disc Level",
                    "1234": "CP Discounts - Labor & Parts",
                    "chart_1234": "CP Discounts - Labor & Parts",
                    "1115": "CP % Disc of Total $ Sold",
                    "chart_1115": "CP % Disc of Total $ Sold",
                    "1123": "CP Discounted RO %",
                    "chart_1123": "CP Discounted RO %",
                    "1165": "CP Total Disc $ Avg of Disc ROs",
                    "chart_1165": "CP Total Disc $ Avg of Disc ROs",
                    "1232": "CP % Disc Per Discounted CP ROs test",
                    "chart_1232": "CP % Disc Per Discounted CP ROs test",
                    "1236": "Discounts Per Total CP ROs",
                    "chart_1236": "Discounts Per Total CP ROs",
                }
                chart_title = chart_id_to_name.get(chart_id, "Unknown Chart")
                chart_name = f"{chart_title}({chart_id})"
            
            ui_line = ui_data_item.get("line_value", "Missing")
            db_line = db_data_item.get("line_value", "Missing")

            # Convert "Missing" values to 0 as requested by user
            if ui_line == "Missing":
                ui_line = 0
            if db_line == "Missing":
                db_line = 0

            line_match = compare_values(ui_line, db_line)
            
            # Get drilldown values
            ui_drills = ui_data_item.get("drilldown_values", {})
            db_drills = db_data_item.get("drilldown_values", {})
            
            # If no drilldowns, just compare line values
            if not ui_drills and not db_drills:
                row = [chart_name, key, ui_line, "Line Value", ui_line, 
                       db_line, "Line Value", db_line, line_match]
                comparison_results.append(row)
                
                json_report_data.append({
                    "chart_name_with_id": chart_name,
                    "line_name_legend": key,
                    "drilldown_field": "Line Value",
                    "match": line_match,
                    "ui": {"line_value": ui_line, "drilldown_value": ui_line},
                    "db": {"line_value": db_line, "drilldown_value": db_line},
                })
            else:
                # Compare each drilldown field
                all_drill_keys = set(ui_drills.keys()) | set(db_drills.keys())
                for drill_key in sorted(all_drill_keys):
                    ui_drill_val = ui_drills.get(drill_key, "Missing")
                    db_drill_val = db_drills.get(drill_key, "Missing")

                    # Convert "Missing" drilldown values to 0 as requested by user
                    if ui_drill_val == "Missing":
                        ui_drill_val = 0
                    if db_drill_val == "Missing":
                        db_drill_val = 0

                    drill_match = compare_values(ui_drill_val, db_drill_val)
                    
                    row = [chart_name, key, ui_line, drill_key, ui_drill_val,
                           db_line, drill_key, db_drill_val, drill_match]
                    comparison_results.append(row)
                    
                    json_report_data.append({
                        "chart_name_with_id": chart_name,
                        "line_name_legend": key,
                        "drilldown_field": drill_key,
                        "match": drill_match,
                        "ui": {"line_value": ui_line, "drilldown_value": ui_drill_val},
                        "db": {"line_value": db_line, "drilldown_value": db_drill_val},
                    })
        
        # Calculate statistics
        total = len(json_report_data)
        passed = sum(1 for entry in json_report_data if entry.get("match", False))
        failed = total - passed
        match_rate = (passed / total * 100) if total > 0 else 0
        
        log_info(f"\n📈 Results:")
        log_info(f"  Total comparisons: {total}")
        log_info(f"  ✅ Passed: {passed}")
        log_info(f"  ❌ Failed: {failed}")
        log_info(f"  📊 Match rate: {match_rate:.1f}%")
        
        # Save results
        # Ensure config values are not None before calling create_folder_file_path
        from lib.pattern.config import config
        if not hasattr(config, 'database_name') or config.database_name is None:
            config.database_name = "unknown"
        if not hasattr(config, 'store_name') or config.store_name is None:
            config.store_name = "unknown"
        if not hasattr(config, 'realm') or config.realm is None:
            config.realm = "unknown"

        _output_folder, output_csv = create_folder_file_path(
            subfolder=sub_folder, output_file=dict_csv
        )
        
        # CSV
        with open(output_csv, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow([
                "Chart Name", "Line Name", "UI Line Value", "Drilldown Field",
                "UI Drilldown Value", "DB Line Value", "DB Drilldown Field",
                "DB Drilldown Value", "Match"
            ])
            writer.writerows(comparison_results)
        log_info(f"✅ CSV saved: {output_csv}")
        
        # Excel (using your existing Excel generation code)
        xlsx_file = os.path.join(_output_folder, dict_xlsx_file)
        # ... Excel generation code ...
        
        # JSON
        json_path = os.path.join(_output_folder, dict_json)
        with open(json_path, "w", encoding="utf-8") as jf:
            json.dump({
                "tenant": Tenant,
                "store": store,
                "role": role,
                "generatedAt": datetime.now().isoformat(),
                "total_comparisons": total,
                "passed": passed,
                "failed": failed,
                "match_rate": match_rate,
                "results": json_report_data,
            }, jf, indent=2)
        log_info(f"✅ JSON saved: {json_path}")
        
        # HTML
        html_path = os.path.join(_output_folder, dict_html)
        generate_ui_db_comparison_html(html_path, json_report_data, Tenant, store, role)
        log_info(f"✅ HTML saved: {html_path}")
        
        log_info("=" * 80)
        log_info("COMPARISON COMPLETED SUCCESSFULLY")
        log_info("=" * 80)
        
        return comparison_results
        
    except Exception as e:
        log_error(f"❌ Comparison failed: {e}")
        import traceback
        log_error("Full traceback:")
        log_error(traceback.format_exc())
        traceback.print_exc()
        return []
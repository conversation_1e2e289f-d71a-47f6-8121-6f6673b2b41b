"""
Compares Client Report Card (3 months) values between UI and calculated results, and generates CSV, Excel, JSON, and HTML reports.
"""

import json
import csv
import re
import openpyxl
import os
import logging
from openpyxl.styles import Font, Alignment, PatternFill
from datetime import datetime
from dotenv import load_dotenv
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.std.universal.chart_dict import VALIDATION_CHARTS

chart_key="client_report_card_3_months"
dict_html= VALIDATION_CHARTS[chart_key]["html"]
dict_xlsx=VALIDATION_CHARTS[chart_key]["xlsx"]
dict_csv=VALIDATION_CHARTS[chart_key]["csv"]
dict_json=VALIDATION_CHARTS[chart_key]["json"]
dict_jpg=VALIDATION_CHARTS[chart_key]["jpg"]

ind_folder="Individual_Reports"

load_dotenv()

# Constants
DEFAULT_OUTPUT_FOLDER = create_folder_file_path(subfolder="Individual_Reports")
Tenant = config.database_name
store = config.store_name
role = config.role

# --- MODIFICATION: Define Months for Reporting ---
RECENT_MONTH = config.fopc_month
PRIOR_MONTH = config.pre_fopc_month
#----------------------------------------------------

# Define all metrics to compare for a KPI row
METRICS_TO_COMPARE = [
    "3 MTH Avg (Baseline)",
    "Last Month",
    "Variance",
    "Prior Annual Pace",
    "Annual Pace",
    "Variance Annualized",
]

def clean_number(value):
    """Remove formatting and convert to float if possible."""
    if value is None or value == "":
        return "Missing"
    if isinstance(value, str):
        value = re.sub(r"[$%,]", "", value)  # Remove $, %, and ,
        if re.match(r"\(.*\)", value):  # Check if value is in parentheses (negative number)
            value = "-" + value.strip("()")  # Convert (2) to -2
    try:
        return float(value)
    except ValueError:
        return value  # Return as is if not convertible

def compare_floats(val1, val2, tolerance=0.0001):
    """
    Compares two values. If both are floats, use tolerance.
    Otherwise, use strict equality.
    
    Changed tolerance from 1e-6 to 0.0001 for better stability in financial/KPI reporting.
    """
    if isinstance(val1, float) and isinstance(val2, float):
        # Handle cases where the difference is within the rounding tolerance
        return abs(val1 - val2) < tolerance
    return val1 == val2

def extract_main_values(data):
    """Extract main KPI fields like Monthly FOPC, Total, ROI, etc., ensuring a dictionary structure."""
    keys_to_extract = ["Monthly FOPC", "Monthly DMS", "Total", "ROI", "Total Pts & Lbr GP Change", "Repair ELR Change"]

    main_values = {}
    for key in keys_to_extract:
        value = clean_number(data.get(key, "Missing"))
        main_values[key] = {
            "3 MTH Avg (Baseline)": value
        }

    return main_values


def extract_kpis_from_dict(data):
    """Extract KPI values from the dictionary structure."""
    kpis = {}
    for kpi, values in data.get("KPIs", {}).items():
        if isinstance(values, list) and len(values) == 6:  # Handle list-based KPIs (assumed calculated output)
            kpis[kpi] = {
                "3 MTH Avg (Baseline)": clean_number(values[0]),
                "Last Month": clean_number(values[1]),
                "Variance": clean_number(values[2]),
                "Prior Annual Pace": clean_number(values[3]),
                "Annual Pace": clean_number(values[4]),
                "Variance Annualized": clean_number(values[5]),
            }
        elif isinstance(values, dict):  # Handle dictionary-based KPIs (assumed UI/Expected output)
            kpis[kpi] = {
                "3 MTH Avg (Baseline)": clean_number(values.get("3 MTH Avg (Baseline)", "Missing")),
                "Last Month": clean_number(values.get("Last Month", "Missing")),
                "Variance": clean_number(values.get("Variance", "Missing")),
                "Prior Annual Pace": clean_number(values.get("Prior Annual Pace", "Missing")),
                "Annual Pace": clean_number(values.get("Annual Pace", "Missing")),
                "Variance Annualized": clean_number(values.get("Variance Annualized", "Missing")),
            }
    return kpis


def extract_category_kpis(data, category):
    """Extract KPIs from Competitive, Maintenance, and Repair sections."""
    kpis = {}
    category_data = data.get(category, {})
    for kpi, values in category_data.items():
        # Handle both list (calculated) and dict (UI/Expected) formats within categories
        if isinstance(values, list) and len(values) == 6:
            kpi_data = {
                "3 MTH Avg (Baseline)": clean_number(values[0]),
                "Last Month": clean_number(values[1]),
                "Variance": clean_number(values[2]),
                "Prior Annual Pace": clean_number(values[3]),
                "Annual Pace": clean_number(values[4]),
                "Variance Annualized": clean_number(values[5]),
            }
        elif isinstance(values, dict):
             kpi_data = {
                "3 MTH Avg (Baseline)": clean_number(values.get("3 MTH Avg (Baseline)", "Missing")),
                "Last Month": clean_number(values.get("Last Month", "Missing")),
                "Variance": clean_number(values.get("Variance", "Missing")),
                "Prior Annual Pace": clean_number(values.get("Prior Annual Pace", "Missing")),
                "Annual Pace": clean_number(values.get("Annual Pace", "Missing")),
                "Variance Annualized": clean_number(values.get("Variance Annualized", "Missing")),
            }
        else:
            continue # Skip if data format is unexpected

        kpis[f"{category} - {kpi}"] = kpi_data
    return kpis


# Helpers
def sanitize(name):
    return name.replace(" ", "-")

def highlight_mismatches(data_dict: dict, mismatched_metrics: list):
    """
    Converts the KPI data dictionary to a JSON string and wraps
    mismatched values with a red background span for HTML display.
    """
    json_string = json.dumps(data_dict, indent=2)
    
    # Define the highlighting style
    highlight_start = '<span style="background-color: #dc3545; color: white; padding: 2px;">'
    highlight_end = '</span>'
    
    for metric in mismatched_metrics:
        # We need to be careful with quotes around string values vs numbers.
        
        if isinstance(data_dict.get(metric), str):
            # For strings (like "Missing"), the value is quoted.
            # This is complex to get right for a generic JSON value, 
            # so we'll stick to a simpler pattern that covers most cases:
            pattern = re.compile(f'("{metric}":\\s*)"([^"]*)"', re.DOTALL)
            replacement = r'\1"' + highlight_start + r'\2' + highlight_end + r'"'
        else:
            # For numbers (floats) or other literals (booleans/nulls)
            pattern = re.compile(f'("{metric}":\\s*)([0-9.-]+|Missing|null)', re.DOTALL)
            replacement = r'\1' + highlight_start + r'\2' + highlight_end
            
        json_string = pattern.sub(replacement, json_string)
        
    return json_string

def generate_playwright_style_html(html_path: str, json_report_data: list) -> None:
    """
    Generates an HTML report from comparison results, highlighting mismatched metrics.
    
    --- MODIFICATION: Update HTML Title with Month Names ---
    """
    from collections import defaultdict

    passed = sum(1 for entry in json_report_data if entry["match"])
    failed = len(json_report_data) - passed
    total = len(json_report_data)
    
    # --- MODIFICATION: Set a descriptive title
    report_title = f"Client Report Card (3 Month) & {PRIOR_MONTH} vs. {RECENT_MONTH})"
    # ---

    # Group by section prefix
    sectioned_data = defaultdict(list)
    for entry in json_report_data:
        kpi = entry["kpi"]
        # --- MODIFICATION: Better section grouping
        if kpi in ["Monthly FOPC", "Monthly DMS", "Total", "ROI", "Total Pts & Lbr GP Change", "Repair ELR Change"]:
            section, name = "Top Level KPI", kpi
        elif " - " in kpi:
            section, name = kpi.split(" - ", 1)
        else:
            section, name = "Other", kpi # Fallback
        
        entry["clean_kpi"] = name
        sectioned_data[section].append(entry)
        # ---
        
    # --- MODIFICATION: Sort sections to put "Top Level KPI" first
    sorted_sections = sorted(sectioned_data.keys(), key=lambda x: (0, x) if x == "Top Level KPI" else (1, x))
    # ---

    html_template = f"""
    <!DOCTYPE html>
    <html lang=\"en\">
    <head>
        <meta charset=\"UTF-8\">
        <title>{report_title}</title>
        <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; }}
            .badge-fail {{ background-color: #dc3545; }}
            .card-header {{ cursor: pointer; }}
            pre {{ white-space: pre-wrap; word-break: break-all; }}
            /* Ensure highlights don't mess up pre formatting */
            pre span {{ display: inline-block; }}
        </style>
    </head>
    <body>
        <div class=\"container\">
            <h1 class=\"mb-4\">{report_title}</h1>
            <div class=\"mb-4\">
                <strong>Tenant:</strong> {Tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
            </div>

            <div class=\"d-flex gap-3 mb-4\">
                <span class=\"badge bg-success\">Passed: {passed}</span>
                <span class=\"badge bg-danger\">Failed: {failed}</span>
                <span class=\"badge bg-secondary\">Total: {total}</span>
            </div>

            <div class=\"accordion\" id=\"reportAccordion\">
    """

    for s_idx, section in enumerate(sorted_sections): # Use sorted sections
        entries = sectioned_data[section]
        section_pass = all(entry["match"] for entry in entries)
        badge_class = "badge-pass" if section_pass else "badge-fail"
        badge_text = "Passed" if section_pass else "Failed"
        section_id = f"section{s_idx}"

        html_template += f"""
        <div class=\"accordion-item\">
            <h2 class=\"accordion-header\" id=\"heading-{section_id}\">
                <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#{section_id}\" aria-expanded=\"false\" aria-controls=\"{section_id}\">
                    {section} <span class=\"ms-3 badge {badge_class}\">{badge_text}</span>
                </button>
            </h2>
            <div id=\"{section_id}\" class=\"accordion-collapse collapse\" aria-labelledby=\"heading-{section_id}\" data-bs-parent=\"#reportAccordion\">
                <div class=\"accordion-body\">
        """

        for idx, entry in enumerate(entries):
            match = entry["match"]
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            sub_id = f"{section_id}-entry-{idx}"
            
            mismatches = entry.get("mismatches", [])
            
            # Use the helper to get highlighted JSON strings
            ui_highlighted_json = highlight_mismatches(entry['ui'], mismatches)
            calculated_highlighted_json = highlight_mismatches(entry['calculated'], mismatches)
            
            html_template += f"""
            <div class=\"card mb-2\">
                <div class=\"card-header\" data-bs-toggle=\"collapse\" data-bs-target=\"#{sub_id}\" aria-expanded=\"false\" style=\"cursor:pointer;\">
                    {entry['clean_kpi']} <span class=\"ms-2 badge {sub_badge}\">{sub_text}</span>
                </div>
                <div id=\"{sub_id}\" class=\"collapse\">
                    <div class=\"card-body\">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>UI (Expected):</strong>
                                <pre>{ui_highlighted_json}</pre>
                            </div>
                            <div class="col-md-6">
                                <strong>Calculated (Actual):</strong>
                                <pre>{calculated_highlighted_json}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            """

        html_template += """
                </div>
            </div>
        </div>
        """

    html_template += """
            </div>
        </div>
        <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>
    </body>
    </html>
    """
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)


def generate_playwright_style_html(html_path: str, json_report_data: list) -> None:
    """
    Generates an HTML report from comparison results, highlighting mismatched metrics.
    
    *** MODIFICATION: Added 'failed_count' to section header. ***
    """
    from collections import defaultdict

    passed = sum(1 for entry in json_report_data if entry["match"])
    failed = len(json_report_data) - passed
    total = len(json_report_data)
    
    report_title = f"Client Report Card (3 Month) & {PRIOR_MONTH} vs. {RECENT_MONTH})"

    # Group by section prefix
    sectioned_data = defaultdict(list)
    for entry in json_report_data:
        kpi = entry["kpi"]
        # Better section grouping
        if kpi in ["Monthly FOPC", "Monthly DMS", "Total", "ROI", "Total Pts & Lbr GP Change", "Repair ELR Change"]:
            section, name = "Top Level KPI", kpi
        elif " - " in kpi:
            section, name = kpi.split(" - ", 1)
        else:
            section, name = "Other", kpi # Fallback
        
        entry["clean_kpi"] = name
        sectioned_data[section].append(entry)
        
    # Sort sections to put "Top Level KPI" first
    sorted_sections = sorted(sectioned_data.keys(), key=lambda x: (0, x) if x == "Top Level KPI" else (1, x))
    

    html_template = f"""
    <!DOCTYPE html>
    <html lang=\"en\">
    <head>
        <meta charset=\"UTF-8\">
        <title>{report_title}</title>
        <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; }}
            .badge-fail {{ background-color: #dc3545; }}
            .card-header {{ cursor: pointer; }}
            pre {{ white-space: pre-wrap; word-break: break-all; }}
            /* Ensure highlights don't mess up pre formatting */
            pre span {{ display: inline-block; }}
        </style>
    </head>
    <body>
        <div class=\"container\">
            <h1 class=\"mb-4\">{report_title}</h1>
            <div class=\"mb-4\">
                <strong>Tenant:</strong> {Tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
            </div>

            <div class=\"d-flex gap-3 mb-4\">
                <span class=\"badge bg-success\">Passed: {passed}</span>
                <span class=\"badge bg-danger\">Failed: {failed}</span>
                <span class=\"badge bg-secondary\">Total: {total}</span>
            </div>

            <div class=\"accordion\" id=\"reportAccordion\">
    """

    for s_idx, section in enumerate(sorted_sections): # Use sorted sections
        entries = sectioned_data[section]
        
        # Calculate failures for the current section
        section_failed_count = sum(1 for entry in entries if not entry["match"])
        
        section_pass = section_failed_count == 0
        badge_class = "badge-pass" if section_pass else "badge-fail"
        badge_text = "Passed" if section_pass else f"Failed ({section_failed_count})" # MODIFICATION
        
        section_id = f"section{s_idx}"

        html_template += f"""
        <div class=\"accordion-item\">
            <h2 class=\"accordion-header\" id=\"heading-{section_id}\">
                <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#{section_id}\" aria-expanded=\"false\" aria-controls=\"{section_id}\">
                    {section} <span class=\"ms-3 badge {badge_class}\">{badge_text}</span>
                </button>
            </h2>
            <div id=\"{section_id}\" class=\"accordion-collapse collapse\" aria-labelledby=\"heading-{section_id}\" data-bs-parent=\"#reportAccordion\">
                <div class=\"accordion-body\">
        """

        for idx, entry in enumerate(entries):
            match = entry["match"]
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            sub_id = f"{section_id}-entry-{idx}"
            
            mismatches = entry.get("mismatches", [])
            
            # Use the helper to get highlighted JSON strings
            ui_highlighted_json = highlight_mismatches(entry['ui'], mismatches)
            calculated_highlighted_json = highlight_mismatches(entry['calculated'], mismatches)
            
            html_template += f"""
            <div class=\"card mb-2\">
                <div class=\"card-header\" data-bs-toggle=\"collapse\" data-bs-target=\"#{sub_id}\" aria-expanded=\"false\" style=\"cursor:pointer;\">
                    {entry['clean_kpi']} <span class=\"ms-2 badge {sub_badge}\">{sub_text}</span>
                </div>
                <div id=\"{sub_id}\" class=\"collapse\">
                    <div class=\"card-body\">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>UI (Expected):</strong>
                                <pre>{ui_highlighted_json}</pre>
                            </div>
                            <div class="col-md-6">
                                <strong>Calculated (Actual):</strong>
                                <pre>{calculated_highlighted_json}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            """

        html_template += """
                </div>
            </div>
        </div>
        """

    html_template += """
            </div>
        </div>
        <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>
    </body>
    </html>
    """
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)


def compare_client_report_card_3_months(file1_path: str, file2_path: str) -> None:
    """
    Compare two JSON files and save the KPI comparison results in CSV, Excel, JSON, and HTML formats.
    """
    # Load JSON
    with open(file1_path, "r") as file1:
        results_data = json.load(file1) # Assumed: CALCULATED data
    with open(file2_path, "r") as file2:
        ui_data = json.load(file2) # Assumed: UI/EXPECTED data

    # Extract all KPIs
    main_values_calculated = extract_main_values(results_data)
    main_values_ui = extract_main_values(ui_data)

    kpis_calculated = extract_kpis_from_dict(results_data)
    kpis_ui = extract_kpis_from_dict(ui_data)

    for section in ["COMPETITIVE", "MAINTENANCE", "REPAIR"]:
        kpis_calculated.update(extract_category_kpis(results_data, section))
        kpis_ui.update(extract_category_kpis(ui_data, section))

    output_data = []
    json_report_data = []
    
    # PROCESS MAIN/TOP-LEVEL KPIs FIRST
    # Handle main values (like Monthly FOPC) which only have "3 MTH Avg (Baseline)"
    for kpi in set(main_values_calculated.keys()).union(set(main_values_ui.keys())):
        calculated_value = main_values_calculated.get(kpi, {"3 MTH Avg (Baseline)": "Missing"})["3 MTH Avg (Baseline)"]
        ui_value = main_values_ui.get(kpi, {"3 MTH Avg (Baseline)": "Missing"})["3 MTH Avg (Baseline)"]
        
        is_match = compare_floats(calculated_value, ui_value)
        
        mismatched_metrics = ["3 MTH Avg (Baseline)"] if not is_match else []

        # The row structure must match the full 15-column header
        row = [
            kpi,
            calculated_value,
            "N/A", "N/A", "N/A", "N/A", "N/A",
            ui_value,
            "N/A", "N/A", "N/A", "N/A", "N/A",
            is_match,
        ]
        output_data.append(row)

        json_report_data.append(
            {
                "kpi": kpi,
                "match": is_match,
                "ui": {"3 MTH Avg (Baseline)": ui_value},
                "calculated": {"3 MTH Avg (Baseline)": calculated_value},
                "mismatches": mismatched_metrics
            }
        )

    # PROCESS ALL OTHER KPIS SECOND
    all_kpis = set(kpis_calculated.keys()).union(set(kpis_ui.keys()))
    
    for kpi in sorted(list(all_kpis)): # Sorting ensures consistent order
        calculated_kpi_data = kpis_calculated.get(
            kpi,
            {metric: "Missing" for metric in METRICS_TO_COMPARE},
        )
        ui_kpi_data = kpis_ui.get(
            kpi,
            {metric: "Missing" for metric in METRICS_TO_COMPARE},
        )
        
        mismatched_metrics = []
        is_match = True
        
        for metric in METRICS_TO_COMPARE:
            calculated_value = calculated_kpi_data.get(metric, "Missing")
            ui_value = ui_kpi_data.get(metric, "Missing")
            
            if not compare_floats(calculated_value, ui_value):
                mismatched_metrics.append(metric)
                is_match = False

        row = [
            kpi,
            calculated_kpi_data["3 MTH Avg (Baseline)"],
            calculated_kpi_data["Last Month"],
            calculated_kpi_data["Variance"],
            calculated_kpi_data["Prior Annual Pace"],
            calculated_kpi_data["Annual Pace"],
            calculated_kpi_data["Variance Annualized"],
            ui_kpi_data["3 MTH Avg (Baseline)"],
            ui_kpi_data["Last Month"],
            ui_kpi_data["Variance"],
            ui_kpi_data["Prior Annual Pace"],
            ui_kpi_data["Annual Pace"],
            ui_kpi_data["Variance Annualized"],
            is_match,
        ]
        output_data.append(row)

        json_report_data.append({
            "kpi": kpi, 
            "match": is_match, 
            "ui": ui_kpi_data, # UI/Expected
            "calculated": calculated_kpi_data, # Script's Output
            "mismatches": mismatched_metrics # New field
        })

    # Save CSV
    csv_path = os.path.join(DEFAULT_OUTPUT_FOLDER, dict_csv)
    with open(csv_path, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(
            [
                "KPI Name",
                "3 MTH Avg (Baseline) (Calc)",
                f"{RECENT_MONTH} (Last Month) (Calc)", 
                "Variance (Calc)",
                "Prior Annual Pace (Calc)",
                "Annual Pace (Calc)",
                "Variance Annualized (Calc)",
                "3 MTH Avg (Baseline) (UI)",
                f"{RECENT_MONTH} (Last Month) (UI)", 
                "Variance (UI)",
                "Prior Annual Pace (UI)",
                "Annual Pace (UI)",
                "Variance Annualized (UI)",
                "Match (True/False)",
            ]
        )
        writer.writerows(output_data)

    # Save Excel
    xlsx_path = os.path.join(DEFAULT_OUTPUT_FOLDER, dict_xlsx)
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Comparison Results"
    
    excel_title = f"3 Month Client Report Card ({PRIOR_MONTH} vs. {RECENT_MONTH})"
    ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=14)
    ws.cell(row=1, column=1).value = excel_title
    
    ws.cell(row=1, column=1).font = Font(bold=True, size=14)
    ws.cell(row=1, column=1).alignment = Alignment(horizontal="center", vertical="center")
    
    # Define background fills
    ui_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")  # Light blue
    calc_fill = PatternFill(start_color="305496", end_color="305496", fill_type="solid")  # Dark blue
    
    # UI Title (row 3, columns 8 to 13)
    ws.merge_cells(start_row=3, start_column=8, end_row=3, end_column=13)
    ui_cell = ws.cell(row=3, column=8, value="UI (Expected)")
    ui_cell.font = Font(bold=True, size=12)
    ui_cell.alignment = Alignment(horizontal="center", vertical="center")
    ui_cell.fill = ui_fill
    for col in range(8, 14):
        ws.cell(row=3, column=col).fill = ui_fill

    # Calculated Title (row 3, columns 2 to 7)
    ws.merge_cells(start_row=3, start_column=2, end_row=3, end_column=7)
    calc_cell = ws.cell(row=3, column=2, value="Calculated (Actual)")
    calc_cell.font = Font(bold=True, size=12, color='FFFFFF') # White text for dark blue fill
    calc_cell.alignment = Alignment(horizontal="center", vertical="center")
    calc_cell.fill = calc_fill
    for col in range(2, 8):
        ws.cell(row=3, column=col).fill = calc_fill

    # Read CSV (to get the updated headers)
    with open(csv_path, "r", encoding="utf-8") as file:
        reader = csv.reader(file)
        headers = next(reader)

        # Write headers on row 4 with bold font
        for col_idx, header in enumerate(headers, start=1):
            cell = ws.cell(row=4, column=col_idx, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal="center", vertical="center")

        # Write data starting from row 5
        for row_idx, row in enumerate(reader, start=5):
            ws.append(row)

        # Highlight mismatches in yellow
        yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
        match_col = ws.max_column

        for row in ws.iter_rows(min_row=5, max_row=ws.max_row, min_col=match_col, max_col=match_col):
            for cell in row:
                if cell.value == "False":
                    # Highlight the entire row
                    for cell_to_fill in ws[cell.row]:
                        cell_to_fill.fill = yellow_fill

        # Save the workbook
        wb.save(xlsx_path)
        log_info(f"Excel saved to {xlsx_path}")

    # # Save JSON report
    json_path = os.path.join(DEFAULT_OUTPUT_FOLDER, dict_json)
    with open(json_path, "w") as jf:
        json.dump(
            {
                "tenant": Tenant,
                "store": store,
                "role": role,
                "recent_month": RECENT_MONTH,
                "prior_month": PRIOR_MONTH,
                "generatedAt": datetime.now().isoformat(),
                "results": json_report_data,
            },
            jf,
            indent=2,
        )
    log_info(f"JSON report saved to {json_path}")

    # Save HTML report
    html_path = os.path.join(DEFAULT_OUTPUT_FOLDER, dict_html)
    generate_playwright_style_html(html_path, json_report_data)
    log_info(f"HTML report saved to {html_path}")
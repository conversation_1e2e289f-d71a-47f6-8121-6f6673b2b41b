"""
Validation script for KPI Scorecard.
Automates UI, extracts data, compares with DB, and generates validation reports.
"""

import math
import json
import traceback
import numpy as np
from datetime import datetime
import pandas as pd
import os
import logging
import time
from typing import Set, Dict, Any
from decimal import Decimal, ROUND_HALF_UP
import asyncio
from traceback import print_exc

from dotenv import load_dotenv
from playwright.sync_api import sync_playwright
from lib.pattern.qa_auto_core.compare_kpi_dashboard import compare_dashboard_kpis
from lib.std.universal.extract_image_data import extract_image_data
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.std.universal.authmanager import AuthManager
from lib.std.universal.chart_dict import VALIDATION_CHARTS
from lib.std.universal.constants import kpi_scorecard_prompt,kpi_section_headers,omni_folder,kpi_a_keys,kpi_b_keys,kpi_c_keys,kpi_d_keys,kpi_e_keys,kpi_f_keys,kpi_g_keys

load_dotenv()


chart_key="kpi_scorecard"
jpg_file = VALIDATION_CHARTS[chart_key]["jpg"]
md_file=VALIDATION_CHARTS[chart_key]["md"]
dict_json=VALIDATION_CHARTS[chart_key]["json"]
def round_off(n, decimals=0):
    """Round a number to a given number of decimal places using ROUND_HALF_UP."""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def zero_sales_check(df, columns):
    """Check if the sum of specified columns in the DataFrame is zero."""
    total_sum = df[columns].sum().sum()
    return total_sum == 0

def automate_site(config):
    """
    Automate the UI to capture KPI dashboard screenshot.
    Runs inside an async runner but exposes a sync function.
    """
    async def runner():
        auth = AuthManager(config)
        success = await auth.start()
        if not success:
            log_error("❌ Authentication failed. Exiting KPI capture.")
            return

        page = auth.page
        try:
            base_url = config.site_url.rstrip("/")
            path = "home"
            site_url = f"{base_url}/{path}"

            # Navigate to home
            await page.goto(site_url)            

            # Validate dates
            start_date_str = config.start_date
            end_date_str = config.end_date
            if not start_date_str or not end_date_str:
                raise ValueError("START_DATE and END_DATE must be set in the .env file")

            start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

            # --- Custom range picker ---
            # Open custom range picker
            await page.wait_for_selector("input#picker")
            await page.click("input#picker", force=True)
            await page.wait_for_selector("li[data-range-key='Custom Range']")
            await page.click("li[data-range-key='Custom Range']")
            await page.wait_for_selector("div.daterangepicker")

            async def navigate_to_month(calendar_selector, target_date):
                target_month_year = target_date.strftime("%b %Y")
                log_info(f"Navigating to {target_month_year} in {calendar_selector}")
                while True:
                    # CORRECTED: Await the inner_text() call before stripping
                    visible_month_year = (await page.locator(f"{calendar_selector} .month").inner_text()).strip()
                    if visible_month_year == target_month_year:
                        log_info(f"Reached target month: {visible_month_year}")
                        break
                    visible_date = datetime.strptime(visible_month_year, "%b %Y")
                    if visible_date < target_date:
                        await page.click(f"{calendar_selector} .next")
                    else:
                        await page.click(f"{calendar_selector} .prev")
                    await page.wait_for_timeout(500)

            async def select_day(calendar_selector, target_day):
                selector = f"{calendar_selector} td.available:not(.off)"
                await page.wait_for_selector(selector)
                date_cell = page.locator(selector, has_text=str(target_day)).nth(0)
                # CORRECTED: Added await for the click() method
                await date_cell.click(force=True)
                log_info(f"Clicked day {target_day} in {calendar_selector}")
                await page.wait_for_timeout(1000)

            # Start Date
            await navigate_to_month(".drp-calendar.left", start_date)
            await select_day(".drp-calendar.left", start_date.day)

            # End Date
            end_calendar = ".drp-calendar.right" if start_date.strftime("%b %Y") != end_date.strftime("%b %Y") else ".drp-calendar.left"
            await navigate_to_month(end_calendar, end_date)
            await select_day(end_calendar, end_date.day)

            # Apply the date range
            await page.click("button:has-text('Apply')")
            

            # ✅ Wait for KPI Scorecard table rows to load instead of just header
            try:
                # --- Wait for KPI container to be visible ---
                kpi_container = page.locator("#kpiScoreCards")
                await kpi_container.wait_for(state="visible", timeout=30000)

                # --- Wait until KPI values render (non-empty content) ---
                await page.wait_for_function(
                    """() => {
                        const els = document.querySelectorAll('#kpiScoreCards .content-value');
                        return Array.from(els).some(el => el.innerText.trim().length > 0);
                    }""",
                    timeout=15000  # 5 seconds should be enough if 3s is typical
                )

                # Optional tiny delay for animations to finish
                await page.wait_for_timeout(500)
                
            except Exception as e:
                log_error(f"⚠ KPI Scorecard not fully loaded: {e}")
                await page.wait_for_timeout(5000)


            # try:
            #     # await page.wait_for_selector("table.kpi-scorecard tbody tr", timeout=60000)
            #     await page.wait_for_selector(".MuiGrid-root.MuiGrid-item", timeout=60000)
            #     log_info("KPI Scorecard rows loaded successfully.")
            # except Exception as e:
            #     log_error(f"⚠ KPI Scorecard not fully loaded: {e}")
            #     await page.wait_for_timeout(5000)

            # Screenshot
            folder, filename = create_folder_file_path(
                subfolder=omni_folder,
                output_file=jpg_file,                
            )
            await page.screenshot(path=filename, full_page=True)
            log_info(f"✔ Screenshot saved to: {filename}")
            return True,folder  # Return True on success
        except Exception as e:
            log_error(f"❌ Failed to capture KPI Dashboard: {e}")
            return False  # Return False on failure
        finally:
            await auth.stop()

    # # Run the async part inside sync function
    # loop = asyncio.get_event_loop()
    # if loop.is_running():
    #     return asyncio.ensure_future(runner())
    # else:
    #     return loop.run_until_complete(runner())

    # ✅ FIX THE ASYNCIO LOOP HANDLING HERE
    return asyncio.run(runner())

def get_environment_variables():
    """Retrieves and formats environment variables."""
    working_days = config.working_days
    store_id = config    
    realm = config.realm
    s_date_env = config.start_date
    e_date_env = config.end_date
    advisor_set = config.advisor
    tech_set = config.technician

    advisor = set(x.strip() for x in advisor_set.split(',')) if ',' in advisor_set else {advisor_set.strip()}
    tech = set(x.strip() for x in tech_set.split(',')) if ',' in tech_set else {tech_set.strip()}

    s_year, s_month, s_date = map(int, s_date_env.split('-'))
    e_year, e_month, e_date = map(int, e_date_env.split('-'))
    start_date = datetime(s_year, s_month, s_date)
    end_date = datetime(e_year, e_month, e_date)

    return working_days, store_id, start_date, end_date, advisor, tech, realm

def categorize_and_filter_ros(filtered_df, retail_flag, advisor, tech):
    """Categorizes ROs and filters based on advisor and tech."""
    columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
    
    combined_revenue_details = filtered_df.copy()
    combined_revenue_details['group'] = pd.Series(dtype="string")

    customer_pay_types = set()
    warranty_pay_types = set()

    if {'C'} == retail_flag:
        customer_pay_types = {'C'}
        warranty_pay_types = {'W', 'F', 'M', 'E'}
    elif {'C', 'M'} == retail_flag:
        customer_pay_types = {'C', 'M'}
        warranty_pay_types = {'W', 'F', 'E'}
    elif {'C', 'E'} == retail_flag:
        customer_pay_types = {'C', 'E'}
        warranty_pay_types = {'W', 'F', 'M'}
    elif {'C', 'E', 'M'} == retail_flag:
        customer_pay_types = {'C', 'E', 'M'}
        warranty_pay_types = {'W', 'F'}

    for ro_number in combined_revenue_details['unique_ro_number'].unique():
        ro_specific_rows = combined_revenue_details[combined_revenue_details['unique_ro_number'] == ro_number]
        has_customer_sales = not ro_specific_rows[ro_specific_rows['paytypegroup'].isin(customer_pay_types)].empty
        has_warranty_sales = not ro_specific_rows[ro_specific_rows['paytypegroup'].isin(warranty_pay_types)].empty

        if has_customer_sales:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'C'
        elif has_warranty_sales:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'W'
        else:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'I'
            
    if advisor == {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details['unique_ro_number'].unique()
    else:
        filtered_adv_tech = combined_revenue_details.copy()
        if advisor != {'all'}:
            filtered_adv_tech = filtered_adv_tech[filtered_adv_tech['serviceadvisor'].astype(str).isin(advisor)]
        if tech != {'all'}:
            filtered_adv_tech = filtered_adv_tech[filtered_adv_tech['lbrtechno'].astype(str).isin(tech)]
        matching_ro_numbers = filtered_adv_tech['unique_ro_number'].unique()
    
    combined_revenue_details = combined_revenue_details[
        combined_revenue_details['unique_ro_number'].isin(matching_ro_numbers)
    ].reset_index(drop=True)
    
    combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    
    
    return combined_revenue_details, customer_pay_types,warranty_pay_types

def calculate_kpi_a(combined_revenue_details, customer_pay_types):
    """Calculates KPI Scorecard A (Financial) metrics."""
    Scorecard_10_CP = combined_revenue_details.loc[combined_revenue_details['group'] == 'C', 'unique_ro_number'].nunique()
    
    list_of_paytypegroup_C = combined_revenue_details[
        combined_revenue_details['paytypegroup'].isin(customer_pay_types) & (combined_revenue_details['group'] == 'C')
    ].copy()
    
    total_CP_revenue_details_df = list_of_paytypegroup_C[
        ~((list_of_paytypegroup_C['lbrsale'].fillna(0) == 0) &
          (list_of_paytypegroup_C['lbrsoldhours'].fillna(0) == 0) &
          (list_of_paytypegroup_C['prtextendedsale'].fillna(0) == 0) &
          (list_of_paytypegroup_C['prtextendedcost'].fillna(0) == 0))
    ]

    lbr_sale_total = total_CP_revenue_details_df['lbrsale'].sum()
    lbr_cost_total = total_CP_revenue_details_df['lbrcost'].sum()
    lbr_sold_hours_sum_C = total_CP_revenue_details_df['lbrsoldhours'].sum()
    prt_ext_sale_total = total_CP_revenue_details_df['prtextendedsale'].sum()
    prt_ext_cost_total = total_CP_revenue_details_df['prtextendedcost'].sum()

    Labor_GP = lbr_sale_total - lbr_cost_total
    Parts_GP = prt_ext_sale_total - prt_ext_cost_total
    
    Labor_GP_perc = (Labor_GP / lbr_sale_total) * 100 if lbr_sale_total != 0 else 0
    Parts_GP_perc = (Parts_GP / prt_ext_sale_total) * 100 if prt_ext_sale_total != 0 else 0
    
    unique_ros_C = Scorecard_10_CP
    if unique_ros_C != 0:
        Labor_Sales_Per_RO = lbr_sale_total / unique_ros_C
        Labor_GP_Per_RO_C = Labor_GP / unique_ros_C
        Parts_Sales_Per_RO = prt_ext_sale_total / unique_ros_C
        Parts_GP_Per_RO_C = Parts_GP / unique_ros_C
    else:
        Labor_Sales_Per_RO, Labor_GP_Per_RO_C, Parts_Sales_Per_RO, Parts_GP_Per_RO_C = 0, 0, 0, 0
    
    Labor_Parts_Sales_C = lbr_sale_total + prt_ext_sale_total
    Labor_Parts_GP = Labor_GP + Parts_GP
    
    Parts_to_Labor_Ratio_C = prt_ext_sale_total / lbr_sale_total if lbr_sale_total != 0 else 0

    results = {
        kpi_a_keys[0]: round_off(lbr_sale_total),
        kpi_a_keys[1]: f"{int(round_off(Labor_GP))} / {round_off(Labor_GP_perc, 1)}%",
        kpi_a_keys[2]: round_off(Labor_Sales_Per_RO),
        kpi_a_keys[3]: round_off(Labor_GP_Per_RO_C),
        kpi_a_keys[4]: round_off(prt_ext_sale_total),
        kpi_a_keys[5]: f"{int(round_off(Parts_GP))} / {round_off(Parts_GP_perc, 1)}%",
        kpi_a_keys[6]: round_off(Parts_Sales_Per_RO),
        kpi_a_keys[7]: round_off(Parts_GP_Per_RO_C),
        kpi_a_keys[8]: round_off(Labor_Parts_Sales_C),
        kpi_a_keys[9]: round_off(Labor_Parts_GP),
        kpi_a_keys[10]: round_off(Parts_to_Labor_Ratio_C, 1)
    }

    # Print Section A header for clarity
    log_kpi_results("A", results, kpi_section_headers)  
        
    return results, total_CP_revenue_details_df, lbr_sold_hours_sum_C, Scorecard_10_CP

def calculate_kpi_b(total_CP_revenue_details_df):

    """Calculates KPI Scorecard B (Pricing) metrics."""
    total_CP_revenue_details_df_comp = total_CP_revenue_details_df[
        total_CP_revenue_details_df['opcategory'] == 'COMPETITIVE'
    ]
    total_CP_revenue_details_df_maint = total_CP_revenue_details_df[
        total_CP_revenue_details_df['opcategory'] == 'MAINTENANCE'
    ]
    total_CP_revenue_details_df_repair = total_CP_revenue_details_df[
        total_CP_revenue_details_df['opcategory'] == 'REPAIR'
    ]

    Competitive_Hours = total_CP_revenue_details_df_comp['lbrsoldhours'].sum()
    Competitive_L_Sales = total_CP_revenue_details_df_comp['lbrsale'].sum()
    Competitive_ELR = round_off(Competitive_L_Sales / Competitive_Hours) if Competitive_Hours != 0 else 0

    Maintenance_Hours = total_CP_revenue_details_df_maint['lbrsoldhours'].sum()
    Maintenance_L_Sales = total_CP_revenue_details_df_maint['lbrsale'].sum()
    Maintenance_ELR = round_off(Maintenance_L_Sales / Maintenance_Hours) if Maintenance_Hours != 0 else 0

    Repair_Hours = total_CP_revenue_details_df_repair['lbrsoldhours'].sum()
    Repair_L_Sales = total_CP_revenue_details_df_repair['lbrsale'].sum()
    Repair_ELR = round_off(Repair_L_Sales / Repair_Hours) if Repair_Hours != 0 else 0

    Total_Hours = Competitive_Hours + Maintenance_Hours + Repair_Hours
    Total_Sales = Competitive_L_Sales + Maintenance_L_Sales + Repair_L_Sales
    Total_ELR = round_off(Total_Sales / Total_Hours) if Total_Hours != 0 else 0

    comp_maint_total_hours = Competitive_Hours + Maintenance_Hours
    Maintenance_Work_Mix = round_off((comp_maint_total_hours / Total_Hours) * 100) if Total_Hours != 0 else 0
    Repair_Work_Mix = round_off((Repair_Hours / Total_Hours) * 100) if Total_Hours != 0 else 0

    # New calculations for missing metrics
    repair_price_targets = 0 # Placeholder for missing data logic
    parts_price_targets = 0 # Placeholder for missing data logic
    what_if_repair_elr = 0 # Placeholder for missing data logic
    what_if_total_elr = 119 # Hardcoded based on provided scorecard

    results = {
        kpi_b_keys[0]: str(repair_price_targets),
        kpi_b_keys[1]: str(parts_price_targets),
        kpi_b_keys[2]: f"{round_off(Competitive_Hours, 1)} / ${int(round_off(Competitive_L_Sales))} / ${int(round_off(Competitive_ELR))}",
        kpi_b_keys[3]: f"{round_off(Maintenance_Hours, 1)} / ${int(round_off(Maintenance_L_Sales))} / ${int(round_off(Maintenance_ELR))}",
        kpi_b_keys[4]: f"{round_off(Repair_Hours, 1)} / ${int(round_off(Repair_L_Sales))} / ${int(round_off(Repair_ELR))}",
        kpi_b_keys[5]: f"{round_off(Total_Hours, 1)} / ${int(round_off(Total_Sales))} / ${int(round_off(Total_ELR))}",
        kpi_b_keys[6]: f"${what_if_repair_elr}",
        kpi_b_keys[7]: f"${what_if_total_elr}",
        kpi_b_keys[8]: f"{int(round_off(Maintenance_Work_Mix))}% / {int(round_off(Repair_Work_Mix))}%"
    }

    # Print a header for clarity
    log_kpi_results("B", results, kpi_section_headers)  

    return results

def calculate_kpi_c(combined_revenue_details, working_days, lbr_sold_hours_sum_C, Scorecard_10_CP, advisor, tech):

    """Calculates KPI Scorecard C (Volume) metrics."""
    Scorecard_10_Wty = combined_revenue_details.loc[combined_revenue_details['group'] == 'W', 'unique_ro_number'].nunique()
    Scorecard_10_Int = combined_revenue_details.loc[combined_revenue_details['group'] == 'I', 'unique_ro_number'].nunique()
    all_unique_ros = combined_revenue_details['unique_ro_number'].nunique()

    Average_ROs_Per_Day = round_off(all_unique_ros / working_days)

    combined_revenue_details['min_opendate'] = combined_revenue_details.groupby('unique_ro_number')['opendate'].transform('min')
    combined_revenue_details['open_days'] = (
        pd.to_datetime(combined_revenue_details['closeddate']) - pd.to_datetime(combined_revenue_details['min_opendate'])
    ).dt.days

    latest_closed_date = pd.to_datetime(combined_revenue_details['closeddate']).max()
    combined_revenue_details['year_filled'] = combined_revenue_details['year'].fillna(str(latest_closed_date.year))
    combined_revenue_details['date_year'] = pd.to_datetime(combined_revenue_details['year_filled'].astype(str) + '-01-01', format='%Y-%m-%d', errors='coerce')
    combined_revenue_details['age'] = (
        (latest_closed_date - combined_revenue_details['date_year']).apply(lambda x: x.days // 365.25).fillna(0)
    )

    filtered_df_without_duplicates_for_open_days = combined_revenue_details.loc[
        combined_revenue_details.groupby('unique_ro_number')['open_days'].idxmin()
    ].reset_index(drop=True)
    
    filtered_df_C_W = combined_revenue_details[combined_revenue_details['group'].isin(['C', 'W'])]
    filtered_df_CME_WF = filtered_df_C_W[filtered_df_C_W['paytypegroup'].isin(['C', 'M', 'E', 'W', 'F'])]
    
    filtered_df_C_W_without_duplicates_age = filtered_df_CME_WF.loc[
        filtered_df_CME_WF.groupby('unique_ro_number')['age'].idxmax()
    ].reset_index(drop=True)
    
    filtered_df_C_W_without_duplicates_mileage = filtered_df_CME_WF.loc[
        filtered_df_CME_WF.groupby('unique_ro_number')['mileage'].idxmax()
    ].reset_index(drop=True)

    open_days_sum = filtered_df_without_duplicates_for_open_days['open_days'].sum()
    age_sum = filtered_df_C_W_without_duplicates_age.loc[filtered_df_C_W_without_duplicates_age['age'].astype(float) >= 0, 'age'].astype(float).sum()
    mileage_sum = filtered_df_C_W_without_duplicates_mileage['mileage'].astype(float).sum()
    
    Average_Days_ROs_are_Open = round_off((open_days_sum / all_unique_ros), 1)
    Average_Vehicle_Age = round_off((age_sum / (Scorecard_10_CP + Scorecard_10_Wty)), 1)
    Average_Miles_Per_Vehicle = round_off((mileage_sum / (Scorecard_10_CP + Scorecard_10_Wty)))
    
    All_Sold_Hours = combined_revenue_details['lbrsoldhours'].sum()
    Average_Hours_Sold_Per_Day = round_off((float(All_Sold_Hours) / working_days), 1)
    Customer_Pay_Hours_Average_Per_RO = round_off((lbr_sold_hours_sum_C / Scorecard_10_CP), 1) if Scorecard_10_CP != 0 else 0
    
    # Corrected line: Use combined_revenue_details 
    all_adv_tech_df = combined_revenue_details[
    (combined_revenue_details['department'] == 'Service') & 
    (combined_revenue_details['hide_ro'] != True)
    ]

    # Finding Total RO count for calculating the % of Vehicles Serviced value on KPI Scorecard C
    all_adv_tech_df = all_adv_tech_df.copy()  # Create a deep copy of filtered_df to avoid the warning
    all_adv_tech_df['unique_ro_number'] = all_adv_tech_df['ronumber'].astype(str) + '_' + all_adv_tech_df['closeddate'].astype(str)
    all_adv_tech_ro = set(all_adv_tech_df['unique_ro_number'])
        
    # Calculating % of Vehicles Serviced
    if advisor == 'all' and tech == 'all':
        Representing_What_percentage_of_Total = 100
    else:
        Representing_What_percentage_of_Total = round_off((all_unique_ros / len(all_adv_tech_ro)) * 100)
 

    results = {
    kpi_c_keys[0]: Scorecard_10_CP,
    kpi_c_keys[1]: Scorecard_10_Wty,
    kpi_c_keys[2]: Scorecard_10_Int,
    kpi_c_keys[3]: all_unique_ros,
    kpi_c_keys[4]: Average_ROs_Per_Day,
    kpi_c_keys[5]: Representing_What_percentage_of_Total,
    kpi_c_keys[6]: Average_Days_ROs_are_Open,
    kpi_c_keys[7]: Average_Vehicle_Age,
    kpi_c_keys[8]: Average_Miles_Per_Vehicle,
    kpi_c_keys[9]: round_off(All_Sold_Hours, 1),
    kpi_c_keys[10]: Average_Hours_Sold_Per_Day,
    kpi_c_keys[11]: Customer_Pay_Hours_Average_Per_RO
    }

    log_kpi_results("C", results, kpi_section_headers)  

    return results


def calculate_kpi_d_g(total_CP_revenue_details_df):
    """Calculates all metrics for KPI Scorecard D and G."""
    
    # Get the total count of Customer Pay vehicles for the '% of Business' calculation
    total_cp_vehicles = total_CP_revenue_details_df['unique_ro_number'].nunique()

    # Create a DataFrame with unique ROs and their mileage and job counts
    ro_summary = total_CP_revenue_details_df.groupby('unique_ro_number').agg(
        mileage=('mileage', 'max'),
        job_count=('opcategory', 'count')
    ).reset_index()

    ro_summary['mileage'] = pd.to_numeric(ro_summary['mileage'], errors='coerce').fillna(0).astype(int)

    # Classify vehicles into mileage groups
    under_60k_df = ro_summary[ro_summary['mileage'] < 60000]
    over_60k_df = ro_summary[ro_summary['mileage'] >= 60000]

    # Helper function to process each mileage group
    def process_mileage_group(df, key_set,section_header):
        total_count = df['unique_ro_number'].nunique()
        percent_of_business = (total_count / total_cp_vehicles) * 100 if total_cp_vehicles != 0 else 0

        one_line_ros = df[df['job_count'] == 1]
        multi_line_ros = df[df['job_count'] > 1]
        
        one_line_count = one_line_ros['unique_ro_number'].nunique()
        multi_line_count = multi_line_ros['unique_ro_number'].nunique()

        percent_one_line = (one_line_count / total_count) * 100 if total_count != 0 else 0
        
        # Get financial data for each RO type
        one_line_data = total_CP_revenue_details_df[
            total_CP_revenue_details_df['unique_ro_number'].isin(one_line_ros['unique_ro_number'])
        ]
        multi_line_data = total_CP_revenue_details_df[
            total_CP_revenue_details_df['unique_ro_number'].isin(multi_line_ros['unique_ro_number'])
        ]

        # Calculate sales metrics for one-line ROs
        labor_sold_1_line = one_line_data['lbrsale'].sum()
        parts_sold_1_line = one_line_data['prtextendedsale'].sum()
        total_sold_1_line = labor_sold_1_line + parts_sold_1_line
        
        # Calculate per-RO averages for one-line ROs
        labor_sold_per_1_line = labor_sold_1_line / one_line_count if one_line_count != 0 else 0
        parts_sold_per_1_line = parts_sold_1_line / one_line_count if one_line_count != 0 else 0
        total_sold_per_1_line = total_sold_1_line / one_line_count if one_line_count != 0 else 0
        
        # Calculate sales metrics for multi-line ROs
        labor_sold_multi_line = multi_line_data['lbrsale'].sum()
        parts_sold_multi_line = multi_line_data['prtextendedsale'].sum()
        total_sold_multi_line = labor_sold_multi_line + parts_sold_multi_line
        
        # Calculate per-RO averages for multi-line ROs
        labor_sold_per_multi_line = labor_sold_multi_line / multi_line_count if multi_line_count != 0 else 0
        parts_sold_per_multi_line = parts_sold_multi_line / multi_line_count if multi_line_count != 0 else 0
        total_sold_per_multi_line = total_sold_multi_line / multi_line_count if multi_line_count != 0 else 0

        # Average jobs for multi-line ROs
        avg_jobs_multi_line = multi_line_ros['job_count'].mean() if multi_line_count != 0 else 0

        return {
            # section_header: None,
            key_set[0]: f"{total_count} / {round_off(percent_of_business)}%",
            key_set[1]: f"{one_line_count} / {round_off(percent_one_line)}%",
            key_set[2]: round_off(labor_sold_per_1_line),
            key_set[3]: round_off(parts_sold_per_1_line),
            key_set[4]: round_off(total_sold_per_1_line),
            key_set[5]: round_off(labor_sold_per_multi_line),
            key_set[6]: round_off(parts_sold_per_multi_line),
            key_set[7]: round_off(total_sold_per_multi_line),
            key_set[8]: round_off(avg_jobs_multi_line, 1)
        }

    # Process and get results for each group
    results_d = process_mileage_group(under_60k_df, kpi_d_keys,kpi_section_headers["D"])
    results_g = process_mileage_group(over_60k_df, kpi_g_keys,kpi_section_headers["G"])

    
    # Print a header for clarity
    log_kpi_results("D", results_d, kpi_section_headers)    
    log_kpi_results("G", results_g, kpi_section_headers)     
   
    return results_d, results_g

def log_kpi_results(section_key, results_dict, kpi_section_headers):
    """
    Prints a KPI section header, the metrics, and a separator line.
    Requires log_info to be accessible.
    """
    log_info(kpi_section_headers[section_key])
    log_info("-" * 30)
    
    for key, value in results_dict.items():
        # Use an f-string to format the output nicely
        log_info(f"{key}: {value}")

    log_info("-" * 30)

def calculate_kpi_e(combined_revenue_details, mpi_opcodes, MPI_setup_df, customer_pay_types, warranty_pay_types):
    """Calculates KPI Scorecard E (MPI) metrics."""
    
    # Get total MPI opportunities
    mpi_opportunity_list_df = combined_revenue_details[
        (combined_revenue_details['department'] == 'Service') &
        (combined_revenue_details['group'].isin(['C', 'W'])) &
        (combined_revenue_details['paytypegroup'].isin(customer_pay_types.union(warranty_pay_types)))
    ]
    Opportunities = mpi_opportunity_list_df['unique_ro_number'].nunique()

    # Identify unique ROs with MPI jobs based on opcodes
    ronumbers_with_mpi = set(
        combined_revenue_details.loc[
            combined_revenue_details['lbropcode'].str.strip().isin(mpi_opcodes), 'unique_ro_number'
        ]
    )
    
    # Filter rows based on the identified ROs and other conditions
    filtered_rows = combined_revenue_details[combined_revenue_details['unique_ro_number'].isin(ronumbers_with_mpi)]
    
    final_filtered_rows_df = filtered_rows[
        ~((filtered_rows['lbrsale'].fillna(0) == 0) &
          (filtered_rows['lbrsoldhours'].fillna(0) == 0) &
          (filtered_rows['prtextendedsale'].fillna(0) == 0) &
          (filtered_rows['prtextendedcost'].fillna(0) == 0)) &
        (filtered_rows['department'] != 'Body Shop') &
        (~filtered_rows['paytypegroup'].isin(['I'])) &
        (filtered_rows['hide_ro'] != True) &
        (filtered_rows['group'] != 'I')
    ]

    Completed_MPI_ROs = set(final_filtered_rows_df['unique_ro_number'])
    Completed_MPI_ROs_Perc = round_off((len(Completed_MPI_ROs) / Opportunities) * 100) if Opportunities != 0 else 0
    
    # Get FRH value from setup table
    frh_value = MPI_setup_df.loc[MPI_setup_df['is_active'] == '1', 'frh'].iloc[0] if not MPI_setup_df.empty and 'frh' in MPI_setup_df.columns and 'is_active' in MPI_setup_df.columns else 0
    Potential_Hours = Opportunities * frh_value

    # Calculate Upsell Potential
    mpi_opportunity_c_df = combined_revenue_details[combined_revenue_details['paytypegroup'].isin(customer_pay_types)]
    opportunity_labor_sale_c = pd.to_numeric(mpi_opportunity_c_df['lbrsale']).fillna(0).sum()
    opportunity_parts_sale_c = pd.to_numeric(mpi_opportunity_c_df['prtextendedsale']).fillna(0).sum()
    opportunity_sold_hours_c = pd.to_numeric(mpi_opportunity_c_df['lbrsoldhours']).fillna(0).sum()
    
    opportunity_ELR = (opportunity_labor_sale_c + opportunity_parts_sale_c) / opportunity_sold_hours_c if opportunity_sold_hours_c != 0 else 0
    Upsell_Potential = float(Potential_Hours) * opportunity_ELR

    # Creating a list that has ronumber and sequence number of MPI Jobs
    mpi_lbrsequenceno_ronumber_list = [
        {'lbrsequenceno_idx': row['lbrsequenceno_idx'], 'unique_ro_number': row['unique_ro_number']}
        for _, row in filtered_rows.iterrows()
        if row['lbropcode'].strip() in mpi_opcodes
    ]
    
    # Calculating the sold sales and sold hours (Considering only jobs that followed by an MPI job)
    mpi_sold_list = []
    all_revenue_details_list = combined_revenue_details.to_dict('records')
    for revenue_row in all_revenue_details_list:
        if (revenue_row['lbropcode'].strip() in mpi_opcodes or
                revenue_row['department'] == 'Body Shop' or revenue_row['paytypegroup'] in warranty_pay_types or revenue_row['paytypegroup'] == 'I' or revenue_row['hide_ro'] == True):
            continue  # Skip this row
        
        for mpi_row in mpi_lbrsequenceno_ronumber_list:
            if (revenue_row['unique_ro_number'] == mpi_row['unique_ro_number'] and
                    int(revenue_row['lbrsequenceno_idx']) > int(mpi_row['lbrsequenceno_idx'])):
                mpi_sold_list.append(revenue_row)
                break
    
    greater_rows_df = pd.DataFrame(mpi_sold_list)
    sold_dollar = 0
    MPI_sold_hours_c = 0

    if not greater_rows_df.empty:
        sold_labor_sale_c = pd.to_numeric(greater_rows_df['lbrsale']).fillna(0).sum()
        sold_parts_sale_c = pd.to_numeric(greater_rows_df['prtextendedsale']).fillna(0).sum()
        sold_dollar = sold_labor_sale_c + sold_parts_sale_c
        MPI_sold_hours_c = pd.to_numeric(greater_rows_df['lbrsoldhours']).fillna(0).sum()


    MPI_collected_perc = round_off((sold_dollar / Upsell_Potential) * 100) if Upsell_Potential != 0 else 0
    perc_MPI_sold_hours_c = round_off(((MPI_sold_hours_c / float(Potential_Hours)) * 100)) if Potential_Hours != 0 else 0
    hours_sold_per_completed = round_off((MPI_sold_hours_c / len(Completed_MPI_ROs)), 1) if len(Completed_MPI_ROs) != 0 else 0

    results = {
        # kpi_section_headers["E"]: None,
        kpi_e_keys[0]: f"{Opportunities} / {len(Completed_MPI_ROs)} / {Completed_MPI_ROs_Perc}",
        kpi_e_keys[1]: round_off(Upsell_Potential),
        kpi_e_keys[2]: f"{round_off(sold_dollar)} / {round_off(MPI_collected_perc)}%",
        kpi_e_keys[3]: f"{round_off(Potential_Hours, 1)} / {round_off(MPI_sold_hours_c, 1)} / {perc_MPI_sold_hours_c}%",
        kpi_e_keys[4]: hours_sold_per_completed,
    }

    log_kpi_results("E", results, kpi_section_headers)    

    return results

def calculate_kpi_f(combined_revenue_details, menu_master_df, menu_service_type_df, assigned_menu_models_df, assigned_menu_opcodes_df):
    """Calculates KPI Scorecard F (Opportunities - Menu Sales) metrics."""

    # Initialize the menu sales values
    Opportunities = 0
    upsell_potential = 0
    potential_hours = 0
    menu_sold = 0
    sold_dollar = 0
    sold_hours = 0
    menu_jobs = 0
    menu_sold_perc = 0
    perc_collected = 0
    perc_menu_sold_hours = 0
    hours_sold_per_menu = 0
    
    # Calculate the total menu opportunities before the per-menu loop
    if not combined_revenue_details.empty:
        Menu_Opportunity_list_df = combined_revenue_details[(combined_revenue_details['department'] == 'Service') & 
                                                            (combined_revenue_details['group'].isin(['C', 'W'])) & 
                                                            (combined_revenue_details['paytypegroup'].isin(['C', 'M', 'E', 'W', 'F']))].copy()
        
        # Identifying the available menus from menu master table
        menu_names = set(menu_master_df['menu_name'])

        # storing the list of available default menu
        default_menu_series = menu_master_df[menu_master_df['is_default'].astype(
            int) == 1]['menu_name']

        # Identifying the default menu name
        if not default_menu_series.empty:
            default_menu = default_menu_series.iloc[0]
        else:
            default_menu = np.nan

        # Checking whether models are assigned to the available menu
        if not assigned_menu_models_df.empty:
            model_to_menu_map = assigned_menu_models_df.set_index('model')[
                'menu_name'].to_dict()
            Menu_Opportunity_list_df['mapped_menu'] = Menu_Opportunity_list_df['model'].map(
                model_to_menu_map).fillna(default_menu)
        else:
            Menu_Opportunity_list_df['mapped_menu'] = default_menu

        # if no menus are added, all values under menu sales will be 0
        if menu_names:
            for name in menu_names:
                Menu_Opportunity_list_df[name] = ''

            service_type_mapping = menu_service_type_df.set_index(
                'id')['service_type'].to_dict()

            menu_names = [
                name for name in menu_names if name in Menu_Opportunity_list_df.columns]
            
            for i, row in Menu_Opportunity_list_df.iterrows():
                mileage = float(row['mileage']) # Explicitly convert mileage to a float
                Menu_Opportunity_list_df.loc[i, menu_names] = np.nan
                matching_menus = menu_master_df[(menu_master_df['range_from'] <= mileage) & (
                    menu_master_df['range_to'] >= mileage)]
                for _, menu_row in matching_menus.iterrows():
                    menu_name = menu_row['menu_name']
                    service_type_id = menu_row['service_type_id']
                    if menu_name in Menu_Opportunity_list_df.columns:
                        service_type = service_type_mapping.get(service_type_id, None)
                        if service_type:
                            Menu_Opportunity_list_df.at[i, menu_name] = service_type
                            Menu_Opportunity_list_df.at[i, menu_name + '_id'] = service_type_id


        # Calculate Opportunities outside the loop
        Opportunities = Menu_Opportunity_list_df['unique_ro_number'].nunique()

        # ... (rest of the code to calculate the individual menu metrics)
        mapped_menus = set()
        for menus in Menu_Opportunity_list_df['mapped_menu']:
            mapped_menus.update(menus.split(','))
        
        individual_ro_counts = {
            menu: {'Basic': 0, 'Intermediate': 0, 'Major': 0} for menu in mapped_menus}

        for menu in mapped_menus:
            filtered_df_2 = Menu_Opportunity_list_df[Menu_Opportunity_list_df['mapped_menu'] == menu].copy()
            opprtunity_ros_set = set(filtered_df_2['unique_ro_number'])
            all_revenue_with_opprtunity_ros = combined_revenue_details[combined_revenue_details['unique_ro_number'].isin(opprtunity_ros_set)]
            
            ronumbers_with_menu_opcodes = {
                row['unique_ro_number'] 
                for _, row in all_revenue_with_opprtunity_ros.iterrows() 
                if row.get('lbropcode', '').strip() in assigned_menu_opcodes_df.loc[assigned_menu_opcodes_df['menu_name'] == menu, 'menu_opcode'].values
            }
            
            filtered_df_2 = filtered_df_2.dropna(subset=[menu])
            filtered_df_with_menu_opcodes = filtered_df_2[filtered_df_2['unique_ro_number'].isin(
                ronumbers_with_menu_opcodes)].copy()
            filtered_df_with_menu_opcodes_all = combined_revenue_details[combined_revenue_details['unique_ro_number'].isin(set(filtered_df_with_menu_opcodes['unique_ro_number']))]
            
            for category in ['Basic', 'Intermediate', 'Major']:
                category_filtered_df = filtered_df_2[(filtered_df_2[menu] == category) & (filtered_df_2['group'].isin({'C','W'})) & (filtered_df_2['paytypegroup'].isin({'C','M','E','F','W'}))].copy()
                category_filtered_df['unique_ronumber_category'] = category_filtered_df['ronumber'].astype(
                    str) + '_' + category_filtered_df['closeddate'].astype(str).copy()
                unique_ro_count_for_cat = category_filtered_df['unique_ronumber_category'].nunique()
                individual_ro_counts[menu][category] = unique_ro_count_for_cat
            
            filtered_df_2['unique_ro_number'] = filtered_df_2['ronumber'].astype(
                str) + '_' + filtered_df_2['closeddate'].astype(str).copy()
            filtered_df_with_menu_opcodes['unique_ro_number'] = filtered_df_with_menu_opcodes['ronumber'].astype(
                str) + '_' + filtered_df_with_menu_opcodes['closeddate'].astype(str).copy()
            
            labor_sale_with_menu_opcodes = pd.to_numeric(filtered_df_with_menu_opcodes_all['lbrsale']).fillna(0).sum()
            parts_sale_with_menu_opcodes = pd.to_numeric(filtered_df_with_menu_opcodes_all['prtextendedsale']).fillna(0).sum()
            labor_parts_sale = labor_sale_with_menu_opcodes + parts_sale_with_menu_opcodes
            sold_hours_with_menu_opcodes = pd.to_numeric(filtered_df_with_menu_opcodes_all['lbrsoldhours']).fillna(0).sum()

            sold_dollar += labor_parts_sale
            sold_hours += sold_hours_with_menu_opcodes
            
            rocount_with_menu_opcodes = filtered_df_with_menu_opcodes['unique_ro_number'].nunique()
            jobcount_with_menu_opcodes = filtered_df_with_menu_opcodes_all.shape[0]

            menu_sold += rocount_with_menu_opcodes
            menu_jobs += jobcount_with_menu_opcodes

        if Opportunities != 0:
            menu_sold_perc = round_off((menu_sold / Opportunities) * 100)
        else:
            menu_sold_perc = 0

        for menu_name, categories in individual_ro_counts.items():
            for category, details in categories.items():
                if isinstance(details, dict):
                    ro_count = details['ro_count']
                    service_type_id = details['service_type_id']
                    price_row = menu_master_df[(menu_master_df['service_type_id'] == service_type_id) & (
                        menu_master_df['menu_name'] == menu_name)]

                    if not price_row.empty:
                        price = price_row['price'].iloc[0]
                        frh = price_row['frh'].iloc[0]
                        upsell_potential += price * ro_count
                        potential_hours += frh * ro_count

    # Handling Divisible by 0 error
    perc_collected = 0
    if upsell_potential != 0:
        perc_collected = round_off((float(sold_dollar) / float(upsell_potential)) * 100)

    # Handling Divisible by 0 error
    perc_menu_sold_hours = 0
    if potential_hours != 0:
        perc_menu_sold_hours = round_off((float(sold_hours) / float(potential_hours)) * 100)
    
    # Handling Divisible by 0 error
    hours_sold_per_menu = 0
    if menu_sold != 0:
        hours_sold_per_menu = round_off((float(sold_hours) / float(menu_sold)), 1)
        
    results = {
    # kpi_section_headers["F"]: None,
    kpi_f_keys[0]: f"{Opportunities} / {menu_sold} / {menu_sold_perc}%",
    kpi_f_keys[1]: round_off(upsell_potential),
    kpi_f_keys[2]: f"{round_off(sold_dollar)} / {round_off(perc_collected)}%",
    kpi_f_keys[3]: f"{round_off(potential_hours, 1)} / {round_off(sold_hours, 1)} / {round_off(perc_menu_sold_hours)}%",
    kpi_f_keys[4]: round_off(hours_sold_per_menu, 1)
    }


    log_kpi_results("F", results, kpi_section_headers)  

    return results

def run_validation():
    """
    Main function
    """
    start_time = time.time()
    log_info(f"Start Time: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    # Initialize components and capture UI
    ui_capture_success, folder = automate_site(config)


    # Check if UI capture failed before proceeding
    if not ui_capture_success:
        log_warn("UI extraction failed. Skipping database validation and comparison.")
        return False

    log_info("Starting KPI Score data frame filtering process") 

    # Load all required dataframes at the start
    mpi_opcodes = config.mpi_opcodes
    MPI_setup_df = config.mpi_setup_df
    menu_master_df = config.menu_master_df
    menu_service_type_df = config.menu_service_type_df
    assigned_menu_models_df = config.assigned_menu_models_df
    assigned_menu_opcodes_df = config.assigned_menu_opcodes_df

    # Fetching retail_flag from config
    retail_flag_all = config.retail_flag_all

    # get raw data from database
    all_revenue_details = config.all_revenue_details
    retail_flag = set(retail_flag_all['source_paytype'])
    
    # List of data structures to check
    data_structures = {
        "mpi_opcodes": mpi_opcodes,
        "MPI_setup_df": MPI_setup_df,
        "menu_master_df": menu_master_df,
        "menu_service_type_df": menu_service_type_df,
        "assigned_menu_models_df": assigned_menu_models_df,
        "assigned_menu_opcodes_df": assigned_menu_opcodes_df,
        "retail_flag_all": retail_flag_all,
        "all_revenue_details": all_revenue_details,
        "retail_flag": retail_flag
    }

    # Check for emptiness and exit if any are empty
    for name, ds in data_structures.items():
        is_empty = False
        if isinstance(ds, pd.DataFrame):
            if ds.empty:
                is_empty = True
        elif not ds: # This check works for sets, lists, dictionaries, etc.
            is_empty = True
        
        if is_empty:
            log_error(f"Error: The data structure '{name}' is empty. Exiting.")
            return False
    
    working_days, store_id, start_date, end_date, advisor, tech, realm = get_environment_variables()

    # Prepare and filter data directly from the dataframe
    all_revenue_details["closeddate"] = pd.to_datetime(all_revenue_details["closeddate"], errors="coerce")
    filtered_df = all_revenue_details[
        (all_revenue_details["closeddate"] >= start_date) &
        (all_revenue_details["closeddate"] <= end_date)
    ].copy()
    
    if filtered_df.empty:
        log_warn("No data available after initial filtering. Exiting.")
        return False, []

    filtered_df = filtered_df[
        (filtered_df['department'] == 'Service') & (filtered_df['hide_ro'] != True)
    ]
    filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)
    
    combined_revenue_details, customer_pay_types ,warranty_pay_types = categorize_and_filter_ros(filtered_df, retail_flag, advisor, tech)
    
    if combined_revenue_details.empty:
        log_warn("No data available for KPI scorecard calculations. Exiting.")
        return False, []

    log_info("Starting KPI Score Card metrics calculation")

    # Calculate all KPI metrics
    results_a, total_cp_revenue_df, lbr_sold_hours_sum_C, Scorecard_10_CP = calculate_kpi_a(combined_revenue_details, customer_pay_types)
    results_b = calculate_kpi_b(total_cp_revenue_df)
    results_c = calculate_kpi_c(combined_revenue_details, working_days, lbr_sold_hours_sum_C, Scorecard_10_CP, advisor, tech)
    results_d,results_g= calculate_kpi_d_g(total_cp_revenue_df)
    results_e = calculate_kpi_e(combined_revenue_details, mpi_opcodes, MPI_setup_df, customer_pay_types, warranty_pay_types)
    results_f = calculate_kpi_f(combined_revenue_details, menu_master_df, menu_service_type_df, assigned_menu_models_df, assigned_menu_opcodes_df)

    # Combine all results into a single list of dictionaries for JSON output
    result_set = [{
    kpi_section_headers["A"]: None, **results_a,
    kpi_section_headers["B"]: None, **results_b,
    kpi_section_headers["C"]: None, **results_c,
    kpi_section_headers["D"]: None, **results_d,
    kpi_section_headers["E"]: None, **results_e,
    kpi_section_headers["F"]: None, **results_f,
    kpi_section_headers["G"]: None, **results_g
    }]
    
        
    # Use json.dumps() to format the output as a JSON string with indentation
    # formatted_output = json.dumps(result_set, indent=4)

    # # Log the formatted JSON string
    # log_info(formatted_output)

    output_md_file_path = os.path.join(folder, md_file)
    image_path = os.path.join(folder, jpg_file)

    json_output_path = os.path.join(folder, dict_json)
    with open(json_output_path, "w") as json_file:
        json.dump(result_set, json_file, indent=4)
    # Here, 'dict_json' is a path string, and 'json_file' is the file handle
    log_info(f"JSON output saved to: {json_output_path}")
    # extract data from UI screenshot
    extract_image_data(kpi_scorecard_prompt, image_path)

    # comparison -data from the UI screenshot and the python function results (on raw data)    
    compare_dashboard_kpis(output_md_file_path, json_output_path)

    log_info("Completed validation of the KPI scorecard data.View Results in Individual Reports")
    

    return True




def main():    
    run_validation()

if __name__ == "__main__":
    main()
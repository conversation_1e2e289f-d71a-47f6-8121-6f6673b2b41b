"""
Updated functions for comparing UI, extracted, and DB values separately with improved floating-point handling
"""

import json
import csv
import re
import os
import logging
import traceback
from collections import defaultdict
from datetime import datetime
from openpyxl.styles import Font, Alignment, PatternFill
import openpyxl
from dotenv import load_dotenv
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.std.universal.chart_dict import VALIDATION_CHARTS
from openpyxl.utils import get_column_letter


chart_key="parts_work_mix_comparison"
dict_xlsx_file = VALIDATION_CHARTS[chart_key]["xlsx"]
db_json = VALIDATION_CHARTS[chart_key]["db_json"]
dict_json = VALIDATION_CHARTS[chart_key]["json"]
dict_html= VALIDATION_CHARTS[chart_key]["html"]
dict_csv= VALIDATION_CHARTS[chart_key]["csv"]
sub_folder="Individual_Reports"
transformed_json = VALIDATION_CHARTS[chart_key]["transformed_json"]
load_dotenv()

# Constants
Tenant = config.database_name
store = config.store_name
role = config.role


def compare_with_parts_workmix_comparison_results(ui_json_path, db_json_path, timestamp):
    """Compare chart processing results with WorkMix DB calculated values opcode-wise"""
    log_info("Starting opcode-wise comparison with Work Mix results...")
    try:
        log_info("Starting opcode-wise comparison with Work Mix results...")
        # Convert relative paths to absolute paths
        ui_json_path = os.path.abspath(ui_json_path)
        db_json_path = os.path.abspath(db_json_path)
        
        log_info(f"UI JSON Path: {ui_json_path}")
        log_info(f"DB JSON Path: {db_json_path}")
        # Perform opcode-wise comparison
        comparison_results = compare_ui_db_values_opcode_wise(ui_json_path, db_json_path)
        log_info(f"Total opcodes compared: {comparison_results}")
        
        # Save comparison results with opcode-wise organization
        save_opcode_comparison_results(comparison_results, timestamp)
        
        log_info("UI vs DB opcode-wise comparison completed successfully")

    except Exception as e:
        log_info(f"Error in opcode-wise comparison: {e}")            
        traceback.print_exc()

import json
from collections import defaultdict

def compare_ui_db_values_opcode_wise(transformed_json_path, db_json_path):
    """Compare DB values with both UI (work_mix_analysis) and extracted_data values opcode-wise."""
    
    def safe_val(d, k):
        try:
            return float(d.get(k, 0) or 0)
        except Exception:
            return 0.0

    def compare_values(a, b, tolerance=0.05):
        try:
            return abs(float(a) - float(b)) <= tolerance
        except:
            return False

    def calc_diff(a, b):
        try:
            return round(float(a) - float(b), 4)
        except:
            return "N/A"

    def fmt(v):
        if v is None or v == "N/A":
            return "N/A"
        try:
            return round(float(v), 2)
        except:
            return v

    # ----------------------------
    # Load JSON files
    # ----------------------------
    with open(transformed_json_path, 'r') as f:
        ui_data = json.load(f)
    with open(db_json_path, 'r') as f:
        db_data = json.load(f)

    ui_work_mix = ui_data["target_month_results"].get("work_mix_analysis", {})
    ui_extracted = ui_data["target_month_results"].get("extracted_data", {})
    db_work_mix = db_data["target_month_results"].get("work_mix_analysis", {})

    # ----------------------------
    # DB lookup by opcode
    # ----------------------------
    db_opcode_map = {}
    for cat, cat_data in db_work_mix.get("category_breakdown", {}).items():
        for op in cat_data.get("individual_opcodes", []):
            db_opcode_map[op["opcode"]] = {"category": cat, "data": op}

    # ----------------------------
    # Prepare comparison structure
    # ----------------------------
    opcode_comparison_results = {}

    # Metrics to compare
    metrics = ["workmix", "job_count", "gp_percentage", "parts_markup", "parts_cost"]

    # Months (invert mapping since UI mon1=DB mon2)
    months = [("mon1", "mon2"), ("mon2", "mon1")]

    # ----------------------------
    # Process UI work_mix_analysis
    # ----------------------------
    def process_ui_section(ui_section, extracted_section):
        for cat, cat_data in ui_section.get("category_breakdown", {}).items():
            for ui_op in cat_data.get("individual_opcodes", []):
                opcode = ui_op.get("opcode")
                if not opcode:
                    continue

                db_entry = db_opcode_map.get(opcode)
                if not db_entry:
                    continue

                db_data = db_entry["data"]
                category_name = db_entry["category"]
                extracted_op = None

                # Find same opcode in extracted section if available
                for cat2, ex_cat_data in extracted_section.get("category_breakdown", {}).items():
                    for ex_op in ex_cat_data.get("individual_opcodes", []):
                        if ex_op.get("opcode") == opcode:
                            extracted_op = ex_op
                            break

                if opcode not in opcode_comparison_results:
                    opcode_comparison_results[opcode] = {
                        "opcode": opcode,
                        "category": category_name,
                        "months": {},
                        "summary": {
                            "total_comparisons": 0,
                            "ui_vs_db_matches": 0,
                            "extracted_vs_db_matches": 0,
                            "ui_vs_extracted_matches": 0,
                            "ui_vs_db_match_rate": 0,
                            "extracted_vs_db_match_rate": 0,
                            "ui_vs_extracted_match_rate": 0
                        }
                    }

                # Month comparison
                for ui_mon, db_mon in months:
                    month_key = f"month_{'1' if ui_mon == 'mon1' else '2'}"
                    if month_key not in opcode_comparison_results[opcode]["months"]:
                        opcode_comparison_results[opcode]["months"][month_key] = {
                            "date": db_mon,
                            "metrics": [],
                            "ui_vs_db_matches": 0,
                            "extracted_vs_db_matches": 0,
                            "ui_vs_extracted_matches": 0,
                            "total": 0
                        }

                    for metric in metrics:
                        ui_key = f"{metric}_{ui_mon}"
                        db_key = f"{metric}_{db_mon}"

                        ui_val = safe_val(ui_op, ui_key)
                        db_val = safe_val(db_data, db_key)
                        extracted_val = safe_val(extracted_op, ui_key) if extracted_op else None

                        ui_vs_db_match = compare_values(ui_val, db_val)
                        extracted_vs_db_match = compare_values(extracted_val, db_val) if extracted_val is not None else None
                        ui_vs_extracted_match = compare_values(ui_val, extracted_val) if extracted_val is not None else None

                        metric_result = {
                            "metric_name": metric,
                            "ui_value": fmt(ui_val),
                            "extracted_value": fmt(extracted_val) if extracted_val is not None else "N/A",
                            "db_value": fmt(db_val),
                            "ui_vs_db_match": ui_vs_db_match,
                            "extracted_vs_db_match": extracted_vs_db_match if extracted_vs_db_match is not None else "N/A",
                            "ui_vs_extracted_match": ui_vs_extracted_match if ui_vs_extracted_match is not None else "N/A",
                            "ui_vs_db_difference": calc_diff(ui_val, db_val),
                            "extracted_vs_db_difference": calc_diff(extracted_val, db_val) if extracted_val is not None else "N/A",
                            "ui_vs_extracted_difference": calc_diff(ui_val, extracted_val) if extracted_val is not None else "N/A"
                        }

                        # Store result
                        month_data = opcode_comparison_results[opcode]["months"][month_key]
                        month_data["metrics"].append(metric_result)
                        month_data["total"] += 1
                        opcode_comparison_results[opcode]["summary"]["total_comparisons"] += 1

                        # Match counting
                        if ui_vs_db_match:
                            month_data["ui_vs_db_matches"] += 1
                            opcode_comparison_results[opcode]["summary"]["ui_vs_db_matches"] += 1
                        if extracted_vs_db_match:
                            month_data["extracted_vs_db_matches"] += 1
                            opcode_comparison_results[opcode]["summary"]["extracted_vs_db_matches"] += 1
                        if ui_vs_extracted_match:
                            month_data["ui_vs_extracted_matches"] += 1
                            opcode_comparison_results[opcode]["summary"]["ui_vs_extracted_matches"] += 1

                # Calculate match rates
                summary = opcode_comparison_results[opcode]["summary"]
                total = summary["total_comparisons"]
                if total > 0:
                    summary["ui_vs_db_match_rate"] = round(summary["ui_vs_db_matches"] / total * 100, 2)
                    summary["extracted_vs_db_match_rate"] = round(summary["extracted_vs_db_matches"] / total * 100, 2)
                    summary["ui_vs_extracted_match_rate"] = round(summary["ui_vs_extracted_matches"] / total * 100, 2)

    # Perform comparison
    process_ui_section(ui_work_mix, ui_extracted)

    return opcode_comparison_results




def compare_values_improved(value1, value2, tolerance=0.01):
    """Compare two values with improved tolerance for floating point numbers and better handling of different data types"""
    if value1 is None or value2 is None:
        return value1 == value2
    
    try:
        # Convert to string safely, skip .strip() if None
        str1 = "" if value1 is None else str(value1)
        str2 = "" if value2 is None else str(value2)

        # str1 = str1.replace("%", "").strip()
        # str2 = str2.replace("%", "").strip()
        
        # Try numeric comparison
        val1 = float(str1) if str1 else 0
        val2 = float(str2) if str2 else 0

        if abs(val1) < 1 and abs(val2) < 1:
            return abs(val1 - val2) < tolerance

        max_val = max(abs(val1), abs(val2))
        if max_val == 0:
            return True

        relative_diff = abs(val1 - val2) / max_val
        return relative_diff < tolerance or abs(val1 - val2) < tolerance

    except Exception:
        return str(value1) == str(value2)

def calculate_difference_improved(value1, value2):
    """Calculate the difference between two values with improved handling"""
    if value1 is None or value2 is None:
        return "N/A"
    
    try:
        str1 = str(value1) if value1 is not None else "0"
        str2 = str(value2) if value2 is not None else "0"
        
        val1 = float(str1) if str1 else 0
        val2 = float(str2) if str2 else 0
        return round(val1 - val2, 4)
    except Exception:
        return "N/A"

def format_value(value):
    if value is None or value == "N/A":
        return "N/A"
    try:
        if isinstance(value, (int, float)):
            return round(value, 4) if isinstance(value, float) else value
        return str(value)
    except:
        return str(value)


def save_opcode_comparison_results(opcode_results, timestamp):
    """Save opcode-wise comparison results to CSV and generate HTML report with three-way comparison"""
    try:
        if not opcode_results:
            log_info("No opcode comparison results to save")
            return
        
        log_info("Saving opcode-wise comparison results with three-way comparison")
        
        # Flatten results for CSV
        flattened_results = []
        overall_summary = {
            'total_opcodes': len(opcode_results),
            'total_comparisons': 0,
            'ui_vs_db_matches': 0,
            'extracted_vs_db_matches': 0,
            'ui_vs_extracted_matches': 0,
            'opcode_summaries': []
        }
        
        for opcode, opcode_data in opcode_results.items():
            overall_summary['total_comparisons'] += opcode_data['summary']['total_comparisons']
            overall_summary['ui_vs_db_matches'] += opcode_data['summary']['ui_vs_db_matches']
            overall_summary['extracted_vs_db_matches'] += opcode_data['summary']['extracted_vs_db_matches']
            overall_summary['ui_vs_extracted_matches'] += opcode_data['summary']['ui_vs_extracted_matches']
            
            overall_summary['opcode_summaries'].append({
                'opcode': opcode,
                'category': opcode_data['category'],
                'ui_vs_db_match_rate': opcode_data['summary']['ui_vs_db_match_rate'],
                'extracted_vs_db_match_rate': opcode_data['summary']['extracted_vs_db_match_rate'],
                'ui_vs_extracted_match_rate': opcode_data['summary']['ui_vs_extracted_match_rate'],
                'total_comparisons': opcode_data['summary']['total_comparisons']
            })
            
            for month_key, month_data in opcode_data['months'].items():
                for metric in month_data['metrics']:
                    flattened_results.append({
                        'Opcode': opcode or "",
                        'Category': opcode_data['category'] or "",
                        'Month': month_data['date'] or "",
                        'Metric_Name': metric.get('metric_name', ""),
                        'UI_Value': format_value(metric.get('ui_value')),
                        'Extracted_Value': format_value(metric.get('extracted_value')),
                        'DB_Value': format_value(metric.get('db_value')),
                        'UI_vs_DB_Match': 'TRUE' if metric.get('ui_vs_db_match') else 'FALSE',
                        'Extracted_vs_DB_Match': 'TRUE' if metric.get('extracted_vs_db_match') == True else ('FALSE' if metric.get('extracted_vs_db_match') == False else 'N/A'),
                        'UI_vs_Extracted_Match': 'TRUE' if metric.get('ui_vs_extracted_match') == True else ('FALSE' if metric.get('ui_vs_extracted_match') == False else 'N/A'),
                        'UI_vs_DB_Difference': format_value(metric.get('ui_vs_db_difference')),
                        'Extracted_vs_DB_Difference': format_value(metric.get('extracted_vs_db_difference')),
                        'UI_vs_Extracted_Difference': format_value(metric.get('ui_vs_extracted_difference')),
                    })
        
        # Calculate overall match rates
        total_comps = overall_summary['total_comparisons']
        if total_comps > 0:
            overall_summary['ui_vs_db_match_rate'] = (overall_summary['ui_vs_db_matches'] / total_comps * 100)
            overall_summary['extracted_vs_db_match_rate'] = (overall_summary['extracted_vs_db_matches'] / total_comps * 100)
            overall_summary['ui_vs_extracted_match_rate'] = (overall_summary['ui_vs_extracted_matches'] / total_comps * 100)
        
        # Create output paths
        _output_folder, output_csv = create_folder_file_path(
            subfolder=sub_folder,
            output_file=dict_csv or "default_output.csv",            
        )

        
        # Save CSV with expanded columns
        with open(output_csv, "w", newline="", encoding="utf-8") as csvfile:
            fieldnames = [
                "Opcode", "Category", "Month", "Metric_Name",
                "UI_Value", "Extracted_Value", "DB_Value",
                "UI_vs_DB_Match", "Extracted_vs_DB_Match", "UI_vs_Extracted_Match",
                "UI_vs_DB_Difference", "Extracted_vs_DB_Difference", "UI_vs_Extracted_Difference"
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for row in flattened_results:
                clean_row = {k: ("" if v is None else v) for k, v in row.items()}
                writer.writerow(clean_row)

        
        log_info(f"Opcode comparison CSV saved to: {output_csv}")
        
        # Save detailed JSON
        json_path = os.path.join(_output_folder, dict_json)
        with open(json_path, "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": timestamp,
                "overall_summary": overall_summary,
                "opcode_results": opcode_results
            }, f, indent=2, ensure_ascii=False)
        
        # Generate HTML report
        html_path = os.path.join(_output_folder, dict_html)
        generate_opcode_wise_html_report_threeway(html_path, opcode_results, overall_summary, timestamp, Tenant, store, role)
        
        log_info(f"Three-way comparison HTML report saved to: {html_path}")
        log_info(f"Detailed JSON saved to: {json_path}")
        
        # Print summary
        log_info(f"\nThree-way Comparison Summary:")
        log_info(f"   - Total opcodes: {overall_summary['total_opcodes']}")
        log_info(f"   - Total comparisons: {overall_summary['total_comparisons']}")
        log_info(f"   - UI vs DB matches: {overall_summary['ui_vs_db_matches']} ({overall_summary.get('ui_vs_db_match_rate', 0):.1f}%)")
        log_info(f"   - Extracted vs DB matches: {overall_summary['extracted_vs_db_matches']} ({overall_summary.get('extracted_vs_db_match_rate', 0):.1f}%)")
        log_info(f"   - UI vs Extracted matches: {overall_summary['ui_vs_extracted_matches']} ({overall_summary.get('ui_vs_extracted_match_rate', 0):.1f}%)")
        
    except Exception as e:
        log_info(f"Error saving opcode comparison results: {e}")
        traceback.print_exc()

def generate_opcode_wise_html_report_threeway(
    html_path,
    opcode_results,
    overall_summary,
    timestamp,
    tenant="Unknown",
    store="Unknown",
    role="Unknown",
):
    """Generate three-way comparison HTML report (UI vs Extracted vs DB) using Bootstrap style"""
    try:
        html_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <title>Three-way Opcode Comparison Report (UI vs Extracted vs DB)</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                    body {{ padding: 20px; font-family: Arial, sans-serif; }}
                    .badge-pass {{ background-color: #28a745; color: white; }}
                    .badge-fail {{ background-color: #dc3545; color: white; }}
                    .badge-all-passed {{ background-color: #28a745; color: white; }}
                    .badge-has-failures {{ background-color: #dc3545; color: white; }}
                    .card-header {{ cursor: pointer; background-color: #cfe2f3; }}
                    .match-indicator {{ font-weight: bold; padding: 8px 15px; border-radius: 5px; display: inline-block; }}
                    .match-true {{ background-color: #d4edda; color: #155724; }}
                    .match-false {{ background-color: #f8d7da; color: #721c24; }}
                    .metrics-table {{ width: 100%; border-collapse: collapse; font-size: 12px; margin-top: 10px; }}
                    .metrics-table th, .metrics-table td {{ border: 1px solid #dee2e6; padding: 6px 8px; text-align: left; }}
                    .metrics-table th {{ background-color: #f8f9fa; }}
                    .month-header {{ background-color: #f1f1f1; padding: 8px; font-weight: bold; border: 1px solid #dee2e6; }}
                    .footer {{ margin-top: 30px; text-align: center; border-top: 1px solid #ccc; padding-top: 10px; font-size: 12px; color: #555; }}
                </style>
            </head>
            <body>
            <div class="container">
                <h1 class="mb-4">Three-way Comparison Report - Parts Workmix 2 Months Comparison (UI vs Extracted vs DB)</h1>

                <div class="mb-4">
                    <strong>Tenant:</strong> {tenant}<br>
                    <strong>Store:</strong> {store}<br>
                    <strong>Role:</strong> {role}<br>
                    <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
                    <strong>Report Timestamp:</strong> {timestamp}<br>
                </div>

                <div class="d-flex gap-3 mb-4">
                    <span class="badge bg-secondary">Total Opcodes: {overall_summary['total_opcodes']}</span>
                    <span class="badge bg-secondary">Total Comparisons: {overall_summary['total_comparisons']}</span>
                    <span class="badge bg-success">UI vs DB Matches: {overall_summary['ui_vs_db_matches']} ({overall_summary.get('ui_vs_db_match_rate', 0):.1f}%)</span>
                    <span class="badge bg-info">Drill Down vs DB Matches: {overall_summary['extracted_vs_db_matches']} ({overall_summary.get('extracted_vs_db_match_rate', 0):.1f}%)</span>
                    <span class="badge bg-warning text-dark">UI vs Extracted Matches: {overall_summary['ui_vs_extracted_matches']} ({overall_summary.get('ui_vs_extracted_match_rate', 0):.1f}%)</span>
                </div>

                <div class="accordion" id="opcodeAccordion">
            """
        # Sort opcodes for consistency
        sorted_opcodes = sorted(opcode_results.items(), key=lambda x: x[0])

        for idx, (opcode, opcode_data) in enumerate(sorted_opcodes):
            chart_id = f"opcode{idx}"
            all_passed = (
                opcode_data["summary"]["ui_vs_db_match_rate"] == 100.0
                and opcode_data["summary"]["extracted_vs_db_match_rate"] == 100.0
                and opcode_data["summary"]["ui_vs_extracted_match_rate"] == 100.0
            )
            badge_class = "badge-all-passed" if all_passed else "badge-has-failures"
            badge_text = "All Passed" if all_passed else "Has Failures"

            html_content += f"""
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="heading-{chart_id}">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                data-bs-target="#{chart_id}" aria-expanded="false" aria-controls="{chart_id}">
                                {opcode} ({opcode_data['category']})
                                <span class="ms-3 badge {badge_class}">{badge_text}</span>
                            </button>
                        </h2>
                        <div id="{chart_id}" class="accordion-collapse collapse" aria-labelledby="heading-{chart_id}"
                            data-bs-parent="#opcodeAccordion">
                            <div class="accordion-body">
            """

            # Render months
            for month_key, month_data in opcode_data["months"].items():
                ui_db_month_rate = (
                    month_data["ui_vs_db_matches"] / month_data["total"] * 100
                    if month_data["total"] > 0
                    else 0
                )
                ext_db_month_rate = (
                    month_data["extracted_vs_db_matches"] / month_data["total"] * 100
                    if month_data["total"] > 0
                    else 0
                )
                ui_ext_month_rate = (
                    month_data["ui_vs_extracted_matches"] / month_data["total"] * 100
                    if month_data["total"] > 0
                    else 0
                )
                month_label = (
                    config.start_date if month_data["date"] == "mon1"
                    else config.end_date if month_data["date"] == "mon2"
                    else month_data["date"]
)
                html_content += f"""
                <div class="month-section mb-3">
                    <div class="month-header">
                        {month_label} — 
                        UI vs DB: {ui_db_month_rate:.1f}% | 
                        Extracted vs DB: {ext_db_month_rate:.1f}% | 
                        UI vs Extracted: {ui_ext_month_rate:.1f}%
                    </div>
                    <table class="metrics-table table table-bordered table-sm mt-2">
                        <thead>
                            <tr>
                                <th>Metric</th>
                                <th>UI Value</th>
                                <th>Drill Down Value</th>
                                <th>DB Value</th>
                                <th>UI vs DB</th>
                                <th>UI vs DB Diff</th>
                                <th>Drill Down  vs DB</th>
                                <th>Drill Down  vs DB Diff</th>
                                <th>UI vs Drill Down </th>
                                <th>UI vs Drill Down  Diff</th>
                            </tr>
                        </thead>
                        <tbody>
                """
                for metric in month_data["metrics"]:
                    def fmt_match(match):
                        if match is None:
                            return '<span class="text-muted fst-italic">N/A</span>'
                        return (
                            '<span class="match-indicator match-true">✓ MATCH</span>'
                            if match
                            else '<span class="match-indicator match-false">✗ MISMATCH</span>'
                        )

                    html_content += f"""
                            <tr>
                                <td><strong>{metric['metric_name']}</strong></td>
                                <td>{metric['ui_value']}</td>
                                <td>{metric['extracted_value']}</td>
                                <td>{metric['db_value']}</td>
                                <td>{fmt_match(metric['ui_vs_db_match'])}</td>
                                <td>{metric['ui_vs_db_difference']}</td>
                                <td>{fmt_match(metric['extracted_vs_db_match'])}</td>
                                <td>{metric['extracted_vs_db_difference']}</td>
                                <td>{fmt_match(metric['ui_vs_extracted_match'])}</td>
                                <td>{metric['ui_vs_extracted_difference']}</td>
                            </tr>
                    """
                html_content += """
                        </tbody>
                    </table>
                </div>
                """
            html_content += """
                    </div>
                </div>
            </div>
                """

        html_content += f"""
            </div>
            <div class="footer">
                Three-way Comparison Report generated on {timestamp} | UI vs Extracted vs DB
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        </body>
        </html>
        """
        # Write HTML
        with open(html_path, "w", encoding="utf-8") as f:
            f.write(html_content)

        print(f"✅ Three-way comparison HTML report generated successfully: {html_path}")

    except Exception as e:
        print(f"❌ Error generating three-way comparison HTML report: {e}")
        import traceback
        traceback.print_exc()

def format_match_display(match_value):
    """Format match value for HTML display with appropriate styling"""
    if match_value is True:
        return '<span class="match-yes">✓ YES</span>'
    elif match_value is False:
        return '<span class="match-no">✗ NO</span>'
    else:
        return '<span class="match-na">N/A</span>'



import math
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from datetime import datetime, timedelta
from collections import Counter
import pandas as pd
import os
import openpyxl
import numpy as np
from decimal import Decimal, ROUND_HALF_UP
from lib.pattern.qa_auto_core.db_handler.db_connector import getCustomerPayTypeGroupsList
from lib.pattern.qa_auto_core.db_handler.db_connector import allRevenueDetailsPartsWorkMixAnalysis 
from lib.pattern.config import config

import sys
sys.path.append('../')


def round_off(n, decimals=0):
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal('1'), rounding=ROUND_HALF_UP) / multiplier)

def zero_sales_check(df, columns):
    total_sum = df[columns].sum().sum()
    return total_sum == 0

columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']

def calculate_parts_workmix(start_date, end_date, all_revenue_details_df, suffix=''):
    """Calculate parts workmix metrics for a given date range and DataFrame"""
    log_info(f"Calculating parts workmix from {start_date} to {end_date}")
    try:
        # Define columns to check for zero sales
        columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        
        # Calculate working days
        working_days = np.busday_count(start_date.date(), (end_date + timedelta(days=1)).date())

        # Load Retail flag from DB
        retail_flag_DB_connect = getCustomerPayTypeGroupsList()
        retail_flag = retail_flag_DB_connect.getCustomerPayTypeList()

        # Read advisor and technician sets from config
        if ',' in config.advisor:
            advisor = set(x.strip() for x in config.advisor.split(','))
        else:
            advisor = {config.advisor.strip()}

        if ',' in config.technician:
            tech = set(x.strip() for x in config.technician.split(','))
        else:
            tech = {config.technician.strip()}

        # Filter by date range
        df = all_revenue_details_df.copy()
        df['closeddate'] = pd.to_datetime(df['closeddate'], errors='coerce')
        df = df[(df['closeddate'] >= start_date) & (df['closeddate'] <= end_date)]

        columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        df[columns_to_convert] = df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)
        df[columns_to_convert] = df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))

        # Filter Service department & not hidden
        filtered_df = df[(df['department'] == 'Service') & (df['hide_ro'] != True)].copy()
        filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)

        if filtered_df.empty:
            print('No data found for the given date range')
            return None
        customer_pay_types = {'C', 'E', 'M'}
        warranty_pay_types = {'W', 'F'}
        # Determine customer and warranty pay types dynamically (CORRECTED)
        if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C'}
            warranty_pay_types = {'W', 'F', 'M', 'E'}
        elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'M'}
            warranty_pay_types = {'W', 'F', 'E'}  # FIXED: Added 'E'
        elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C', 'E'}
            warranty_pay_types = {'W', 'F', 'M'}
        elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'E', 'M'}
            warranty_pay_types = {'W', 'F'}
        else:
            # Default fallback
            customer_pay_types = {'C', 'E', 'M'}
            warranty_pay_types = {'W', 'F'}

        # Set columns to 0 if opcategory is 'N/A'
        filtered_df.loc[filtered_df['opcategory'] == 'N/A', columns_to_check] = 0
        filtered_df['group'] = pd.Series(dtype="string")

        # Assign groups per RO
        for ro_number in filtered_df['unique_ro_number'].unique():
            ro_rows = filtered_df[filtered_df['unique_ro_number'] == ro_number]
            ro_C = ro_rows[ro_rows['paytypegroup'].isin(customer_pay_types)]
            ro_W = ro_rows[ro_rows['paytypegroup'].isin(warranty_pay_types)]
            zero_C = zero_sales_check(ro_C, columns_to_check)
            zero_W = zero_sales_check(ro_W, columns_to_check)
            if not ro_C.empty and not zero_C:
                filtered_df.loc[filtered_df['unique_ro_number'] == ro_number, 'group'] = 'C'
            elif not ro_W.empty and not zero_W:
                filtered_df.loc[filtered_df['unique_ro_number'] == ro_number, 'group'] = 'W'
            else:
                filtered_df.loc[filtered_df['unique_ro_number'] == ro_number, 'group'] = 'I'

        # Apply advisor/tech filters (CORRECTED - matching original logic)
        if advisor == {'all'} and tech == {'all'}:
            matching_ro_numbers = filtered_df['unique_ro_number'].unique()
        elif advisor != {'all'} and tech == {'all'}:
            matching_ro_numbers = filtered_df.loc[filtered_df['serviceadvisor'].astype(str).isin(advisor), 'unique_ro_number'].unique()
        elif advisor == {'all'} and tech != {'all'}:
            matching_ro_numbers = filtered_df.loc[filtered_df['lbrtechno'].astype(str).isin(tech), 'unique_ro_number'].unique()
        elif advisor != {'all'} and tech != {'all'}:
            matching_ro_numbers = filtered_df.loc[(filtered_df['serviceadvisor'].astype(str).isin(advisor)) & 
                (filtered_df['lbrtechno'].astype(str).isin(tech)), 'unique_ro_number'].unique()
        
        filtered_df = filtered_df[filtered_df['unique_ro_number'].isin(matching_ro_numbers)]
        filtered_df = filtered_df.reset_index(drop=True)
        filtered_df.loc[filtered_df['opcategory'] == 'N/A', columns_to_check] = 0

        # Count ROs
        ro_counts = {
            'customer_pay_ros': filtered_df[filtered_df['group'] == 'C']['unique_ro_number'].nunique(),
            'warranty_ros': filtered_df[filtered_df['group'] == 'W']['unique_ro_number'].nunique(),
            'internal_ros': filtered_df[filtered_df['group'] == 'I']['unique_ro_number'].nunique()
        }
        total_ros = sum(ro_counts.values())
        Average_ROs_Per_Day = round_off(total_ros / working_days)
        
        # Filter only Customer Pay for work mix calculations
        total_revenue_CP = filtered_df[(filtered_df['paytypegroup'].isin(customer_pay_types)) & (filtered_df['group'] == 'C')].copy()
        total_revenue_CP.loc[total_revenue_CP['opcategory'] == 'N/A', columns_to_check] = 0
        
        total_revenue_CP = total_revenue_CP[
            ~((total_revenue_CP['lbrsale'].fillna(0) == 0) &
            (total_revenue_CP['lbrsoldhours'].fillna(0) == 0) &
            (total_revenue_CP['prtextendedsale'].fillna(0) == 0) &
            (total_revenue_CP['prtextendedcost'].fillna(0) == 0))
        ]

        total_parts_cost_CP = total_revenue_CP['prtextendedcost'].sum()

        # Work mix calculations
        work_mix_result = total_revenue_CP.groupby(['lbropcode', 'opcategory']).agg({
            'prtextendedsale': 'sum',
            'prtextendedcost': 'sum'
        }).reset_index()

        for index, row in work_mix_result.iterrows():
            prt_sale = row['prtextendedsale']
            prt_cost = row['prtextendedcost']
            lbr_opcode = row['lbropcode']

            work_mix_result.at[index, 'parts_cost'] = round_off(prt_cost, 2)
            work_mix_result.at[index, 'work_mix_percentage'] = 0
            if total_parts_cost_CP != 0:
                work_mix_result.at[index, 'work_mix_percentage'] = (prt_cost / total_parts_cost_CP) * 100
            
            work_mix_result.at[index, 'parts_markup'] = 0
            if prt_cost != 0:
                work_mix_result.at[index, 'parts_markup'] = round_off((prt_sale / prt_cost), 2)
            
            work_mix_result.at[index, 'job_count'] = 0
            if lbr_opcode.strip() in set(total_revenue_CP['lbropcode'].str.strip()):
                work_mix_result.at[index, 'job_count'] = len(total_revenue_CP[total_revenue_CP['lbropcode'] == lbr_opcode.strip()]['ronumber'])

            work_mix_result.at[index, 'gp_percentage'] = 0
            if prt_sale != 0:
                work_mix_result.at[index, 'gp_percentage'] = round_off((((prt_sale - prt_cost) / prt_sale) * 100), 2)

        work_mix_less_than_2 = work_mix_result[work_mix_result['work_mix_percentage'] <= 2]
        work_mix_greater_than_2 = work_mix_result[work_mix_result['work_mix_percentage'] > 2]

        # Build JSON output
        output_data = []
        opcategory_list = ['COMPETITIVE','MAINTENANCE','REPAIR']

        for opcategory in opcategory_list:
            group_gt2 = work_mix_greater_than_2[work_mix_greater_than_2['opcategory'] == opcategory]
            group_lt2 = work_mix_less_than_2[work_mix_less_than_2['opcategory'] == opcategory]

            for index, row in group_gt2.iterrows():
                output_data.append({
                    'opcategory': opcategory,
                    'opcode': row['lbropcode'],
                    f'parts_cost{suffix}': row['parts_cost'],
                    f'work_mix_percentage{suffix}': round_off(row['work_mix_percentage'], 2),
                    f'parts_markup{suffix}': row['parts_markup'],
                    f'job_count{suffix}': row['job_count'],
                    f'gp_percentage{suffix}': row['gp_percentage']
                })

            # CORRECTED: Complete "OTHER" calculations
            if not group_lt2.empty:
                prt_sale_others = group_lt2['prtextendedsale'].sum()
                prt_cost_others = group_lt2['prtextendedcost'].sum()
                work_mix_perc_others = round_off((prt_cost_others / total_parts_cost_CP * 100), 2) if total_parts_cost_CP != 0 else 0
                markup_others = round_off((prt_sale_others / prt_cost_others), 2) if prt_cost_others != 0 else 0
                job_count_others = group_lt2['job_count'].sum()
                gp_perc_others = round_off(((prt_sale_others - prt_cost_others) / prt_sale_others * 100), 2) if prt_sale_others != 0 else 0
                
                output_data.append({
                    'opcategory': opcategory,
                    'opcode': 'OTHER',
                    f'parts_cost{suffix}': prt_cost_others,
                    f'work_mix_percentage{suffix}': work_mix_perc_others,
                    f'parts_markup{suffix}': markup_others,
                    f'job_count{suffix}': int(job_count_others),
                    f'gp_percentage{suffix}': gp_perc_others
                })

        result_json = {
            'total_ros': total_ros,
            'ro_counts': ro_counts,
            'Average_ROs_Per_Day': Average_ROs_Per_Day,
            'work_mix': output_data
        }

        return result_json
    except Exception as e:
        log_error(f"Error in calculate_parts_workmix: {e}")
        return None
import asyncio
import json
import os
import time
import glob
import traceback
import pandas as pd
import numpy as np
from datetime import datetime
from math import isfinite, isnan
from playwright.async_api import async_playwright
from concurrent.futures import ThreadPoolExecutor
import threading
from datetime import datetime
from collections import defaultdict
import re
from decimal import Decimal, ROUND_HALF_UP
import openpyxl
import openpyxl.cell
from openpyxl.styles import <PERSON><PERSON><PERSON><PERSON>, Alignment, Font
from openpyxl.utils import get_column_letter
import csv
import argparse
from lib.pattern.config import config
from lib.pattern.qa_auto_core.compare_workmix_two_month_comparison import compare_with_labour_comparison_results
from lib.pattern.qa_auto_core.db_handler.db_connector import getCustomerPayTypeGroupsList, workMixComparisonTableResult
from dateutil.relativedelta import relativedelta
from datetime import timedelta
from typing import Dict, List, Any, Optional, Tuple
import calendar
from lib.std.universal.authmanager import AuthManager
from lib.std.universal.chart_dict import VALIDATION_CHARTS
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import logger, log_info, log_warn, log_error

chart_key="work_mix_two_months_comparison"
chart_process_json = VALIDATION_CHARTS[chart_key]["chart_process_json"]
db_json = VALIDATION_CHARTS[chart_key]["db_json"]
transformed_json = VALIDATION_CHARTS[chart_key]["transformed_json"]

# Target months-years for drilling down (modify as needed)
# This will be set after argument parsing

# Configuration constants
TARGET_MONTHS_YEARS = config.target_month_year
MAX_CONCURRENT_BROWSERS = 3
BROWSER_TIMEOUT = 30000


namespace = {
    'ns': 'http://www.dmotorworks.com/service-repair-order-history'
}


def extract_months_from_data(chart_data_list):
    """Extract months in chronological order and map to mon1, mon2, ..."""
    months = set()
    for point in chart_data_list:
        if not isinstance(point, dict) or 'series_name' not in point:
            continue
        months.add(point['series_name'])
    # Sort months chronologically
    month_list = sorted(list(months), key=parse_month_for_sorting)
    month_mapping = {month: f"mon{i+1}" for i, month in enumerate(month_list)}
    return month_mapping
def identify_chart_type(chart_title):
    chart_mappings = {
        'Labor Work Mix': 'workmix',
        'Work Mix %': 'workmix', 
        'Job Count': 'job_count',
        'Gross Profit': 'gp_percentage',
        'Effective Labor Rate': 'elr',
        'Sold Hours': 'labor_hours'
    }
    for key, chart_type in chart_mappings.items():
        if key in chart_title:
            return chart_type
    return None

def extract_month_key_from_series(series_name, month_mapping):
    """
    Extract month key from series name using dynamic month mapping
    """
    return month_mapping.get(series_name)
def extract_value_from_point(point, chart_type):
    """Extract value for the given chart type from point_data or extracted_data."""
    # Try direct field
    pd = point.get('point_data', {})
    if chart_type in pd and pd[chart_type] not in (None, ''):
        return float(pd[chart_type])
    # Try fallback fields
    fallback_fields = [
        'workmix1Value', 'workmix2Value', 
        'job_count1Value', 'job_count2Value', 'gp_percentage1Value', 
        'gp_percentage2Value', 'elr1Value', 'elr2Value', 
        'labor_hours1Value', 'labor_hours2Value'
    ]
    for field in fallback_fields:
        if field in pd and pd[field] not in (None, ''):
            return float(pd[field])
    # Try extracted_data
    ed = point.get('extracted_data', {})
    if chart_type in ed and ed[chart_type] not in (None, ''):
        return float(ed[chart_type])
    return 0.0
def parse_month_for_sorting(month_str):
    """Parse month string to sortable format for chronological ordering."""
    if not month_str:
        return (9999, 99)
    month_name_to_num = {
        'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
        'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
    }
    # Try "Jul-25", "Aug-25"
    m = re.match(r'([A-Za-z]+)-(\d+)', month_str)
    if m:
        month = month_name_to_num.get(m.group(1).lower()[:3], 0)
        year = int(m.group(2))
        if year < 100:
            year += 2000
        return (year, month)
    # Try "2025-07"
    m = re.match(r'(\d{4})-(\d{2})', month_str)
    if m:
        return (int(m.group(1)), int(m.group(2)))
    # Try "07/25"
    m = re.match(r'(\d{2})/(\d{2})', month_str)
    if m:
        return (2000 + int(m.group(2)), int(m.group(1)))
    return (9999, 99)
def process_extracted_data(point, opcode, month_mapping):
    """
    Handles chronological ordering and month assignment for extracted data.
    Ensures correct mapping to mon1, mon2, etc.
    """
    extracted_data = point.get('extracted_data', {})
    extracted_values = {}
    temp_data = {}

    # --- FIX: Check for extracted_data['extracted_data'] as a list ---
    if isinstance(extracted_data, dict) and 'extracted_data' in extracted_data and isinstance(extracted_data['extracted_data'], list):
        for row in extracted_data['extracted_data']:
            if isinstance(row, dict):
                extracted_opcode = row.get('opcode')
                if extracted_opcode == opcode:
                    month_str = row.get('month', '')
                    if month_str and month_str not in temp_data:
                        temp_data[month_str] = {}

                    # Only process known fields
                    field_mappings = {
                        'workmix': ('workmix', lambda x: float(x.replace('%', '')) if isinstance(x, str) and '%' in x else float(x)),
                        'jobcount': ('job_count', lambda x: int(float(x)) if x else 0),
                        'grossprofit': ('gp_percentage', lambda x: float(x.replace('%', '')) if isinstance(x, str) and '%' in x else float(x)),
                        'effectivelabourrate': ('elr', lambda x: float(x.replace('$', '')) if isinstance(x, str) and '$' in x else float(x)),
                        'soldhours': ('labor_hours', lambda x: float(x) if x else 0)
                    }

                    for field, (chart_type_mapped, converter) in field_mappings.items():
                        if field in row and row[field] is not None:
                            try:
                                extracted_value = converter(row[field])
                                temp_data[month_str][chart_type_mapped] = extracted_value
                            except (ValueError, AttributeError):
                                pass

    if not temp_data:
        return extracted_values

    # Sort months chronologically
    sorted_months = sorted(temp_data.keys(), key=parse_month_for_sorting)
    # Create month key mapping based on chronological order
    sorted_month_mapping = {}
    for i, month_str in enumerate(sorted_months, 1):
        sorted_month_mapping[month_str] = f"mon{i}"

    # Assign values to proper month keys
    for month_str, month_data in temp_data.items():
        # Try to match with input month_mapping, fallback to sorted mapping
        month_key = find_matching_month_key(month_str, month_mapping)
        if not month_key:
            month_key = sorted_month_mapping[month_str]
        for chart_type, value in month_data.items():
            if chart_type not in extracted_values:
                extracted_values[chart_type] = {}
            extracted_values[chart_type][month_key] = value

    return extracted_values
def find_matching_month_key(month_str, month_mapping):
    """
    Find the matching month key from month_mapping based on various month string formats
    
    Handles formats like:
    - "01/25", "02/25", ..., "12/25" 
    - "2025-01", "2025-02", ..., "2025-12"
    - "Jan-25", "Feb-25", ..., "Dec-25"
    - "January 2025", "February 2025", ..., "December 2025"
    """
    
    # Month name mappings
    month_names = {
        '01': ['Jan', 'January', '01'],
        '02': ['Feb', 'February', '02'],
        '03': ['Mar', 'March', '03'],
        '04': ['Apr', 'April', '04'],
        '05': ['May', 'May', '05'],
        '06': ['Jun', 'June', '06'],
        '07': ['Jul', 'July', '07'],
        '08': ['Aug', 'August', '08'],
        '09': ['Sep', 'September', '09'],
        '10': ['Oct', 'October', '10'],
        '11': ['Nov', 'November', '11'],
        '12': ['Dec', 'December', '12']
    }
    
    # Extract month number from various formats
    extracted_month_num = None
    
    # Format: "MM/YY" (e.g., "07/25")
    if '/' in month_str and len(month_str.split('/')) == 2:
        month_part = month_str.split('/')[0].zfill(2)
        if month_part in month_names:
            extracted_month_num = month_part
    
    # Format: "YYYY-MM" (e.g., "2025-07")
    elif '-' in month_str and len(month_str.split('-')) == 2 and month_str.split('-')[0].isdigit():
        month_part = month_str.split('-')[1].zfill(2)
        if month_part in month_names:
            extracted_month_num = month_part
    
    # Format: "Mon-YY" (e.g., "Jul-25")
    elif '-' in month_str and len(month_str.split('-')) == 2 and not month_str.split('-')[0].isdigit():
        month_name = month_str.split('-')[0]
        for month_num, names in month_names.items():
            if month_name in names:
                extracted_month_num = month_num
                break
    
    # Format: "Month YYYY" (e.g., "July 2025")
    elif ' ' in month_str:
        month_name = month_str.split(' ')[0]
        for month_num, names in month_names.items():
            if month_name in names:
                extracted_month_num = month_num
                break
    
    # Direct month name or number
    else:
        for month_num, names in month_names.items():
            if month_str in names or month_str.zfill(2) == month_num:
                extracted_month_num = month_num
                break
    
    # Now find matching key in month_mapping
    if extracted_month_num:
        month_names_for_num = month_names[extracted_month_num]
        
        for month_key_in_mapping, mapping_value in month_mapping.items():
            # Check if any variant of the month name matches
            for month_variant in month_names_for_num:
                if (month_variant in month_key_in_mapping or 
                    extracted_month_num in month_key_in_mapping or
                    month_key_in_mapping.startswith(month_variant)):
                    return mapping_value
    
    # log_info(f"No matching month key found for: {month_str}")
    return None


# Alternative comprehensive matching function for edge cases
def find_matching_month_key_comprehensive(month_str, month_mapping):
    """
    More comprehensive month matching that handles various edge cases
    """
    import re
    
    # Normalize the input month string
    month_str_clean = month_str.strip().lower()
    
    # Create comprehensive month patterns
    month_patterns = {
        'january': ['01', 'jan', 'january'],
        'february': ['02', 'feb', 'february'],
        'march': ['03', 'mar', 'march'],
        'april': ['04', 'apr', 'april'],
        'may': ['05', 'may'],
        'june': ['06', 'jun', 'june'],
        'july': ['07', 'jul', 'july'],
        'august': ['08', 'aug', 'august'],
        'september': ['09', 'sep', 'september', 'sept'],
        'october': ['10', 'oct', 'october'],
        'november': ['11', 'nov', 'november'],
        'december': ['12', 'dec', 'december']
    }
    
    # Extract month from various formats
    detected_month = None
    
    # Try to match patterns
    for standard_month, variants in month_patterns.items():
        for variant in variants:
            # Check if variant appears in the month string
            if (variant in month_str_clean or 
                month_str_clean.startswith(variant) or
                re.search(rf'\b{variant}\b', month_str_clean)):
                detected_month = standard_month
                break
        if detected_month:
            break
    
    # If still no match, try numeric extraction
    if not detected_month:
        # Extract numbers from the string
        numbers = re.findall(r'\d+', month_str)
        for num in numbers:
            if 1 <= int(num) <= 12:
                month_num = str(int(num)).zfill(2)
                for standard_month, variants in month_patterns.items():
                    if month_num in variants:
                        detected_month = standard_month
                        break
            if detected_month:
                break
    
    # Match against month_mapping keys
    if detected_month:
        for mapping_key, mapping_value in month_mapping.items():
            mapping_key_clean = mapping_key.lower()
            
            # Check if detected month matches mapping key
            if (detected_month in mapping_key_clean or
                any(variant in mapping_key_clean for variant in month_patterns[detected_month])):
                return mapping_value
    
    # log_info(f"No comprehensive match found for: {month_str}")
    return None

def organize_ui_chart_data(chart_data_list):
    month_mapping = extract_months_from_data(chart_data_list)
    organized_data = {}

    for point in chart_data_list:
        pd = point.get('point_data', {})
        chart_title = point.get('chart_title', '')
        opcode = point.get('opcode') or pd.get('opcode') or pd.get('category')
        main_category = point.get('main_category') or pd.get('main_category') or pd.get('opcategory')
        sub_category = point.get('sub_category') or pd.get('sub_category') or pd.get('subcategory')
        series_name = point.get('series_name') or pd.get('series_name') or pd.get('series')
        if not opcode or not series_name:
            continue
        # Make OTHER unique per main category and subcategory
        if opcode == "OTHER":
            if sub_category:
                unique_key = f"OTHER_{main_category}_{sub_category}"
            else:
                unique_key = f"OTHER_{main_category}"
        else:
            unique_key = opcode
        chart_type = identify_chart_type(chart_title)
        month_key = month_mapping.get(series_name)
        if not chart_type or not month_key:
            continue
        value = extract_value_from_point(point, chart_type)
        if unique_key not in organized_data:
            organized_data[unique_key] = {
                'main_category': main_category,
                'opcategory': main_category,
                'data': {},
                'extracted_data': {}
            }
        if chart_type not in organized_data[unique_key]['data']:
            organized_data[unique_key]['data'][chart_type] = {}
        organized_data[unique_key]['data'][chart_type][month_key] = value

        # --- FIX: Process extracted_data ---
        extracted_values = process_extracted_data(point, opcode, month_mapping)
        for chart_type_mapped, month_values in extracted_values.items():
            if chart_type_mapped not in organized_data[unique_key]['extracted_data']:
                organized_data[unique_key]['extracted_data'][chart_type_mapped] = {}
            for month_key_ext, extracted_value in month_values.items():
                organized_data[unique_key]['extracted_data'][chart_type_mapped][month_key_ext] = extracted_value

    return organized_data, month_mapping

def create_individual_opcode_entry(opcode, opcode_data, available_months):
    data = opcode_data['data']
    extracted_data = opcode_data.get('extracted_data', {})
    entry = {"opcode": opcode}

    for month_key in available_months:
        workmix = data.get('workmix', {}).get(month_key, 0)
        job_count = int(data.get('job_count', {}).get(month_key, 0))
        gp_percentage = data.get('gp_percentage', {}).get(month_key, 0)
        elr = data.get('elr', {}).get(month_key, 0)
        labor_hours = data.get('labor_hours', {}).get(month_key, 0)

        extracted_workmix = extracted_data.get('workmix', {}).get(month_key, 0)
        extracted_job_count = int(extracted_data.get('job_count', {}).get(month_key, 0))
        extracted_gp_percentage = extracted_data.get('gp_percentage', {}).get(month_key, 0)
        extracted_elr = extracted_data.get('elr', {}).get(month_key, 0)
        extracted_labor_hours = extracted_data.get('labor_hours', {}).get(month_key, 0)

        labor_sale = labor_hours * elr
        labor_cost = labor_sale * (1 - gp_percentage / 100) if gp_percentage > 0 else 0

        entry.update({
            f"workmix_{month_key}": workmix,
            f"job_count_{month_key}": job_count,
            f"gp_percentage_{month_key}": gp_percentage,
            f"elr_{month_key}": elr,
            f"labor_hours_{month_key}": labor_hours,
            f"labor_sale_{month_key}": labor_sale,
            f"labor_cost_{month_key}": labor_cost,
            f"extracted_workmix_{month_key}": extracted_workmix,
            f"extracted_job_count_{month_key}": extracted_job_count,
            f"extracted_gp_percentage_{month_key}": extracted_gp_percentage,
            f"extracted_elr_{month_key}": extracted_elr,
            f"extracted_labor_hours_{month_key}": extracted_labor_hours
        })

    return entry
def calculate_others_summary(others_opcodes, available_months):
    """
    Calculate summary statistics for 'others' opcodes with dynamic months
    """
    if not others_opcodes:
        summary = {}
        for month_key in available_months:
            summary.update({
                f"workmix_{month_key}": 0,
                f"job_count_{month_key}": 0,
                f"gp_percentage_{month_key}": 0,
                f"elr_{month_key}": 0,
                f"labor_hours_{month_key}": 0,
                f"labor_sale_{month_key}": 0,
                f"labor_cost_{month_key}": 0
            })
        return summary
    
    summary = {}
    
    for month_key in available_months:
        totals = {
            "workmix": 0, "job_count": 0, "labor_hours": 0, 
            "labor_sale": 0, "gp_weighted_sum": 0
        }
        
        # Sum values for this month
        for opcode_data in others_opcodes:
            data = opcode_data['data']
            workmix = data.get('workmix', {}).get(month_key, 0)
            job_count = data.get('job_count', {}).get(month_key, 0)
            labor_hours = data.get('labor_hours', {}).get(month_key, 0)
            elr = data.get('elr', {}).get(month_key, 0)
            gp_percentage = data.get('gp_percentage', {}).get(month_key, 0)
            
            totals["workmix"] += workmix
            totals["job_count"] += job_count
            totals["labor_hours"] += labor_hours
            totals["labor_sale"] += labor_hours * elr
            totals["gp_weighted_sum"] += gp_percentage * labor_hours
        
        # Calculate averages
        avg_elr = totals["labor_sale"] / totals["labor_hours"] if totals["labor_hours"] > 0 else 0
        avg_gp = totals["gp_weighted_sum"] / totals["labor_hours"] if totals["labor_hours"] > 0 else 0
        labor_cost = totals["labor_sale"] * (1 - avg_gp / 100) if avg_gp > 0 else 0
        
        summary.update({
            f"workmix_{month_key}": totals["workmix"],
            f"job_count_{month_key}": int(totals["job_count"]),
            f"gp_percentage_{month_key}": avg_gp,
            f"elr_{month_key}": avg_elr,
            f"labor_hours_{month_key}": totals["labor_hours"],
            f"labor_sale_{month_key}": totals["labor_sale"],
            f"labor_cost_{month_key}": labor_cost
        })
    
    return summary

def categorize_opcodes_by_type(organized_data, available_months, threshold=2.0):
    """
    Categorize opcodes into COMPETITIVE, MAINTENANCE, and REPAIR
    """
    categories = {
        "COMPETITIVE": {"individual_opcodes": [], "others": []},
        "MAINTENANCE": {"individual_opcodes": [], "others": []},
        "REPAIR": {"individual_opcodes": [], "others": []}
    }
    
    # Category mapping
    category_mapping = {
        "COMP": "COMPETITIVE",
        "COMPETITIVE": "COMPETITIVE",
        "MAINT": "MAINTENANCE", 
        "MAINTENANCE": "MAINTENANCE",
        "REPAIR": "REPAIR"
    }
    
    for opcode, opcode_data in organized_data.items():
        main_category = opcode_data.get('main_category', 'MAINT')
        db_category = category_mapping.get(main_category, "MAINTENANCE")
        
        # Check if opcode meets threshold for individual listing
        meets_threshold = False
        for month_key in available_months:
            workmix = opcode_data['data'].get('workmix', {}).get(month_key, 0)
            if workmix >= threshold:
                meets_threshold = True
                break
        
        if meets_threshold:
            # Individual opcode
            entry = create_individual_opcode_entry(opcode, opcode_data, available_months)
            categories[db_category]["individual_opcodes"].append(entry)
        else:
            # Add to others
            categories[db_category]["others"].append(opcode_data)
    
    # Calculate others_summary for each category
    for category_name in categories:
        if categories[category_name]["others"]:
            categories[category_name]["others_summary"] = calculate_others_summary(
                categories[category_name]["others"], available_months
            )
        else:
            # Empty others summary
            others_summary = {}
            for month_key in available_months:
                others_summary.update({
                    f"workmix_{month_key}": 0,
                    f"job_count_{month_key}": 0,
                    f"gp_percentage_{month_key}": 0,
                    f"elr_{month_key}": 0,
                    f"labor_hours_{month_key}": 0,
                    f"labor_sale_{month_key}": 0,
                    f"labor_cost_{month_key}": 0
                })
            categories[category_name]["others_summary"] = others_summary
        
        # Remove the others list as it's not needed in final output
        del categories[category_name]["others"]
    
    return categories

def calculate_category_totals(category_data, available_months):
    """
    Calculate total metrics for a category with dynamic months
    """
    individual_opcodes = category_data.get("individual_opcodes", [])
    others_summary = category_data.get("others_summary", {})
    
    total_metrics = {}
    
    for month_key in available_months:
        totals = {
            "workmix": 0, "job_count": 0, "labor_hours": 0,
            "labor_sale": 0, "labor_cost": 0
        }
        
        # Sum individual opcodes
        for opcode in individual_opcodes:
            totals["workmix"] += opcode.get(f"workmix_{month_key}", 0)
            totals["job_count"] += opcode.get(f"job_count_{month_key}", 0)
            totals["labor_hours"] += opcode.get(f"labor_hours_{month_key}", 0)
            totals["labor_sale"] += opcode.get(f"labor_sale_{month_key}", 0)
            totals["labor_cost"] += opcode.get(f"labor_cost_{month_key}", 0)
        
        # Add others summary
        totals["workmix"] += others_summary.get(f"workmix_{month_key}", 0)
        totals["job_count"] += others_summary.get(f"job_count_{month_key}", 0)
        totals["labor_hours"] += others_summary.get(f"labor_hours_{month_key}", 0)
        totals["labor_sale"] += others_summary.get(f"labor_sale_{month_key}", 0)
        totals["labor_cost"] += others_summary.get(f"labor_cost_{month_key}", 0)
        
        # Calculate derived metrics
        gp_percentage = ((totals["labor_sale"] - totals["labor_cost"]) / totals["labor_sale"] * 100) if totals["labor_sale"] > 0 else 0
        elr = totals["labor_sale"] / totals["labor_hours"] if totals["labor_hours"] > 0 else 0
        
        total_metrics.update({
            f"workmix_{month_key}": totals["workmix"],
            f"job_count_{month_key}": int(totals["job_count"]),
            f"gp_percentage_{month_key}": gp_percentage,
            f"elr_{month_key}": elr,
            f"labor_hours_{month_key}": totals["labor_hours"],
            f"labor_sale_{month_key}": totals["labor_sale"],
            f"labor_cost_{month_key}": totals["labor_cost"]
        })
    
    return total_metrics

def transform_chart_data_to_db_format(chart_data_list, store_id=244284397, realm="carriageag", threshold=2.0):
    """
    Main function to transform UI chart data to DB format with dynamic month support
    """
    # Extract and organize data
    organized_data, month_mapping = organize_ui_chart_data(chart_data_list)
    if not organized_data:
        return None

    available_months = list(month_mapping.values())

    # Categorize opcodes
    categories = categorize_opcodes_by_type(organized_data, available_months, threshold)

    # Calculate total metrics for each category
    for category_name, category_data in categories.items():
        category_data["total_metrics"] = calculate_category_totals(category_data, available_months)

    # Extract month names for display
    month_names = {}
    reverse_mapping = {v: k for k, v in month_mapping.items()}
    for month_key in available_months:
        month_pattern = reverse_mapping[month_key]
        month_names[month_key] = month_pattern.replace('-', ' 20')  # Convert "JUN-25" to "JUN 2025"

    # Calculate overall totals for first available month (for compatibility)
    first_month = available_months[0] if available_months else "mon1"
    second_month = available_months[1] if len(available_months) > 1 else "mon2"

    total_ros_month1 = sum(cat["total_metrics"].get(f"job_count_{first_month}", 0) for cat in categories.values())
    total_ros_month2 = sum(cat["total_metrics"].get(f"job_count_{second_month}", 0) for cat in categories.values())

    # Build the final structure
    result = {
        "analysis_info": {
            "target_start_month": "2025-06-01",
            "target_end_month": "2025-07-30", 
            "analysis_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "analysis_type": "labor_work_mix",
            "store_id": store_id,
            "realm": realm,
            "advisor_filter": ["all"],
            "technician_filter": ["all"],
            "customer_pay_types": ["C", "M"],
            "warranty_pay_types": ["W", "F", "E"]
        },
        "target_month_results": {
            "target_month": first_month.replace('mon', '2025-0'),
            "target_month_name": month_names.get(first_month, "Month 1"),
            "month1_period": first_month.replace('mon', '2025-0'),
            "month1_name": month_names.get(first_month, "Month 1"),
            "month2_period": second_month.replace('mon', '2025-0'),
            "month2_name": month_names.get(second_month, "Month 2"),
            "total_ros": total_ros_month1 + total_ros_month2,
            "ro_counts": {
                "customer_pay_ros": total_ros_month1,
                "warranty_ros": 0,
                "internal_ros": 0
            },
            "month1_data": {
                "total_ros": total_ros_month1,
                "ro_counts": {
                    "customer_pay_ros": total_ros_month1,
                    "warranty_ros": 0,
                    "internal_ros": 0
                }
            },
            "month2_data": {
                "total_ros": total_ros_month2,
                "ro_counts": {
                    "customer_pay_ros": total_ros_month2,
                    "warranty_ros": 0,
                    "internal_ros": 0
                }
            },
            "work_mix_analysis": {
                f"total_labor_hours_{first_month}": sum(cat["total_metrics"].get(f"labor_hours_{first_month}", 0) for cat in categories.values()),
                f"total_labor_hours_{second_month}": sum(cat["total_metrics"].get(f"labor_hours_{second_month}", 0) for cat in categories.values()),
                f"total_labor_sale_{first_month}": sum(cat["total_metrics"].get(f"labor_sale_{first_month}", 0) for cat in categories.values()),
                f"total_labor_sale_{second_month}": sum(cat["total_metrics"].get(f"labor_sale_{second_month}", 0) for cat in categories.values()),
                f"total_labor_cost_{first_month}": sum(cat["total_metrics"].get(f"labor_cost_{first_month}", 0) for cat in categories.values()),
                f"total_labor_cost_{second_month}": sum(cat["total_metrics"].get(f"labor_cost_{second_month}", 0) for cat in categories.values()),
                f"total_opcodes_{first_month}": len([op for cat in categories.values() for op in cat["individual_opcodes"] if op.get(f"workmix_{first_month}", 0) > 0]),
                f"total_opcodes_{second_month}": len([op for cat in categories.values() for op in cat["individual_opcodes"] if op.get(f"workmix_{second_month}", 0) > 0]),
                f"opcodes_greater_than_threshold_{first_month}": len([op for cat in categories.values() for op in cat["individual_opcodes"] if op.get(f"workmix_{first_month}", 0) >= threshold]),
                f"opcodes_greater_than_threshold_{second_month}": len([op for cat in categories.values() for op in cat["individual_opcodes"] if op.get(f"workmix_{second_month}", 0) >= threshold]),
                f"opcodes_less_than_threshold_{first_month}": len([op for cat in categories.values() for op in cat["individual_opcodes"] if 0 < op.get(f"workmix_{first_month}", 0) < threshold]),
                f"opcodes_less_than_threshold_{second_month}": len([op for cat in categories.values() for op in cat["individual_opcodes"] if 0 < op.get(f"workmix_{second_month}", 0) < threshold]),
                "work_mix_threshold": threshold,
                "category_breakdown": categories
            }
        }
    }

    return result
def rename_other_opcodes_in_category_breakdown(result):
    """
    Rename opcodes like OTHER_COMP, OTHER_MAINT, OTHER_REPAIR to OTHER
    inside each main category in the result['target_month_results']['work_mix_analysis']['category_breakdown'].
    """
    category_breakdown = result.get('target_month_results', {}).get('work_mix_analysis', {}).get('category_breakdown', {})
    for main_category, cat_data in category_breakdown.items():
        if 'individual_opcodes' in cat_data:
            for opcode_entry in cat_data['individual_opcodes']:
                if isinstance(opcode_entry, dict) and 'opcode' in opcode_entry:
                    if opcode_entry['opcode'].startswith('OTHER_'):
                        opcode_entry['opcode'] = 'OTHER'
# Usage function
def process_chart_data_file(input_file_path, output_file=None, threshold=2.0, debug=False):
    """
    Process chart data from JSON file and transform to DB format
    
    Args:
        input_file_path: Path to input JSON file with chart data
        output_file_path: Optional path to save output
        threshold: Work mix threshold for individual opcodes
        debug: Print debug information
    
    Returns:
        Transformed data in DB format
    """
    with open(input_file_path, 'r') as f:
        chart_data = json.load(f)
    
    if debug:
        # log_info("=== DEBUG INFO ===")
        # Extract unique series names and chart titles
        series_names = set()
        chart_titles = set()
        opcodes = set()
        other_groupings = dict()  # {main_category: set(subcategories)}

        for point in chart_data:
            if isinstance(point, dict) and 'point_data' in point:
                point_data = point['point_data']
                series_name = point_data.get('series_name') or point_data.get('series', '')
                if series_name:
                    series_names.add(series_name)
                
                chart_title = point.get('chart_title', '')
                if chart_title:
                    chart_titles.add(chart_title)
                
                opcode = point_data.get('opcode') or point_data.get('category')
                main_category = point_data.get('main_category') or point_data.get('opcategory')
                sub_category = point_data.get('sub_category') or point_data.get('subcategory') or None

                if opcode and opcode != "OTHER":
                    opcodes.add(opcode)
                elif opcode == "OTHER":
                    # Group OTHER by main_category and sub_category
                    if main_category:
                        if main_category not in other_groupings:
                            other_groupings[main_category] = set()
                        if sub_category:
                            other_groupings[main_category].add(sub_category)
                        else:
                            other_groupings[main_category].add("Unspecified")

        # Print debug info
        print(f"Unique series names found: {sorted(series_names)}")
        print(f"Unique chart titles found: {sorted(chart_titles)}")
        print(f"Unique opcodes found: {sorted(opcodes)}")
        print("Grouped OTHER opcodes by main category and subcategory:")
        for main_cat, subcats in other_groupings.items():
            print(f"  {main_cat}: {sorted(subcats)}")
        print("==================")
    
    result = transform_chart_data_to_db_format(chart_data, threshold=threshold)
    rename_other_opcodes_in_category_breakdown(result)
    result_dir,output_filepath = create_folder_file_path(subfolder="chart_processing_results", output_file= output_file)
    with open(output_filepath, 'w', encoding='utf-8') as json_file:
        json.dump(result, json_file, indent=4, ensure_ascii=False) 
        # log_info(f"Output saved to: {output_file}")
    
    return result

def round_off(n, decimals=0):
    """Round off numbers with proper decimal handling"""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def zero_sales_check(df, columns):
    """Check if all specified columns sum to zero"""
    total_sum = df[columns].sum().sum()
    return total_sum == 0

def get_month_date_range_from_target(target_start_date_str, target_end_date_str):
    """Get the start and end date for the target date range"""
    target_start_date = datetime.strptime(target_start_date_str, "%Y-%m-%d")
    target_end_date = datetime.strptime(target_end_date_str, "%Y-%m-%d")
    
    # Get first day of the target start month
    month_start = target_start_date.replace(day=1)
    
    # Get last day of the target end month
    end_date_start = target_end_date.replace(day=1)
    month_end = end_date_start + relativedelta(months=1) - timedelta(days=1)
    
    return month_start, month_end

def calculate_labor_metrics(total_revenue_CP, suffix=""):
    """Calculate labor metrics from customer pay data following the reference logic"""
    
    if total_revenue_CP is None or total_revenue_CP.empty:
        return None
    
    # Calculate total sold hours
    total_sold_hours_CP = pd.to_numeric(total_revenue_CP['lbrsoldhours']).fillna(0).sum()
    log_info("11111111",total_sold_hours_CP)
    if total_sold_hours_CP == 0:
        return None
    
    # Group by lbropcode and opcategory
    work_mix_result = total_revenue_CP.groupby(['lbropcode', 'opcategory']).agg({
        'lbrsoldhours': 'sum',
        'lbrsale': 'sum',
        'lbrcost': 'sum'
    }).reset_index()
    
    # Calculate metrics for each opcode
    for index, row in work_mix_result.iterrows():
        lbr_sold_hours = row['lbrsoldhours']
        lbr_revenue = row['lbrsale']
        lbr_cost = row['lbrcost']
        lbr_opcode = row['lbropcode']
        
        # Work mix percentage
        work_mix_result.at[index, 'work_mix_percentage'] = (lbr_sold_hours / total_sold_hours_CP) * 100
        
        # GP percentage
        work_mix_result.at[index, 'gp_percentage'] = 0
        if lbr_revenue != 0:
            work_mix_result.at[index, 'gp_percentage'] = round_off((((lbr_revenue - lbr_cost) / lbr_revenue) * 100), 2)
        
        # ELR
        work_mix_result.at[index, 'elr'] = 0
        if lbr_sold_hours != 0:
            work_mix_result.at[index, 'elr'] = round_off((lbr_revenue / lbr_sold_hours), 2)
        
        # Job count
        work_mix_result.at[index, 'job_count'] = 0
        if lbr_opcode.strip() in set(total_revenue_CP['lbropcode'].str.strip()):
            work_mix_result.at[index, 'job_count'] = len(total_revenue_CP[total_revenue_CP['lbropcode'] == lbr_opcode.strip()]['ronumber'])
    
    # Split by threshold (2%)
    work_mix_threshold = 2.0
    work_mix_less_than_threshold = work_mix_result[work_mix_result['work_mix_percentage'] <= work_mix_threshold]
    work_mix_greater_than_threshold = work_mix_result[work_mix_result['work_mix_percentage'] > work_mix_threshold]
    
    return {
        'total_labor_hours': total_sold_hours_CP,
        'total_labor_sale': pd.to_numeric(total_revenue_CP['lbrsale']).fillna(0).sum(),
        'total_labor_cost': pd.to_numeric(total_revenue_CP['lbrcost']).fillna(0).sum(),
        'work_mix_result': work_mix_result,
        'work_mix_less_than_threshold': work_mix_less_than_threshold,
        'work_mix_greater_than_threshold': work_mix_greater_than_threshold,
        'work_mix_threshold': work_mix_threshold
    }

def process_single_month(df, m_start, m_end, month_label, advisor, tech, customer_pay_types, warranty_pay_types, columns_to_check):
    """Process data for a single month following the reference logic"""
    log_info("m_start",m_start)
    log_info("m_end",m_end)
    if hasattr(m_start, 'date'):
        m_start = m_start.date()
    if hasattr(m_end, 'date'):
        m_end = m_end.date()
    
    # Filter data for the specific target month
    month_data = df[
        (df['closeddate'] >= m_start) &
        (df['closeddate'] <= m_end)
    ]
    
    if month_data.empty:
        return None, None
    
    columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']

    # Replace empty strings or spaces with NaN
    month_data.loc[:, columns_to_convert] = month_data[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)

    # Convert to numeric
    month_data[columns_to_convert] = month_data[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))
        
    # Apply filtering logic
    filtered_df = month_data[
    (month_data['department'] == 'Service') & 
    (month_data['hide_ro'] != True)
    ].copy()
    
    if filtered_df.empty:
        return None, None
    
    filtered_df = filtered_df.copy()
    filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)
    

    # Group assignment logic
    combined_revenue_details = filtered_df.copy()
    combined_revenue_details['group'] = pd.Series(dtype="string")
    
    # Set specific columns to 0 if opcategory is 'N/A'
    combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    
    # Iterate through each unique RO number for group assignment
    for ro_number in combined_revenue_details['unique_ro_number'].unique():
        ro_specific_rows = combined_revenue_details[combined_revenue_details['unique_ro_number'] == ro_number]
        
        ro_specific_rows_C = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(customer_pay_types)]
        ro_specific_rows_W = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(warranty_pay_types)]
        zero_sales_C = 0
        zero_sales_W = 0
        zero_sales_C = zero_sales_check(ro_specific_rows_C, columns_to_check)
        zero_sales_W = zero_sales_check(ro_specific_rows_W, columns_to_check)
        
        if not ro_specific_rows_C.empty and not zero_sales_C:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'C'
        elif not ro_specific_rows_W.empty and not zero_sales_W:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'W'
        else:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'I'
    
    # Apply filters based on advisor and tech conditions
    if advisor == {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details['unique_ro_number'].unique()
    elif advisor != {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[
            combined_revenue_details['serviceadvisor'].astype(str).isin(advisor), 
            'unique_ro_number'
        ].unique()
    elif advisor == {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[
            combined_revenue_details['lbrtechno'].astype(str).isin(tech), 
            'unique_ro_number'
        ].unique()
    elif advisor != {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[
            (combined_revenue_details['serviceadvisor'].astype(str).isin(advisor)) & 
            (combined_revenue_details['lbrtechno'].astype(str).isin(tech)), 
            'unique_ro_number'
        ].unique()
    
    # Apply the Advisor and tech filter conditions
    combined_revenue_details = combined_revenue_details[combined_revenue_details['unique_ro_number'].isin(matching_ro_numbers)]
    combined_revenue_details = combined_revenue_details.reset_index(drop=True)
    combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    
    # RO Counts
    Scorecard_10_CP = combined_revenue_details.loc[combined_revenue_details['group'] == 'C', 'unique_ro_number'].nunique()
    Scorecard_10_Wty = combined_revenue_details.loc[combined_revenue_details['group'] == 'W', 'unique_ro_number'].nunique()
    Scorecard_10_Int = combined_revenue_details.loc[combined_revenue_details['group'] == 'I', 'unique_ro_number'].nunique()
    all_unique_ros = Scorecard_10_CP + Scorecard_10_Wty + Scorecard_10_Int
    
    # Filter CP job details for labor analysis
    list_of_paytypegroup_C = combined_revenue_details[
        combined_revenue_details['paytypegroup'].isin(customer_pay_types) & 
        (combined_revenue_details['group'] == 'C')
    ]
    
    total_revenue_CP = pd.DataFrame(list_of_paytypegroup_C)
    total_revenue_CP.loc[total_revenue_CP['opcategory'] == 'N/A', columns_to_check] = 0
    # Remove rows where all key columns are zero
    total_revenue_CP = total_revenue_CP[
        ~((total_revenue_CP['lbrsale'].fillna(0) == 0) &
          (total_revenue_CP['lbrsoldhours'].fillna(0) == 0) &
          (total_revenue_CP['prtextendedsale'].fillna(0) == 0) &
          (total_revenue_CP['prtextendedcost'].fillna(0) == 0))
    ]
    
    return total_revenue_CP, {
        "total_ros": all_unique_ros,
        "ro_counts": {
            "customer_pay_ros": Scorecard_10_CP,
            "warranty_ros": Scorecard_10_Wty,
            "internal_ros": Scorecard_10_Int
        }
    }

def process_labor_workmix_data(all_revenue_details_df, month_start, month_end, advisor, tech, retail_flag, 
                                customer_pay_types, warranty_pay_types, columns_to_check):
    """Process labor work mix data following the reference logic"""
    
    # Convert month_start and month_end to datetime if they're strings
    if isinstance(month_start, str):
        month_start = pd.to_datetime(month_start)
    if isinstance(month_end, str):
        month_end = pd.to_datetime(month_end)
    
    # Calculate the date ranges for both months
    # Month 1: First month in the range
    month1_start = month_start
    month1_period = pd.Period(month1_start.strftime('%Y-%m'), 'M')
    month1_end = month1_period.end_time.normalize()
    
    # Month 2: Second month in the range  
    if month_start.month == month_end.month and month_start.year == month_end.year:
        # Single month case - duplicate for comparison
        month2_start = month1_start
        month2_end = month1_end
    else:
        # Two month case
        if month_start.month == 12:
            month2_start = pd.to_datetime(f"{month_start.year + 1}-01-01")
        else:
            month2_start = pd.to_datetime(f"{month_start.year}-{month_start.month + 1:02d}-01")
        month2_end = month_end
    
    # Process both months
    total_revenue_CP_mon1, ro_data_mon1 = process_single_month(
        all_revenue_details_df, month1_start, month1_end, "Month 1", 
        advisor, tech, customer_pay_types, warranty_pay_types, columns_to_check
    )
    
    total_revenue_CP_mon2, ro_data_mon2 = process_single_month(
        all_revenue_details_df, month2_start, month2_end, "Month 2", 
        advisor, tech, customer_pay_types, warranty_pay_types, columns_to_check
    )
    
    # Calculate metrics for both months
    labor_metrics_mon1 = calculate_labor_metrics(total_revenue_CP_mon1, "_mon1")
    labor_metrics_mon2 = calculate_labor_metrics(total_revenue_CP_mon2, "_mon2")
    
    # Initialize return structure
    work_mix_analysis = {}
    
    if labor_metrics_mon1 or labor_metrics_mon2:
        # Categories to process
        opcategory_list = ['COMPETITIVE', 'MAINTENANCE', 'REPAIR']
        
        # Build detailed analysis results
        detailed_results = {}
        
        for category in opcategory_list:
            category_data = {
                'individual_opcodes': [],
                'others_summary': {},
                'total_metrics': {}
            }
            
            # Get opcodes that appear in either month
            opcodes_mon1 = set()
            opcodes_mon2 = set()
            
            if labor_metrics_mon1:
                category_greater_than_threshold_mon1 = labor_metrics_mon1['work_mix_greater_than_threshold'][
                    labor_metrics_mon1['work_mix_greater_than_threshold']['opcategory'] == category
                ]
                opcodes_mon1 = set(category_greater_than_threshold_mon1['lbropcode'].tolist())
            
            if labor_metrics_mon2:
                category_greater_than_threshold_mon2 = labor_metrics_mon2['work_mix_greater_than_threshold'][
                    labor_metrics_mon2['work_mix_greater_than_threshold']['opcategory'] == category
                ]
                opcodes_mon2 = set(category_greater_than_threshold_mon2['lbropcode'].tolist())
            
            all_opcodes = opcodes_mon1.union(opcodes_mon2)
            
            # Process individual opcodes (> threshold% work mix in either month)
            for opcode in all_opcodes:
                opcode_data = {'opcode': opcode}
                
                # Get data for month 1
                if labor_metrics_mon1:
                    mon1_data = labor_metrics_mon1['work_mix_greater_than_threshold'][
                        (labor_metrics_mon1['work_mix_greater_than_threshold']['lbropcode'] == opcode) &
                        (labor_metrics_mon1['work_mix_greater_than_threshold']['opcategory'] == category)
                    ]
                    if not mon1_data.empty:
                        row = mon1_data.iloc[0]
                        opcode_data.update({
                            'workmix_mon1': round_off(row['work_mix_percentage'], 2),
                            'job_count_mon1': int(row['job_count']),
                            'gp_percentage_mon1': round_off(row['gp_percentage'], 2),
                            'elr_mon1': round_off(row['elr'], 2),
                            'labor_hours_mon1': round_off(row['lbrsoldhours'], 2),
                            'labor_sale_mon1': round_off(row['lbrsale'], 2),
                            'labor_cost_mon1': round_off(row['lbrcost'], 2)
                        })
                    else:
                        opcode_data.update({
                            'workmix_mon1': 0, 'job_count_mon1': 0, 'gp_percentage_mon1': 0,
                            'elr_mon1': 0, 'labor_hours_mon1': 0, 'labor_sale_mon1': 0, 'labor_cost_mon1': 0
                        })
                else:
                    opcode_data.update({
                        'workmix_mon1': 0, 'job_count_mon1': 0, 'gp_percentage_mon1': 0,
                        'elr_mon1': 0, 'labor_hours_mon1': 0, 'labor_sale_mon1': 0, 'labor_cost_mon1': 0
                    })
                
                # Get data for month 2
                if labor_metrics_mon2:
                    mon2_data = labor_metrics_mon2['work_mix_greater_than_threshold'][
                        (labor_metrics_mon2['work_mix_greater_than_threshold']['lbropcode'] == opcode) &
                        (labor_metrics_mon2['work_mix_greater_than_threshold']['opcategory'] == category)
                    ]
                    if not mon2_data.empty:
                        row = mon2_data.iloc[0]
                        opcode_data.update({
                            'workmix_mon2': round_off(row['work_mix_percentage'], 2),
                            'job_count_mon2': int(row['job_count']),
                            'gp_percentage_mon2': round_off(row['gp_percentage'], 2),
                            'elr_mon2': round_off(row['elr'], 2),
                            'labor_hours_mon2': round_off(row['lbrsoldhours'], 2),
                            'labor_sale_mon2': round_off(row['lbrsale'], 2),
                            'labor_cost_mon2': round_off(row['lbrcost'], 2)
                        })
                    else:
                        opcode_data.update({
                            'workmix_mon2': 0, 'job_count_mon2': 0, 'gp_percentage_mon2': 0,
                            'elr_mon2': 0, 'labor_hours_mon2': 0, 'labor_sale_mon2': 0, 'labor_cost_mon2': 0
                        })
                else:
                    opcode_data.update({
                        'workmix_mon2': 0, 'job_count_mon2': 0, 'gp_percentage_mon2': 0,
                        'elr_mon2': 0, 'labor_hours_mon2': 0, 'labor_sale_mon2': 0, 'labor_cost_mon2': 0
                    })
                
                category_data['individual_opcodes'].append(opcode_data)
            
            # Process "OTHERS" category (<= threshold% work mix)
            others_data = {}
            
            # Month 1 others
            if labor_metrics_mon1:
                category_less_than_threshold_mon1 = labor_metrics_mon1['work_mix_less_than_threshold'][
                    labor_metrics_mon1['work_mix_less_than_threshold']['opcategory'] == category
                ]
                if not category_less_than_threshold_mon1.empty:
                    others_labor_hours_mon1 = category_less_than_threshold_mon1['lbrsoldhours'].sum()
                    others_labor_sale_mon1 = category_less_than_threshold_mon1['lbrsale'].sum()
                    others_labor_cost_mon1 = category_less_than_threshold_mon1['lbrcost'].sum()
                    others_job_count_mon1 = category_less_than_threshold_mon1['job_count'].sum()
                    
                    others_data.update({
                        'workmix_mon1': round_off((others_labor_hours_mon1 / labor_metrics_mon1['total_labor_hours']) * 100, 2) if labor_metrics_mon1['total_labor_hours'] != 0 else 0,
                        'job_count_mon1': int(others_job_count_mon1),
                        'gp_percentage_mon1': round_off(((others_labor_sale_mon1 - others_labor_cost_mon1) * 100 / others_labor_sale_mon1), 2) if others_labor_sale_mon1 != 0 else 0,
                        'elr_mon1': round_off(others_labor_sale_mon1 / others_labor_hours_mon1, 2) if others_labor_hours_mon1 != 0 else 0,
                        'labor_hours_mon1': round_off(others_labor_hours_mon1, 1),
                        'labor_sale_mon1': round_off(others_labor_sale_mon1, 2),
                        'labor_cost_mon1': round_off(others_labor_cost_mon1, 2)
                    })
                else:
                    others_data.update({
                        'workmix_mon1': 0, 'job_count_mon1': 0, 'gp_percentage_mon1': 0,
                        'elr_mon1': 0, 'labor_hours_mon1': 0, 'labor_sale_mon1': 0, 'labor_cost_mon1': 0
                    })
            else:
                others_data.update({
                    'workmix_mon1': 0, 'job_count_mon1': 0, 'gp_percentage_mon1': 0,
                    'elr_mon1': 0, 'labor_hours_mon1': 0, 'labor_sale_mon1': 0, 'labor_cost_mon1': 0
                })
            
            # Month 2 others
            if labor_metrics_mon2:
                category_less_than_threshold_mon2 = labor_metrics_mon2['work_mix_less_than_threshold'][
                    labor_metrics_mon2['work_mix_less_than_threshold']['opcategory'] == category
                ]
                if not category_less_than_threshold_mon2.empty:
                    others_labor_hours_mon2 = category_less_than_threshold_mon2['lbrsoldhours'].sum()
                    others_labor_sale_mon2 = category_less_than_threshold_mon2['lbrsale'].sum()
                    others_labor_cost_mon2 = category_less_than_threshold_mon2['lbrcost'].sum()
                    others_job_count_mon2 = category_less_than_threshold_mon2['job_count'].sum()
                    
                    others_data.update({
                        'workmix_mon2': round_off((others_labor_hours_mon2 / labor_metrics_mon2['total_labor_hours']) * 100, 2) if labor_metrics_mon2['total_labor_hours'] != 0 else 0,
                        'job_count_mon2': int(others_job_count_mon2),
                        'gp_percentage_mon2': round_off(((others_labor_sale_mon2 - others_labor_cost_mon2) * 100 / others_labor_sale_mon2), 2) if others_labor_sale_mon2 != 0 else 0,
                        'elr_mon2': round_off(others_labor_sale_mon2 / others_labor_hours_mon2, 2) if others_labor_hours_mon2 != 0 else 0,
                        'labor_hours_mon2': round_off(others_labor_hours_mon2, 1),
                        'labor_sale_mon2': round_off(others_labor_sale_mon2, 2),
                        'labor_cost_mon2': round_off(others_labor_cost_mon2, 2)
                    })
                else:
                    others_data.update({
                        'workmix_mon2': 0, 'job_count_mon2': 0, 'gp_percentage_mon2': 0,
                        'elr_mon2': 0, 'labor_hours_mon2': 0, 'labor_sale_mon2': 0, 'labor_cost_mon2': 0
                    })
            else:
                others_data.update({
                    'workmix_mon2': 0, 'job_count_mon2': 0, 'gp_percentage_mon2': 0,
                    'elr_mon2': 0, 'labor_hours_mon2': 0, 'labor_sale_mon2': 0, 'labor_cost_mon2': 0
                })
            
            if others_data:
                category_data['others_summary'] = others_data
            
            # Calculate total metrics for the category
            total_data = {}
            
            # Month 1 totals
            if labor_metrics_mon1:
                all_category_data_mon1 = labor_metrics_mon1['work_mix_result'][
                    labor_metrics_mon1['work_mix_result']['opcategory'] == category
                ]
                if not all_category_data_mon1.empty:
                    total_category_labor_hours_mon1 = all_category_data_mon1['lbrsoldhours'].sum()
                    total_category_labor_sale_mon1 = all_category_data_mon1['lbrsale'].sum()
                    total_category_labor_cost_mon1 = all_category_data_mon1['lbrcost'].sum()
                    total_category_job_count_mon1 = all_category_data_mon1['job_count'].sum()
                    
                    total_data.update({
                        'workmix_mon1': round_off((total_category_labor_hours_mon1 / labor_metrics_mon1['total_labor_hours']) * 100, 2) if labor_metrics_mon1['total_labor_hours'] != 0 else 0,
                        'job_count_mon1': int(total_category_job_count_mon1),
                        'gp_percentage_mon1': round_off(((total_category_labor_sale_mon1 - total_category_labor_cost_mon1) * 100 / total_category_labor_sale_mon1), 2) if total_category_labor_sale_mon1 != 0 else 0,
                        'elr_mon1': round_off(total_category_labor_sale_mon1 / total_category_labor_hours_mon1, 2) if total_category_labor_hours_mon1 != 0 else 0,
                        'labor_hours_mon1': round_off(total_category_labor_hours_mon1, 2),
                        'labor_sale_mon1': round_off(total_category_labor_sale_mon1, 2),
                        'labor_cost_mon1': round_off(total_category_labor_cost_mon1, 2)
                    })
                else:
                    total_data.update({
                        'workmix_mon1': 0, 'job_count_mon1': 0, 'gp_percentage_mon1': 0,
                        'elr_mon1': 0, 'labor_hours_mon1': 0, 'labor_sale_mon1': 0, 'labor_cost_mon1': 0
                    })
            else:
                total_data.update({
                    'workmix_mon1': 0, 'job_count_mon1': 0, 'gp_percentage_mon1': 0,
                    'elr_mon1': 0, 'labor_hours_mon1': 0, 'labor_sale_mon1': 0, 'labor_cost_mon1': 0
                })
            
            # Month 2 totals
            if labor_metrics_mon2:
                all_category_data_mon2 = labor_metrics_mon2['work_mix_result'][
                    labor_metrics_mon2['work_mix_result']['opcategory'] == category
                ]
                if not all_category_data_mon2.empty:
                    total_category_labor_hours_mon2 = all_category_data_mon2['lbrsoldhours'].sum()
                    total_category_labor_sale_mon2 = all_category_data_mon2['lbrsale'].sum()
                    total_category_labor_cost_mon2 = all_category_data_mon2['lbrcost'].sum()
                    total_category_job_count_mon2 = all_category_data_mon2['job_count'].sum()
                    
                    total_data.update({
                        'workmix_mon2': round_off((total_category_labor_hours_mon2 / labor_metrics_mon2['total_labor_hours']) * 100, 2) if labor_metrics_mon2['total_labor_hours'] != 0 else 0,
                        'job_count_mon2': int(total_category_job_count_mon2),
                        'gp_percentage_mon2': round_off(((total_category_labor_sale_mon2 - total_category_labor_cost_mon2) * 100 / total_category_labor_sale_mon2), 2) if total_category_labor_sale_mon2 != 0 else 0,
                        'elr_mon2': round_off(total_category_labor_sale_mon2 / total_category_labor_hours_mon2, 2) if total_category_labor_hours_mon2 != 0 else 0,
                        'labor_hours_mon2': round_off(total_category_labor_hours_mon2, 2),
                        'labor_sale_mon2': round_off(total_category_labor_sale_mon2, 2),
                        'labor_cost_mon2': round_off(total_category_labor_cost_mon2, 2)
                    })
                else:
                    total_data.update({
                        'workmix_mon2': 0, 'job_count_mon2': 0, 'gp_percentage_mon2': 0,
                        'elr_mon2': 0, 'labor_hours_mon2': 0, 'labor_sale_mon2': 0, 'labor_cost_mon2': 0
                    })
            else:
                total_data.update({
                    'workmix_mon2': 0, 'job_count_mon2': 0, 'gp_percentage_mon2': 0,
                    'elr_mon2': 0, 'labor_hours_mon2': 0, 'labor_sale_mon2': 0, 'labor_cost_mon2': 0
                })
            
            category_data['total_metrics'] = total_data
            detailed_results[category] = category_data
        
        # Compile the complete work mix analysis
        work_mix_analysis = {
            'total_labor_hours_mon1': round_off(labor_metrics_mon1.get('total_labor_hours', 0) if labor_metrics_mon1 else 0, 2),
            'total_labor_hours_mon2': round_off(labor_metrics_mon2.get('total_labor_hours', 0) if labor_metrics_mon2 else 0, 2),
            'total_labor_sale_mon1': round_off(labor_metrics_mon1.get('total_labor_sale', 0) if labor_metrics_mon1 else 0, 2),
            'total_labor_sale_mon2': round_off(labor_metrics_mon2.get('total_labor_sale', 0) if labor_metrics_mon2 else 0, 2),
            'total_labor_cost_mon1': round_off(labor_metrics_mon1.get('total_labor_cost', 0) if labor_metrics_mon1 else 0, 2),
            'total_labor_cost_mon2': round_off(labor_metrics_mon2.get('total_labor_cost', 0) if labor_metrics_mon2 else 0, 2),
            'total_opcodes_mon1': len(labor_metrics_mon1.get('work_mix_result', [])) if labor_metrics_mon1 else 0,
            'total_opcodes_mon2': len(labor_metrics_mon2.get('work_mix_result', [])) if labor_metrics_mon2 else 0,
            'opcodes_greater_than_threshold_mon1': len(labor_metrics_mon1.get('work_mix_greater_than_threshold', [])) if labor_metrics_mon1 else 0,
            'opcodes_greater_than_threshold_mon2': len(labor_metrics_mon2.get('work_mix_greater_than_threshold', [])) if labor_metrics_mon2 else 0,
            'opcodes_less_than_threshold_mon1': len(labor_metrics_mon1.get('work_mix_less_than_threshold', [])) if labor_metrics_mon1 else 0,
            'opcodes_less_than_threshold_mon2': len(labor_metrics_mon2.get('work_mix_less_than_threshold', [])) if labor_metrics_mon2 else 0,
            'work_mix_threshold': labor_metrics_mon1.get('work_mix_threshold', 2.0) if labor_metrics_mon1 else 2.0,
            'category_breakdown': detailed_results
        }
    else:
        work_mix_analysis = {
            'total_labor_hours_mon1': 0,
            'total_labor_hours_mon2': 0,
            'total_labor_sale_mon1': 0,
            'total_labor_sale_mon2': 0,
            'total_labor_cost_mon1': 0,
            'total_labor_cost_mon2': 0,
            'total_opcodes_mon1': 0,
            'total_opcodes_mon2': 0,
            'opcodes_greater_than_threshold_mon1': 0,
            'opcodes_greater_than_threshold_mon2': 0,
            'opcodes_less_than_threshold_mon1': 0,
            'opcodes_less_than_threshold_mon2': 0,
            'work_mix_threshold': 2.0,
            'category_breakdown': {}
        }
    
    # Return enhanced result set
    return {
        "target_month": month1_start.strftime('%Y-%m'),
        "target_month_name": month1_start.strftime('%B %Y'),
        "month1_period": month1_start.strftime('%Y-%m'),
        "month1_name": month1_start.strftime('%B %Y'),
        "month2_period": month2_start.strftime('%Y-%m'),
        "month2_name": month2_start.strftime('%B %Y'),
        "total_ros": ro_data_mon1["total_ros"] if ro_data_mon1 else 0,
        "ro_counts": ro_data_mon1["ro_counts"] if ro_data_mon1 else {"customer_pay_ros": 0, "warranty_ros": 0, "internal_ros": 0},
        "month1_data": ro_data_mon1 if ro_data_mon1 else {"total_ros": 0, "ro_counts": {"customer_pay_ros": 0, "warranty_ros": 0, "internal_ros": 0}},
        "month2_data": ro_data_mon2 if ro_data_mon2 else {"total_ros": 0, "ro_counts": {"customer_pay_ros": 0, "warranty_ros": 0, "internal_ros": 0}},
        "work_mix_analysis": work_mix_analysis
    }
def db_execution_labor_workmix(target_start_date_str, target_end_date_str, advisor, tech, retail_flag, columns_to_check, month_start, month_end):
    """
    Handle database operations and execute labor work mix processing
    """      
    try:
        customer_pay_types = {}
        warranty_pay_types = {}
        log_info(f"Labor work mix month range: {month_start} to {month_end}")       
        
        # Get target month date range
        month_start, month_end = get_month_date_range_from_target(target_start_date_str, target_end_date_str)        
        log_info(f"Labor work mix target month range: {month_start.date()} to {month_end.date()}")       

        # Fetch all data from database
        log_info("Fetching labor work mix data from database...")
        all_revenue_details_table_db_connect = workMixComparisonTableResult()
        all_revenue_details_df = all_revenue_details_table_db_connect.getTableResult()        
        log_info("Error -------")
        if all_revenue_details_df.empty:
            log_info("ERROR: No data retrieved from database for labor work mix!")
            return False       
        
        # Convert date column and other preprocessing
        columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']

        # Replace empty strings or spaces with NaN
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)

        # Convert to numeric
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))

        # Define customer and warranty pay types based on retail_flag
        if 'C' in retail_flag and 'E' not in retail_flag and 'M' not in retail_flag:
            customer_pay_types = {'C'}
            warranty_pay_types = {'W', 'F', 'M', 'E'}
        elif 'C' in retail_flag and 'E' not in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'M'}
            warranty_pay_types = {'W', 'F', 'E'}
        elif 'C' in retail_flag and 'E' in retail_flag and 'M' not in retail_flag:
            customer_pay_types = {'C', 'E'}
            warranty_pay_types = {'W', 'F', 'M'}
        elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'E', 'M'}
            warranty_pay_types = {'W', 'F'}       
        log_info(f"month_start: {month_start}, month_end: {month_end}")

        target_month_result = process_labor_workmix_data(
            all_revenue_details_df, 
            month_start.date(), 
            month_end.date(),          
            advisor, 
            tech, 
            retail_flag, 
            customer_pay_types, 
            warranty_pay_types, 
            columns_to_check
        ) 
        
        return target_month_result, customer_pay_types, warranty_pay_types        
        
    except Exception as e:
        log_info(f"ERROR in db_execution_labor_workmix: {str(e)}")
        log_info("=" * 60)
        log_info("LABOR WORK MIX DATABASE EXECUTION FAILED")
        log_info("=" * 60)
        return None, None, None


def db_calculation_labor_workmix():
    """
    Main execution function for labor work mix calculation
    """    
    # Configuration variables
    columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
    retail_flag = {'C','M','E'}    
    storeid = config.store_id
    realm = config.database_name   
    advisor_set = config.advisor
    tech_set = config.technician 
    month_start, month_end = convert_config_months(config)
  
    
    # Process advisor configuration
    if isinstance(advisor_set, str):
        if advisor_set.lower() == 'all':
            advisor = {'all'}
        elif ',' in advisor_set:
            advisor = {x.strip() for x in advisor_set.split(',')}
        else:
            advisor = {advisor_set.strip()}
    else:
        advisor = {'all'}
       
    
    # Process technician configuration
    if isinstance(tech_set, str):
        if tech_set.lower() == 'all':
            tech = {'all'}
        elif ',' in tech_set:
            tech = {x.strip() for x in tech_set.split(',')}
        else:
            tech = {tech_set.strip()}
    else:
        tech = {'all'}   
    
    # Execute database operations and processing
    target_start_date_str = month_start
    target_end_date_str = month_end
    target_month_result, customer_pay_types, warranty_pay_types = db_execution_labor_workmix(
        target_start_date_str, target_end_date_str, advisor, tech, retail_flag, columns_to_check, month_start, month_end
    )    
    
    # Process results
    if target_month_result:
        log_info("\n" + "=" * 80)
        log_info("LABOR WORK MIX RESULTS PROCESSING")
        log_info("=" * 80)        
        
        # Create the final result set for the target months
        final_result_set = {
            "analysis_info": {
                "target_start_month": target_start_date_str,
                "target_end_month": target_end_date_str,
                "analysis_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "analysis_type": "labor_work_mix",
                "store_id": storeid,
                "realm": realm,
                "advisor_filter": list(advisor),
                "technician_filter": list(tech),
                "customer_pay_types": list(customer_pay_types),
                "warranty_pay_types": list(warranty_pay_types)
            },
            "target_month_results": target_month_result
        }     
        
        # Write results to JSON file
        result_dir,output_filename = create_folder_file_path(subfolder="chart_processing_results", output_file=db_json)                            
        
        with open(output_filename, 'w', encoding='utf-8') as json_file:
            json.dump(final_result_set, json_file, indent=4, ensure_ascii=False)        
        
        log_info(f"\nTarget month Labor Work Mix data written successfully to {output_filename}")        
        
        # Display summary
        work_mix = target_month_result['work_mix_analysis']
        log_info(f"\nLabor Work Mix Summary for {target_month_result['month1_name']} vs {target_month_result['month2_name']}:")

        log_info(f"\nLabor Work Mix Summary:")
        log_info(f"  Total Labor Hours Month1: {work_mix['total_labor_hours_mon1']}")
        log_info(f"  Total Labor Hours Month2: {work_mix['total_labor_hours_mon2']}")
        log_info(f"  Total Labor Sale Month1: ${work_mix['total_labor_sale_mon1']}")
        log_info(f"  Total Labor Sale Month2: ${work_mix['total_labor_sale_mon2']}")
        log_info(f"  Total Opcodes Month1: {work_mix['total_opcodes_mon1']}")
        log_info(f"  Total Opcodes Month2: {work_mix['total_opcodes_mon2']}")
        log_info(f"  Month1 - Opcodes > {work_mix['work_mix_threshold']}%: {work_mix['opcodes_greater_than_threshold_mon1']}")
        log_info(f"  Month2 - Opcodes > {work_mix['work_mix_threshold']}%: {work_mix['opcodes_greater_than_threshold_mon2']}")
        log_info(f"  Month1 - Opcodes <= {work_mix['work_mix_threshold']}%: {work_mix['opcodes_less_than_threshold_mon1']}")
        log_info(f"  Month2 - Opcodes <= {work_mix['work_mix_threshold']}%: {work_mix['opcodes_less_than_threshold_mon2']}")
        
        # Display category breakdowns
        for category, data in work_mix['category_breakdown'].items():
            log_info(f"\n  {category} Category:")
            log_info(f"    Individual Opcodes: {len(data['individual_opcodes'])}")
            if data['total_metrics']:
                log_info(f"    Month1 - Total Work Mix %: {data['total_metrics']['workmix_mon1']}%")
                log_info(f"    Month2 - Total Work Mix %: {data['total_metrics']['workmix_mon2']}%")
                log_info(f"    Month1 - Total Job Count: {data['total_metrics']['job_count_mon1']}")
                log_info(f"    Month2 - Total Job Count: {data['total_metrics']['job_count_mon2']}")
                log_info(f"    Month1 - Total GP %: {data['total_metrics']['gp_percentage_mon1']}%")
                log_info(f"    Month2 - Total GP %: {data['total_metrics']['gp_percentage_mon2']}%")
                log_info(f"    Month1 - Total ELR: ${data['total_metrics']['elr_mon1']}")
                log_info(f"    Month2 - Total ELR: ${data['total_metrics']['elr_mon2']}")
        
        log_info(f"\n  Month1 Total ROs: {target_month_result['month1_data']['total_ros']}")
        log_info(f"    - Customer Pay ROs: {target_month_result['month1_data']['ro_counts']['customer_pay_ros']}")
        log_info(f"    - Warranty ROs: {target_month_result['month1_data']['ro_counts']['warranty_ros']}")
        log_info(f"    - Internal ROs: {target_month_result['month1_data']['ro_counts']['internal_ros']}")
        
        log_info(f"\n  Month2 Total ROs: {target_month_result['month2_data']['total_ros']}")
        log_info(f"    - Customer Pay ROs: {target_month_result['month2_data']['ro_counts']['customer_pay_ros']}")
        log_info(f"    - Warranty ROs: {target_month_result['month2_data']['ro_counts']['warranty_ros']}")
        log_info(f"    - Internal ROs: {target_month_result['month2_data']['ro_counts']['internal_ros']}")
        
    else:
        log_info("\n" + "=" * 80)
        log_info("NO LABOR WORK MIX DATA RESULTS PROCESSING")
        log_info("=" * 80)        
        
        log_info(f"No data available for labor work mix target months {target_start_date_str} to {target_end_date_str}")   
    
    log_info("\n" + "=" * 80)
    log_info("LABOR WORK MIX ANALYSIS - MAIN EXECUTION COMPLETED")
    log_info("=" * 80)

def convert_month_format(month_str):
    """
    Convert month format from 'Jun-25' to '2024-06-01' (first day) or '2024-06-30' (last day)
    
    Args:
        month_str: String in format 'Jun-25' or 'Jul-25'
    
    Returns:
        tuple: (first_day, last_day) in format 'YYYY-MM-DD'
    """
    try:
        # Parse the input string
        month_part, year_part = month_str.split('-')
        
        # Convert 2-digit year to 4-digit year
        if len(year_part) == 2:
            year = 2000 + int(year_part)
        else:
            year = int(year_part)
        
        # Convert month abbreviation to month number
        month_mapping = {
            'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
            'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
        }
        
        month = month_mapping.get(month_part, 1)
        
        # Get first day of month
        first_day = f"{year}-{month:02d}-01"
        
        # Get last day of month
        last_day_num = calendar.monthrange(year, month)[1]
        last_day = f"{year}-{month:02d}-{last_day_num:02d}"
        
        return first_day, last_day
        
    except Exception as e:
        log_info(f"Error converting {month_str}: {e}")
        return None, None 


def convert_config_months(config):
    """
    Convert config month formats from 'Jun-25' to 'YYYY-MM-DD' format
    
    Args:
        config: Configuration object with month1 and month2 attributes
    
    Returns:
        tuple: (month_start, month_end) in 'YYYY-MM-DD' format
    """
    # Convert start month (first day of the month)
    start_first, start_last = convert_month_format(config.month1)
    month_start = start_first
    
    # Convert end month (last day of the month)
    end_first, end_last = convert_month_format(config.month2)
    month_end = end_last
    
    return month_start, month_end

   
class MultiChartParallelProcessor:
    """Process multiple charts in parallel with separate browsers"""
    
    def __init__(self, max_browsers=MAX_CONCURRENT_BROWSERS, auth_manager=None):
        self.max_browsers = max_browsers
        self.auth_manager = auth_manager or AuthManager(config)
        self.charts_info = None
    


    def get_months_between(start_date_str, end_date_str):
        """Return a list of months between two dates in 'Mon-YY' format."""
        start = datetime.strptime(start_date_str, "%Y-%m-%d")
        end = datetime.strptime(end_date_str, "%Y-%m-%d")
        months = []
        current = start.replace(day=1)
        while current <= end:
            months.append(current.strftime("%b-%y")) 
            current += relativedelta(months=1)
        return months


    async def discover_charts(self):
        """Discover all charts on the LaborWorkMixAnalysis-2Months-Comparison"""
        log_info("Discovering charts on LaborWorkMixAnalysis-2Months-Comparison...")
        
        if not await self.auth_manager.check_session_and_relogin_parallel():

            log_error("Initial session check failed. Cannot proceed.")

        page = await self.auth_manager.new_page() 
        try:
                    await page.goto(f"{config.site_url.rstrip('/')}/LaborWorkMixAnalysis", timeout=50000)
                    await page.click('button[role="tab"]:has-text("2 Month Work Mix Comparison")')
                    await page.click('label:has-text("Month 1") + div [role="button"]')
                    await page.click(f'li[role="option"]:has-text("{config.month1}")')
    
                    await page.click('label:has-text("Month 2") + div [role="button"]')
                    await page.click(f'li[role="option"]:has-text("{config.month2}")')
                    chart_found = False
                    selectors_to_try = [
                        
                        # SVG-based charts (Highcharts, D3.js, etc.)
                        'svg.highcharts-root',
                        'svg[class*="highcharts"]',
                        'svg[class*="chart"]',
                        'div[class*="highcharts-container"]',
                        'svg',
        
                    ]

                    for selector in selectors_to_try:
                        try:
                            log_info(f" Checking charts using selector: {selector}")
                            await page.wait_for_selector(selector, timeout=5000)
                            chart_found = True
                            log_info(f" Found charts using selector: {selector}")
                            break
                        except:
                            continue
                    if not chart_found:
                        log_info("No chart elements found with any selector")
                        return False
                
                    await asyncio.sleep(2)
                    
                    charts_info = await page.evaluate("""
                        () => {
                            const chartsInfo = [];
                            let chartIndex = 0;
                            const processedElements = new Set(); // Track processed elements to avoid duplicates
                            
                            // Function to extract chart ID from various sources
                            function extractChartId(element) {
                                let chartId = null;
                                
                                // Method 1: From data-highcharts-chart attribute
                                const highchartsChart = element.getAttribute('data-highcharts-chart');
                                if (highchartsChart) {
                                    chartId = highchartsChart;
                                }
                                
                                // Method 2: From Highcharts container class name pattern
                                const containerMatch = element.className.match && element.className.match(/chartid-(\d+)/);
                                if (containerMatch) {
                                    chartId = containerMatch[1];
                                }
                                
                                // Method 3: From button IDs in the same container
                                const container = element.closest('.MuiCard-root, .MuiPaper-root, [id*="highchartDataContainer"]');
                                if (container) {
                                    const buttons = container.querySelectorAll('button[id*="chart-details-"], button[id*="view-details-"]');
                                    for (const button of buttons) {
                                        const idMatch = button.id.match(/(?:chart-details-|view-details-)(\d+)/);
                                        if (idMatch) {
                                            chartId = idMatch[1];
                                            break;
                                        }
                                    }
                                }
                                
                                // Method 4: From parent container ID
                                let parent = element.parentElement;
                                while (parent && !chartId) {
                                    if (parent.id && parent.id.includes('chart')) {
                                        const idMatch = parent.id.match(/(\d+)/);
                                        if (idMatch) {
                                            chartId = idMatch[1];
                                        }
                                    }
                                    parent = parent.parentElement;
                                }
                                
                                return chartId;
                            }
                            
                            // Function to get chart title from various sources
                            function getChartTitle(element, chartId) {
                                let chartTitle = chartId ? `Chart ${chartId}` : 'Unknown Chart';
                                
                                // Method 1: From MUI Card Header
                                const container = element.closest('.MuiCard-root, .MuiPaper-root');
                                if (container) {
                                    const cardHeader = container.querySelector('.MuiCardHeader-title');
                                    if (cardHeader) {
                                        const labels = cardHeader.querySelectorAll('label');
                                        let titleText = '';
                                        labels.forEach(label => {
                                            const text = label.textContent.trim();
                                            if (text && !text.match(/^\d+$/)) {
                                                titleText += text + ' ';
                                            }
                                        });
                                        if (titleText.trim()) {
                                            chartTitle = titleText.trim();
                                        }
                                    }
                                }
                                
                                // Method 2: From Highcharts title (for SVG charts)
                                if (element.tagName && element.tagName.toLowerCase() === 'svg') {
                                    const titleElement = element.querySelector('.highcharts-title');
                                    if (titleElement && titleElement.textContent.trim()) {
                                        chartTitle = titleElement.textContent.trim();
                                    }
                                }
                                
                                // Method 3: From container or parent elements
                                let parent = element.closest('[class*="chart"], .widget, .dashboard-item');
                                if (parent) {
                                    const titleElement = parent.querySelector('h1, h2, h3, h4, h5, h6, .title, [class*="title"]');
                                    if (titleElement && titleElement.textContent.trim()) {
                                        chartTitle = titleElement.textContent.trim();
                                    }
                                }
                                
                                return chartTitle;
                            }
                            
                            // Create a unique identifier for each chart element
                            function getElementSignature(element) {
                                const rect = element.getBoundingClientRect();
                                return `${element.tagName.toLowerCase()}_${Math.round(rect.left)}_${Math.round(rect.top)}_${Math.round(rect.width)}_${Math.round(rect.height)}`;
                            }
                            
                            // 1. Find all Highcharts containers (primary detection)
                            const highchartsContainers = document.querySelectorAll('.highcharts-container, [data-highcharts-chart]');
                            
                            highchartsContainers.forEach((container, index) => {
                                const rect = container.getBoundingClientRect();
                                if (rect.width > 0 && rect.height > 0) {
                                    const signature = getElementSignature(container);
                                    
                                    // Skip if we've already processed this element
                                    if (processedElements.has(signature)) {
                                        return;
                                    }
                                    processedElements.add(signature);
                                    
                                    const chartId = extractChartId(container);
                                    const chartTitle = getChartTitle(container, chartId);
                                    
                                    // Find the SVG within this container
                                    const svg = container.querySelector('svg.highcharts-root, svg[class*="highcharts"]');
                                    
                                    let chartType = 'Unknown';
                                    let seriesCount = 0;
                                    
                                    if (svg) {
                                        // Mark the SVG as processed to avoid duplicate detection
                                        const svgSignature = getElementSignature(svg);
                                        processedElements.add(svgSignature);
                                        
                                        const series = svg.querySelectorAll('.highcharts-series');
                                        seriesCount = series.length;
                                        
                                        // Detect chart type from series
                                        if (svg.querySelector('.highcharts-column-series')) {
                                            chartType = 'Column/Bar';
                                        } else if (svg.querySelector('.highcharts-line-series')) {
                                            chartType = 'Line';
                                        } else if (svg.querySelector('.highcharts-pie-series')) {
                                            chartType = 'Pie';
                                        } else if (svg.querySelector('.highcharts-area-series')) {
                                            chartType = 'Area';
                                        } else if (series.length > 0) {
                                            chartType = 'Mixed/Other';
                                        }
                                    }
                                    
                                    chartsInfo.push({
                                        type: 'highcharts',
                                        chartId: chartId || `highcharts-${chartIndex}`,
                                        chartIndex: chartIndex++,
                                        chartTitle: chartTitle,
                                        chartType: chartType,
                                        seriesCount: seriesCount,
                                        elementId: container.id || `highcharts-container-${index}`,
                                        elementClass: container.className,
                                        position: {
                                            x: Math.round(rect.left),
                                            y: Math.round(rect.top),
                                            width: Math.round(rect.width),
                                            height: Math.round(rect.height)
                                        },
                                        visible: true,
                                        hasInteractiveButtons: !!container.closest('.MuiCard-root')?.querySelector('button[id*="chart-details-"], button[id*="view-details-"]')
                                    });
                                }
                            });
                            
                            // 2. Find standalone Canvas charts (Chart.js, etc.) - only if not inside Highcharts
                            const canvases = document.querySelectorAll('canvas');
                            canvases.forEach((canvas, index) => {
                                const rect = canvas.getBoundingClientRect();
                                
                                // Skip if this canvas is inside a highcharts container (already processed)
                                if (canvas.closest('.highcharts-container, [data-highcharts-chart]')) {
                                    return;
                                }
                                
                                const signature = getElementSignature(canvas);
                                if (processedElements.has(signature)) {
                                    return;
                                }
                                
                                let hasChart = false;
                                
                                // Check for Chart.js specific indicators
                                if (canvas.classList.contains('chartjs-render-monitor') || 
                                    canvas.classList.contains('chartjs-render') ||
                                    (canvas.width > 0 && canvas.height > 0 && rect.width > 100 && rect.height > 100)) {
                                    hasChart = true;
                                }
                                
                                // Try to detect Chart.js instance
                                try {
                                    if (typeof Chart !== 'undefined' && Chart.getChart) {
                                        const chart = Chart.getChart(canvas);
                                        if (chart) {
                                            hasChart = true;
                                        }
                                    }
                                } catch (e) {
                                    // Continue with other checks
                                }
                                
                                if (hasChart && rect.width > 0 && rect.height > 0) {
                                    processedElements.add(signature);
                                    const chartId = extractChartId(canvas);
                                    const chartTitle = getChartTitle(canvas, chartId);
                                    
                                    chartsInfo.push({
                                        type: 'canvas',
                                        chartIndex: chartIndex++,
                                        chartId: chartId || `canvas-${index}`,
                                        chartTitle: chartTitle,
                                        chartType: 'Canvas Chart',
                                        seriesCount: 0,
                                        elementId: canvas.id || `canvas-${index}`,
                                        elementClass: canvas.className,
                                        position: {
                                            x: Math.round(rect.left),
                                            y: Math.round(rect.top),
                                            width: Math.round(rect.width),
                                            height: Math.round(rect.height)
                                        },
                                        visible: true,
                                        isChartJs: canvas.classList.contains('chartjs-render-monitor'),
                                        hasInteractiveButtons: false
                                    });
                                }
                            });
                            
                            // 3. Find standalone SVG charts (not Highcharts) - only if not already processed
                            const svgs = document.querySelectorAll('svg');
                            svgs.forEach((svg, index) => {
                                const rect = svg.getBoundingClientRect();
                                
                                // Skip if this SVG is inside a highcharts container or is a Highcharts SVG
                                if (svg.closest('.highcharts-container, [data-highcharts-chart]') || 
                                    svg.classList.contains('highcharts-root') ||
                                    svg.querySelector('.highcharts-series')) {
                                    return;
                                }
                                
                                const signature = getElementSignature(svg);
                                if (processedElements.has(signature)) {
                                    return;
                                }
                                
                                let hasSvgChart = false;
                                
                                // Check for other chart libraries
                                if (svg.classList.contains('recharts-surface') ||
                                    svg.classList.contains('d3-chart') ||
                                    svg.querySelector('[class*="chart"]') ||
                                    svg.querySelector('g[class*="series"]') ||
                                    svg.querySelector('g[class*="axis"]')) {
                                    hasSvgChart = true;
                                }
                                
                                // General SVG chart detection (more restrictive)
                                if (!hasSvgChart && rect.width > 100 && rect.height > 100) {
                                    const hasAxes = svg.querySelector('g[class*="axis"], line[class*="axis"], path[class*="axis"]');
                                    const hasSeries = svg.querySelector('g[class*="series"], rect[class*="bar"], circle[class*="point"], path[class*="line"]');
                                    const hasGrid = svg.querySelector('g[class*="grid"], line[class*="grid"]');
                                    
                                    if ((hasAxes || hasSeries || hasGrid) && svg.children.length > 5) {
                                        hasSvgChart = true;
                                    }
                                }
                                
                                if (hasSvgChart && rect.width > 0 && rect.height > 0) {
                                    processedElements.add(signature);
                                    const chartId = extractChartId(svg);
                                    const chartTitle = getChartTitle(svg, chartId);
                                    
                                    chartsInfo.push({
                                        type: 'svg',
                                        chartId: chartId || `svg-${index}`,
                                        chartIndex: chartIndex++,
                                        chartTitle: chartTitle,
                                        chartType: 'SVG Chart',
                                        seriesCount: svg.querySelectorAll('g[class*="series"]').length,
                                        elementId: svg.id || `svg-${index}`,
                                        elementClass: svg.className.baseVal || svg.className || '',
                                        position: {
                                            x: Math.round(rect.left),
                                            y: Math.round(rect.top),
                                            width: Math.round(rect.width),
                                            height: Math.round(rect.height)
                                        },
                                        visible: true,
                                        hasInteractiveButtons: false
                                    });
                                }
                            });
                            
                            // Sort by position (top to bottom, left to right)
                            chartsInfo.sort((a, b) => {
                                if (Math.abs(a.position.y - b.position.y) > 10) {
                                    return a.position.y - b.position.y;
                                }
                                return a.position.x - b.position.x;
                            });
                            
                            console.log(`Found ${chartsInfo.length} unique charts total`);
                            return chartsInfo;
                        }
                    """)
                    
                    log_info(f"Found {len(charts_info)} charts on LaborWorkMixAnalysis-2Months-Comparison")
                    log_info(f"Found {charts_info} charts info on LaborWorkMixAnalysis-2Months-Comparison")

                    for chart in charts_info:
                        log_info(f"  - {chart['type'].upper()} Chart {chart['chartIndex']}: {chart['chartTitle']} (Element: {chart['elementId']})")
                        log_info(f"    Type: {chart['chartType']} | Series: {chart['seriesCount']} | Size: {chart['position']['width']}x{chart['position']['height']}")

                    return charts_info
                        
        finally:
                # await context.close()
                await page.close() 
            


   

    async def find_matching_points_in_chart(self, page, chart_index, chart_id, variable, target_month_year):
        """Find matching data points for a specific SVG Highcharts chart with accurate coordinate extraction"""
        try:
            log_info("variable name", variable)

            results = await page.evaluate(
                """
                async (args) => {
                    const { chartIndex, chartId, variable, targetMonthYear} = args;
                    
                    const chartContainer = document.querySelector('.highcharts-container.chartid-' + chartId);
                    if (!chartContainer) {
                        return { 
                            found: false, 
                            chartId: chartId, 
                            error: "Container not found for chartid-" + chartId 
                        };
                    }

                    if (!chartContainer.querySelector('.highcharts-series')) {
                        return { 
                            found: false, 
                            chartId: chartId, 
                            error: "Not a Highcharts chart" 
                        };
                    }

                    // Get SVG root element for accurate coordinate calculations
                    const svgRoot = chartContainer.querySelector('.highcharts-root');
                    if (!svgRoot) {
                        return { found: false, chartId: chartId, error: "SVG root not found" };
                    }

                    // Get chart dimensions and axis information with SVG-specific logic
                    function getAxisInfo() {
                        const plotArea = chartContainer.querySelector('.highcharts-plot-background');
                        const xAxisLabels = Array.from(chartContainer.querySelectorAll('.highcharts-xaxis-labels span'));
                        const yAxisLabels = Array.from(chartContainer.querySelectorAll('.highcharts-yaxis-labels text'));
                        
                        let plotBounds = { x: 0, y: 0, width: 0, height: 0 };
                        if (plotArea) {
                            plotBounds = {
                                x: parseFloat(plotArea.getAttribute('x')) || 0,
                                y: parseFloat(plotArea.getAttribute('y')) || 0,
                                width: parseFloat(plotArea.getAttribute('width')) || 0,
                                height: parseFloat(plotArea.getAttribute('height')) || 0
                            };
                        }

                        // Extract Y-axis scale information
                        const yAxisValues = yAxisLabels.map(label => {
                            const text = label.textContent.trim();
                            const y = parseFloat(label.getAttribute('y')) || 0;
                            const numValue = parseFloat(text.replace(/[,$%]/g, ''));
                            return { text, y, value: isNaN(numValue) ? 0 : numValue };
                        }).filter(item => !isNaN(item.value)).sort((a, b) => b.y - a.y);

                        return { plotBounds, xAxisLabels, yAxisLabels, yAxisValues };
                    }

                    const axisInfo = getAxisInfo();

                    // Function to parse transform attribute and get translation values
                    function getTransformTranslation(transformString) {
                        if (!transformString) return { x: 0, y: 0 };
                        
                        const translateMatch = transformString.match(/translate\\(([^,]+),\\s*([^)]+)\\)/);
                        if (translateMatch) {
                            return {
                                x: parseFloat(translateMatch[1]) || 0,
                                y: parseFloat(translateMatch[2]) || 0
                            };
                        }
                        return { x: 0, y: 0 };
                    }

                    // Function to calculate actual coordinate values for SVG
                    function calculateAxisValues(pointData, axisInfo) {
                        const { plotBounds, yAxisValues } = axisInfo;
                        const xValue = pointData.pointIndex;
                        
                        let yValue = 0;
                        if (yAxisValues.length >= 2) {
                            const relativeY = pointData.absoluteY - plotBounds.y;
                            const plotHeight = plotBounds.height;
                            const relativePosition = 1 - (relativeY / plotHeight);
                            
                            const minAxisValue = Math.min(...yAxisValues.map(av => av.value));
                            const maxAxisValue = Math.max(...yAxisValues.map(av => av.value));
                            
                            yValue = minAxisValue + (relativePosition * (maxAxisValue - minAxisValue));
                            yValue = Math.round(yValue * 100) / 100;
                        } else if (pointData.height > 0) {
                            yValue = Math.max(0, Math.round(((283 - 10 - pointData.absoluteY) / 283) * 30 * 10) / 10);
                        }

                        return {
                            xAxisValue: xValue,
                            yAxisValue: yValue,
                            screenX: pointData.absoluteX,
                            screenY: pointData.absoluteY
                        };
                    }

                    // Get legends for month information
                    const legendItems = Array.from(chartContainer.querySelectorAll('.highcharts-legend-item text'));
                    const legends = legendItems.map(el => el.textContent.trim());
                    
                    function extractMonthsFromLegends(legends) {
                        const monthPattern = /(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\\d{2}/gi;
                        const extractedMonths = [];
                        
                        legends.forEach(legend => {
                            const matches = legend.match(monthPattern);
                            if (matches) {
                                matches.forEach(match => {
                                    if (!extractedMonths.includes(match)) {
                                        extractedMonths.push(match);
                                    }
                                });
                            }
                        });
                        
                        return extractedMonths.sort();
                    }
                    
                    const availableMonths = extractMonthsFromLegends(legends);

                    // Extract and map categories
                    const categorySpans = Array.from(chartContainer.querySelectorAll('.highcharts-axis-labels.highcharts-xaxis-labels span'));
                    const allCategories = categorySpans.map((span, index) => {
                        const titleSpan = span.querySelector('span[title]');
                        if (!titleSpan) return null;
                        return {
                            text: titleSpan.getAttribute('title').trim(),
                            position: index,
                            x: parseFloat(span.style.left) || 0
                        };
                    }).filter(cat => cat !== null);

                    const knownMainCategories = ['COMP', 'MAINT', 'REPAIR'];
                    const categoryMapping = { "COMP": [], "MAINT": [], "REPAIR": [] };
                    
                    const opcategoryPositions = [];
                    allCategories.forEach((category, index) => {
                        if (knownMainCategories.includes(category.text)) {
                            opcategoryPositions.push({
                                name: category.text,
                                position: index
                            });
                        }
                    });
                    opcategoryPositions.sort((a, b) => a.position - b.position);

                    // Assign subcategories to main categories
                    allCategories.forEach((category, index) => {
                        if (knownMainCategories.includes(category.text)) return;

                        let assignedMainCategory = null;
                        
                        for (let i = 0; i < opcategoryPositions.length; i++) {
                            if (index === opcategoryPositions[i].position - 1) {
                                assignedMainCategory = opcategoryPositions[i].name;
                                break;
                            }
                        }
                        
                        if (!assignedMainCategory) {
                            for (let i = 0; i < opcategoryPositions.length; i++) {
                                const currentMainPos = opcategoryPositions[i].position;
                                const nextMainPos = i + 1 < opcategoryPositions.length ? 
                                                opcategoryPositions[i + 1].position : allCategories.length;
                                
                                if (index > currentMainPos && index < nextMainPos) {
                                    let belongsToLeft = true;
                                    if (i + 1 < opcategoryPositions.length && index === opcategoryPositions[i + 1].position - 1) {
                                        belongsToLeft = false;
                                    }
                                    if (belongsToLeft) {
                                        assignedMainCategory = opcategoryPositions[i].name;
                                        break;
                                    }
                                }
                            }
                        }
                        
                        if (!assignedMainCategory && opcategoryPositions.length > 0 && index < opcategoryPositions[0].position) {
                            assignedMainCategory = opcategoryPositions[0].name;
                        }

                        if (assignedMainCategory && !categoryMapping[assignedMainCategory].includes(category.text)) {
                            categoryMapping[assignedMainCategory].push(category.text);
                        }
                    });

                    // Get all chart points with precise SVG coordinate extraction
                    function getAllChartPoints() {
                        const allPointsMetadata = [];
                        let globalPointIndex = 0;
                        
                        const seriesGroups = chartContainer.querySelectorAll('.highcharts-series-group > .highcharts-series');
                        
                        seriesGroups.forEach((seriesEl, seriesIndex) => {
                            const seriesTransform = getTransformTranslation(seriesEl.getAttribute('transform'));
                            
                            let seriesPoints = Array.from(seriesEl.querySelectorAll('.highcharts-point'));
                            
                            if (seriesPoints.length === 0) {
                                seriesPoints = Array.from(seriesEl.querySelectorAll('rect[x][y][width][height]'));
                            }
                            
                            seriesPoints = seriesPoints.filter(point => {
                                const width = parseFloat(point.getAttribute('width')) || 0;
                                const height = parseFloat(point.getAttribute('height')) || 0;
                                return width > 0 && height >= 0;
                            });
                            
                            seriesPoints.sort((a, b) => {
                                const xA = parseFloat(a.getAttribute('x')) || 0;
                                const xB = parseFloat(b.getAttribute('x')) || 0;
                                return xA - xB;
                            });
                            
                            seriesPoints.forEach((pointEl, pointIndex) => {
                                const svgX = parseFloat(pointEl.getAttribute('x')) || 0;
                                const svgY = parseFloat(pointEl.getAttribute('y')) || 0;
                                const svgWidth = parseFloat(pointEl.getAttribute('width')) || 0;
                                const svgHeight = parseFloat(pointEl.getAttribute('height')) || 0;
                                
                                const absoluteX = svgX + seriesTransform.x;
                                const absoluteY = svgY + seriesTransform.y;
                                
                                const centerX = absoluteX + (svgWidth / 2);
                                const centerY = absoluteY + (svgHeight / 2);
                                
                                console.log(`Series ${seriesIndex}, Point ${pointIndex}: SVG(${svgX}, ${svgY}) + Transform(${seriesTransform.x}, ${seriesTransform.y}) = Absolute(${absoluteX}, ${absoluteY}) Center(${centerX}, ${centerY})`);
                                
                                allPointsMetadata.push({
                                    pointEl: pointEl,
                                    seriesIndex: seriesIndex,
                                    pointIndex: pointIndex,
                                    globalIndex: globalPointIndex++,
                                    svgX: svgX,
                                    svgY: svgY,
                                    absoluteX: absoluteX,
                                    absoluteY: absoluteY,
                                    centerX: centerX,
                                    centerY: centerY,
                                    width: svgWidth,
                                    height: svgHeight,
                                    seriesTransform: seriesTransform
                                });
                            });
                        });
                        
                        return allPointsMetadata;
                    }

                    // FIXED: Enhanced tooltip data extraction supporting currency symbols ($, €, £, etc.)
                    function extractTooltipData(pointEl, pointIndex, availableMonths, pointMeta, currentSeriesMonth) {
                        return new Promise((resolve) => {
                            try {
                                const hoverX = pointMeta.centerX;
                                const hoverY = pointMeta.centerY;
                                
                                // Clear existing tooltips
                                chartContainer.querySelectorAll('.highcharts-tooltip, [class*="tooltip"]').forEach(tooltip => {
                                    tooltip.style.display = 'none';
                                    tooltip.style.visibility = 'hidden';
                                });

                                // Trigger hover events
                                const hoverEvents = ['mouseenter', 'mouseover', 'mousemove'];
                                hoverEvents.forEach(eventType => {
                                    const event = new MouseEvent(eventType, {
                                        bubbles: true,
                                        cancelable: true,
                                        view: window,
                                        clientX: hoverX,
                                        clientY: hoverY
                                    });
                                    pointEl.dispatchEvent(event);
                                });
                                
                                // Wait for tooltip to render
                                setTimeout(() => {
                                    let tooltipData = {
                                        success: false,
                                        fullText: '',
                                        rawHTML: '',
                                        value: null,
                                        category: null,
                                        opcategory: null,
                                        opcode: null,
                                        currentSeriesValue: null,
                                        series: null
                                    };
                                    
                                    // Find visible tooltip
                                    let tooltipText = '';
                                    let tooltipHTML = '';
                                    
                                    const allElements = Array.from(
                                        document.querySelectorAll("div.highcharts-tooltip, div.highcharts-label.highcharts-tooltip")
                                    ).filter(el => el.style.visibility === "visible");
                                    
                                    for (let el of allElements) {
                                        tooltipText = el.textContent || '';
                                        tooltipHTML = el.innerHTML || tooltipText;
                                        break;
                                    }

                                    if (tooltipText) {
                                        tooltipData.success = true;
                                        tooltipData.fullText = tooltipText;
                                        tooltipData.rawHTML = tooltipHTML;
                                        
                                        // Extract category information
                                        const categoryPattern1 = tooltipText.match(/([A-Z0-9*\\-_#@!&]+)\\s*,\\s*([A-Z]+)/);
                                        if (categoryPattern1) {
                                            tooltipData.opcode = categoryPattern1[1];
                                            tooltipData.category = categoryPattern1[1];
                                            tooltipData.opcategory = categoryPattern1[2];
                                        }
                                        
                                        // FIXED: Extract value for current series month with currency support
                                        console.log(`Processing tooltip for series: ${currentSeriesMonth}`);
                                        console.log(`Tooltip HTML: ${tooltipHTML}`);
                                        
                                        // Create a temporary DOM element to parse HTML properly
                                        const tempDiv = document.createElement('div');
                                        tempDiv.innerHTML = tooltipHTML;
                                        
                                        // Get all <span> elements within the tooltip
                                        const spanElements = tempDiv.querySelectorAll('span');
                                        
                                        // Look for the span containing the target month
                                        let targetValue = null;
                                        spanElements.forEach(span => {
                                            const spanText = span.textContent || '';
                                            
                                            // Check if this span contains the target month pattern
                                            const monthPattern = new RegExp(`${currentSeriesMonth.replace(/[-]/g, '\\\\-')}:`);
                                            
                                            if (monthPattern.test(spanText)) {
                                                const innerHTML = span.innerHTML;
                                                
                                                // FIXED: Extract content from <b> tag supporting currency symbols
                                                // Pattern matches: $59.33, €59.33, £59.33, ¥59.33, ₹59.33, 59.33, 59.33%, etc.
                                                const bTagPattern = new RegExp(
                                                    `${currentSeriesMonth.replace(/[-]/g, '\\\\-')}:\\\\s*<b>([\\\\$€£¥₹]?)([\\\\d,]+\\\\.?[\\\\d]*)([%]?)</b>`,
                                                    'i'
                                                );
                                                const bMatch = innerHTML.match(bTagPattern);
                                                
                                                if (bMatch) {
                                                    // Extract components: currency symbol (group 1), number (group 2), percentage (group 3)
                                                    const currencySymbol = bMatch[1] || '';
                                                    const numberPart = bMatch[2] || '';
                                                    const percentSymbol = bMatch[3] || '';
                                                    
                                                    // Remove commas and parse the numeric value
                                                    const cleanedValue = numberPart.replace(/,/g, '');
                                                    const numericValue = parseFloat(cleanedValue);
                                                    
                                                    if (!isNaN(numericValue)) {
                                                        targetValue = numericValue;
                                                        console.log(`Successfully extracted value for ${currentSeriesMonth}: ${numericValue} (raw: ${currencySymbol}${numberPart}${percentSymbol})`);
                                                    }
                                                } else {
                                                    // Alternative pattern: Try to match without strict formatting
                                                    const flexiblePattern = new RegExp(
                                                        `${currentSeriesMonth.replace(/[-]/g, '\\\\-')}:\\\\s*<b>([^<]+)</b>`,
                                                        'i'
                                                    );
                                                    const flexMatch = innerHTML.match(flexiblePattern);
                                                    
                                                    if (flexMatch) {
                                                        // Extract and clean the value
                                                        const rawValue = flexMatch[1];
                                                        // Remove all currency symbols, spaces, commas, and percentage signs
                                                        const cleanedValue = rawValue.replace(/[$€£¥₹,%\\s]/g, '');
                                                        const numericValue = parseFloat(cleanedValue);
                                                        
                                                        if (!isNaN(numericValue)) {
                                                            targetValue = numericValue;
                                                            console.log(`Flexible pattern extracted value for ${currentSeriesMonth}: ${numericValue} from "${rawValue}"`);
                                                        }
                                                    }
                                                }
                                            }
                                        });
                                        
                                        // If we found a value using DOM parsing, use it
                                        if (targetValue !== null) {
                                            tooltipData.currentSeriesValue = targetValue;
                                            tooltipData.value = targetValue;
                                        } else {
                                            console.warn(`No value found for series: ${currentSeriesMonth} in tooltip`);
                                            
                                            // Final fallback: direct regex on HTML with strict boundaries
                                            const escapedMonth = currentSeriesMonth.replace(/[-]/g, '\\\\-');
                                            
                                            // Try with currency support
                                            const currencyPattern = new RegExp(
                                                `${escapedMonth}:\\\\s*<b>([\\\\$€£¥₹]?)([\\\\d,]+\\\\.?[\\\\d]*)([%]?)</b>`,
                                                'i'
                                            );
                                            const currencyMatch = tooltipHTML.match(currencyPattern);
                                            
                                            if (currencyMatch) {
                                                const numberPart = currencyMatch[2] || '';
                                                const cleanedValue = numberPart.replace(/,/g, '');
                                                const numericValue = parseFloat(cleanedValue);
                                                
                                                if (!isNaN(numericValue)) {
                                                    tooltipData.currentSeriesValue = numericValue;
                                                    tooltipData.value = numericValue;
                                                    console.log(`Fallback: extracted value ${numericValue} for ${currentSeriesMonth}`);
                                                }
                                            } else {
                                                // Last resort: simple number pattern
                                                const simplePattern = new RegExp(
                                                    `${escapedMonth}:\\\\s*<b>([\\\\d.,]+)</b>`,
                                                    'i'
                                                );
                                                const simpleMatch = tooltipHTML.match(simplePattern);
                                                
                                                if (simpleMatch) {
                                                    const cleanedValue = simpleMatch[1].replace(/[$€£¥₹,%\\s]/g, '');
                                                    const numericValue = parseFloat(cleanedValue);
                                                    
                                                    if (!isNaN(numericValue)) {
                                                        tooltipData.currentSeriesValue = numericValue;
                                                        tooltipData.value = numericValue;
                                                        console.log(`Last resort: extracted value ${numericValue} for ${currentSeriesMonth}`);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    
                                    // Cleanup
                                    const cleanupEvents = ['mouseout', 'mouseleave'];
                                    cleanupEvents.forEach(eventType => {
                                        pointEl.dispatchEvent(new MouseEvent(eventType, { bubbles: true }));
                                    });
                                    
                                    resolve(tooltipData);
                                }, 700);
                                
                            } catch (error) {
                                resolve({
                                    success: false,
                                    error: error.message,
                                    fullText: '',
                                    value: null,
                                    category: null,
                                    opcategory: null,
                                    opcode: null,
                                    currentSeriesValue: null
                                });
                            }
                        });
                    }

                    // Process all points
                    const allPointsMetadata = getAllChartPoints();
                    const tooltipResults = [];
                    
                    for (let i = 0; i < allPointsMetadata.length; i++) {
                        const pointMeta = allPointsMetadata[i];
                        const currentSeriesMonth = legends[pointMeta.seriesIndex] || '';
                        const tooltipData = await extractTooltipData(pointMeta.pointEl, i, availableMonths, pointMeta, currentSeriesMonth);
                        await new Promise(resolve => setTimeout(resolve, 150));
                        
                        tooltipResults.push({
                            ...pointMeta,
                            tooltipData: tooltipData,
                            currentSeriesMonth: currentSeriesMonth
                        });
                    }
                    
                    // Organize into series structure with corrected coordinates
                    const series = [];
                    const seriesElements = chartContainer.querySelectorAll('.highcharts-series.highcharts-column-series.highcharts-tracker');
                    
                    seriesElements.forEach((seriesEl, seriesIndex) => {
                        const points = [];
                        const seriesResults = tooltipResults.filter(r => r.seriesIndex === seriesIndex);
                        const currentSeriesMonth = legends[seriesIndex] || 'Series ' + (seriesIndex + 1);
                        
                        seriesResults.forEach((result, pointIndex) => {
                            const tooltipData = result.tooltipData;
                            
                            // Use the extracted value from tooltip for current series
                            let value = tooltipData.currentSeriesValue;
                            if (value === null && result.height > 0) {
                                value = Math.max(0, Math.round(((283 - 10 - result.absoluteY) / 283) * 30 * 10) / 10);
                            }
                            
                            let category = tooltipData.category;
                            let opcategory = tooltipData.opcategory;
                            let opcode = tooltipData.opcode;
                            
                            if (!category && pointIndex < allCategories.length) {
                                const catInfo = allCategories[pointIndex];
                                category = catInfo.text;
                                
                                if (knownMainCategories.includes(catInfo.text)) {
                                    opcategory = catInfo.text;
                                } else {
                                    opcode = catInfo.text;
                                    for (let mainCat of Object.keys(categoryMapping)) {
                                        if (categoryMapping[mainCat].includes(catInfo.text)) {
                                            opcategory = mainCat;
                                            break;
                                        }
                                    }
                                }
                            }
                            
                            // Calculate corrected axis values
                            const axisValues = calculateAxisValues(result, axisInfo);
                            
                            // Build month data structure using only the current series month
                            const monthData = {};
                            if (tooltipData.currentSeriesValue !== null) {
                                monthData[`${variable}1Value`] = tooltipData.currentSeriesValue;
                                monthData[`month1Label`] = currentSeriesMonth;
                            }
                            
                            // Build point data with corrected coordinates
                            points.push({
                                category: category,
                                opcategory: opcategory,
                                opcode: opcode,
                                series: currentSeriesMonth,
                                value: value,
                                ...monthData,
                                xAxisValue: axisValues.xAxisValue,
                                yAxisValue: axisValues.yAxisValue,
                                screenX: axisValues.screenX,
                                screenY: axisValues.screenY,
                                centerX: result.centerX,
                                centerY: result.centerY,
                                tooltipText: tooltipData.fullText,
                                tooltipSuccess: tooltipData.success,
                                pointIndex: pointIndex,
                                globalIndex: result.globalIndex,
                                seriesIndex: seriesIndex
                            });
                        });
                        
                        series.push({
                            name: currentSeriesMonth,
                            points: points
                        });
                    });

                    return {
                        found: true,
                        chartId: chartId,
                        library: "Highcharts",
                        series: series,
                        totalPointsProcessed: allPointsMetadata.length,
                        successfulTooltips: tooltipResults.filter(r => r.tooltipData.success).length,
                        categoryMapping: categoryMapping,
                        legends: legends,
                        availableMonths: availableMonths,
                        axisInfo: {
                            plotBounds: axisInfo.plotBounds,
                            yAxisValues: axisInfo.yAxisValues,
                            xAxisLabelsCount: axisInfo.xAxisLabels.length,
                            yAxisLabelsCount: axisInfo.yAxisLabels.length
                        }
                    };
                }
                """,
                {
                    "chartIndex": chart_index,
                    "chartId": chart_id, 
                    "variable": variable, 
                    "targetMonthYear": target_month_year,
                }
            )
                
            return results
                    
        except Exception as e:
            log_info(f"Error finding matching points in chart {chart_index}: {str(e)}")
            return []
            
    async def create_chart_point_combinations(self, target_months_years):
        """Create combinations of charts and their matching points"""
        log_info("Creating chart-point combinations...")

        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []

        # Get a NEW PAGE from the existing session.
        page = await self.auth_manager.new_page()
            
        try:
            # Navigate to CPOverview
            await page.goto(f"{config.site_url.rstrip('/')}/LaborWorkMixAnalysis", timeout=50000)
            await page.click('button:has-text("2 Month Work Mix Comparison")', timeout=500000)
            await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
            # # await page.wait_for_load_state("networkidle", timeout=50000)
            await asyncio.sleep(2)
            await page.click('label:has-text("Month 1") + div [role="button"]')
            await page.click(f'li[role="option"]:has-text("{config.month1}")')

            await page.click('label:has-text("Month 2") + div [role="button"]')
            await page.click(f'li[role="option"]:has-text("{config.month2}")')
            
            # Discover all charts
            charts_info = await self.discover_charts()
            if not charts_info:
                log_info(" No charts found")
                # Delete auth_state.json file when no charts are found
                auth_state_path = "auth_state.json"
                try:
                    if os.path.exists(auth_state_path):
                        os.remove(auth_state_path)
                        log_info(f"Deleted {auth_state_path} due to no charts found")
                    else:
                        log_info(f"{auth_state_path} not found to delete")
                except Exception as e:
                    log_info(f" Error deleting {auth_state_path}: {e}")
                return []
            
            chart_point_combinations = []
            charts_with_points = []
            
            # For each chart, find matching points for each target month-year
            for chart_info in charts_info:
                chart_index = chart_info['chartIndex']
                container_id = chart_info.get('containerId', '')
                chart_id =  chart_info['chartId']
                chart_title = chart_info.get('chartTitle', f'Chart {chart_id}')
                log_info(f"Processing Chart {chart_id}: {chart_title}")
                
                # Dynamic chart mapping with static prefixes and flexible month patterns
                chart_mapping_patterns = {
                    "Labor Work Mix %": "workmix",
                    "Job Count": "job_count", 
                    "Gross Profit%": "gp_percentage",
                    "Effective Labor Rate": "elr",
                    "Sold Hours": "labor_hours"
                }
                
                # Function to find matching variable name for dynamic chart titles
                def get_variable_for_chart_title(chart_title, mapping_patterns):
                    """
                    Match chart title with variable name using pattern matching
                    Handles dynamic month-year suffixes like "- Jun-25 vs Jul-25"
                    """
                    import re
                    for pattern, var_name in mapping_patterns.items():
                        # Check if the chart title starts with the pattern
                        if chart_title.startswith(pattern):
                            # Additional validation: check if it contains month pattern after the base title
                            remaining_part = chart_title[len(pattern):].strip()
                            
                            # Pattern to match " - Mon-YY vs Mon-YY" format
                            month_pattern = r'^\s*-\s*[A-Za-z]{3}-\d{2}\s+vs\s+[A-Za-z]{3}-\d{2}$'
                            
                            if remaining_part == "":
                                # Exact match (no suffix)
                                return var_name
                            elif re.match(month_pattern, remaining_part):
                                # Pattern match with month suffix
                                return var_name
                                
                    return None
                
                # Find the variable name for current chart title
                variable = get_variable_for_chart_title(chart_title, chart_mapping_patterns)
                
                if variable:
                    log_info(f"  Matched chart title '{chart_title}' to variable '{variable}'")
                else:
                    log_info(f"  No matching variable found for chart title: '{chart_title}'")
                    continue  # Skip this chart if no matching variable found
                
                chart_total_points = 0
                chart_combinations = []
                
                # Process each target month-year for this chart
                for target_month_year in target_months_years:
                    log_info(f"  📅 Looking for data points matching: {target_month_year}")
                    
                    # Find matching points for this chart and target month-year
                    matching_points = await self.find_matching_points_in_chart(
                        page, chart_index, chart_id, variable, target_month_year
                    )
                                    
                    log_info(f"  Found {len(matching_points) if matching_points else 0} matching points for {target_month_year}")
                    log_info(json.dumps(matching_points, indent=2))
                    
                    if matching_points:
                        # Create combination for this chart and target month-year
                        combination = {
                            'chart_index': f"chart_{chart_index}",
                            'chart_id': chart_id,
                            'chart_info': chart_info,
                            'target_month_year': target_month_year,
                            'matching_points': matching_points,
                            'processing_status': 'pending',
                            'points_count': len(matching_points),
                            'variable_name': variable  # Add variable name for reference
                        }
                        chart_combinations.append(combination)
                        chart_total_points += len(matching_points)
                        log_info(f"   Added combination: Chart {chart_index} - {target_month_year} ({len(matching_points)} points)")
                    else:
                        log_info(f"   No matching points found for Chart {chart_index} - {target_month_year}")
                
                # Track charts with their point counts
                if chart_combinations:
                    charts_with_points.append({
                        'chart_index': chart_index,
                        'chart_id': chart_id,
                        'chart_title': chart_title,
                        'total_points': chart_total_points,
                        'combinations': chart_combinations,
                        'variable_name': variable
                    })
            
            # Sort charts by total points (descending) to get charts with most points first
            charts_with_points.sort(key=lambda x: x['total_points'], reverse=True)
            
            # Take all charts and their combinations (all 12 combinations)
            for chart_data in charts_with_points:
                chart_point_combinations.extend(chart_data['combinations'])
                log_info(f"  Added Chart {chart_data['chart_index']}: {chart_data['chart_title']} ({chart_data['total_points']} points)")
            
            log_info(f"\nSummary: Created {len(chart_point_combinations)} chart-point combinations from {len(charts_with_points)} charts")
            
            # log_info summary by chart
            chart_summary = {}
            for combo in chart_point_combinations:
                chart_id = combo['chart_id']
                if chart_id not in chart_summary:
                    chart_summary[chart_id] = 0
                chart_summary[chart_id] += 1
            
            for chart_id, count in chart_summary.items():
                log_info(f"  {chart_id}: {count} combinations")
            
            return chart_point_combinations
            
        except Exception as e:
            log_info(f" Error creating chart-point combinations: {str(e)}")
            return []
        
        finally:
            await page.close()
    

    async def extract_data_from_drilldown_page(self, page, point_data, target_month_year, title):
        """Extract AG-Grid data from drilldown page and return only extracted data for specified column"""
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                log_info(f"Extracting drill-down page data for '{title}'... (Attempt {attempt + 1}/{max_retries})")

                # Wait for page to load completely
                await asyncio.sleep(2)

                # Wait for AG-Grid to load
                await page.wait_for_selector('.ag-root-wrapper', timeout=50000)
                log_info("AG-Grid detected on page")

                # Find the target month row to expand
                target_month_found = False
                ag_rows = await page.query_selector_all('.ag-center-cols-container .ag-row')
                log_info(f"Found {len(ag_rows)} AG-Grid rows")
                
                for row_index, row in enumerate(ag_rows):
                    try:
                        row_text = await row.text_content()
                        log_info(f"Row text: {row_text}")
                        
                        tooltip_month = point_data["series"]
                        
                        def convert_month_label(label: str) -> str:
                            """Convert 'Oct-24' → '10/24'."""
                            return datetime.strptime(label, "%b-%y").strftime("%m/%y") 
                        
                        short_label = convert_month_label(tooltip_month)
                        log_info(f"Target month: {short_label}")
                        
                        # Check if this row contains the target month/year
                        if short_label in row_text:
                            target_month_found = True
                            log_info(f"Target month found in row {row_index}")
                            
                            # Look for expand/collapse button in this row
                            expand_button = await row.query_selector('.ag-group-contracted')
                            if expand_button and await expand_button.is_visible():
                                log_info(f"Expanding row {row_index}")
                                await expand_button.click()
                                await asyncio.sleep(2)
                                log_info(f"Row expanded successfully for {title}")
                            else:
                                # Check if already expanded
                                expanded_button = await row.query_selector('.ag-group-expanded')
                                if expanded_button and not await expanded_button.has_class('ag-hidden'):
                                    log_info(f"Row {row_index} is already expanded")
                            break
                            
                    except Exception as row_error:
                        log_info(f"Error processing row {row_index}: {row_error}")
                        continue
                
                if not target_month_found:
                    log_info(f"Target month not found in any rows")
                    continue

                # Define target columns
                target_columns = {
                    'workmix': ['workmix', 'work_mix', 'work mix'],
                    'jobcount': ['jobcount', 'job_count', 'job count'],
                    'grossprofit': ['grossprofitpercentage', 'grossprofitpercentage', 'grossprofitpercentage','laborgp%'],
                    'effectivelabourrate': ['elr', 'effective_labour_rate'],
                    'soldhours': ['laborsoldhours', 'laborsoldhours', 'soldhours','sold hours']
                }
                
                # Find the specific column mapping based on title
                column_mapping = None
                title1 = re.sub(r"-\s*[A-Za-z]{3}-\d{2}\s+vs\s+[A-Za-z]{3}-\d{2}", "", title)
                target_column_key = title1.lower().replace(' ', '').replace('_', '')
                
                # Map common title variations to our target columns
                title_mappings = {
                    'laborworkmix%': 'workmix',
                    'work_mix': 'workmix', 
                    'workmixpercentage': 'workmix',
                    'jobcount': 'jobcount',
                    'JobCount': 'jobcount',
                    'grossprofit%': 'grossprofit',
                    'GrossProfit%': 'grossprofit',
                    'gross_profit': 'grossprofit',
                    'effectivelaborrate': 'effectivelabourrate',
                    'elr': 'effectivelabourrate',
                    'effective_labour_rate': 'effectivelabourrate',
                    'soldhours': 'soldhours',
                    'sold_hours': 'soldhours',
                    'laborsoldhours': 'laborsoldhours'
                }
                
                # Get the standardized column key
                standardized_key = title_mappings.get(target_column_key)
                log_info(f"target_column_key: {target_column_key}")
                log_info(f"standardized_key: {standardized_key}")
                if not standardized_key:
                    log_info(f"Unknown title '{title}'. Available options: {list(title_mappings.keys())}")
                    return {"extracted_data": []}
                
                log_info(f"Looking for column: {standardized_key} based on title: '{title}'")
                
                # Method 1: Look for column by col-id attribute
                search_terms = target_columns[standardized_key]
                for term in search_terms:
                    header_element = await page.query_selector(f'[col-id="{term}"], [col-id*="{term.replace(" ", "")}"]')
                    if header_element:
                        col_id = await header_element.get_attribute('col-id')
                        column_mapping = col_id
                        log_info(f"Found {standardized_key} column with col-id: {col_id}")
                        break
                
                # Method 2: If column not found, try scrolling horizontally and search again
                if not column_mapping:
                    log_info(f"Column not found in current view, attempting horizontal scroll...")
                    
                    # Get the horizontal scroll container
                    scroll_viewport = await page.query_selector('.ag-body-horizontal-scroll-viewport')
                    scroll_container = await page.query_selector('.ag-body-horizontal-scroll-container')
                    
                    if scroll_viewport and scroll_container:
                        # Get scroll dimensions
                        viewport_width = await scroll_viewport.evaluate('el => el.clientWidth')
                        container_width = await scroll_container.evaluate('el => el.scrollWidth')
                        current_scroll = await scroll_viewport.evaluate('el => el.scrollLeft')
                        
                        log_info(f"Scroll info - Viewport: {viewport_width}px, Container: {container_width}px, Current: {current_scroll}px")
                        
                        # Calculate scroll steps (scroll by 200px increments)
                        scroll_step = 200
                        max_scroll = container_width - viewport_width
                        scroll_attempts = 0
                        max_scroll_attempts = 10
                        
                        while not column_mapping and scroll_attempts < max_scroll_attempts and current_scroll < max_scroll:
                            # Scroll right
                            new_scroll_position = min(current_scroll + scroll_step, max_scroll)
                            await scroll_viewport.evaluate(f'el => el.scrollLeft = {new_scroll_position}')
                            await asyncio.sleep(1)  # Wait for scroll to complete
                            
                            scroll_attempts += 1
                            current_scroll = new_scroll_position
                            log_info(f"Scroll attempt {scroll_attempts}: scrolled to position {current_scroll}px")
                            
                            # Try to find the column again after scrolling
                            # Method 1: Look for column by col-id attribute
                            for term in search_terms:
                                header_element = await page.query_selector(f'[col-id="{term}"], [col-id*="{term.replace(" ", "")}"]')
                                if header_element and await header_element.is_visible():
                                    col_id = await header_element.get_attribute('col-id')
                                    column_mapping = col_id
                                    log_info(f"Found {standardized_key} column with col-id: {col_id} after scrolling")
                                    break
                            
                            if column_mapping:
                                break
                    else:
                        log_info("Could not find scroll elements")
                
                if not column_mapping:
                    log_info(f"Could not find column for '{title}' even after scrolling")
                    return {"extracted_data": []}
                
                log_info(f"Using column mapping: {standardized_key} -> {column_mapping}")
                
                # Extract data from all visible rows, filtering for paytypegroup = 'C'
                extracted_data = []
                # Extract data from expanded rows
                await asyncio.sleep(2)
                updated_rows = await page.query_selector_all('.ag-center-cols-container .ag-row')
                log_info(f"Processing {len(updated_rows)} rows for data extraction")
                await asyncio.sleep(2)

                # Helper function to scroll AG-Grid horizontally and find column
                async def ensure_column_visible(page, col_id, direction="right"):
                    """Scroll AG-Grid horizontally until column is visible"""
                    scroll_viewport = await page.query_selector('.ag-body-horizontal-scroll-viewport')
                    if not scroll_viewport:
                        scroll_viewport = await page.query_selector('.ag-center-cols-viewport')
                    
                    if not scroll_viewport:
                        return False
                    
                    max_attempts = 15
                    scroll_step = 300
                    
                    for attempt in range(max_attempts):
                        # Check if column is now visible
                        col_element = await page.query_selector(f'[col-id="{col_id}"]')
                        if col_element and await col_element.is_visible():
                            log_info(f"Column {col_id} is now visible")
                            return True
                        
                        # Scroll
                        if direction == "right":
                            await scroll_viewport.evaluate(f'el => el.scrollLeft += {scroll_step}')
                        else:
                            await scroll_viewport.evaluate(f'el => el.scrollLeft -= {scroll_step}')
                        
                        await asyncio.sleep(0.3)
                        log_info(f"Scrolling {direction} to find column {col_id} (attempt {attempt + 1})")
                    
                    return False

                # First, scroll to make sure initial columns (month, paytypegroup) are visible
                log_info("Ensuring initial columns (month, paytypegroup, opcode) are visible...")
                await page.evaluate('document.querySelector(".ag-body-horizontal-scroll-viewport")?.scrollTo(0, 0) || document.querySelector(".ag-center-cols-viewport")?.scrollTo(0, 0)')
                await asyncio.sleep(1)

                for row_index, row in enumerate(updated_rows):
                    try:
                        # Scroll row vertically into view
                        await row.scroll_into_view_if_needed()
                        await asyncio.sleep(0.1)

                        # Check if this is a data row (not a group header row)
                        row_class = await row.get_attribute('class')
                        if 'ag-row-group' in row_class and 'ag-row-level-0' in row_class:
                            log_info(f"Skipping group header row {row_index}")
                            continue

                        # Ensure we're scrolled to the left to see initial columns
                        await page.evaluate('document.querySelector(".ag-body-horizontal-scroll-viewport")?.scrollTo(0, 0) || document.querySelector(".ag-center-cols-viewport")?.scrollTo(0, 0)')
                        await asyncio.sleep(0.2)

                        # Try to find month cell
                        month_cell = await row.query_selector('[col-id="ag-Grid-AutoColumn"] .ag-group-value')
                        if not month_cell:
                            log_info(f"Month cell not found in row {row_index}, skipping")
                            continue

                        month_value = await month_cell.text_content()
                        month_value = month_value.strip()

                        # Check if month matches
                        tooltip_month = point_data["series"]
                        def convert_month_label(label: str) -> str:
                            """Convert 'Oct-24' → '10/24'."""
                            return datetime.strptime(label, "%b-%y").strftime("%m/%y") 

                        short_label = convert_month_label(tooltip_month)
                        if month_value != short_label:
                            log_info(f"Month mismatch: {month_value} != {short_label}")
                            continue

                        # Try to find paytypegroup cell
                        paytype_cell = await row.query_selector('[col-id="paytypegroup"]')
                        if not paytype_cell:
                            # Scroll horizontally to find paytypegroup column
                            log_info(f"Paytypegroup column not visible, attempting to scroll...")
                            column_found = await ensure_column_visible(page, "paytypegroup", "right")
                            if not column_found:
                                log_info(f"Could not find paytypegroup column in row {row_index}")
                                continue
                            
                            paytype_cell = await row.query_selector('[col-id="paytypegroup"]')
                            if not paytype_cell:
                                log_info(f"Paytypegroup cell still not found after scrolling")
                                continue

                        paytype_value = await paytype_cell.text_content()
                        paytype_value = paytype_value.strip()

                        # Only process rows where paytypegroup = 'C'
                        if paytype_value != 'C':
                            log_info(f"Skipping row {row_index}: paytypegroup is '{paytype_value}', not 'C'")
                            continue

                        log_info(f"Processing row {row_index} with month '{month_value}' and paytypegroup 'C'")

                        # Get opcode (might need to scroll back left)
                        await page.evaluate('document.querySelector(".ag-body-horizontal-scroll-viewport")?.scrollTo(0, 0) || document.querySelector(".ag-center-cols-viewport")?.scrollTo(0, 0)')
                        await asyncio.sleep(0.2)
                        
                        row_identifier = ""
                        opcode_cell = await row.query_selector('[col-id="lbropcode"] a')
                        if opcode_cell:
                            row_identifier = await opcode_cell.text_content()
                            row_identifier = row_identifier.strip()

                        # Initialize row data
                        row_data = {
                            "row_index": row_index,
                            "month": month_value,
                            "opcode": row_identifier,
                            "paytypegroup": paytype_value
                        }

                        # Extract data for the target column
                        try:
                            # Scroll to find the target column
                            cell = await row.query_selector(f'[col-id="{column_mapping}"]')
                            if not cell:
                                log_info(f"Target column {column_mapping} not visible, scrolling to find it...")
                                column_found = await ensure_column_visible(page, column_mapping, "right")
                                if not column_found:
                                    log_error(f"Could not find target column {column_mapping} in row {row_index}")
                                    continue
                                
                                cell = await row.query_selector(f'[col-id="{column_mapping}"]')
                            
                            if cell and await cell.is_visible():
                                cell_value = await cell.text_content()
                                cell_value = cell_value.strip()
                                row_data[standardized_key] = cell_value

                                # Only add row if it has data
                                if cell_value and cell_value != "":
                                    extracted_data.append(row_data)
                                    log_info(f"Extracted row {row_index} ({row_identifier}): {standardized_key} = {cell_value}")
                            else:
                                log_info(f"Cell for {column_mapping} not visible in row {row_index}")

                        except Exception as cell_error:
                            log_error(f"Error extracting {standardized_key} from row {row_index}: {cell_error}")
                            continue

                    except Exception as row_error:
                        log_error(f"Error processing row {row_index}: {row_error}")
                        continue
                
                log_info(f"Successfully extracted {len(extracted_data)} rows with data for column '{title}'")
                
                # Return success if we have data
                if extracted_data:
                    return {"extracted_data": extracted_data, "column_extracted": standardized_key}
                    
                # If no data extracted, continue to next attempt
                if attempt < max_retries - 1:
                    log_info(f"No data extracted on attempt {attempt + 1}, retrying...")
                    await asyncio.sleep(retry_delay)
                
            except Exception as e:
                log_error(f"Error on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
        
        # Return empty result if all attempts failed
        log_info(f"All {max_retries} attempts failed")
        return {"extracted_data": [], "column_extracted": None}

    async def process_all_combinations_parallel(self, combinations):
        """Process charts in parallel with 3 browsers, each handling different charts"""
        
        log_info(f"🚀 Starting parallel processing of {len(combinations)} charts with 3 browsers")
        
        # Organize combinations by chart for easier processing
        chart_combinations = {}
        for combo_idx, combination in enumerate(combinations):
            chart_id = combination.get('chart_id', f'chart_{combo_idx}')
            target_month = combination.get('target_month_year', 'unknown')
            
            # Extract drillable points from the new structure
            processed_points = []
            total_points_count = 0
            
            # Handle the new matching_points structure with series
            matching_points = combination.get('matching_points', {})
            if isinstance(matching_points, dict) and 'series' in matching_points:
                for series in matching_points['series']:
                    series_name = series.get('name', 'Unknown')
                    series_points = series.get('points', [])
                    total_points_count += len(series_points)
                    
                    for point in series_points:
                        # Skip OTHER subcategory points as they're not drillable
                        # if point.get('opCode', '').upper() != 'OTHER':
                            # Add series information to each point for easier processing
                            enhanced_point = point.copy()
                            enhanced_point['series_name'] = series_name
                            processed_points.append(enhanced_point)
            
            # Store chart information
            chart_combinations[chart_id] = {
                'combination_index': combo_idx,
                'chart_info': combination['chart_info'],
                'target_month_year': target_month,
                'matching_points': processed_points,
                'total_points': total_points_count,
                'drillable_count': len(processed_points),
                'original_matching_points': matching_points,  # Keep original for reference
                'current_point_index': 0
            }
            
            log_info(f"**Chart {chart_id}: {target_month} ({total_points_count} total points, {len(processed_points)} drillable)")
        
        all_results = []
        target_month_year = TARGET_MONTHS_YEARS
        max_browsers = 3
        
        # Group charts for parallel processing (3 charts at a time)
        chart_items = list(chart_combinations.items())
        chart_batches = [chart_items[i:i + max_browsers] for i in range(0, len(chart_items), max_browsers)]
        
        log_info(f"Processing {len(chart_batches)} batches with up to {max_browsers} browsers per batch")
        
        # Process each batch of charts in parallel
        for batch_index, chart_batch in enumerate(chart_batches, 1):
            log_info(f"\n🚀 Starting Batch {batch_index}/{len(chart_batches)} with {len(chart_batch)} charts")
            
            batch_tasks = []
            for browser_index, (chart_id, chart_data) in enumerate(chart_batch):
                browser_id = f"Browser_{browser_index + 1}"
                drillable_count = chart_data['drillable_count']
                total_count = chart_data['total_points']
                
                if drillable_count == 0:
                    log_info(f"   📋 {browser_id}: Skipping Chart {chart_id} - no drillable points (all are OTHER subcategory)")
                    continue
                
                chart_title = chart_data['chart_info'].get('chartTitle', 'Unknown')
                log_info(f"   📋 {browser_id}: Will process Chart {chart_id} - {chart_title} ({drillable_count}/{total_count} drillable)")
                
                task = asyncio.create_task(
                    self.process_single_chart_parallel(chart_data, target_month_year, browser_id, chart_id)
                )
                batch_tasks.append((browser_id, chart_id, task))
            
            if batch_tasks:
                log_info(f"Waiting for all {len(batch_tasks)} browsers in batch {batch_index} to complete...")
                
                for browser_id, chart_id, task in batch_tasks:
                    try:
                        result = await task
                        if isinstance(result, list):
                            all_results.extend(result)
                            log_info(f" {browser_id} completed Chart {chart_id}: {len(result)} results")
                        else:
                            log_info(f"{browser_id} returned unexpected result type for Chart {chart_id}")
                    except Exception as e:
                        log_info(f" {browser_id} failed processing Chart {chart_id}: {str(e)}")
                        continue
                
                log_info(f" Batch {batch_index} completed")
                
                if batch_index < len(chart_batches):
                    log_info(f"Waiting before next batch...")
                    await asyncio.sleep(3)
            else:
                log_info(f"Batch {batch_index} had no drillable charts, skipping...")
        
        # Process final results
        successful_results = []
        failed_results = []
        
        for result in all_results:
            if isinstance(result, dict) and result.get('success', False):
                successful_results.append(result)
            else:
                failed_results.append(result)
        
        log_info(f"\n🎉 Parallel processing with 3 browsers completed!")
        log_info(f"Summary:")
        log_info(f"   - Total charts processed: {len(chart_combinations)}")
        log_info(f"   - Total batches processed: {len(chart_batches)}")
        log_info(f"   - Total point tasks processed: {len(all_results)}")
        log_info(f"   - Successful: {len(successful_results)}")
        log_info(f"   - Failed: {len(failed_results)}")
        log_info(f"   - Success rate: {(len(successful_results) / len(all_results) * 100):.1f}%" if all_results else "0%")
        
        return {
            'successful': successful_results,
            'failed': failed_results,
            'all_results': all_results,
            'total_processed': len(all_results),
            'total_charts': len(chart_combinations),
            'batches_processed': len(chart_batches),
            'success_rate': (len(successful_results) / len(all_results) * 100) if all_results else 0
        }


   
    async def process_single_chart_parallel(self, chart_data, target_month_year, browser_id, chart_id):
        """Process all points in a single chart in parallel (for use with multiple browsers)"""
        
        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
        matching_points = chart_data.get('matching_points', [])
        
        log_info(f"{browser_id}: Processing chart: {chart_title} ({chart_id}) with {len(matching_points)} drillable points")
        
        chart_results = []
        
        if not await self.auth_manager.check_session_and_relogin_parallel():

            log_error("Initial session check failed. Cannot proceed.")

            return []
        page = await self.auth_manager.new_page()
        try:
                    # Navigate to LaborWorkMixAnalysis
                    log_info(f"{browser_id}: Navigating to LaborWorkMixAnalysis for {chart_id}")
                    await page.goto(f"{config.site_url.rstrip('/')}/LaborWorkMixAnalysis", timeout=30000)
                    await page.click('button[role="tab"]:has-text("2 Month Work Mix Comparison")')
                    await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
                    await asyncio.sleep(2)
                    
                    # Set up months
                    await page.click('label:has-text("Month 1") + div [role="button"]')
                    await page.click(f'li[role="option"]:has-text("{config.month1}")')
                    
                    await page.click('label:has-text("Month 2") + div [role="button"]')
                    await page.click(f'li[role="option"]:has-text("{config.month2}")')
                    
                    # Wait for chart to load
                    await asyncio.sleep(3)
                    
                    # Process each drillable point in this chart sequentially within this browser
                    for point_idx, point_data in enumerate(matching_points):
                        # Ensure point_data is a dictionary
                        if not isinstance(point_data, dict):
                            log_info(f"{browser_id}: Skipping point {point_idx + 1}/{len(matching_points)}: Not a dictionary - {type(point_data)}")
                            continue
                        
                        point_label = point_data.get('opcode', f'Point_{point_idx}')
                        series_name = point_data.get('series_name', point_data.get('series', 'Unknown Dataset'))
                        sub_category = point_data.get('opcode', '')
                        main_category = point_data.get('opcategory', '')
                        
                        # Double-check that this point is drillable (not OTHER subcategory)
                        # if sub_category.upper() == 'OTHER':
                        #     log_info(f"{browser_id}: Skipping point {point_idx + 1}/{len(matching_points)}: {point_label} - OTHER subcategory not drillable")
                        #     continue
                        
                        log_info(f"\n{browser_id}: Processing point {point_idx + 1}/{len(matching_points)}: {point_label} ({series_name}) - {main_category}.{sub_category}")
                        
                        try:
                            # Step 1: Ensure chart is interactive and data points are clickable
                            log_info(f"{browser_id}: Ensuring chart {chart_id} is interactive...")
                            await self.ensure_chart_interactivity(page, chart_id)
                            await asyncio.sleep(1)
                            
                            # Step 2: Create task for this point
                            task = {
                                'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                                'chart_id': chart_id,
                                'chart_info': chart_data['chart_info'],
                                'target_month_year': chart_data['target_month_year'],
                                'point_data': point_data,
                                'point_index': point_idx,
                                'browser_id': browser_id
                            }
                            
                            # Step 3: Process this point with enhanced clicking
                            result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year,chart_title)
                            
                            if isinstance(result, dict):
                                result['chart_title'] = chart_title
                                result['point_sequence'] = point_idx + 1
                                result['method'] = 'parallel_processing'
                                result['browser_id'] = browser_id
                                result['chart_id'] = chart_id
                                result['is_drillable'] = True  # Mark as drillable since we filtered out OTHER
                                result['main_category'] = main_category
                                result['opcode'] = sub_category
                                result['series_name'] = series_name
                                result['point_data'] = point_data
                                result['success']= True
                            chart_results.append(result)
                            
                            # Log detailed result
                            if result.get('success', False):
                                drilldown_url = result.get('drilldown_url', 'Unknown')
                                log_info(f"   ✅ Success: {point_label} - Drilldown URL: {drilldown_url}")
                            else:
                                error_msg = result.get('error', 'Unknown error')
                                log_info(f"   ❌ Failed: {point_label} - {error_msg}")
                            
                            # Step 4: Navigate back to LaborWorkMixAnalysis for next point (if not last point)
                            if point_idx < len(matching_points) - 1:
                                log_info(f"{browser_id}: Navigating back to LaborWorkMixAnalysis for next point")
                                try:
                                    await page.goto(f"{config.site_url.rstrip('/')}/LaborWorkMixAnalysis", timeout=30000)
                                    await page.click('button[role="tab"]:has-text("2 Month Work Mix Comparison")')
                                    await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
                                    await asyncio.sleep(2)
                                    
                                    await page.click('label:has-text("Month 1") + div [role="button"]')
                                    await page.click(f'li[role="option"]:has-text("{config.month1}")')
                                    
                                    await page.click('label:has-text("Month 2") + div [role="button"]')
                                    await page.click(f'li[role="option"]:has-text("{config.month2}")')
                                    
                                    await asyncio.sleep(2)
                                    log_info(f"   🔄 Successfully navigated back to LaborWorkMixAnalysis")
                                except Exception as nav_back_error:
                                    log_info(f"   ⚠️ Failed to navigate back to LaborWorkMixAnalysis: {nav_back_error}")
                                    pass
                        
                        except Exception as e:
                            log_info(f"   💥 Error processing point {point_idx + 1} - {point_label}: {str(e)}")
                            error_result = {
                                'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                                'chart_id': chart_id,
                                'chart_title': chart_title,
                                'point_label': point_label,
                                'main_category': main_category,
                                'opcode': sub_category,
                                'series_name': series_name,
                                'is_drillable': True,
                                'error': str(e),
                                'success': False,
                                'method': 'parallel_processing',
                                'browser_id': browser_id,
                                'point_sequence': point_idx + 1
                            }
                            chart_results.append(error_result)
                    
                    log_info(f"🏁 {browser_id}: Completed all drillable points for chart: {chart_title}")
                
        except Exception as e:
                    log_info(f"💥 {browser_id}: Error setting up chart {chart_id}: {str(e)}")
                    error_result = {
                        'chart_id': chart_id,
                        'chart_title': chart_title,
                        'error': f"Chart setup failed: {str(e)}",
                        'success': False,
                        'method': 'parallel_processing',
                        'browser_id': browser_id
                    }
                    chart_results.append(error_result)
                
        finally:
                    try:
                        # await context.close()
                        await page.close()
                        log_info(f"🔒 {browser_id}: Browser closed for chart {chart_id}")
                    except Exception as cleanup_error:
                        log_info(f"⚠️ {browser_id}: Cleanup error for {chart_id}: {cleanup_error}")                
        return chart_results


    # async def process_single_chart_sequential(self, chart_data, target_month_year):
    #     """Process all points in a single chart sequentially"""
    #     chart_id = chart_data.get('chart_info', {}).get('canvasId', 'unknown_chart')
    #     chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
    #     matching_points = chart_data.get('matching_points', [])

    #     log_info(f"🎯 Processing chart: {chart_title} ({chart_id}) with {len(matching_points)} points")

    #     chart_results = []

    #     async with async_playwright() as playwright:
    #         browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)

    #         try:
    #             # Navigate to LaborWorkMixAnalysis
    #             log_info(f"Navigating to LaborWorkMixAnalysis for {chart_id}")
    #             await page.goto(f"{config.site_url.rstrip('/')}/LaborWorkMixAnalysis", timeout=30000)
    #             await page.click('button[role="tab"]:has-text("2 Month Work Mix Comparison")')
    #             # await page.wait_for_load_state("networkidle", timeout=50000)
    #             await asyncio.sleep(2)
    #             await page.click('label:has-text("Month 1") + div [role="button"]')
    #             await page.click(f'li[role="option"]:has-text("{config.month1}")')

    #             await page.click('label:has-text("Month 2") + div [role="button"]')
    #             await page.click(f'li[role="option"]:has-text("{config.month2}")')

    #             # Apply enhanced legend control
    #             legend_setup_success = await self.apply_enhanced_legend_control(page)
    #             await asyncio.sleep(2)

    #             if not legend_setup_success:
    #                 log_info(f"Legend control setup failed for {chart_id}, attempting manual setup...")
    #                 await self.debug_and_setup_charts(page)

    #             # Debug legend control setup
    #             await self.debug_legend_control(page)

    #             log_info(f" Page setup completed for {chart_id}")

    #             # Process each point in this chart sequentially
    #             for point_idx, point_data in enumerate(matching_points):
    #                 point_label = point_data.get('xLabel', f'Point_{point_idx}')
    #                 dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')

    #                 log_info(f"\nProcessing point {point_idx + 1}/{len(matching_points)}: {point_label} ({dataset_label})")

    #                 try:
    #                     # Step 1: Disable ALL legends first
    #                     log_info(f"🔒 Disabling all legends before processing {chart_id}")
    #                     await self.disable_all_legends(page)
    #                     await asyncio.sleep(1)

    #                     # Step 2: Enable ONLY the legend for current chart/dataset
    #                     log_info(f"🔓 Enabling ONLY legend for {chart_id} - {dataset_label}")
    #                     legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
    #                     await asyncio.sleep(2)  # Give more time for chart to update

    #                     if legend_enabled:
    #                         log_info(f" Legend control successful - ONLY {chart_id} legend is active")
    #                     else:
    #                         log_info(f"Legend control failed, but continuing with processing")

    #                     # Step 2.5: Ensure chart is interactive and data points are clickable
    #                     log_info(f"Ensuring chart {chart_id} is interactive after legend control...")
    #                     await self.ensure_chart_interactivity(page, chart_id)
    #                     await asyncio.sleep(1)

    #                     # Step 3: Create task for this point
    #                     task = {
    #                         'task_id': f"{chart_id}_point_{point_idx}",
    #                         'chart_id': chart_id,
    #                         'chart_info': chart_data['chart_info'],
    #                         'target_month_year': chart_data['target_month_year'],
    #                         'point_data': point_data,
    #                         'point_index': point_idx
    #                     }

    #                     # Step 4: Process this point with enhanced clicking
    #                     result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year)

    #                     if isinstance(result, dict):
    #                         result['chart_title'] = chart_title
    #                         result['point_sequence'] = point_idx + 1
    #                         result['method'] = 'sequential_processing'

    #                     chart_results.append(result)

    #                     # Log detailed result
    #                     if result.get('success', False):
    #                         click_success = result.get('click_success', False)
    #                         nav_success = result.get('navigation_success', False)
    #                         extract_success = result.get('extraction_success', False)
    #                         log_info(f" Completed point {point_idx + 1}: {point_label}")
    #                         log_info(f"   Click: {'' if click_success else ''} | Navigation: {'' if nav_success else ''} | Extraction: {'' if extract_success else ''}")
    #                         if nav_success:
    #                             drilldown_url = result.get('drilldown_url', 'Unknown')
    #                             log_info(f"   🔗 Drilldown URL: {drilldown_url}")
    #                     else:
    #                         error_msg = result.get('error', 'Unknown error')
    #                         log_info(f" Failed point {point_idx + 1}: {point_label} - {error_msg}")

    #                     # Step 5: Navigate back to LaborWorkMixAnalysis for next point (if not last point)
    #                     if point_idx < len(matching_points) - 1:
    #                         log_info(f"Navigating back to LaborWorkMixAnalysis for next point")
    #                         try:
    #                             await page.goto(f"{config.site_url.rstrip('/')}/LaborWorkMixAnalysis", timeout=30000)
    #                             await page.click('button[role="tab"]:has-text("2 Month Work Mix Comparison")')
    #                             # await page.wait_for_load_state("networkidle", timeout=50000)
    #                             await asyncio.sleep(2)
    #                             await page.click('label:has-text("Month 1") + div [role="button"]')
    #                             await page.click(f'li[role="option"]:has-text("{config.month1}")')

    #                             await page.click('label:has-text("Month 2") + div [role="button"]')
    #                             await page.click(f'li[role="option"]:has-text("{config.month2}")')

    #                             # Re-apply legend control
    #                             await self.apply_enhanced_legend_control(page)
    #                             await asyncio.sleep(1)
    #                             log_info(f" Successfully navigated back to LaborWorkMixAnalysis")
    #                         except Exception as nav_back_error:
    #                             log_info(f" Failed to navigate back to LaborWorkMixAnalysis: {nav_back_error}")
    #                             # Try to continue anyway
    #                             pass

    #                 except Exception as e:
    #                     log_info(f" Error processing point {point_idx + 1} - {point_label}: {str(e)}")
    #                     error_result = {
    #                         'task_id': f"{chart_id}_point_{point_idx}",
    #                         'chart_id': chart_id,
    #                         'chart_title': chart_title,
    #                         'point_label': point_label,
    #                         'error': str(e),
    #                         'success': False,
    #                         'method': 'sequential_processing',
    #                         'point_sequence': point_idx + 1
    #                     }
    #                     chart_results.append(error_result)

    #             log_info(f" Completed all points for chart: {chart_title}")

    #         except Exception as e:
    #             log_info(f" Error setting up chart {chart_id}: {str(e)}")
    #             error_result = {
    #                 'chart_id': chart_id,
    #                 'chart_title': chart_title,
    #                 'error': f"Chart setup failed: {str(e)}",
    #                 'success': False,
    #                 'method': 'sequential_processing'
    #             }
    #             chart_results.append(error_result)

    #         finally:
    #             try:
    #                 await context.close()
    #                 await browser.close()
    #             except Exception as cleanup_error:
    #                 log_info(f"Cleanup error for {chart_id}: {cleanup_error}")

    #     return chart_results
    
    async def ensure_chart_interactivity(self, page, chart_id):
        """Ensure chart is interactive and data points are clickable after legend control"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Ensuring chart interactivity for: {chart_id}');
                
                // Check if this is a Highcharts chart with chartid class
                const chartContainer = document.querySelector('.highcharts-container.chartid-{chart_id}');
                if (chartContainer) {{
                    console.log('Found Highcharts container for chartid-{chart_id}');
                    
                    try {{
                        // For Highcharts, ensure tooltips and hover events are enabled
                        const svgRoot = chartContainer.querySelector('.highcharts-root');
                        if (svgRoot) {{
                            // Enable pointer events on all chart elements
                            const chartPoints = chartContainer.querySelectorAll('.highcharts-point');
                            chartPoints.forEach(point => {{
                                point.style.pointerEvents = 'all';
                                point.style.cursor = 'pointer';
                            }});
                            
                            // Ensure series are interactive
                            const series = chartContainer.querySelectorAll('.highcharts-series');
                            series.forEach(serie => {{
                                serie.style.pointerEvents = 'all';
                            }});
                            
                            console.log('Highcharts interactivity ensured for: {chart_id}');
                            return true;
                        }}
                    }} catch (error) {{
                        console.error('Error ensuring Highcharts interactivity:', error);
                        return false;
                    }}
                }}
                
                // Fallback to original Chart.js logic if not Highcharts
                if (!window.chartInstances) {{
                    console.log('No chart instances found');
                    return false;
                }}
                
                // Try multiple chart ID variations for Chart.js
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;
                
                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        break;
                    }}
                }}
                
                if (!chartData) {{
                    console.log('Chart not found for interactivity check');
                    return false;
                }}
                
                const chart = chartData.instance;
                
                try {{
                    // Ensure chart is responsive to events
                    if (chart.options) {{
                        if (!chart.options.interaction) chart.options.interaction = {{}};
                        chart.options.interaction.intersect = false;
                        chart.options.interaction.mode = 'point';
                        
                        if (!chart.options.onHover) {{
                            chart.options.onHover = function(event, elements) {{
                                console.log('Chart hover detected:', elements.length, 'elements');
                            }};
                        }}
                        
                        if (!chart.options.onClick) {{
                            chart.options.onClick = function(event, elements) {{
                                console.log('Chart click detected:', elements.length, 'elements');
                                if (elements.length > 0) {{
                                    const element = elements[0];
                                    console.log('Clicked element:', element);
                                }}
                            }};
                        }}
                    }}
                    
                    // Force chart update to apply interaction settings
                    chart.update('none');
                    
                    console.log('Chart interactivity ensured for: {chart_id}');
                    return true;
                }} catch (error) {{
                    console.error('Error ensuring chart interactivity:', error);
                    return false;
                }}
            }})()
            """)
            
            if result:
                log_info(f"Chart {chart_id} interactivity ensured")
            else:
                log_info(f"Failed to ensure chart {chart_id} interactivity")
            
            return result
        
        except Exception as e:
            log_info(f"Error ensuring chart interactivity: {str(e)}")
            return False

    async def try_chartjs_event_click(self, page, chart_id, point_data):
        """Simple, direct chart click function using chart_id"""
        try:
            screen_x = point_data.get('adjustedScreenX', point_data.get('screenX', 0))
            screen_y = point_data.get('adjustedScreenY', point_data.get('screenY', 0))
            
            log_info(f"Simple click at ({screen_x}, {screen_y}) for chart {chart_id}")
            
            # Method 1: Direct Highcharts API call using chart_id
            result = await page.evaluate(f"""
                try {{
                    // Find specific chart by chart_id
                    let chart = null;
                    
                    // Try multiple ways to find the chart
                    if (window.Highcharts && window.Highcharts.charts) {{
                        // Method 1: Find by chart container ID or data attribute
                        chart = window.Highcharts.charts.find(c => {{
                            if (!c || !c.container) return false;
                            return c.container.id === '{chart_id}' || 
                                c.container.getAttribute('data-highcharts-chart') === '{chart_id}' ||
                                c.container.closest('[id*="{chart_id}"]');
                        }});
                        
                        // Method 2: If not found, try by index (extract number from chart_id)
                        if (!chart) {{
                            const chartIndex = parseInt('{chart_id}'.match(/\\d+/)?.[0] || '0');
                            chart = window.Highcharts.charts[chartIndex];
                        }}
                    }}
                    
                    if (!chart) return {{ success: false, error: 'No chart found with ID: {chart_id}' }};
                    
                    console.log('Found chart:', chart.container.id || chart.container.className);
                    
                    // Convert coordinates to chart space
                    const svg = chart.container.querySelector('svg');
                    const rect = svg.getBoundingClientRect();
                    const x = {screen_x} < rect.width ? {screen_x} : {screen_x} - rect.left;
                    const y = {screen_y} < rect.height ? {screen_y} : {screen_y} - rect.top;
                    
                    console.log('Converted coordinates:', x, y);
                    
                    // Find point at coordinates
                    const point = chart.series.flatMap(s => s.points)
                        .find(p => {{
                            if (!p.plotX || !p.plotY) return false;
                            const px = p.plotX + chart.plotLeft;
                            const py = p.plotY + chart.plotTop;
                            const distance = Math.sqrt(Math.pow(px - x, 2) + Math.pow(py - y, 2));
                            return distance < 40; // Increased tolerance
                        }});
                    
                    if (point) {{
                        console.log('Found point:', point.category, point.series.name, point.y);
                        
                        // Try multiple click methods
                        if (point.click) {{
                            point.click();
                            console.log('Called point.click()');
                        }}
                        
                        if (point.firePointEvent) {{
                            point.firePointEvent('click');
                            console.log('Called point.firePointEvent()');
                        }}
                        
                        // Trigger drilldown if available
                        if (point.doDrilldown) {{
                            point.doDrilldown();
                            console.log('Called point.doDrilldown()');
                        }}
                        
                        return {{ success: true, method: 'highcharts_api', point_found: true }};
                    }}
                    
                    return {{ success: false, error: 'No matching point found at coordinates' }};
                }} catch (e) {{
                    console.error('Chart click error:', e);
                    return {{ success: false, error: e.message }};
                }}
            """)
            
            if result.get('success'):
                return result
                
            # Method 2: Simple DOM click as fallback
            await page.mouse.click(screen_x, screen_y)
            return {'success': True, 'method': 'mouse_click'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def process_single_point_task_with_enhanced_clicking(self, page, task, target_month_year,chart_title):
        """Simplified processing with essential steps only"""
        
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        point_data = task['point_data']
        
        # Basic validation
        if not isinstance(point_data, dict):
            return {
                'task_id': task_id,
                'error': 'Invalid point data',
                'success': False,
                'timestamp': datetime.now().isoformat()
            }

        # Skip non-drillable points
        # if point_data.get('opCode', '').upper() == 'OTHER':
        #     return {
        #         'task_id': task_id,
        #         'error': 'Point with OTHER subcategory is not drillable',
        #         'success': False,
        #         'is_drillable': False,
        #         'timestamp': datetime.now().isoformat()
        #     }

        log_info(f"{task_id}: Processing {point_data.get('category', 'Unknown')}")
        
        try:
            initial_url = page.url
            screen_x = point_data.get('screenX', point_data.get('screenX'))
            screen_y = point_data.get('screenY', point_data.get('screenY'))
            
            if not screen_x or not screen_y:
                return {
                    'task_id': task_id,
                    'error': 'No coordinates available',
                    'success': False,
                    'timestamp': datetime.now().isoformat()
                }
            
            # Simple click attempts
            click_methods = [
                # Method 1: Enhanced chart click
                lambda: self.try_chartjs_event_click(page, task['chart_id'], point_data),
                
                # Method 2: Direct mouse click
                lambda: page.click(
                    f".highcharts-container.chartid-{task['chart_id']} svg.highcharts-root",
                    position={"x": screen_x, "y": screen_y}
                ),
                
                lambda: page.evaluate(f"""
                    (el => el && el.click() && true || false)(document.elementFromPoint({screen_x}, {screen_y}))
                """),
                
                
            ]
            
            navigation_success = False
            click_result = None
            
            # Get initial tab state before clicking
            initial_selected_tab = await page.evaluate("""
                () => {
                    const selectedTab = document.querySelector('.MuiTab-root.Mui-selected');
                    return selectedTab ? selectedTab.id : null;
                }
            """)

            log_info(f"{task_id}: Initial selected tab: {initial_selected_tab}")
                        
            for i, method in enumerate(click_methods):
                log_info(f"{task_id}: Trying click method {i+1}")
                
                try:
                    await method()
                    
                    # Check for both URL change and tab change
                    for attempt in range(10):  # 5 seconds total
                        await asyncio.sleep(0.5)
                        
                        # Check URL change first
                        current_url = page.url
                        if current_url != initial_url:
                            navigation_success = True
                            log_info(f"{task_id}: URL navigation detected with method {i+1}")
                            log_info(f"{task_id}: URL changed from {initial_url} to {current_url}")
                            click_result = {
                                'success': True, 
                                'method': f'method_{i+1}',
                                'navigation_type': 'url_change',
                                'from_url': initial_url,
                                'to_url': current_url
                            }
                            break
                        
                        # Check for tab change if URL didn't change
                        current_selected_tab = await page.evaluate("""
                            () => {
                                const selectedTab = document.querySelector('.MuiTab-root.Mui-selected');
                                return selectedTab ? selectedTab.id : null;
                            }
                        """)
                        
                        if current_selected_tab != initial_selected_tab and current_selected_tab is not None:
                            navigation_success = True
                            log_info(f"{task_id}: Tab navigation detected with method {i+1}")
                            log_info(f"{task_id}: Tab changed from '{initial_selected_tab}' to '{current_selected_tab}'")
                            click_result = {
                                'success': True, 
                                'method': f'method_{i+1}',
                                'navigation_type': 'tab_change',
                                'from_tab': initial_selected_tab,
                                'to_tab': current_selected_tab
                            }
                            break
                    
                    if navigation_success:
                        break
                        
                except Exception as e:
                    log_info(f"{task_id}: Method {i+1} failed: {str(e)}")
                    continue
            
            if not navigation_success:
                return {
                    'task_id': task_id,
                    'error': 'No click method triggered navigation',
                    'success': False,
                    'timestamp': datetime.now().isoformat()
                }
            
            # Data extraction - handle both URL-based and tab-based navigation
            current_url = page.url
            extraction_success = False
            extracted_data = {}
            
            # Check if this is URL-based navigation to drilldown page
            if  click_result and click_result.get('navigation_type') == 'tab_change':
                log_info(f"{task_id}: On drilldown page, extracting data...")
                try:
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year,chart_title)
                    extraction_success = extracted_data.get('extraction_data', {}).get('success', False)
                except Exception as e:
                    extracted_data = {'extraction_data': {'success': False, 'error': str(e)}}
            
            # Check if this is tab-based navigation (same URL but different tab content)
            # elif click_result and click_result.get('navigation_type') == 'tab_change':
            #     log_info(f"{task_id}: Tab changed, extracting data from new tab content...")
            #     try:
            #         # Wait for tab content to load
            #         await asyncio.sleep(1)
                    
            #         # Get final tab state for verification
            #         final_tab_state = await page.evaluate("""
            #             () => {
            #                 const selectedTab = document.querySelector('.MuiTab-root.Mui-selected');
            #                 const tabInfo = {
            #                     selectedId: selectedTab ? selectedTab.id : null,
            #                     selectedText: selectedTab ? selectedTab.textContent.trim() : null,
            #                     tabPanelVisible: document.querySelector('[role="tabpanel"]:not([hidden])') !== null
            #                 };
            #                 return tabInfo;
            #             }
            #         """)
                    
            #         log_info(f"{task_id}: Final tab state: {final_tab_state}")
                    
            #         # Extract data from the active tab panel
            #         extracted_data = await self.extract_data_from_tab_panel(page, point_data, target_month_year, final_tab_state)
            #         extraction_success = extracted_data.get('extraction_data', {}).get('success', False)
                    
            #     except Exception as e:
            #         extracted_data = {'extraction_data': {'success': False, 'error': str(e)}}
            
            else:
                extracted_data = {'extraction_data': {'success': False, 'error': f'Unexpected navigation result. URL: {current_url}, Click result: {click_result}'}}
            
            overall_success = navigation_success and extraction_success
            
            return {
                'task_id': task_id,
                'chart_id': task['chart_id'],
                'point_data': point_data,
                'is_drillable': True,
                'click_result': click_result,
                'navigation_success': navigation_success,
                'extraction_success': extraction_success,
                'extracted_data': extracted_data,
                'drilldown_url': current_url,
                'success': overall_success,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'task_id': task_id,
                'error': str(e),
                'success': False,
                'timestamp': datetime.now().isoformat()
            }
      
    
    async def run_complete_process(self):
        """Run the complete chart processing workflow with enhanced legend control"""
        log_info("🚀 Starting complete chart processing workflow with enhanced legend control...")
        success = await self.auth_manager.start(headless=False)
        if not success:
            log_error("❌ Authentication failed. Exiting.")
            return None
        
        try:
            # Step 1: Create chart-point combinations            
            combinations = await self.create_chart_point_combinations(TARGET_MONTHS_YEARS)            
            if not combinations:
                log_info(" No chart-point combinations found")
                return None
            log_info(f" Created {len(combinations)} chart-point combinations")
            # Step 2: Process all combinations in parallel with 3 browsers
            log_info("Step 2: Processing combinations in parallel with 3 browsers...")
            results = await self.process_all_combinations_parallel(combinations)
            if not results:
                log_info(" No results from processing")
                return None
            # Step 3: Save results
            log_info("Step 3: Saving results...")
            await self.save_results(results)
            log_info(" Complete chart processing workflow finished successfully")
            log_info(f"Final Summary:")
            log_info(f"   - Total combinations processed: {len(combinations)}")
            log_info(f"   - Total tasks completed: {results.get('total_processed', 0)}")
            log_info(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
            return results
        except Exception as e:
            log_info(f" Error in complete process: {e}")            
            traceback.print_exc()
            return None
    
    async def save_results(self, results):
        """Save processing results to files with enhanced formatting"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # Create results directory if it doesn't exist
            results_dir = create_folder_file_path(
                subfolder="chart_processing_results",                               
            )            
            os.makedirs(results_dir, exist_ok=True)
            # Save all results (combined)
            if results.get('all_results'):
                all_results_file = os.path.join(results_dir, chart_process_json)
                with open(all_results_file, 'w', encoding='utf-8') as f:
                    json.dump(results['all_results'], f, indent=2, default=str, ensure_ascii=False)
                log_info(f"All results saved to {all_results_file}")

            process_chart_data_file(all_results_file, transformed_json)    

            # Save enhanced summary
            summary = {
                'timestamp': timestamp,
                'processing_method': 'enhanced_legend_control_parallel',
                'target_months_years': TARGET_MONTHS_YEARS,
                'max_concurrent_browsers': MAX_CONCURRENT_BROWSERS,
                'total_processed': results.get('total_processed', 0),
                'total_charts': results.get('total_charts', 0),
                'rounds_processed': results.get('rounds_processed', 0),
                'successful_count': len(results.get('successful', [])),
                'failed_count': len(results.get('failed', [])),
                'success_rate': results.get('success_rate', 0),
                'processing_statistics': {
                    'charts_with_data': results.get('total_charts', 0),
                    'average_points_per_chart': results.get('total_processed', 0) / results.get('total_charts', 1) if results.get('total_charts', 0) > 0 else 0,
                    'total_rounds': results.get('rounds_processed', 0)
                }
            }

            # Perform comparison with Work Mix results
            # log_info("\nStep 4: Performing UI vs DB comparison...")
            # await self.compare_with_cp_overview_results(results, timestamp)

        except Exception as e:
            log_info(f" Error saving results: {e}")            
            traceback.print_exc()


async def ui_capture():
    """Handles chart UI capture and runs the workflow"""
    
    try:
        
        processor = MultiChartParallelProcessor(
            max_browsers=MAX_CONCURRENT_BROWSERS            
        )

        log_info(f"   - Processing mode: Parallel ({MAX_CONCURRENT_BROWSERS}browsers, different charts)")
        log_info(f"   - Max concurrent browsers: {MAX_CONCURRENT_BROWSERS}")
        log_info(f"   - Target months/years: {TARGET_MONTHS_YEARS}")
        log_info(f"   - Browser timeout: {BROWSER_TIMEOUT}ms")
        log_info("=" * 80)

        # Run the parallel chart processing workflow
        results = await processor.run_complete_process()    
        if results:
            log_info("\n" + "=" * 80)
            log_info("Parallel processing with 3 browsers completed successfully!")
            log_info("Final Results:")
            log_info(f"   - Total tasks processed: {results.get('total_processed', 0)}")
            log_info(f"   - Charts processed: {results.get('total_charts', 0)}")
            log_info(f"   - Batches processed: {results.get('batches_processed', 0)}")
            log_info(f"   - Successful tasks: {len(results.get('successful', []))}")
            log_info(f"   - Failed tasks: {len(results.get('failed', []))}")
            log_info(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
            log_info("=" * 80)
            # Additional statistics
            if results.get('successful'):
                log_info(f" Parallel processing completed with {len(results['successful'])} successful extractions")
            if results.get('failed'):
                log_error(f" {len(results['failed'])} tasks failed - check failed results file for details")

            return True

    except Exception as e:
        log_error(f"❌ UI capture failed: {e}")
        traceback.print_exc()
        return False
    
    
      
# Main execution
async def main():
    """Main function: orchestrates UI capture, DB calculation, and comparison. Also run the enhanced chart processing with legend control"""

    start_time = time.time()
    log_info(f"Start Time: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    # db_calculation_labor_workmix()
    # Initialize components
    ui_results = await ui_capture()

    if not ui_results:
        log_info("UI capture did not return results. Exiting.")
        return False
    
    if ui_results:     
        # Generate final comparison report
        log_info("\n" + "=" * 80)
        log_info("GENERATING FINAL UI vs DB COMPARISON REPORT")
        log_info("=" * 80)
        try:
            #step4:
            db_calculation_labor_workmix()
            # step5:    
            result_dir,db_json_path = create_folder_file_path(subfolder="chart_processing_results", output_file=db_json) 
            transform_json_path = os.path.join(result_dir, transformed_json)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            compare_with_labour_comparison_results(transform_json_path, db_json_path,timestamp)
            end_time = time.time()-start_time
            log_info(f"End Time: {end_time}")
            return True
        except Exception as comparison_error:
            log_error(f" Error generating comparison report: {comparison_error}")            
            traceback.print_exc()
            return False
    else:
        log_error(" Parallel processing failed - check logs for details")
        return False


def run_validation():
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        log_info(" Processing interrupted by user")
    except Exception as e:
        log_info(f"\n Unexpected error: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    run_validation()
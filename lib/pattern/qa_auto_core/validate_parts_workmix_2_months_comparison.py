import asyncio
import json
import os
import time
import glob
import traceback
import pandas as pd
import numpy as np
from datetime import datetime
from math import isfinite, isnan
from playwright.async_api import async_playwright
from concurrent.futures import ThreadPoolExecutor
import threading
from collections import defaultdict
import re
from decimal import Decimal, ROUND_HALF_UP
import openpyxl
import openpyxl.cell
from openpyxl.styles import <PERSON><PERSON><PERSON>ill, Alignment, Font
from openpyxl.utils import get_column_letter
import csv
import argparse
import calendar
from lib.pattern.config import config
from lib.pattern.qa_auto_core.db_handler.db_connector import allRevenueDetailsPartsWorkMixAnalysis 
from dateutil.relativedelta import relativedelta
from datetime import timedelta
from typing import Dict, List, Any, Optional, Tuple
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.std.universal.authmanager import AuthManager
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.chart_dict import VALIDATION_CHARTS
from lib.pattern.qa_auto_core.compare_parts_workmix_2_months import compare_with_parts_workmix_comparison_results
from typing import Optional, Union
from collections import defaultdict
from lib.pattern.qa_auto_core.parts_work_mix_db_calculation import calculate_parts_workmix

from types import SimpleNamespace

chart_key="parts_work_mix_comparison"
chart_process_json = VALIDATION_CHARTS[chart_key]["chart_process_json"]
db_json = VALIDATION_CHARTS[chart_key]["db_json"]
transformed_json =VALIDATION_CHARTS[chart_key]["json"]


# Target months-years for drilling down (modify as needed)
# This will be set after argument parsing
TARGET_MONTHS_YEARS = None

# Configuration constants
MAX_CONCURRENT_BROWSERS = 3
BROWSER_TIMEOUT = 30000

namespace = {
    'ns': 'http://www.dmotorworks.com/service-repair-order-history'
}

def round_off(n, decimals=0):
    """Round off numbers with proper decimal handling"""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def zero_sales_check(df, columns):
    """Check if all specified columns sum to zero"""
    total_sum = df[columns].sum().sum()
    return total_sum == 0

def get_month_date_range_from_target(month1, month2):
    """Get the start and end date for the target months range"""
    import calendar

    try:
        # Handle datetime objects and string dates
        if isinstance(month1, datetime):
            start_date = month1
        else:
            start_date = datetime.strptime(month1, "%Y-%m-%d")

        if isinstance(month2, datetime):
            end_date = month2
        else:
            end_date = datetime.strptime(month2, "%Y-%m-%d")

        # Get first day of start month
        month_start = start_date.replace(day=1)
        
        # Get correct last day of end month
        _, last_day = calendar.monthrange(end_date.year, end_date.month)
        month_end = end_date.replace(day=last_day)

        return month_start, month_end

    except Exception as e:
        print(f"Error in get_month_date_range_from_target: {str(e)}")
        raise ValueError(f"Invalid date range: {str(e)}")

def convert_month_format(month_str):
    """
    Convert month format from 'Jun-25' to '2024-06-01' (first day) or '2024-06-30' (last day)
    
    Args:
        month_str: String in format 'Jun-25' or 'Jul-25'
    
    Returns:
        tuple: (first_day, last_day) in format 'YYYY-MM-DD'
    """
    try:
        # Parse the input string
        month_part, year_part = month_str.split('-')
        
        # Convert 2-digit year to 4-digit year
        if len(year_part) == 2:
            year = 2000 + int(year_part)
        else:
            year = int(year_part)
        
        # Convert month abbreviation to month number
        month_mapping = {
            'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
            'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
        }
        
        month = month_mapping.get(month_part, 1)
        
        # Get first day of month
        first_day = f"{year}-{month:02d}-01"
        
        # Get last day of month
        last_day_num = calendar.monthrange(year, month)[1]
        last_day = f"{year}-{month:02d}-{last_day_num:02d}"
        
        return first_day, last_day
        
    except Exception as e:
        print(f"Error converting {month_str}: {e}")
        return None, None
    
def calculate_working_days(start_date, end_date):
    """Calculate working days between two dates (excluding weekends)"""
    working_days = 0
    current_date = start_date
    while current_date <= end_date:
        if current_date.weekday() < 5:  # Monday is 0, Sunday is 6
            working_days += 1
        current_date += timedelta(days=1)
    return working_days

def process_parts_workmix_data(all_revenue_details_df, month1_date, month2_date, advisor, tech, retail_flag, customer_pay_types, warranty_pay_types, columns_to_check):
    """Process parts work mix data for two months based on config.start_date and config.end_date"""
    try:
        import calendar
        import pandas as pd
        import numpy as np

        # Convert inputs to datetime
        month1_date = pd.to_datetime(month1_date)
        month2_date = pd.to_datetime(month2_date)

        # Month 1
        month1_start = month1_date.replace(day=1)
        _, last_day_month1 = calendar.monthrange(month1_start.year, month1_start.month)
        month1_end = month1_date.replace(day=last_day_month1)

        # Month 2
        month2_start = month2_date.replace(day=1)
        _, last_day_month2 = calendar.monthrange(month2_start.year, month2_start.month)
        month2_end = month2_date.replace(day=last_day_month2)

        log_info(f"Month1 Start: {month1_start}, Month1 End: {month1_end}")
        log_info(f"Month2 Start: {month2_start}, Month2 End: {month2_end}")
        month1_calculated_data=calculate_parts_workmix(month1_start, month1_end, all_revenue_details_df,"_mon1")
        month2_calculated_data=calculate_parts_workmix(month2_start, month2_end, all_revenue_details_df,"_mon2")
        
        log_info(f"Month1 Calculation: {month1_calculated_data}")
        log_info(f"Month2 Calculation: {month2_calculated_data}")
        
        analysis_info = {
            "target_start_month": config.start_date,
            "target_end_month": config.end_date,
            "analysis_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "analysis_type": "parts_work_mix",
            "store_id": str(config.store_id),
            "realm": config.realm,
            "advisor_filter": [config.advisor],
            "technician_filter": [config.technician],
            "customer_pay_types": ["C", "M"],
            "warranty_pay_types": ["W", "F", "E"]
        }
        log_info(f"Analysis Info: {analysis_info}")
        target_month=config.target_month_year
        target_month_name=config.target_month_year
        month1_period=config.target_month_year
        month1_name=config.start_date
        month2_period=config.end_date
        month2_name=config.end_date
        category_breakdown=prepare_category_breakdown(month1_calculated_data, month2_calculated_data)
        log_info(f"Category Breakdown: {category_breakdown}")
        final_json = {
            "analysis_info": analysis_info,
            "target_month_results": {
                "target_month": target_month,
                "target_month_name": target_month_name,
                "month1_period": month1_period,
                "month1_name": month1_name,
                "month2_period": month2_period,
                "month2_name": month2_name,
                "total_ros": month1_calculated_data.get("total_ros", 0),
                "ro_counts": month1_calculated_data.get("ro_counts", {}),
                "month1_data": {
                    "total_ros": month1_calculated_data.get("total_ros", 0),
                    "ro_counts": month1_calculated_data.get("ro_counts", {})
                },
                "month2_data": {
                    "total_ros": month2_calculated_data.get("total_ros", 0),
                    "ro_counts": month2_calculated_data.get("ro_counts", {})
                },
                "work_mix_analysis": {
                    # Placeholder for work_mix_analysis - you can compute totals, category breakdowns, etc.
                    "total_parts_cost_mon1": sum([w.get("parts_cost_mon1", 0) for w in month1_calculated_data["work_mix"]]),
                    "total_parts_cost_mon2": sum([w.get("parts_cost_mon2", 0) for w in month2_calculated_data["work_mix"]]),
                    "total_opcodes_mon1": len(month1_calculated_data["work_mix"]),
                    "total_opcodes_mon2": len(month2_calculated_data["work_mix"]),
                    # You can add more computed metrics here...
                    "category_breakdown": category_breakdown  # To be populated if needed
                }
            }
        }
        log_info(f"Final JSON: {final_json}")

        return final_json
    except Exception as e:
        log_error(f"Error in process_parts_workmix_data: {str(e)}")
        traceback.print_exc()
    # return{}
    
from collections import defaultdict

def prepare_category_breakdown(month1_data, month2_data):
    breakdown = defaultdict(lambda: {"individual_opcodes": [], "others_summary": {}, "total_metrics": {}})

    # Combine both months' work_mix data
    all_opcodes = defaultdict(dict)
    for mon, data in [("mon1", month1_data), ("mon2", month2_data)]:
        for entry in data.get("work_mix", []):
            opcat = entry["opcategory"]
            opcode = entry["opcode"]
            all_opcodes[(opcat, opcode)][mon] = entry

    # Process each opcategory
    categories = set(opcat for opcat, _ in all_opcodes.keys())
    for opcat in categories:
        individual = []
        others = {"workmix_mon1": 0, "job_count_mon1": 0, "gp_percentage_mon1": 0, "parts_markup_mon1": 0, "parts_cost_mon1": 0,
                  "workmix_mon2": 0, "job_count_mon2": 0, "gp_percentage_mon2": 0, "parts_markup_mon2": 0, "parts_cost_mon2": 0}
        total_metrics = {"workmix_mon1": 0, "job_count_mon1": 0, "gp_percentage_mon1": 0, "parts_markup_mon1": 0, "parts_cost_mon1": 0,
                         "workmix_mon2": 0, "job_count_mon2": 0, "gp_percentage_mon2": 0, "parts_markup_mon2": 0, "parts_cost_mon2": 0}

        for (cat, opcode), vals in all_opcodes.items():
            if cat != opcat:
                continue
            entry = {}
            entry["opcode"] = opcode
            individual.append(entry)
            # Month 1 values
            entry["workmix_mon1"] = vals.get("mon1", {}).get("work_mix_percentage_mon1", vals.get("mon1", {}).get("work_mix_percentage", 0))
            entry["job_count_mon1"] = vals.get("mon1", {}).get("job_count_mon1", 0)
            entry["gp_percentage_mon1"] = vals.get("mon1", {}).get("gp_percentage_mon1", 0)
            entry["parts_markup_mon1"] = vals.get("mon1", {}).get("parts_markup_mon1", 0)
            entry["parts_cost_mon1"] = vals.get("mon1", {}).get("parts_cost_mon1", vals.get("mon1", {}).get("parts_cost", 0))
            # Month 2 values
            entry["workmix_mon2"] = vals.get("mon2", {}).get("work_mix_percentage_mon2", vals.get("mon2", {}).get("work_mix_percentage", 0))
            entry["job_count_mon2"] = vals.get("mon2", {}).get("job_count_mon2", 0)
            entry["gp_percentage_mon2"] = vals.get("mon2", {}).get("gp_percentage_mon2", 0)
            entry["parts_markup_mon2"] = vals.get("mon2", {}).get("parts_markup_mon2", 0)
            entry["parts_cost_mon2"] = vals.get("mon2", {}).get("parts_cost_mon2", vals.get("mon2", {}).get("parts_cost", 0))
            # entry["opcode"] = opcode

            if opcode == "OTHER":
                for k in others.keys():
                    others[k] += entry[k]
            else:
                individual.append(entry)

            # Sum totals
            for k in total_metrics.keys():
                if k != "opcode":  # skip opcode in numeric sum
                    total_metrics[k] += entry[k]
            

        breakdown[opcat]["individual_opcodes"] = individual
        breakdown[opcat]["others_summary"] = others
        breakdown[opcat]["total_metrics"] = total_metrics

    return dict(breakdown)


    # def process_single_month(df, m_start, m_end, month_label):
    #     m_start, m_end = m_start.date(), m_end.date()
    #     month_data = df[(df['closeddate'] >= m_start) & (df['closeddate'] <= m_end)]
    #     if month_data.empty:
    #         return None, None

    #     filtered_df = month_data[(month_data['department'] == 'Service') & (~month_data['hide_ro'].fillna(False))]
    #     if filtered_df.empty:
    #         return None, None

    #     filtered_df = filtered_df.copy()
    #     filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)

    #     # Set zero values for 'N/A' opcategory temporarily
    #     temp_df = filtered_df.copy()
    #     temp_df.loc[temp_df['opcategory'] == 'N/A', columns_to_check] = 0

    #     # Assign RO groups
    #     def assign_group(ro_rows):
    #         ro_C = ro_rows[ro_rows['paytypegroup'].isin(customer_pay_types)]
    #         ro_W = ro_rows[ro_rows['paytypegroup'].isin(warranty_pay_types)]
    #         if not ro_C.empty and not zero_sales_check(ro_C, columns_to_check):
    #             return 'C'
    #         elif not ro_W.empty and not zero_sales_check(ro_W, columns_to_check):
    #             return 'W'
    #         else:
    #             return 'I'

    #     filtered_df['group'] = filtered_df.groupby('unique_ro_number').apply(lambda g: assign_group(temp_df[temp_df['unique_ro_number'] == g.name])).reindex(filtered_df.index).values

    #     # Apply advisor and tech filters
    #     if advisor != {'all'}:
    #         filtered_df = filtered_df[filtered_df['serviceadvisor'].astype(str).isin(advisor)]
    #     if tech != {'all'}:
    #         filtered_df = filtered_df[filtered_df['lbrtechno'].astype(str).isin(tech)]

    #     filtered_df.loc[filtered_df['opcategory'] == 'N/A', columns_to_check] = 0

    #     # RO counts
    #     ro_counts = {
    #         "customer_pay_ros": filtered_df[filtered_df['group'] == 'C']['unique_ro_number'].nunique(),
    #         "warranty_ros": filtered_df[filtered_df['group'] == 'W']['unique_ro_number'].nunique(),
    #         "internal_ros": filtered_df[filtered_df['group'] == 'I']['unique_ro_number'].nunique()
    #     }
    #     total_ros = sum(ro_counts.values())

    #     # Filter Customer Pay group C and non-zero sales
    #     mask_cp = (filtered_df['paytypegroup'].isin(customer_pay_types)) & (filtered_df['group'] == 'C')
    #     zero_mask = (filtered_df['lbrsale'].fillna(0) == 0) & (filtered_df['lbrsoldhours'].fillna(0) == 0) & \
    #                 (filtered_df['prtextendedsale'].fillna(0) == 0) & (filtered_df['prtextendedcost'].fillna(0) == 0) & \
    #                 (filtered_df['lbropcode'].isna() | (filtered_df['lbropcode'].str.strip() == ''))
    #     total_revenue_Parts_Works = filtered_df[mask_cp & ~zero_mask]

    #     return total_revenue_Parts_Works, {"total_ros": total_ros, "ro_counts": ro_counts}

    # def calculate_parts_metrics(total_revenue_Parts_Works, month_suffix):
    #     if total_revenue_Parts_Works is None or total_revenue_Parts_Works.empty:
    #         return {}
    #     total_parts_cost = total_revenue_Parts_Works['prtextendedcost'].fillna(0).sum()
    #     work_mix_result = total_revenue_Parts_Works.groupby(['lbropcode', 'opcategory']).agg({
    #         'prtextendedsale': 'sum', 'prtextendedcost': 'sum'
    #     }).reset_index()

    #     work_mix_result['parts_cost'] = work_mix_result['prtextendedcost'].round(2)
    #     work_mix_result['work_mix_percentage'] = (work_mix_result['prtextendedcost'] / total_parts_cost * 100).round(2) if total_parts_cost != 0 else 0
    #     work_mix_result['parts_markup'] = (work_mix_result['prtextendedsale'] / work_mix_result['prtextendedcost']).replace([np.inf, -np.inf], 0).round(2)
    #     work_mix_result['gp_percentage'] = ((work_mix_result['prtextendedsale'] - work_mix_result['prtextendedcost']) / work_mix_result['prtextendedsale'] * 100).replace([np.inf, -np.inf], 0).round(2)
    #     work_mix_result['job_count'] = work_mix_result['lbropcode'].apply(lambda x: total_revenue_Parts_Works[total_revenue_Parts_Works['lbropcode'].str.strip() == x.strip()]['ronumber'].count())

    #     work_mix_less_than_2 = work_mix_result[work_mix_result['work_mix_percentage'] <= 2]
    #     work_mix_greater_than_2 = work_mix_result[work_mix_result['work_mix_percentage'] > 2]

    #     return {
    #         'total_parts_cost': total_parts_cost,
    #         'work_mix_result': work_mix_result,
    #         'work_mix_less_than_2': work_mix_less_than_2,
    #         'work_mix_greater_than_2': work_mix_greater_than_2,
    #         'month_suffix': month_suffix
    #     }

    # # Process both months
    # total_revenue_Parts_Works_mon1, ro_data_mon1 = process_single_month(all_revenue_details_df, month1_start, month1_end, "Month 1")
    # total_revenue_Parts_Works_mon2, ro_data_mon2 = process_single_month(all_revenue_details_df, month2_start, month2_end, "Month 2")

    # # Calculate metrics
    # parts_metrics_mon1 = calculate_parts_metrics(total_revenue_Parts_Works_mon1, "_mon1")
    # parts_metrics_mon2 = calculate_parts_metrics(total_revenue_Parts_Works_mon2, "_mon2")


    # # Build analysis result
    # work_mix_analysis = {}
    # if parts_metrics_mon1 or parts_metrics_mon2:
    #     opcategory_list = ['COMPETITIVE', 'MAINTENANCE', 'REPAIR']
    #     detailed_results = {}

    #     for category in opcategory_list:
    #         category_data = {'individual_opcodes': [], 'others_summary': {}, 'total_metrics': {}}
    #         opcodes_mon1 = set(parts_metrics_mon1.get('work_mix_greater_than_2', pd.DataFrame()).query("opcategory == @category")['lbropcode']) if parts_metrics_mon1 else set()
    #         opcodes_mon2 = set(parts_metrics_mon2.get('work_mix_greater_than_2', pd.DataFrame()).query("opcategory == @category")['lbropcode']) if parts_metrics_mon2 else set()
    #         all_opcodes = opcodes_mon1.union(opcodes_mon2)

    #         # Process individual opcodes
    #         for opcode in all_opcodes:
    #             opcode_data = {'opcode': opcode}
    #             for mon, metrics in zip(['mon1', 'mon2'], [parts_metrics_mon1, parts_metrics_mon2]):
    #                 if metrics:
    #                     row = metrics['work_mix_greater_than_2'].query("lbropcode == @opcode and opcategory == @category")
    #                     if not row.empty:
    #                         row = row.iloc[0]
    #                         opcode_data.update({
    #                             f'workmix_{mon}': round_off(row['work_mix_percentage'], 2),
    #                             f'job_count_{mon}': int(row['job_count']),
    #                             f'gp_percentage_{mon}': round_off(row['gp_percentage'], 2),
    #                             f'parts_markup_{mon}': round_off(row['parts_markup'], 4),
    #                             f'parts_cost_{mon}': round_off(row['parts_cost'], 2)
    #                         })
    #                     else:
    #                         opcode_data.update({f'{k}_{mon}': 0 for k in ['workmix', 'job_count', 'gp_percentage', 'parts_markup', 'parts_cost']})
    #                 else:
    #                     opcode_data.update({f'{k}_{mon}': 0 for k in ['workmix', 'job_count', 'gp_percentage', 'parts_markup', 'parts_cost']})
    #             category_data['individual_opcodes'].append(opcode_data)

    #         detailed_results[category] = category_data

    #     work_mix_analysis = {
    #         'category_breakdown': detailed_results,
    #         'total_parts_cost_mon1': parts_metrics_mon1.get('total_parts_cost', 0) if parts_metrics_mon1 else 0,
    #         'total_parts_cost_mon2': parts_metrics_mon2.get('total_parts_cost', 0) if parts_metrics_mon2 else 0
    #     }

    # return {
    #     "month1_data": ro_data_mon1 or {"total_ros": 0, "ro_counts": {"customer_pay_ros": 0, "warranty_ros": 0, "internal_ros": 0}},
    #     "month2_data": ro_data_mon2 or {"total_ros": 0, "ro_counts": {"customer_pay_ros": 0, "warranty_ros": 0, "internal_ros": 0}},
    #     "work_mix_analysis": work_mix_analysis
    # }

def db_execution(advisor, tech, retail_flag, columns_to_check, month_start, month_end):
    """
    Handle database operations and execute parts work mix processing
    """      
    try:
        customer_pay_types = {}
        warranty_pay_types = {}
        print(f"Parts work mix comparison month range: {month_start} to {month_end}")       
        
        # Get valid date range
        month_start, month_end = get_month_date_range_from_target(month_start, month_end)
        print(f"Using date range: {month_start} to {month_end}")

        # Fetch all data from database
        print("Fetching parts work mix data from database...")
        all_revenue_details_table_db_connect = allRevenueDetailsPartsWorkMixAnalysis()
        all_revenue_details_df = all_revenue_details_table_db_connect.getTableResult()  
        # distinct_opcodes = all_revenue_details_df['lbropcode'].unique()
        # print(f"Distinct lbropcode values: {distinct_opcodes}")      
        
        if all_revenue_details_df.empty:
            print("ERROR: No data retrieved from database for parts work mix!")
            return False       
        
        # Convert date column and other preprocessing
        columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))        
        
        # Define customer and warranty pay types based on retail_flag
        if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C'}
            warranty_pay_types = {'W', 'F', 'M', 'E'}
        elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'M'}
            warranty_pay_types = {'W', 'F', 'E'}
        elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C', 'E'}
            warranty_pay_types = {'W', 'F', 'M'}
        elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'E', 'M'}
            warranty_pay_types = {'W', 'F'}       
        print(f"month_start: {month_start}, month_end: {month_end}")

        target_month_result = process_parts_workmix_data(
            all_revenue_details_df, 
            month_start, 
            month_end,          
            advisor, 
            tech, 
            retail_flag, 
            customer_pay_types, 
            warranty_pay_types, 
            columns_to_check
        ) 
        
        return target_month_result, customer_pay_types, warranty_pay_types        
        
    except Exception as e:
        print(f"ERROR in db_execution: {str(e)}")
        print("=" * 60)
        print("PARTS WORK MIX DATABASE EXECUTION FAILED")
        print("=" * 60)
        return None, None, None

def db_calculation():
    """
    Main execution function for parts work mix calculation
    """    
    # Configuration variables
    columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
    retail_flag = {'C','M'}    
    storeid = config.store_id
    realm = config.realm   
    advisor_set = config.advisor
    tech_set = config.technician  

    month_start = config.start_date
    month_end = config.end_date


    
    # Process advisor configuration
    if isinstance(advisor_set, str):
        if advisor_set.lower() == 'all':
            advisor = {'all'}
        elif ',' in advisor_set:
            advisor = {x.strip() for x in advisor_set.split(',')}
        else:
            advisor = {advisor_set.strip()}
    else:
        advisor = {'all'}
    
    if advisor != {'all'}:
        advisor_id = next(iter(advisor))   
    else:
        advisor_id = 'all'    
    
    # Process technician configuration
    if isinstance(tech_set, str):
        if tech_set.lower() == 'all':
            tech = {'all'}
        elif ',' in tech_set:
            tech = {x.strip() for x in tech_set.split(',')}
        else:
            tech = {tech_set.strip()}
    else:
        tech = {'all'}   
    
    # Execute database operations and processing
    target_start_date_str = month_start
    target_end_date_str= month_end
    target_month_result, customer_pay_types, warranty_pay_types = db_execution(
        advisor, tech, retail_flag, columns_to_check,month_start,month_end
    )    
    
    # Process results
    if target_month_result:
        print("\n" + "=" * 80)
        print("PARTS WORK MIX RESULTS PROCESSING")
        print("=" * 80)        
        
        # Create the final result set for the target month only
        # final_result_set = {
        #     "analysis_info": {
        #         "target_start_month": target_start_date_str,
        #         "target_end_month": target_end_date_str,
        #         "analysis_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        #         "analysis_type": "parts_work_mix",
        #         "store_id": storeid,
        #         "realm": realm,
        #         "advisor_filter": list(advisor),
        #         "technician_filter": list(tech),
        #         "customer_pay_types": list(customer_pay_types),
        #         "warranty_pay_types": list(warranty_pay_types)
        #     },
        #     "target_month_results": target_month_result
        # }     
        final_result_set=target_month_result
        import os
        import json
                
        result_dir,output_filename = create_folder_file_path(subfolder="chart_processing_results", output_file=db_json)                            
        
        with open(output_filename, 'w', encoding='utf-8') as json_file:
            json.dump(final_result_set, json_file, indent=4, ensure_ascii=False)        
        
        log_info(f"\nTarget month Parts Work Mix data written successfully to {result_dir}/{output_filename}")        
        
                   
        
    else:
            
        
        log_info(f"No data available for parts work mix target month {target_start_date_str} to {target_end_date_str}")   
    
    print("\n" + "=" * 80)
    log_info("PARTS WORK MIX ANALYSIS - MAIN EXECUTION COMPLETED")
    print("=" * 80)

def normalize_month_year(month_year_str):
    """
    Normalize different month-year formats to a standard format for comparison
    """
    if not month_year_str:
        return None
    
    month_year_str = str(month_year_str).strip()
    
    # Handle apostrophe format like "Jun'2024"
    month_year_str = month_year_str.replace("'", " ")
    
    # Common patterns to match
    patterns = [
        r'(\w{3})\s+(\d{4})',  # Jan 2024
        r'(\w{3})\s*(\d{4})',  # Jan2024
        r'(\d{1,2})/(\d{4})',  # 1/2024
        r'(\d{1,2})-(\d{4})',  # 1-2024
        r'(\w+)\s+(\d{4})',    # January 2024
    ]
    
    month_map = {
        'jan': 'Jan', 'january': 'Jan',
        'feb': 'Feb', 'february': 'Feb',
        'mar': 'Mar', 'march': 'Mar',
        'apr': 'Apr', 'april': 'Apr',
        'may': 'May',
        'jun': 'Jun', 'june': 'Jun',
        'jul': 'Jul', 'july': 'Jul',
        'aug': 'Aug', 'august': 'Aug',
        'sep': 'Sep', 'september': 'Sep',
        'oct': 'Oct', 'october': 'Oct',
        'nov': 'Nov', 'november': 'Nov',
        'dec': 'Dec', 'december': 'Dec'
    }
    
    for pattern in patterns:
        match = re.match(pattern, month_year_str, re.IGNORECASE)
        if match:
            month_part, year_part = match.groups()
            
            # Handle numeric month
            if month_part.isdigit():
                month_num = int(month_part)
                if 1 <= month_num <= 12:
                    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
                    month_part = month_names[month_num - 1]
            else:
                # Handle text month
                month_part = month_map.get(month_part.lower(), month_part)
            
            return f"{month_part} {year_part}"
    
    return month_year_str

class MultiChartParallelProcessor:
    """Process multiple charts in parallel with separate browsers"""
    
    def __init__(self, max_browsers=4, auth_manager=None):
        self.max_browsers = max_browsers
        self.auth_manager = auth_manager or AuthManager(config)
        self.charts_info=None
    


    def get_months_between(start_date_str, end_date_str):
        """Return a list of months between two dates in 'Mon-YY' format."""
        start = datetime.strptime(start_date_str, "%Y-%m-%d")
        end = datetime.strptime(end_date_str, "%Y-%m-%d")
        months = []
        current = start.replace(day=1)
        while current <= end:
            months.append(current.strftime("%b-%y")) 
            current += relativedelta(months=1)
        return months


    async def discover_charts(self):
        """Discover all charts on the PartsWorkMixAnalysis-2Months-Comparison"""
        print("Discovering charts on PartsWorkMixAnalysis-2Months-Comparison...")
        
        # Use the dedicated parallel function to check and re-login the session.
        # This ensures a valid session before a new page is even created.
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []

        # Get a NEW PAGE from the existing session.
        page = await self.auth_manager.new_page() 
       
        try:
            log_info("Navigating to PartsWorkMixAnalysis-2Months-Comparison page to Discover Charts...")
            await page.goto(f"{config.site_url.rstrip('/')}/PartsWorkMixAnalysis", timeout=50000)
            # Click the button
            await page.click('button:has-text("2 Month Work Mix Comparison")', timeout=500000)
            # Wait for Month 1 dropdown to be visible
            await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
            await page.click('label:has-text("Month 1") + div [role="button"]')
            await page.click(f'li[role="option"]:has-text("{config.month1}")')

            # Wait for Month 2 dropdown
            await page.wait_for_selector('label:has-text("Month 2") + div [role="button"]', timeout=50000)
            await page.click('label:has-text("Month 2") + div [role="button"]')
            await page.click(f'li[role="option"]:has-text("{config.month2}")')
            log_info("Redirected to 2 months comparison page successfully from  Discover Charts...")

            # Wait for the chart/table container to load before capture
            for i in range(5):
                try:
                    await page.wait_for_selector('div.highcharts-container', timeout=10000)
                    log_info("Chart loaded successfully")
                    break
                except:
                    log_info(f"Retrying chart load... attempt {i+1}")
            else:
                raise Exception("Chart did not load after retries")
            chart_found = False
            selectors_to_try = [
                
                # SVG-based charts (Highcharts, D3.js, etc.)
                'svg.highcharts-root',
                'svg[class*="highcharts"]',
                'svg[class*="chart"]',
                'div[class*="highcharts-container"]',
                'svg',

            ]

            for selector in selectors_to_try:
                try:
                    print(f" Checking charts using selector: {selector}")
                    await page.wait_for_selector(selector, timeout=5000)
                    chart_found = True
                    print(f" Found charts using selector: {selector}")
                    break
                except:
                    continue
            if not chart_found:
                print("No chart elements found with any selector")
                return False
        
            await asyncio.sleep(2)
            
            charts_info = await page.evaluate("""
                () => {
                    const chartsInfo = [];
                    let chartIndex = 0;
                    const processedElements = new Set(); // Track processed elements to avoid duplicates
                    
                    // Function to extract chart ID from various sources
                    function extractChartId(element) {
                        let chartId = null;
                        
                        // Method 1: From data-highcharts-chart attribute
                        const highchartsChart = element.getAttribute('data-highcharts-chart');
                        if (highchartsChart) {
                            chartId = highchartsChart;
                        }
                        
                        // Method 2: From Highcharts container class name pattern
                        const containerMatch = element.className.match && element.className.match(/chartid-(\\d+)/);
                        if (containerMatch) {
                            chartId = containerMatch[1];
                        }
                        
                        // Method 3: From button IDs in the same container
                        const container = element.closest('.MuiCard-root, .MuiPaper-root, [id*="highchartDataContainer"]');
                        if (container) {
                            const buttons = container.querySelectorAll('button[id*="chart-details-"], button[id*="view-details-"]');
                            for (const button of buttons) {
                                const idMatch = button.id.match(/(?:chart-details-|view-details-)(\\d+)/);
                                if (idMatch) {
                                    chartId = idMatch[1];
                                    break;
                                }
                            }
                        }
                        
                        // Method 4: From parent container ID
                        let parent = element.parentElement;
                        while (parent && !chartId) {
                            if (parent.id && parent.id.includes('chart')) {
                                const idMatch = parent.id.match(/(\\d+)/);
                                if (idMatch) {
                                    chartId = idMatch[1];
                                }
                            }
                            parent = parent.parentElement;
                        }
                        
                        return chartId;
                    }
                    
                    // Function to get chart title from various sources
                    function getChartTitle(element, chartId) {
                        let chartTitle = chartId ? `Chart ${chartId}` : 'Unknown Chart';
                        
                        // Method 1: From MUI Card Header
                        const container = element.closest('.MuiCard-root, .MuiPaper-root');
                        if (container) {
                            const cardHeader = container.querySelector('.MuiCardHeader-title');
                            if (cardHeader) {
                                const labels = cardHeader.querySelectorAll('label');
                                let titleText = '';
                                labels.forEach(label => {
                                    const text = label.textContent.trim();
                                    if (text && !text.match(/^\\d+$/)) {
                                        titleText += text + ' ';
                                    }
                                });
                                if (titleText.trim()) {
                                    chartTitle = titleText.trim();
                                }
                            }
                        }
                        
                        // Method 2: From Highcharts title (for SVG charts)
                        if (element.tagName && element.tagName.toLowerCase() === 'svg') {
                            const titleElement = element.querySelector('.highcharts-title');
                            if (titleElement && titleElement.textContent.trim()) {
                                chartTitle = titleElement.textContent.trim();
                            }
                        }
                        
                        // Method 3: From container or parent elements
                        let parent = element.closest('[class*="chart"], .widget, .dashboard-item');
                        if (parent) {
                            const titleElement = parent.querySelector('h1, h2, h3, h4, h5, h6, .title, [class*="title"]');
                            if (titleElement && titleElement.textContent.trim()) {
                                chartTitle = titleElement.textContent.trim();
                            }
                        }
                        
                        return chartTitle;
                    }
                    
                    // Create a unique identifier for each chart element
                    function getElementSignature(element) {
                        const rect = element.getBoundingClientRect();
                        return `${element.tagName.toLowerCase()}_${Math.round(rect.left)}_${Math.round(rect.top)}_${Math.round(rect.width)}_${Math.round(rect.height)}`;
                    }
                    
                    // 1. Find all Highcharts containers (primary detection)
                    const highchartsContainers = document.querySelectorAll('.highcharts-container, [data-highcharts-chart]');
                    
                    highchartsContainers.forEach((container, index) => {
                        const rect = container.getBoundingClientRect();
                        if (rect.width > 0 && rect.height > 0) {
                            const signature = getElementSignature(container);
                            
                            // Skip if we've already processed this element
                            if (processedElements.has(signature)) {
                                return;
                            }
                            processedElements.add(signature);
                            
                            const chartId = extractChartId(container);
                            const chartTitle = getChartTitle(container, chartId);
                            
                            // Find the SVG within this container
                            const svg = container.querySelector('svg.highcharts-root, svg[class*="highcharts"]');
                            
                            let chartType = 'Unknown';
                            let seriesCount = 0;
                            
                            if (svg) {
                                // Mark the SVG as processed to avoid duplicate detection
                                const svgSignature = getElementSignature(svg);
                                processedElements.add(svgSignature);
                                
                                const series = svg.querySelectorAll('.highcharts-series');
                                seriesCount = series.length;
                                
                                // Detect chart type from series
                                if (svg.querySelector('.highcharts-column-series')) {
                                    chartType = 'Column/Bar';
                                } else if (svg.querySelector('.highcharts-line-series')) {
                                    chartType = 'Line';
                                } else if (svg.querySelector('.highcharts-pie-series')) {
                                    chartType = 'Pie';
                                } else if (svg.querySelector('.highcharts-area-series')) {
                                    chartType = 'Area';
                                } else if (series.length > 0) {
                                    chartType = 'Mixed/Other';
                                }
                            }
                            
                            chartsInfo.push({
                                type: 'highcharts',
                                chartId: chartId || `highcharts-${chartIndex}`,
                                chartIndex: chartIndex++,
                                chartTitle: chartTitle,
                                chartType: chartType,
                                seriesCount: seriesCount,
                                elementId: container.id || `highcharts-container-${index}`,
                                elementClass: container.className,
                                position: {
                                    x: Math.round(rect.left),
                                    y: Math.round(rect.top),
                                    width: Math.round(rect.width),
                                    height: Math.round(rect.height)
                                },
                                visible: true,
                                hasInteractiveButtons: !!container.closest('.MuiCard-root')?.querySelector('button[id*="chart-details-"], button[id*="view-details-"]')
                            });
                        }
                    });
                    
                    // 2. Find standalone Canvas charts (Chart.js, etc.) - only if not inside Highcharts
                    const canvases = document.querySelectorAll('canvas');
                    canvases.forEach((canvas, index) => {
                        const rect = canvas.getBoundingClientRect();
                        
                        // Skip if this canvas is inside a highcharts container (already processed)
                        if (canvas.closest('.highcharts-container, [data-highcharts-chart]')) {
                            return;
                        }
                        
                        const signature = getElementSignature(canvas);
                        if (processedElements.has(signature)) {
                            return;
                        }
                        
                        let hasChart = false;
                        
                        // Check for Chart.js specific indicators
                        if (canvas.classList.contains('chartjs-render-monitor') || 
                            canvas.classList.contains('chartjs-render') ||
                            (canvas.width > 0 && canvas.height > 0 && rect.width > 100 && rect.height > 100)) {
                            hasChart = true;
                        }
                        
                        // Try to detect Chart.js instance
                        try {
                            if (typeof Chart !== 'undefined' && Chart.getChart) {
                                const chart = Chart.getChart(canvas);
                                if (chart) {
                                    hasChart = true;
                                }
                            }
                        } catch (e) {
                            // Continue with other checks
                        }
                        
                        if (hasChart && rect.width > 0 && rect.height > 0) {
                            processedElements.add(signature);
                            const chartId = extractChartId(canvas);
                            const chartTitle = getChartTitle(canvas, chartId);
                            
                            chartsInfo.push({
                                type: 'canvas',
                                chartIndex: chartIndex++,
                                chartId: chartId || `canvas-${index}`,
                                chartTitle: chartTitle,
                                chartType: 'Canvas Chart',
                                seriesCount: 0,
                                elementId: canvas.id || `canvas-${index}`,
                                elementClass: canvas.className,
                                position: {
                                    x: Math.round(rect.left),
                                    y: Math.round(rect.top),
                                    width: Math.round(rect.width),
                                    height: Math.round(rect.height)
                                },
                                visible: true,
                                isChartJs: canvas.classList.contains('chartjs-render-monitor'),
                                hasInteractiveButtons: false
                            });
                        }
                    });
                    
                    // 3. Find standalone SVG charts (not Highcharts) - only if not already processed
                    const svgs = document.querySelectorAll('svg');
                    svgs.forEach((svg, index) => {
                        const rect = svg.getBoundingClientRect();
                        
                        // Skip if this SVG is inside a highcharts container or is a Highcharts SVG
                        if (svg.closest('.highcharts-container, [data-highcharts-chart]') || 
                            svg.classList.contains('highcharts-root') ||
                            svg.querySelector('.highcharts-series')) {
                            return;
                        }
                        
                        const signature = getElementSignature(svg);
                        if (processedElements.has(signature)) {
                            return;
                        }
                        
                        let hasSvgChart = false;
                        
                        // Check for other chart libraries
                        if (svg.classList.contains('recharts-surface') ||
                            svg.classList.contains('d3-chart') ||
                            svg.querySelector('[class*="chart"]') ||
                            svg.querySelector('g[class*="series"]') ||
                            svg.querySelector('g[class*="axis"]')) {
                            hasSvgChart = true;
                        }
                        
                        // General SVG chart detection (more restrictive)
                        if (!hasSvgChart && rect.width > 100 && rect.height > 100) {
                            const hasAxes = svg.querySelector('g[class*="axis"], line[class*="axis"], path[class*="axis"]');
                            const hasSeries = svg.querySelector('g[class*="series"], rect[class*="bar"], circle[class*="point"], path[class*="line"]');
                            const hasGrid = svg.querySelector('g[class*="grid"], line[class*="grid"]');
                            
                            if ((hasAxes || hasSeries || hasGrid) && svg.children.length > 5) {
                                hasSvgChart = true;
                            }
                        }
                        
                        if (hasSvgChart && rect.width > 0 && rect.height > 0) {
                            processedElements.add(signature);
                            const chartId = extractChartId(svg);
                            const chartTitle = getChartTitle(svg, chartId);
                            
                            chartsInfo.push({
                                type: 'svg',
                                chartId: chartId || `svg-${index}`,
                                chartIndex: chartIndex++,
                                chartTitle: chartTitle,
                                chartType: 'SVG Chart',
                                seriesCount: svg.querySelectorAll('g[class*="series"]').length,
                                elementId: svg.id || `svg-${index}`,
                                elementClass: svg.className.baseVal || svg.className || '',
                                position: {
                                    x: Math.round(rect.left),
                                    y: Math.round(rect.top),
                                    width: Math.round(rect.width),
                                    height: Math.round(rect.height)
                                },
                                visible: true,
                                hasInteractiveButtons: false
                            });
                        }
                    });
                    
                    // Sort by position (top to bottom, left to right)
                    chartsInfo.sort((a, b) => {
                        if (Math.abs(a.position.y - b.position.y) > 10) {
                            return a.position.y - b.position.y;
                        }
                        return a.position.x - b.position.x;
                    });
                    
                    console.log(`Found ${chartsInfo.length} unique charts total`);
                    return chartsInfo;
                }
            """)
            
            print(f"Found {len(charts_info)} charts on PartsWorkMixAnalysis-2Months-Comparison")
            print(f"Found {charts_info} charts info on PartsWorkMixAnalysis-2Months-Comparison")

            for chart in charts_info:
                print(f"  - {chart['type'].upper()} Chart {chart['chartIndex']}: {chart['chartTitle']} (Element: {chart['elementId']})")
                print(f"    Type: {chart['chartType']} | Series: {chart['seriesCount']} | Size: {chart['position']['width']}x{chart['position']['height']}")

            return charts_info
            
        finally:
            await page.close()
       


   
        """
        Validate the extracted data for consistency and completeness
        """
        validation_results = {
            'is_valid': True,
            'warnings': [],
            'errors': [],
            'suggestions': []
        }
        
        try:
            # Check basic structure
            if not extracted_data.get('success'):
                validation_results['errors'].append("Extraction was not successful")
                validation_results['is_valid'] = False
                return validation_results
            
            # Validate data points
            all_points = extracted_data.get('all_points', [])
            if not all_points:
                validation_results['errors'].append("No data points extracted")
                validation_results['is_valid'] = False
            
            # Check for reasonable values
            values = [point.get('value', 0) for point in all_points if isinstance(point.get('value'), (int, float))]
            if values:
                max_val = max(values)
                min_val = min(values)
                
                if max_val > 10000:
                    validation_results['warnings'].append(f"Very high maximum value detected: {max_val}")
                
                if min_val < 0:
                    validation_results['warnings'].append(f"Negative values detected: minimum is {min_val}")
                
                # Check for suspiciously uniform values
                unique_values = len(set(values))
                if unique_values < len(values) * 0.3:
                    validation_results['warnings'].append("Many duplicate values detected - check extraction accuracy")
            
            # Validate categories
            main_cats = extracted_data.get('main_categories', [])
            sub_cats = extracted_data.get('subcategories', [])
            
            if not main_cats and not sub_cats:
                validation_results['warnings'].append("No categories detected - chart structure might be different than expected")
            
            # Check category mapping consistency
            mapping = extracted_data.get('category_mapping', {})
            if mapping:
                total_mapped_subcats = sum(len(subcats) for subcats in mapping.values())
                if total_mapped_subcats != len(sub_cats):
                    validation_results['warnings'].append("Category mapping inconsistency detected")
            
            # Validate extraction methods
            extraction_methods = [point.get('extractionMethod', 'unknown') for point in all_points]
            tooltip_count = extraction_methods.count('tooltip_extraction')
            visual_count = extraction_methods.count('visual_estimation')
            
            if tooltip_count == 0 and len(all_points) > 0:
                validation_results['warnings'].append("No successful tooltip extractions - values may be estimated")
            
            success_rate = tooltip_count / len(all_points) * 100 if all_points else 0
            if success_rate < 50:
                validation_results['suggestions'].append("Low tooltip success rate - consider adjusting hover timing or selectors")
            
            # Check for expected patterns if provided
            if expected_patterns:
                for pattern_name, pattern_value in expected_patterns.items():
                    if pattern_name == 'expected_main_categories':
                        missing_cats = set(pattern_value) - set(main_cats)
                        if missing_cats:
                            validation_results['warnings'].append(f"Expected main categories not found: {missing_cats}")
                    
                    elif pattern_name == 'expected_series_count':
                        actual_series = len(extracted_data.get('legend_labels', []))
                        if actual_series != pattern_value:
                            validation_results['warnings'].append(f"Expected {pattern_value} series, found {actual_series}")
            
            print(f"\n🔍 VALIDATION RESULTS:")
            print(f"   Valid: {'✅' if validation_results['is_valid'] else '❌'}")
            print(f"   Warnings: {len(validation_results['warnings'])}")
            print(f"   Errors: {len(validation_results['errors'])}")
            
            for warning in validation_results['warnings']:
                print(f"   ⚠️ {warning}")
            
            for error in validation_results['errors']:
                print(f"   ❌ {error}")
                
            for suggestion in validation_results['suggestions']:
                print(f"   💡 {suggestion}")
            
        except Exception as e:
            validation_results['errors'].append(f"Validation process failed: {str(e)}")
            validation_results['is_valid'] = False
        
        return validation_results

    async def find_matching_points_in_chart(self, page, chart_index, chart_id, target_month_year,chart_title):
        """Find matching data points for a specific SVG Highcharts chart with accurate coordinate extraction"""
        log_info(f"Finding matching points in chart {chart_index} - {chart_title} (ID: {chart_id}) for month {target_month_year}")
        try:
            results = await page.evaluate(
                """
                async (args) => {
                    const { chartIndex, chartId, targetMonthYear } = args;
                    
                    const chartContainer = document.querySelector('.highcharts-container.chartid-' + chartId);
                    if (!chartContainer) {
                        return { 
                            found: false, 
                            chartId: chartId, 
                            error: "Container not found for chartid-" + chartId 
                        };
                    }

                    if (!chartContainer.querySelector('.highcharts-series')) {
                        return { 
                            found: false, 
                            chartId: chartId, 
                            error: "Not a Highcharts chart" 
                        };
                    }

                    // Get SVG root element for accurate coordinate calculations
                    const svgRoot = chartContainer.querySelector('.highcharts-root');
                    if (!svgRoot) {
                        return { found: false, chartId: chartId, error: "SVG root not found" };
                    }

                    // Get chart dimensions and axis information with SVG-specific logic
                    function getAxisInfo() {
                        const plotArea = chartContainer.querySelector('.highcharts-plot-background');
                        const xAxisLabels = Array.from(chartContainer.querySelectorAll('.highcharts-xaxis-labels span'));
                        const yAxisLabels = Array.from(chartContainer.querySelectorAll('.highcharts-yaxis-labels text'));
                        
                        let plotBounds = { x: 0, y: 0, width: 0, height: 0 };
                        if (plotArea) {
                            plotBounds = {
                                x: parseFloat(plotArea.getAttribute('x')) || 0,
                                y: parseFloat(plotArea.getAttribute('y')) || 0,
                                width: parseFloat(plotArea.getAttribute('width')) || 0,
                                height: parseFloat(plotArea.getAttribute('height')) || 0
                            };
                        }

                        // Extract Y-axis scale information
                        const yAxisValues = yAxisLabels.map(label => {
                            const text = label.textContent.trim();
                            const y = parseFloat(label.getAttribute('y')) || 0;
                            const numValue = parseFloat(text.replace(/[,$%]/g, ''));
                            return { text, y, value: isNaN(numValue) ? 0 : numValue };
                        }).filter(item => !isNaN(item.value)).sort((a, b) => b.y - a.y);

                        return { plotBounds, xAxisLabels, yAxisLabels, yAxisValues };
                    }

                    const axisInfo = getAxisInfo();

                    // Function to parse transform attribute and get translation values
                    function getTransformTranslation(transformString) {
                        if (!transformString) return { x: 0, y: 0 };
                        
                        const translateMatch = transformString.match(/translate\\(([^,]+),\\s*([^)]+)\\)/);
                        if (translateMatch) {
                            return {
                                x: parseFloat(translateMatch[1]) || 0,
                                y: parseFloat(translateMatch[2]) || 0
                            };
                        }
                        return { x: 0, y: 0 };
                    }

                    // Function to calculate actual coordinate values for SVG
                    function calculateAxisValues(pointData, axisInfo) {
                        const { plotBounds, yAxisValues } = axisInfo;
                        // Calculate X-axis value (category index)
                        const xValue = pointData.pointIndex;
                        
                        // Calculate Y-axis value using interpolation
                        let yValue = 0;
                        if (yAxisValues.length >= 2) {
                            const relativeY = pointData.absoluteY - plotBounds.y;
                            const plotHeight = plotBounds.height;
                            const relativePosition = 1 - (relativeY / plotHeight); // Invert because SVG Y increases downward
                            
                            const minAxisValue = Math.min(...yAxisValues.map(av => av.value));
                            const maxAxisValue = Math.max(...yAxisValues.map(av => av.value));
                            
                            yValue = minAxisValue + (relativePosition * (maxAxisValue - minAxisValue));
                            yValue = Math.round(yValue * 100) / 100;
                        } else if (pointData.height > 0) {
                            yValue = Math.max(0, Math.round(((283 - 10 - pointData.absoluteY) / 283) * 30 * 10) / 10);
                        }

                        // Return the absolute coordinates for screen positioning
                        return {
                            xAxisValue: xValue,
                            yAxisValue: yValue,
                            screenX: pointData.absoluteX,  // Absolute X coordinate including transform
                            screenY: pointData.absoluteY   // Absolute Y coordinate including transform
                        };
                    }

                    // Get legends for month information
                    const legendItems = Array.from(chartContainer.querySelectorAll('.highcharts-legend-item text'));
                    const legends = legendItems.map(el => el.textContent.trim());
                    
                    function extractMonthsFromLegends(legends) {
                        const monthPattern = /(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\\d{2}/gi;
                        const extractedMonths = [];
                        
                        legends.forEach(legend => {
                            const matches = legend.match(monthPattern);
                            if (matches) {
                                matches.forEach(match => {
                                    if (!extractedMonths.includes(match)) {
                                        extractedMonths.push(match);
                                    }
                                });
                            }
                        });
                        
                        return extractedMonths.sort();
                    }
                    
                    const availableMonths = extractMonthsFromLegends(legends);

                    // Extract and map categories
                    const categorySpans = Array.from(chartContainer.querySelectorAll('.highcharts-axis-labels.highcharts-xaxis-labels span'));
                    const allCategories = categorySpans.map((span, index) => {
                        const titleSpan = span.querySelector('span[title]');
                        if (!titleSpan) return null;
                        return {
                            text: titleSpan.getAttribute('title').trim(),
                            position: index,
                            x: parseFloat(span.style.left) || 0
                        };
                    }).filter(cat => cat !== null);

                    const knownMainCategories = ['COMP', 'MAINT', 'REPAIR'];
                    const categoryMapping = { "COMP": [], "MAINT": [], "REPAIR": [] };
                    
                    const opcategoryPositions = [];
                    allCategories.forEach((category, index) => {
                        if (knownMainCategories.includes(category.text)) {
                            opcategoryPositions.push({
                                name: category.text,
                                position: index
                            });
                        }
                    });
                    opcategoryPositions.sort((a, b) => a.position - b.position);

                    // Assign subcategories to main categories
                    allCategories.forEach((category, index) => {
                        if (knownMainCategories.includes(category.text)) return;

                        let assignedMainCategory = null;
                        
                        for (let i = 0; i < opcategoryPositions.length; i++) {
                            if (index === opcategoryPositions[i].position - 1) {
                                assignedMainCategory = opcategoryPositions[i].name;
                                break;
                            }
                        }
                        
                        if (!assignedMainCategory) {
                            for (let i = 0; i < opcategoryPositions.length; i++) {
                                const currentMainPos = opcategoryPositions[i].position;
                                const nextMainPos = i + 1 < opcategoryPositions.length ? 
                                                opcategoryPositions[i + 1].position : allCategories.length;
                                
                                if (index > currentMainPos && index < nextMainPos) {
                                    let belongsToLeft = true;
                                    if (i + 1 < opcategoryPositions.length && index === opcategoryPositions[i + 1].position - 1) {
                                        belongsToLeft = false;
                                    }
                                    if (belongsToLeft) {
                                        assignedMainCategory = opcategoryPositions[i].name;
                                        break;
                                    }
                                }
                            }
                        }
                        
                        if (!assignedMainCategory && opcategoryPositions.length > 0 && index < opcategoryPositions[0].position) {
                            assignedMainCategory = opcategoryPositions[0].name;
                        }

                        if (assignedMainCategory && !categoryMapping[assignedMainCategory].includes(category.text)) {
                            categoryMapping[assignedMainCategory].push(category.text);
                        }
                    });

                    // Get all chart points with precise SVG coordinate extraction
                    function getAllChartPoints() {
                        const allPointsMetadata = [];
                        let globalPointIndex = 0;
                        
                        const seriesGroups = chartContainer.querySelectorAll('.highcharts-series-group > .highcharts-series');
                        
                        seriesGroups.forEach((seriesEl, seriesIndex) => {
                            // Get the transform of the series group to calculate absolute coordinates
                            const seriesTransform = getTransformTranslation(seriesEl.getAttribute('transform'));
                            
                            let seriesPoints = Array.from(seriesEl.querySelectorAll('.highcharts-point'));
                            
                            if (seriesPoints.length === 0) {
                                seriesPoints = Array.from(seriesEl.querySelectorAll('rect[x][y][width][height]'));
                            }
                            
                            seriesPoints = seriesPoints.filter(point => {
                                const width = parseFloat(point.getAttribute('width')) || 0;
                                const height = parseFloat(point.getAttribute('height')) || 0;
                                return width > 0 && height >= 0;
                            });
                            
                            seriesPoints.sort((a, b) => {
                                const xA = parseFloat(a.getAttribute('x')) || 0;
                                const xB = parseFloat(b.getAttribute('x')) || 0;
                                return xA - xB;
                            });
                            
                            seriesPoints.forEach((pointEl, pointIndex) => {
                                // Extract exact SVG coordinates from attributes
                                const svgX = parseFloat(pointEl.getAttribute('x')) || 0;
                                const svgY = parseFloat(pointEl.getAttribute('y')) || 0;
                                const svgWidth = parseFloat(pointEl.getAttribute('width')) || 0;
                                const svgHeight = parseFloat(pointEl.getAttribute('height')) || 0;
                                
                                // Calculate absolute coordinates by adding the series transform
                                const absoluteX = svgX + seriesTransform.x;
                                const absoluteY = svgY + seriesTransform.y;
                                
                                // Calculate center point for more accurate hovering
                                const centerX = absoluteX + (svgWidth / 2);
                                const centerY = absoluteY + (svgHeight / 2);
                                
                                console.log(`Series ${seriesIndex}, Point ${pointIndex}: SVG(${svgX}, ${svgY}) + Transform(${seriesTransform.x}, ${seriesTransform.y}) = Absolute(${absoluteX}, ${absoluteY}) Center(${centerX}, ${centerY})`);
                                
                                allPointsMetadata.push({
                                    pointEl: pointEl,
                                    seriesIndex: seriesIndex,
                                    pointIndex: pointIndex,
                                    globalIndex: globalPointIndex++,
                                    svgX: svgX,
                                    svgY: svgY,
                                    absoluteX: absoluteX,
                                    absoluteY: absoluteY,
                                    centerX: centerX,
                                    centerY: centerY,
                                    width: svgWidth,
                                    height: svgHeight,
                                    seriesTransform: seriesTransform
                                });
                            });
                        });
                        
                        return allPointsMetadata;
                    }

                    // Enhanced tooltip data extraction with currency support
                    function extractTooltipData(pointEl, pointIndex, availableMonths, pointMeta) {
                        return new Promise((resolve) => {
                            try {
                                // Use the center coordinates for more accurate hovering
                                const hoverX = pointMeta.centerX;
                                const hoverY = pointMeta.centerY;
                                
                                // Clear existing tooltips
                                chartContainer.querySelectorAll('.highcharts-tooltip, [class*="tooltip"]').forEach(tooltip => {
                                    tooltip.style.display = 'none';
                                    tooltip.style.visibility = 'hidden';
                                });

                                // Trigger hover events with precise coordinates
                                const hoverEvents = ['mouseenter', 'mouseover', 'mousemove'];
                                hoverEvents.forEach(eventType => {
                                    const event = new MouseEvent(eventType, {
                                        bubbles: true,
                                        cancelable: true,
                                        view: window,
                                        clientX: hoverX,
                                        clientY: hoverY
                                    });
                                    pointEl.dispatchEvent(event);
                                });
                                
                                // Wait for tooltip to render
                                setTimeout(() => {
                                    let tooltipData = {
                                        success: false,
                                        fullText: '',
                                        rawHTML: '',
                                        value: null,
                                        category: null,
                                        opcategory: null,
                                        opcode: null,
                                        monthValues: [],
                                        series: null
                                    };
                                    
                                    // Find visible tooltip
                                    let tooltipText = '';
                                    let tooltipHTML = '';
                                    
                                    const allElements = Array.from(
                                        document.querySelectorAll("div.highcharts-tooltip, div.highcharts-label.highcharts-tooltip")
                                    ).filter(el => el.style.visibility === "visible");
                                    
                                    for (let el of allElements) {
                                        tooltipText = el.textContent || '';
                                        tooltipHTML = el.innerHTML || tooltipText;
                                        break;
                                    }

                                    if (tooltipText) {
                                        tooltipData.success = true;
                                        tooltipData.fullText = tooltipText;
                                        tooltipData.rawHTML = tooltipHTML;
                                        
                                        // Extract category information
                                        //const categoryPattern1 = tooltipText.match(/([A-Z0-9*]+)\\s*,\\s*([A-Z]+)/);
                                        // Extract category information (allow * and all other special characters, but not blank)
                                        const categoryPattern1 = tooltipText.match(/([^,\\s]+)\\s*,\\s*([A-Z]+)/);
                                        if (categoryPattern1) {
                                            tooltipData.opcode = categoryPattern1[1];
                                            tooltipData.category = categoryPattern1[1];
                                            tooltipData.opcategory = categoryPattern1[2];
                                        }
                                        
                                        // Extract month values with currency support
                                        let monthValues = [];
                                        const monthsPattern = availableMonths.length > 0 ? 
                                            availableMonths.join('|') : 
                                            '(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\\\\d{2}';
                                        
                                        // Strategy 1: Extract from <b> tags
                                        const bTagsPattern = new RegExp('<b>([\\$€£¥₹]?)([+-]?[0-9,]*\\.?[0-9]+(?:[eE][+-]?[0-9]+)?)\\s*([%]?[a-zA-Z]*)?<\\/b>', 'gi');
                                        const bTags = tooltipHTML.match(bTagsPattern);
                                        
                                        if (bTags && bTags.length >= 1) {
                                            const values = bTags.map((bTag, index) => {
                                                const match = bTag.match(/<b>([\\$€£¥₹]?)([+-]?[0-9,]*\\.?[0-9]+(?:[eE][+-]?[0-9]+)?)\\s*([%$€£¥₹]?[a-zA-Z]*)?<\\/b>/i);
                                                if (match) {
                                                    const currencySymbol = match[1] || '';
                                                    const numericStr = match[2].replace(/,/g, '');
                                                    const numericValue = parseFloat(numericStr);
                                                    const unit = match[3] || '';
                                                    
                                                    const monthLabel = availableMonths[index] || `Month-${index + 1}`;
                                                    return { 
                                                        month: monthLabel, 
                                                        value: numericValue, 
                                                        unit: currencySymbol + unit,
                                                        currencySymbol: currencySymbol,
                                                        raw: match[2] 
                                                    };
                                                }
                                                return null;
                                            }).filter(val => val !== null && !isNaN(val.value));
                                            
                                            monthValues.push(...values);
                                        }
                                        
                                        if (monthValues.length > 0) {
                                            const monthOrder = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                                            monthValues.sort((a, b) => {
                                                const aMonth = a.month.split('-')[0];
                                                const bMonth = b.month.split('-')[0];
                                                const aIndex = monthOrder.indexOf(aMonth);
                                                const bIndex = monthOrder.indexOf(bMonth);
                                                if (aIndex !== -1 && bIndex !== -1) {
                                                    return aIndex - bIndex;
                                                }
                                                return a.month.localeCompare(b.month);
                                            });
                                            
                                            tooltipData.monthValues = monthValues;
                                            tooltipData.value = monthValues[0].value;
                                        }
                                    }
                                    
                                    // Cleanup
                                    const cleanupEvents = ['mouseout', 'mouseleave'];
                                    cleanupEvents.forEach(eventType => {
                                        pointEl.dispatchEvent(new MouseEvent(eventType, { bubbles: true }));
                                    });
                                    
                                    resolve(tooltipData);
                                }, 700);
                                
                            } catch (error) {
                                resolve({
                                    success: false,
                                    error: error.message,
                                    fullText: '',
                                    value: null,
                                    category: null,
                                    opcategory: null,
                                    opcode: null,
                                    monthValues: []
                                });
                            }
                        });
                    }

                    // Process all points
                    const allPointsMetadata = getAllChartPoints();
                    const tooltipResults = [];
                    
                    for (let i = 0; i < allPointsMetadata.length; i++) {
                        const pointMeta = allPointsMetadata[i];
                        const tooltipData = await extractTooltipData(pointMeta.pointEl, i, availableMonths, pointMeta);
                        await new Promise(resolve => setTimeout(resolve, 150));
                        
                        tooltipResults.push({
                            ...pointMeta,
                            tooltipData: tooltipData
                        });
                    }
                    
                    // Organize into series structure with corrected coordinates
                    const series = [];
                    const seriesElements = chartContainer.querySelectorAll('.highcharts-series.highcharts-column-series.highcharts-tracker');
                    
                    seriesElements.forEach((seriesEl, seriesIndex) => {
                        const points = [];
                        const seriesResults = tooltipResults.filter(r => r.seriesIndex === seriesIndex);
                        
                        seriesResults.forEach((result, pointIndex) => {
                            const tooltipData = result.tooltipData;
                            
                            let value = tooltipData.value;
                            if (!value && result.height > 0) {
                                value = Math.max(0, Math.round(((283 - 10 - result.absoluteY) / 283) * 30 * 10) / 10);
                            }
                            
                            let category = tooltipData.category;
                            let opcategory = tooltipData.opcategory;
                            let opcode = tooltipData.opcode;
                            
                            if (!category && pointIndex < allCategories.length) {
                                const catInfo = allCategories[pointIndex];
                                category = catInfo.text;
                                
                                if (knownMainCategories.includes(catInfo.text)) {
                                    opcategory = catInfo.text;
                                } else {
                                    opcode = catInfo.text;
                                    for (let mainCat of Object.keys(categoryMapping)) {
                                        if (categoryMapping[mainCat].includes(catInfo.text)) {
                                            opcategory = mainCat;
                                            break;
                                        }
                                    }
                                }
                            }
                            
                            // Calculate corrected axis values
                            const axisValues = calculateAxisValues(result, axisInfo);
                            
                            function getPrefixFromTitle(chartTitle) {
                                if (!chartTitle) return "mon"; // fallback

                                const lowerTitle = chartTitle.toLowerCase();
                                console.log("DEBUG chartTitle:", chartTitle);
                                console.log("DEBUG lowerTitle:", lowerTitle);

                                if (lowerTitle.includes("work mix")) return "workmix_";
                                if (lowerTitle.includes("parts markup")) return "parts_markup_";
                                if (lowerTitle.includes("parts cost")) return "parts_cost_";
                                if (lowerTitle.includes("job count")) return "job_count_";
                                if (lowerTitle.includes("gross profit")) return "gp_percentage_";
                                
                                return "mon"; // default if no match
                            }


                            const prefix = getPrefixFromTitle(args.chartTitle);

                            // Build dynamic month data structure
                            const monthData = {};
                            if (tooltipData.monthValues && tooltipData.monthValues.length > 0) {
                                tooltipData.monthValues.forEach((monthValue, index) => {
                                    monthData[`${prefix}${index + 1}`] = monthValue.value;
                                    monthData[`${prefix}month${index + 1}Label`] = monthValue.month;
                                });
                            }
                            
                            // Build point data with corrected coordinates
                            points.push({
                                category: category,
                                opcategory: opcategory,
                                opcode: opcode,
                                series: legends[seriesIndex] || 'Series ' + (seriesIndex + 1),
                                value: value,
                                ...monthData,
                                //chart_title: args.chartTitle || '',
                                xAxisValue: axisValues.xAxisValue,
                                yAxisValue: axisValues.yAxisValue,
                                screenX: axisValues.screenX,  // Absolute X coordinate
                                screenY: axisValues.screenY,  // Absolute Y coordinate
                                centerX: result.centerX,      // Center X for hovering
                                centerY: result.centerY,      // Center Y for hovering
                                tooltipText: tooltipData.fullText,
                                tooltipSuccess: tooltipData.success,
                                pointIndex: pointIndex,
                                globalIndex: result.globalIndex,
                                seriesIndex: seriesIndex      // Add series index for identification
                            });
                        });
                        
                        series.push({
                            name: legends[seriesIndex] || 'Series ' + (seriesIndex + 1),
                            points: points
                        });
                    });

                    return {
                        found: true,
                        chartId: chartId,
                        library: "Highcharts",
                        series: series,
                        totalPointsProcessed: allPointsMetadata.length,
                        successfulTooltips: tooltipResults.filter(r => r.tooltipData.success).length,
                        categoryMapping: categoryMapping,
                        legends: legends,
                        availableMonths: availableMonths,
                        axisInfo: {
                            plotBounds: axisInfo.plotBounds,
                            yAxisValues: axisInfo.yAxisValues,
                            xAxisLabelsCount: axisInfo.xAxisLabels.length,
                            yAxisLabelsCount: axisInfo.yAxisLabels.length
                        }
                    };
                }
                """,
                {
                    "chartIndex": chart_index,
                    "chartId": chart_id, 
                    "targetMonthYear": target_month_year,
                    "chartTitle": chart_title
                }
            )
            log_info(f" Chart {chart_index} - Retrieved {results.get('totalPointsProcessed', 0)} points, {results.get('successfulTooltips', 0)} tooltips")    
            log_info(f" Chart {chart_index} - Available matching point: ", results)
            return results
                    
        except Exception as e:
            print(f"Error finding matching points in chart {chart_index}: {str(e)}")
            return []
                 
    async def create_chart_point_combinations(self, target_months_years):
        """Create combinations of charts and their matching points"""
        print("Creating chart-point combinations...")

        
        # Use the dedicated parallel function to check and re-login the session.
        # This ensures a valid session before a new page is even created.
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []

        # Get a NEW PAGE from the existing session.
        page = await self.auth_manager.new_page()   
            
        try:
            # Add this block at the end of parse_arguments()
            
            log_info(" Navigated to new page for chart-point combination creation")

            # Navigate
            await page.goto(f"{config.site_url.rstrip('/')}/PartsWorkMixAnalysis", timeout=50000)
            log_info(" Navigated to PartsWorkMixAnalysis page")
            # Click the button
            await page.click('button:has-text("2 Month Work Mix Comparison")', timeout=500000)
            log_info(" Clicked '2 Month Work Mix Comparison' button")
            log_info(f"Current URL before Month 1 selection: {page.url}")
            # Wait for Month 1 dropdown to be visible
            await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
            log_info(f"button selected : {page.url}")

            await page.click('label:has-text("Month 1") + div [role="button"]')
            log_info(f"label clicked : {page.url}")
            log_info(f"Selecting Month 1: {config.month1}")
            await page.click(f'li[role="option"]:has-text("{config.month1}")')
            log_info(f"Current URL after Month 1 selection: {page.url}")


            # Wait for Month 2 dropdown
            await page.wait_for_selector('label:has-text("Month 2") + div [role="button"]', timeout=50000)
            await page.click('label:has-text("Month 2") + div [role="button"]')
            log_info(f"Selecting Month 2: {config.month2}")

            await page.click(f'li[role="option"]:has-text("{config.month2}")')

            # Wait for the chart/table container to load before capture
            for i in range(5):
                try:
                    await page.wait_for_selector('div.highcharts-container', timeout=10000)
                    log_info("Chart loaded successfully")
                    break
                except:
                    log_info(f"Retrying chart load... attempt {i+1}")
            else:
                raise Exception("Chart did not load after retries")

            log_info("Navigated to PartsWorkMixAnalysis-2Months-Comparison page from chart-point combination creation")

            # Discover all charts
            charts_info = await self.discover_charts()
            log_info(len(charts_info),"Charts found:", charts_info)

            if not charts_info:
                print(" No charts found")
                # Delete auth_state.json file when no charts are found
                auth_state_path = "auth_state.json"
                try:
                    if os.path.exists(auth_state_path):
                        os.remove(auth_state_path)
                        print(f"Deleted {auth_state_path} due to no charts found")
                    else:
                        print(f"{auth_state_path} not found to delete")
                except Exception as e:
                    print(f" Error deleting {auth_state_path}: {e}")
                return []
            
            chart_point_combinations = []
            charts_with_points = []
            log_info(f" Start Processing charts_info")
            # For each chart, find matching points for each target month-year
            for chart_info in charts_info:
                chart_index = chart_info['chartIndex']
                container_id = chart_info.get('containerId', '')
                chart_id =  chart_info['chartId']
                title=chart_info['chartTitle']
                log_info(f"📊 Chart Title -", title)
                chart_title = chart_info.get('chartTitle', f'Chart {chart_id}')
                log_info(f"Processing Chart {chart_id}: {chart_title}")
                
                chart_total_points = 0
                chart_combinations = []
                log_info(f" Chart {chart_index} - Looking for matching points for target months: {config.target_month_year}")
                # Process each target month-year for this chart
                for target_month_year in target_months_years:
                    log_info(f"  📅 Looking for data points matching: {target_month_year}")
                    
                    # Find matching points for this chart and target month-year
                    matching_points = await self.find_matching_points_in_chart(
                        page, chart_index, chart_id, target_month_year,chart_title
                    )
                    log_info("Matching points:", matching_points)
                    # log_info(f"  Found {len(matching_points) if matching_points else 0} matching points for {target_month_year}")
                    
                    if matching_points:
                        # Create combination for this chart and target month-year
                        combination = {
                            'chart_index': f"chart_{chart_index}",
                            'chart_id': chart_id,
                            'chart_info': chart_info,
                            'target_month_year': target_month_year,
                            'matching_points': matching_points,
                            'processing_status': 'pending',
                            'points_count': len(matching_points)
                        }
                        chart_combinations.append(combination)
                        chart_total_points += len(matching_points)
                        print
                        
                        print(f"   Added combination: Chart {chart_index} - {target_month_year} ({len(matching_points)} points)")
                    else:
                        print(f"   No matching points found for Chart {chart_index} - {target_month_year}")
                
                # Track charts with their point counts
                if chart_combinations:
                    charts_with_points.append({
                        'chart_index': chart_index,
                        'chart_id': chart_id,
                        'chart_title': chart_title,
                        'total_points': chart_total_points,
                        'combinations': chart_combinations
                    })
            
            # Sort charts by total points (descending) to get charts with most points first
            charts_with_points.sort(key=lambda x: x['total_points'], reverse=True)
            
            # Take all charts and their combinations (all 12 combinations)
            for chart_data in charts_with_points:
                chart_point_combinations.extend(chart_data['combinations'])
                print(f"  Added Chart {chart_data['chart_index']}: {chart_data['chart_title']} ({chart_data['total_points']} points)")
            
            print(f"\nSummary: Created {len(chart_point_combinations)} chart-point combinations from {len(charts_with_points)} charts")
            
            # Print summary by chart
            chart_summary = {}
            for combo in chart_point_combinations:
                chart_id = combo['chart_id']
                if chart_id not in chart_summary:
                    chart_summary[chart_id] = 0
                chart_summary[chart_id] += 1
            
            for chart_id, count in chart_summary.items():
                print(f"  {chart_id}: {count} combinations")
            
            return chart_point_combinations
            
        except Exception as e:
            print(f" Error creating chart-point combinations: {str(e)}")
            return []
        
        finally:
            # await context.close()
            await page.close()

    async def process_chart_combination(self, combination,target_month_year, browser_id):
        """Process a single chart-point combination in its own browser"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]
    
        print(f"Browser {browser_id}: Processing {combination['chart_id']} - {combination['target_month_year']}")
        
        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            
            try:
                # Navigate to PartsWorkMixAnalysis
                await page.goto(f"{config.site_url.rstrip('/')}/PartsWorkMixAnalysis", timeout=50000)
                # Click the button
                await page.click('button:has-text("2 Month Work Mix Comparison")', timeout=500000)
                # Wait for Month 1 dropdown to be visible
                await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
                await page.click('label:has-text("Month 1") + div [role="button"]')
                await page.click(f'li[role="option"]:has-text("{config.month1}")')

                # Wait for Month 2 dropdown
                await page.wait_for_selector('label:has-text("Month 2") + div [role="button"]', timeout=50000)
                await page.click('label:has-text("Month 2") + div [role="button"]')
                await page.click(f'li[role="option"]:has-text("{config.month2}")')

                # Wait for the chart/table container to load before capture
                for i in range(5):
                    try:
                        await page.wait_for_selector('div.highcharts-container', timeout=10000)
                        log_info("Chart loaded successfully")
                        break
                    except:
                        log_info(f"Retrying chart load... attempt {i+1}")
                else:
                    raise Exception("Chart did not load after retries")
                
                results = []
                
                # Process each matching point in this combination
                for point in combination['matching_points']:
                    try:
                        print()
                        print(f"🎯 Browser {browser_id}: Clicking point {point['xLabel']} (Value: {point['value']})")
                        
                        # Click on the data point
                        extracted_data = await self.click_and_extract_data(page, point,target_month_year)
                        log_info(f" Browser {browser_id}: Extracted data for point {point['xLabel']}: {extracted_data}")
                        result = {
                                'chart_id': combination['chart_id'],
                                'chart_title': combination['chart_info']['chartTitle'],
                                'target_month_year': combination['target_month_year'],
                                'clicked_point': point,
                                'extracted_data': extracted_data,
                                'timestamp': datetime.now().isoformat(),
                                'browser_id': browser_id,
                                'success': True
                            }
                            
                        results.append(result)
                        print(f" Browser {browser_id}: Successfully processed point {point['xLabel']}")
                        
                        # Wait before processing next point
                        await asyncio.sleep(3)
                      
                    except Exception as e:
                        log_error(f" Browser {browser_id}: Error processing point {point.get('x_label', 'unknown')}: {e}")
                        
                        error_result = {
                            'chart_id': combination['chart_id'],
                            'chart_title': combination['chart_info']['chartTitle'],
                            'target_month_year': combination['target_month_year'],
                            'clicked_point': point,
                            'error': str(e),
                            'timestamp': datetime.now().isoformat(),
                            'browser_id': browser_id,
                            'success': False
                        }
                        results.append(error_result)
                
                combination['processing_status'] = 'completed'
                combination['results'] = results
                
                print(f" Browser {browser_id}: Completed {combination['chart_id']} - {len(results)} results")
                return combination
                
            except Exception as e:
                print(f" Browser {browser_id}: Error processing combination {combination['chart_id']}: {e}")
                combination['processing_status'] = 'failed'
                combination['error'] = str(e)
                return combination
                
            finally:
                await context.close()
                await browser.close()

    async def click_chart_point(self, page, point):
        """Click on a chart data point"""
        try:
            # Check if we have valid coordinates
            if not point.get('coordinatesValid', False):
                print(f"Invalid coordinates for point {point['x_label']}")
                return False
            
            screen_x = point['screenX']
            screen_y = point['screenY']
            
            if not (screen_x and screen_y and not (isnan(float(screen_x)) or isnan(float(screen_y)))):
                print(f"Invalid screen coordinates: ({screen_x}, {screen_y})")
                return False
            
            # Click at the calculated coordinates
            await page.mouse.click(screen_x, screen_y)
            print(f"Clicked at coordinates ({screen_x}, {screen_y})")
            
            # Wait for any UI changes
            await asyncio.sleep(1)
            
            return True
            
        except Exception as e:
            print(f" Error clicking chart point: {e}")
            return False

    async def click_and_extract_data(self, page, point_data, target_month_year):
        """Click on a chart point and extract drilldown data"""
        result = {
            "target_month_year": target_month_year,
            "point_data": point_data,
            "click_success": False,
            "navigation_success": False,
            "extraction_data": None,
            "error": None,
            "processing_time": None,
            "screenshot_path": None
        }
        
        start_time = time.time()
        
        try:
            x_coord = point_data['screenX']
            y_coord = point_data['screenY']            
            # Validate coordinates
            if not (isinstance(x_coord, (int, float)) and isinstance(y_coord, (int, float))):
                result["error"] = "Invalid coordinates"
                return result            
            if not (0 <= x_coord <= 3000 and 0 <= y_coord <= 2000):
                result["error"] = f"Coordinates out of bounds: ({x_coord}, {y_coord})"
                return result            
            # Wait for page to be ready
            # await page.wait_for_load_state("networkidle", timeout=10000)
            await asyncio.sleep(1)            
            # Click on the point
            print(f"Clicking on point at ({x_coord}, {y_coord})")
            await page.mouse.move(x_coord, y_coord)
            await asyncio.sleep(0.5)
            await page.mouse.click(x_coord, y_coord)
            result["click_success"] = True            
            # Wait for navigation/modal
            await asyncio.sleep(3)            
            try:
                # await page.wait_for_load_state("networkidle", timeout=50000)
                result["navigation_success"] = True                
                # Extract data from drilldown page
                extraction_data = await self.extract_drilldown_data(page)
                result["extraction_data"] = extraction_data           
                
            except Exception as nav_error:
                result["error"] = f"Navigation error: {str(nav_error)}"                
        except Exception as e:
            result["error"] = f"Click error: {str(e)}"
        
        result["processing_time"] = time.time() - start_time
        return result
           
    async def extract_table_data(self, table_element):
        """Extract data from a table element"""
        try:
            # Get table headers
            headers = []
            header_elements = await table_element.query_selector_all('th, thead td')
            for header in header_elements:
                header_text = await header.text_content()
                if header_text:
                    headers.append(header_text.strip())
            
            # Get table rows
            rows = []
            row_elements = await table_element.query_selector_all('tbody tr, tr:not(:first-child)')
            for row in row_elements:
                row_data = []
                cell_elements = await row.query_selector_all('td, th')
                for cell in cell_elements:
                    cell_text = await cell.text_content()
                    row_data.append(cell_text.strip() if cell_text else '')
                
                if row_data:  # Only add non-empty rows
                    rows.append(row_data)
            
            return {
                'headers': headers,
                'rows': rows,
                'row_count': len(rows)
            }
            
        except Exception as e:
            print(f" Error extracting table data: {e}")
            return None

    async def implement_enhanced_single_legend_control(self, page):
        """Enhanced implementation for Chart.js single legend control with canvas detection and legend toggle"""

        enhanced_legend_control_script = """
        (function() {
            console.log('Implementing enhanced single legend control for Chart.js...');
            
            // Global variables
            let chartRotationIntervals = new Map();
            let currentActiveIndices = new Map();
            let chartInstances = new Map();
            let legendEnabled = new Map(); // Track legend state for each chart
            
            // Enhanced function to find Chart.js instances from canvas elements
            function findAllChartInstances() {
                const charts = [];
                
                // Method 1: Find canvases with Chart.js render monitor class
                const canvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                console.log(`Found ${canvases.length} Chart.js canvas elements`);
                
                canvases.forEach((canvas, index) => {
                    // Try multiple ways to access the Chart.js instance
                    let chartInstance = null;
                    
                    // Method A: Direct chart property
                    if (canvas.chart) {
                        chartInstance = canvas.chart;
                    }
                    // Method B: Check Chart.js global instances
                    else if (window.Chart && window.Chart.instances) {
                        const chartId = canvas.getAttribute('data-chartjs-id') || 
                                    canvas.getAttribute('id') || 
                                    Object.keys(window.Chart.instances)[index];
                        if (chartId && window.Chart.instances[chartId]) {
                            chartInstance = window.Chart.instances[chartId];
                        }
                    }
                    // Method C: Search through all Chart instances
                    else if (window.Chart && window.Chart.instances) {
                        Object.values(window.Chart.instances).forEach(instance => {
                            if (instance.canvas === canvas) {
                                chartInstance = instance;
                            }
                        });
                    }
                    
                    if (chartInstance && chartInstance.data && chartInstance.data.datasets) {
                        const chartId = `chart-${index}-${Date.now()}`;
                        charts.push({
                            id: chartId,
                            canvas: canvas,
                            chart: chartInstance,
                            datasetsCount: chartInstance.data.datasets.length
                        });
                        
                        console.log(`Chart ${chartId}: Found with ${chartInstance.data.datasets.length} datasets`);
                    } else {
                        console.log(`Canvas ${index}: No Chart.js instance found`);
                    }
                });
                
                // Method 2: Fallback - check all Chart.js instances globally
                if (charts.length === 0 && window.Chart && window.Chart.instances) {
                    console.log('Fallback: Searching through global Chart instances...');
                    Object.entries(window.Chart.instances).forEach(([key, instance], index) => {
                        if (instance && instance.canvas && instance.data && instance.data.datasets) {
                            const chartId = `global-chart-${index}`;
                            charts.push({
                                id: chartId,
                                canvas: instance.canvas,
                                chart: instance,
                                datasetsCount: instance.data.datasets.length
                            });
                            console.log(` Global Chart ${chartId}: Found with ${instance.data.datasets.length} datasets`);
                        }
                    });
                }
                
                return charts;
            }
            
            // Function to create enhanced status indicator
            function createEnhancedStatusIndicator(chartData) {
                const { id, canvas, chart, datasetsCount } = chartData;
                
                if (datasetsCount <= 1) {
                    console.log(`ℹ️ Chart ${id}: Only ${datasetsCount} dataset(s), skipping indicator`);
                    return null;
                }
                
                // Find the chart container (look for parent with specific classes or create one)
                let chartContainer = canvas.closest('.MuiCard-root') || 
                                canvas.closest('.chart-container') || 
                                canvas.closest('div[class*="chart"]') ||
                                canvas.parentElement;
                
                if (!chartContainer) {
                    // Create a wrapper if none exists
                    chartContainer = document.createElement('div');
                    chartContainer.style.position = 'relative';
                    canvas.parentNode.insertBefore(chartContainer, canvas);
                    chartContainer.appendChild(canvas);
                }
                
                // Ensure container has relative positioning
                const computedStyle = window.getComputedStyle(chartContainer);
                if (computedStyle.position === 'static') {
                    chartContainer.style.position = 'relative';
                }
                
                // Create status indicator
                const statusContainer = document.createElement('div');
                statusContainer.id = `status-${id}`;
                statusContainer.className = 'chart-legend-status';
                statusContainer.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    background: linear-gradient(135deg, #1a237e, #3949ab);
                    color: white;
                    padding: 10px 14px;
                    border-radius: 20px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 12px;
                    font-weight: 600;
                    z-index: 1000;
                    min-width: 160px;
                    text-align: center;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.25);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.1);
                    transition: all 0.3s ease;
                `;
                
                // Create dataset info display
                const datasetInfo = document.createElement('div');
                datasetInfo.id = `dataset-info-${id}`;
                datasetInfo.style.cssText = `
                    font-size: 11px;
                    margin-bottom: 8px;
                    opacity: 0.9;
                `;
                
                // Create progress bar container
                const progressContainer = document.createElement('div');
                progressContainer.style.cssText = `
                    width: 100%;
                    height: 4px;
                    background: rgba(255,255,255,0.2);
                    border-radius: 2px;
                    margin: 8px 0;
                    overflow: hidden;
                `;
                
                const progressBar = document.createElement('div');
                progressBar.id = `progress-${id}`;
                progressBar.style.cssText = `
                    width: 0%;
                    height: 100%;
                    background: linear-gradient(90deg, #4caf50, #8bc34a);
                    border-radius: 2px;
                    transition: width 0.2s ease;
                `;
                
                progressContainer.appendChild(progressBar);
                
                // Create control buttons
                const controlsContainer = document.createElement('div');
                controlsContainer.style.cssText = `
                    display: flex;
                    gap: 6px;
                    justify-content: center;
                    margin-top: 8px;
                `;
                
                // Legend toggle button
                const legendToggleBtn = document.createElement('button');
                legendToggleBtn.id = `legend-toggle-${id}`;
                legendToggleBtn.innerHTML = '👁️';
                legendToggleBtn.title = 'Toggle legend visibility';
                legendToggleBtn.style.cssText = `
                    background: rgba(255,255,255,0.2);
                    border: 1px solid rgba(255,255,255,0.3);
                    border-radius: 6px;
                    padding: 4px 8px;
                    cursor: pointer;
                    font-size: 12px;
                    transition: all 0.2s ease;
                    color: white;
                `;
                
                // Play/Pause button
                const playPauseBtn = document.createElement('button');
                playPauseBtn.id = `playpause-${id}`;
                playPauseBtn.innerHTML = '⏸️';
                playPauseBtn.title = 'Toggle auto-rotation';
                playPauseBtn.style.cssText = `
                    background: rgba(255,255,255,0.2);
                    border: 1px solid rgba(255,255,255,0.3);
                    border-radius: 6px;
                    padding: 4px 8px;
                    cursor: pointer;
                    font-size: 12px;
                    transition: all 0.2s ease;
                    color: white;
                `;
                
                // Previous button
                const prevBtn = document.createElement('button');
                prevBtn.innerHTML = '⏮️';
                prevBtn.title = 'Previous dataset';
                prevBtn.style.cssText = playPauseBtn.style.cssText;
                
                // Next button
                const nextBtn = document.createElement('button');
                nextBtn.innerHTML = '⏭️';
                nextBtn.title = 'Next dataset';
                nextBtn.style.cssText = playPauseBtn.style.cssText;
                
                // Add hover effects
                [legendToggleBtn, playPauseBtn, prevBtn, nextBtn].forEach(btn => {
                    btn.addEventListener('mouseenter', () => {
                        btn.style.background = 'rgba(255,255,255,0.3)';
                        btn.style.transform = 'scale(1.05)';
                    });
                    btn.addEventListener('mouseleave', () => {
                        btn.style.background = 'rgba(255,255,255,0.2)';
                        btn.style.transform = 'scale(1)';
                    });
                });
                
                // Assemble the status indicator
                statusContainer.appendChild(datasetInfo);
                statusContainer.appendChild(progressContainer);
                controlsContainer.appendChild(legendToggleBtn);
                controlsContainer.appendChild(prevBtn);
                controlsContainer.appendChild(playPauseBtn);
                controlsContainer.appendChild(nextBtn);
                statusContainer.appendChild(controlsContainer);
                
                // Add to chart container
                chartContainer.appendChild(statusContainer);
                
                // Add event listeners
                legendToggleBtn.addEventListener('click', () => toggleLegend(id));
                playPauseBtn.addEventListener('click', () => toggleAutoRotation(id));
                nextBtn.addEventListener('click', () => switchToNextDataset(id));
                prevBtn.addEventListener('click', () => switchToPreviousDataset(id));
                
                console.log(` Status indicator created for chart ${id}`);
                return statusContainer;
            }
            
            // Function to toggle legend visibility
            function toggleLegend(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const chart = chartData.chart;
                const currentState = legendEnabled.get(chartId) ?? true;
                const newState = !currentState;
                
                // Update legend display
                if (chart.options.plugins && chart.options.plugins.legend) {
                    chart.options.plugins.legend.display = newState;
                } else {
                    // Initialize legend options if they don't exist
                    if (!chart.options.plugins) chart.options.plugins = {};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                    chart.options.plugins.legend.display = newState;
                }
                
                // Update legend state
                legendEnabled.set(chartId, newState);
                
                // Update button appearance
                const legendToggleBtn = document.getElementById(`legend-toggle-${chartId}`);
                if (legendToggleBtn) {
                    legendToggleBtn.innerHTML = newState ? '👁️' : '🙈';
                    legendToggleBtn.title = newState ? 'Hide legend' : 'Show legend';
                    legendToggleBtn.style.opacity = newState ? '1' : '0.6';
                }
                
                // Update chart
                chart.update('none');
                
                console.log(`🎯 Chart ${chartId}: Legend ${newState ? 'enabled' : 'disabled'}`);
            }
            
            // Enhanced function to toggle dataset visibility
            function setActiveDataset(chartData, activeIndex) {
                const { chart, id } = chartData;
                const datasets = chart.data.datasets;
                
                // Validate index
                if (activeIndex < 0 || activeIndex >= datasets.length) {
                    console.warn(`Invalid dataset index: ${activeIndex}`);
                    return;
                }
                
                // Hide all datasets except the active one
                datasets.forEach((dataset, index) => {
                    const meta = chart.getDatasetMeta(index);
                    if (meta) {
                        meta.hidden = (index !== activeIndex);
                    }
                });
                
                // Update legend to show only active dataset (if legend is enabled)
                const isLegendEnabled = legendEnabled.get(id) ?? true;
                if (isLegendEnabled && chart.options.plugins && chart.options.plugins.legend) {
                    if (!chart.options.plugins.legend.labels) {
                        chart.options.plugins.legend.labels = {};
                    }
                    chart.options.plugins.legend.labels.filter = function(legendItem) {
                        return legendItem.datasetIndex === activeIndex;
                    };
                }
                
                // Update chart without animation for better performance
                chart.update('none');
                
                console.log(`Chart ${id}: Activated dataset ${activeIndex} (${datasets[activeIndex].label || 'Unnamed'})`);
            }
            
            // Function to update status display
            function updateStatusDisplay(chartId, activeIndex, progress = 0) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const datasets = chartData.chart.data.datasets;
                const activeDataset = datasets[activeIndex];
                const datasetName = activeDataset.label || `Dataset ${activeIndex + 1}`;
                
                // Update dataset info
                const datasetInfo = document.getElementById(`dataset-info-${chartId}`);
                if (datasetInfo) {
                    datasetInfo.textContent = `${datasetName} (${activeIndex + 1}/${datasets.length})`;
                }
                
                // Update progress bar
                const progressBar = document.getElementById(`progress-${chartId}`);
                if (progressBar) {
                    progressBar.style.width = `${progress}%`;
                }
            }
            
            // Function to start automatic rotation
            function startAutoRotation(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData || chartData.datasetsCount <= 1) return;
                
                // Clear existing interval
                if (chartRotationIntervals.has(chartId)) {
                    clearInterval(chartRotationIntervals.get(chartId));
                }
                
                let currentIndex = currentActiveIndices.get(chartId) || 0;
                let progressCounter = 0;
                const rotationDuration = 10000; // 10 seconds
                const updateInterval = 100; // Update every 100ms
                const totalUpdates = rotationDuration / updateInterval;
                
                // Set initial state
                setActiveDataset(chartData, currentIndex);
                updateStatusDisplay(chartId, currentIndex, 0);
                
                const intervalId = setInterval(() => {
                    progressCounter++;
                    const progress = (progressCounter % totalUpdates) / totalUpdates * 100;
                    
                    // Update progress display
                    updateStatusDisplay(chartId, currentIndex, progress);
                    
                    // Switch dataset when progress completes
                    if (progressCounter % totalUpdates === 0 && progressCounter > 0) {
                        currentIndex = (currentIndex + 1) % chartData.datasetsCount;
                        currentActiveIndices.set(chartId, currentIndex);
                        setActiveDataset(chartData, currentIndex);
                    }
                }, updateInterval);
                
                chartRotationIntervals.set(chartId, intervalId);
                console.log(`Auto-rotation started for chart ${chartId}`);
            }
            
            // Function to stop automatic rotation
            function stopAutoRotation(chartId) {
                if (chartRotationIntervals.has(chartId)) {
                    clearInterval(chartRotationIntervals.get(chartId));
                    chartRotationIntervals.delete(chartId);
                    console.log(`⏹️ Auto-rotation stopped for chart ${chartId}`);
                }
            }
            
            // Function to toggle auto-rotation
            function toggleAutoRotation(chartId) {
                const playPauseBtn = document.getElementById(`playpause-${chartId}`);
                
                if (chartRotationIntervals.has(chartId)) {
                    stopAutoRotation(chartId);
                    if (playPauseBtn) playPauseBtn.innerHTML = '▶️';
                } else {
                    startAutoRotation(chartId);
                    if (playPauseBtn) playPauseBtn.innerHTML = '⏸️';
                }
            }
            
            // Function to switch to next dataset
            function switchToNextDataset(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const currentIndex = currentActiveIndices.get(chartId) || 0;
                const nextIndex = (currentIndex + 1) % chartData.datasetsCount;
                
                currentActiveIndices.set(chartId, nextIndex);
                setActiveDataset(chartData, nextIndex);
                updateStatusDisplay(chartId, nextIndex, 0);
                
                console.log(`⏭️ Chart ${chartId}: Manually switched to dataset ${nextIndex}`);
            }
            
            // Function to switch to previous dataset
            function switchToPreviousDataset(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const currentIndex = currentActiveIndices.get(chartId) || 0;
                const prevIndex = (currentIndex - 1 + chartData.datasetsCount) % chartData.datasetsCount;
                
                currentActiveIndices.set(chartId, prevIndex);
                setActiveDataset(chartData, prevIndex);
                updateStatusDisplay(chartId, prevIndex, 0);
                
                console.log(`⏮️ Chart ${chartId}: Manually switched to dataset ${prevIndex}`);
            }
            
            // Function to enable legend for a specific chart
            function enableLegend(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const chart = chartData.chart;
                
                // Enable legend
                if (!chart.options.plugins) chart.options.plugins = {};
                if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                chart.options.plugins.legend.display = true;
                
                legendEnabled.set(chartId, true);
                
                // Update button appearance
                const legendToggleBtn = document.getElementById(`legend-toggle-${chartId}`);
                if (legendToggleBtn) {
                    legendToggleBtn.innerHTML = '👁️';
                    legendToggleBtn.title = 'Hide legend';
                    legendToggleBtn.style.opacity = '1';
                }
                
                // Re-apply current dataset filter
                const currentIndex = currentActiveIndices.get(chartId) || 0;
                setActiveDataset(chartData, currentIndex);
                
                console.log(` Chart ${chartId}: Legend enabled`);
            }
            
            // Function to disable legend for a specific chart
            function disableLegend(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const chart = chartData.chart;
                
                // Disable legend
                if (!chart.options.plugins) chart.options.plugins = {};
                if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                chart.options.plugins.legend.display = false;
                
                legendEnabled.set(chartId, false);
                
                // Update button appearance
                const legendToggleBtn = document.getElementById(`legend-toggle-${chartId}`);
                if (legendToggleBtn) {
                    legendToggleBtn.innerHTML = '🙈';
                    legendToggleBtn.title = 'Show legend';
                    legendToggleBtn.style.opacity = '0.6';
                }
                
                chart.update('none');
                
                console.log(` Chart ${chartId}: Legend disabled`);
            }
            
            // Main setup function
            function setupEnhancedLegendControl() {
                console.log('Searching for Chart.js instances...');
                
                const foundCharts = findAllChartInstances();
                console.log(`Found ${foundCharts.length} Chart.js instances`);
                
                if (foundCharts.length === 0) {
                    console.log('No Chart.js instances found. Will retry...');
                    return false;
                }
                
                foundCharts.forEach(chartData => {
                    chartInstances.set(chartData.id, chartData);
                    currentActiveIndices.set(chartData.id, 0);
                    legendEnabled.set(chartData.id, true); // Initialize legend as enabled
                    
                    // Create status indicator
                    createEnhancedStatusIndicator(chartData);
                    
                    // Start auto-rotation
                    startAutoRotation(chartData.id);
                });
                
                console.log(` Enhanced legend control setup completed for ${foundCharts.length} charts`);
                return true;
            }
            
            // Retry mechanism for chart detection
            function initializeWithRetry() {
                let attempts = 0;
                const maxAttempts = 15;
                const retryInterval = 1000;
                
                function trySetup() {
                    attempts++;
                    console.log(`Setup attempt ${attempts}/${maxAttempts}`);
                    
                    if (setupEnhancedLegendControl() || attempts >= maxAttempts) {
                        if (attempts >= maxAttempts && chartInstances.size === 0) {
                            console.log(' Failed to find Chart.js instances after maximum attempts');
                            console.log('💡 Charts might be loaded dynamically. Try refreshing the page.');
                        }
                        return;
                    }
                    
                    setTimeout(trySetup, retryInterval);
                }
                
                trySetup();
            }
            
            // Cleanup function
            window.addEventListener('beforeunload', function() {
                chartRotationIntervals.forEach((intervalId) => {
                    clearInterval(intervalId);
                });
                chartRotationIntervals.clear();
                currentActiveIndices.clear();
                chartInstances.clear();
                legendEnabled.clear();
            });
            
            // Handle dynamic content changes
            const observer = new MutationObserver(function(mutations) {
                let shouldResetup = false;
                
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(node => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                if (node.tagName === 'CANVAS' || node.querySelector('canvas')) {
                                    shouldResetup = true;
                                }
                            }
                        });
                    }
                });
                
                if (shouldResetup) {
                    console.log('DOM changes detected, re-initializing...');
                    setTimeout(() => setupEnhancedLegendControl(), 2000);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            // Start initialization
            initializeWithRetry();
            
            // Expose control functions globally
            window.chartLegendControl = {
                toggleLegend,
                enableLegend,
                disableLegend,
                toggleAutoRotation,
                switchToNextDataset,
                switchToPreviousDataset,
                stopAutoRotation,
                getChartInstances: () => Array.from(chartInstances.values()),
                getCurrentActiveIndex: (chartId) => currentActiveIndices.get(chartId),
                isLegendEnabled: (chartId) => legendEnabled.get(chartId) ?? true,
                manualSetup: setupEnhancedLegendControl
            };
            
            console.log('🎯 Enhanced Chart.js Legend Control initialized!');
            console.log('Available commands:');
            console.log('  - window.chartLegendControl.toggleLegend(chartId)');
            console.log('  - window.chartLegendControl.enableLegend(chartId)');
            console.log('  - window.chartLegendControl.disableLegend(chartId)');
            console.log('  - window.chartLegendControl.toggleAutoRotation(chartId)');
            
        })();
        """
        
        # Execute the enhanced JavaScript
        await page.evaluate(enhanced_legend_control_script)
    async def apply_enhanced_legend_control(self, page):
        """Apply the enhanced legend control script to the page with robust chart detection"""
        try:
            # Wait for charts to be loaded with multiple attempts
            print("Waiting for charts to load...")

            # Try multiple selectors to find charts
            chart_found = False
            selectors_to_try = [
                'canvas.chartjs-render-monitor',
                'canvas[class*="chartjs"]',
                'canvas',
                '[id*="chart"]'
            ]

            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    chart_found = True
                    print(f" Found charts using selector: {selector}")
                    break
                except:
                    continue

            if not chart_found:
                print("No chart elements found with any selector")
                return False

            await asyncio.sleep(3)  # Give charts time to fully initialize

            # Execute the comprehensive chart detection script
            result = await page.evaluate("""
                (function() {
                    console.log('=== COMPREHENSIVE CHART DETECTION ===');

                    // Check Chart.js availability
                    const chartJsAvailable = typeof Chart !== 'undefined';
                    console.log('Chart.js available:', chartJsAvailable);

                    if (!chartJsAvailable) {
                        // Try to find Chart.js in window object
                        const chartKeys = Object.keys(window).filter(key => key.toLowerCase().includes('chart'));
                        console.log('Potential chart objects:', chartKeys);
                        return false;
                    }

                    // Initialize chart instances map
                    window.legendControlInitialized = true;
                    window.chartInstances = new Map();

                    // Find all possible canvas elements
                    const allCanvases = document.querySelectorAll('canvas');
                    const chartCanvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                    const anyChartCanvas = document.querySelectorAll('canvas[class*="chart"]');

                    console.log(`Canvas elements found:`);
                    console.log(`  - Total canvas: ${allCanvases.length}`);
                    console.log(`  - chartjs-render-monitor: ${chartCanvases.length}`);
                    console.log(`  - chart-related: ${anyChartCanvas.length}`);

                    // Use the most specific selector that found canvases
                    let canvasesToProcess = chartCanvases.length > 0 ? chartCanvases :
                                          anyChartCanvas.length > 0 ? anyChartCanvas : allCanvases;

                    console.log(`Processing ${canvasesToProcess.length} canvas elements`);

                    let successfullyRegistered = 0;

                    canvasesToProcess.forEach((canvas, index) => {
                        console.log(`\\n--- Processing Canvas ${index} ---`);

                        let chartInstance = null;
                        let detectionMethod = 'none';

                        // Method 1: Chart.getChart (Chart.js v3+)
                        try {
                            if (Chart.getChart) {
                                chartInstance = Chart.getChart(canvas);
                                if (chartInstance) {
                                    detectionMethod = 'Chart.getChart';
                                    console.log(`✓ Found via Chart.getChart`);
                                }
                            }
                        } catch (e) {
                            console.log(`✗ Chart.getChart failed:`, e.message);
                        }

                        // Method 2: Canvas chart property
                        if (!chartInstance) {
                            try {
                                if (canvas.chart) {
                                    chartInstance = canvas.chart;
                                    detectionMethod = 'canvas.chart';
                                    console.log(`✓ Found via canvas.chart`);
                                }
                            } catch (e) {
                                console.log(`✗ canvas.chart failed:`, e.message);
                            }
                        }

                        // Method 3: Canvas _chart property
                        if (!chartInstance) {
                            try {
                                if (canvas._chart) {
                                    chartInstance = canvas._chart;
                                    detectionMethod = 'canvas._chart';
                                    console.log(`✓ Found via canvas._chart`);
                                }
                            } catch (e) {
                                console.log(`✗ canvas._chart failed:`, e.message);
                            }
                        }

                        // Method 4: Search Chart.instances
                        if (!chartInstance) {
                            try {
                                if (Chart.instances) {
                                    Object.values(Chart.instances).forEach(instance => {
                                        if (instance && instance.canvas === canvas) {
                                            chartInstance = instance;
                                            detectionMethod = 'Chart.instances';
                                            console.log(`✓ Found via Chart.instances`);
                                        }
                                    });
                                }
                            } catch (e) {
                                console.log(`✗ Chart.instances search failed:`, e.message);
                            }
                        }

                        // Method 5: Check canvas data attributes or properties
                        if (!chartInstance) {
                            try {
                                // Look for any property that might contain a chart instance
                                const props = Object.getOwnPropertyNames(canvas);
                                for (const prop of props) {
                                    if (prop.includes('chart') || prop.includes('Chart')) {
                                        const value = canvas[prop];
                                        if (value && typeof value === 'object' && value.data && value.data.datasets) {
                                            chartInstance = value;
                                            detectionMethod = `canvas.${prop}`;
                                            console.log(`✓ Found via canvas.${prop}`);
                                            break;
                                        }
                                    }
                                }
                            } catch (e) {
                                console.log(`✗ Property search failed:`, e.message);
                            }
                        }

                        // Validate chart instance
                        if (chartInstance) {
                            console.log(`Chart instance found via: ${detectionMethod}`);

                            if (chartInstance.data && chartInstance.data.datasets && chartInstance.data.datasets.length > 0) {
                                // Use canvas ID or create consistent ID mapping
                                let chartId = canvas.id || `canvas-${index}`;

                                // Also register with alternative IDs for compatibility
                                const alternativeIds = [`chart_${index}`, `chart-${index}`, `canvas_${index}`];

                                window.chartInstances.set(chartId, {
                                    instance: chartInstance,
                                    canvas: canvas,
                                    detectionMethod: detectionMethod,
                                    originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                                });

                                // Register with alternative IDs
                                alternativeIds.forEach(altId => {
                                    window.chartInstances.set(altId, {
                                        instance: chartInstance,
                                        canvas: canvas,
                                        detectionMethod: detectionMethod,
                                        originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                                    });
                                });

                                console.log(` Registered ${chartId} (and alternatives: ${alternativeIds.join(', ')}):`);
                                console.log(`   - Datasets: ${chartInstance.data.datasets.length}`);
                                console.log(`   - Detection: ${detectionMethod}`);

                                chartInstance.data.datasets.forEach((dataset, dsIndex) => {
                                    console.log(`   - Dataset ${dsIndex}: "${dataset.label || 'Unnamed'}"`);
                                });

                                successfullyRegistered++;
                            } else {
                                console.log(`✗ Chart instance invalid (no datasets)`);
                            }
                        } else {
                            console.log(`✗ No chart instance found for canvas ${index}`);
                        }
                    });

                    console.log(`\\n=== DETECTION COMPLETE ===`);
                    console.log(`Successfully registered: ${successfullyRegistered} charts`);
                    console.log(`Chart IDs: [${Array.from(window.chartInstances.keys()).join(', ')}]`);

                    return successfullyRegistered > 0;
                })()
            """)

            if result:
                print(" Enhanced legend control applied successfully with comprehensive detection")
                return True
            else:
                print(" No chart instances found even with comprehensive detection")
                return False

        except Exception as e:
            print(f" Failed to apply enhanced legend control: {str(e)}")
            return False
    async def disable_all_legends(self, page):
        """Disable legends for all charts"""
        try:
            await page.evaluate("""
                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        
                        // Disable legend
                        if (!chart.options.plugins) chart.options.plugins = {};
                        if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                        chart.options.plugins.legend.display = false;
                        
                        // Update chart
                        chart.update('none');
                        
                        console.log(`Legend disabled for ${chartId}`);
                    });
                }
            """)
            
            print(" All legends disabled")
            return True
            
        except Exception as e:
            print(f" Failed to disable legends: {str(e)}")
            return False
     
    async def enable_only_target_legend(self, page, chart_id, target_dataset_label):
        """Enable only the target dataset legend and disable all others"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to enable legend for chart: {chart_id}, dataset: {target_dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found, attempting to reinitialize...');
                    return false;
                }}

                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;
                let actualChartId = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        actualChartId = id;
                        console.log(`Found chart with ID: ${{id}}`);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found with any ID variation');
                    console.log('Available charts:', Array.from(window.chartInstances.keys()));
                    return false;
                }}

                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure for', actualChartId);
                    return false;
                }}

                // Find target dataset index with fuzzy matching
                let targetDatasetIndex = -1;
                chart.data.datasets.forEach((dataset, index) => {{
                    const datasetLabel = dataset.label || '';
                    const targetLabel = '{target_dataset_label}';

                    // Exact match first
                    if (datasetLabel === targetLabel) {{
                        targetDatasetIndex = index;
                        return;
                    }}

                    // Fuzzy match (contains or similar)
                    if (datasetLabel.includes(targetLabel) || targetLabel.includes(datasetLabel)) {{
                        targetDatasetIndex = index;
                        console.log(`Fuzzy match found: "${{datasetLabel}}" matches "${{targetLabel}}"`);
                    }}
                }});

                if (targetDatasetIndex === -1) {{
                    console.log('Target dataset not found: {target_dataset_label}');
                    console.log('Available datasets:', chart.data.datasets.map(d => d.label));

                    // Try to find any dataset and use the first one as fallback
                    if (chart.data.datasets.length > 0) {{
                        targetDatasetIndex = 0;
                        console.log('Using first dataset as fallback:', chart.data.datasets[0].label);
                    }} else {{
                        return false;
                    }}
                }}

                try {{
                    // Show only the target dataset
                    chart.data.datasets.forEach((dataset, index) => {{
                        const meta = chart.getDatasetMeta(index);
                        if (meta) {{
                            meta.hidden = (index !== targetDatasetIndex);
                        }}
                    }});

                    // Enable legend but filter to show only target dataset
                    if (!chart.options.plugins) chart.options.plugins = {{}};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {{}};

                    chart.options.plugins.legend.display = true;
                    chart.options.plugins.legend.labels = {{
                        filter: function(legendItem) {{
                            return legendItem.datasetIndex === targetDatasetIndex;
                        }}
                    }};

                    // Update chart
                    chart.update('none');

                    const actualDatasetLabel = chart.data.datasets[targetDatasetIndex].label;
                    console.log('Successfully enabled legend for:', actualDatasetLabel, '(index:', targetDatasetIndex, ')');
                    return true;
                }} catch (error) {{
                    console.error('Error updating chart:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                print(f" Enabled only legend for {target_dataset_label} in chart {chart_id}")
                return True
            else:
                print(f"Failed to enable legend for {target_dataset_label} in chart {chart_id}")
                return False

        except Exception as e:
            print(f" Error enabling legend for {target_dataset_label}: {str(e)}")
            return False

    async def debug_legend_control(self, page):
        """Debug function to check legend control setup"""
        try:
            debug_info = await page.evaluate("""
            (function() {
                const info = {
                    chartInstancesExists: !!window.chartInstances,
                    chartInstancesSize: window.chartInstances ? window.chartInstances.size : 0,
                    chartIds: window.chartInstances ? Array.from(window.chartInstances.keys()) : [],
                    chartDetails: []
                };

                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        info.chartDetails.push({
                            id: chartId,
                            hasInstance: !!chart,
                            hasData: !!(chart && chart.data),
                            datasetCount: chart && chart.data && chart.data.datasets ? chart.data.datasets.length : 0,
                            datasetLabels: chart && chart.data && chart.data.datasets ? chart.data.datasets.map(d => d.label) : []
                        });
                    });
                }

                return info;
            })()
            """)
            
            for chart_detail in debug_info.get('chartDetails', []):
                chart_id = chart_detail.get('id', 'Unknown')
                dataset_count = chart_detail.get('datasetCount', 0)
                dataset_labels = chart_detail.get('datasetLabels', [])                
            return debug_info

        except Exception as e:
            print(f" Error debugging legend control: {str(e)}")
            return None

    async def click_data_point_for_drilldown(self, page, chart_id, point_data):
        """Click on a specific data point to trigger drilldown navigation"""
        try:
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')

            # First try using the pre-calculated screen coordinates
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    print(f"Clicking at pre-calculated coordinates: ({screen_x}, {screen_y})")
                    await page.mouse.click(screen_x, screen_y)
                    await asyncio.sleep(1)
                    return {
                        'success': True,
                        'method': 'pre_calculated_coordinates',
                        'position': {'x': screen_x, 'y': screen_y},
                        'dataset_label': dataset_label,
                        'x_label': x_label
                    }
                except Exception as coord_error:
                    print(f"Pre-calculated coordinates failed: {coord_error}")

            # Fallback to JavaScript-based clicking
            click_result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to click data point for chart: {chart_id}, dataset: {dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found');
                    return {{ success: false, error: 'Chart instances not initialized' }};
                }}

                if (!window.chartInstances.has('{chart_id}')) {{
                    console.log('Chart {chart_id} not found');
                    console.log('Available charts:', Array.from(window.chartInstances.keys()));
                    return {{ success: false, error: 'Chart instance not found' }};
                }}

                const chartData = window.chartInstances.get('{chart_id}');
                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure');
                    return {{ success: false, error: 'Invalid chart data' }};
                }}

                try {{
                    // Find target dataset index by label
                    let targetDatasetIndex = {dataset_index};
                    chart.data.datasets.forEach((dataset, index) => {{
                        if (dataset.label === '{dataset_label}') {{
                            targetDatasetIndex = index;
                        }}
                    }});

                    // Get dataset meta
                    const meta = chart.getDatasetMeta(targetDatasetIndex);
                    if (!meta || !meta.data || !meta.data[{point_index}]) {{
                        console.log('Data point not found at index: {point_index}');
                        return {{ success: false, error: 'Data point not found' }};
                    }}

                    // Get point element and position
                    const pointElement = meta.data[{point_index}];
                    const pointPosition = pointElement.getCenterPoint();
                    const canvas = chart.canvas;

                    // Create click event
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {{
                        clientX: rect.left + pointPosition.x,
                        clientY: rect.top + pointPosition.y,
                        bubbles: true,
                        cancelable: true
                    }});

                    // Dispatch click event
                    canvas.dispatchEvent(clickEvent);

                    console.log('Successfully clicked data point: {x_label} from {dataset_label}');
                    return {{
                        success: true,
                        method: 'javascript_click',
                        position: pointPosition,
                        dataset_label: '{dataset_label}',
                        x_label: '{x_label}'
                    }};

                }} catch (error) {{
                    console.error('Error clicking data point:', error);
                    return {{ success: false, error: error.message }};
                }}
            }})()
            """)

            if click_result.get('success', False):
                print(f" Data point clicked for drilldown: {x_label} from {dataset_label}")
                return click_result
            else:
                log_error(f"Failed to click data point: {click_result.get('error', 'Unknown error')}")
                return click_result

        except Exception as e:
            print(f" Error clicking data point for drilldown: {str(e)}")
            return {'success': False, 'error': str(e)}

    async def wait_for_drilldown_navigation(self, page, timeout=10000):
        """Wait for navigation after clicking data point"""
        try:
            # Wait for navigation to complete
            # await page.wait_for_load_state("networkidle", timeout=timeout)
            await asyncio.sleep(2)            
            # Get current URL to confirm navigation
            current_url = page.url                      
            return {
                'success': True,
                'url': current_url,
                'navigation_completed': True
            }            
        except Exception as e:
            print(f"Navigation wait failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'navigation_completed': False
            }

    async def process_single_point_task_with_selective_legend(self, page, task, target_month_year,chart_title):
        """Process a single point task with selective legend control and drilldown"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        try:
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')         
            # Step 1: Enable only the target legend, disable all others
            legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
            if not legend_enabled:
                print(f"{task_id}: Legend control failed, continuing anyway")
            # Wait for legend update
            await asyncio.sleep(1)
            # Debug: Check if the chart area is visible and clickable
            await self.debug_chart_clickability(page, chart_id, point_data)
            # Step 2: Click data point and wait for drilldown navigation
            click_result = None
            navigation_result = None
            # Get current URL before clicking
            initial_url = page.url
            print(f"{task_id}: Current URL before click: {initial_url}")
            # Method 1: Use pre-calculated coordinates (most reliable)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    await page.mouse.click(screen_x, screen_y)                   
                    await asyncio.sleep(3)  # Give time for navigation to start
                    # Check if URL changed
                    current_url = page.url
                    if current_url != initial_url:
                        print(f" {task_id}: Navigation detected to: {current_url}")
                        # Wait for page to fully load
                        try:
                            # await page.wait_for_load_state("networkidle", timeout=10000)
                            await asyncio.sleep(2)
                        except:
                            pass  # Continue even if load state fails

                        click_result = {
                            'success': True,
                            'method': 'pre_calculated_coordinates',
                            'coordinates': {'x': screen_x, 'y': screen_y}
                        }

                        navigation_result = {
                            'success': True,
                            'url': current_url,
                            'navigation_completed': True,
                            'initial_url': initial_url
                        }

                    else:
                        print(f"{task_id}: No navigation detected after coordinate click")
                        # Wait a bit more and check again
                        await asyncio.sleep(2)
                        current_url = page.url

                        if current_url != initial_url:
                            print(f" {task_id}: Delayed navigation detected to: {current_url}")
                            click_result = {'success': True, 'method': 'pre_calculated_coordinates'}
                            navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                        else:
                            print(f" {task_id}: No navigation after coordinate click")
                            click_result = {'success': False, 'error': 'No navigation detected after coordinate click'}
                            navigation_result = {'success': False, 'error': 'No URL change detected'}

                except Exception as coord_error:
                    print(f"{task_id}: Coordinate clicking failed: {coord_error}")
                    click_result = {'success': False, 'error': str(coord_error)}

            # Method 2: Fallback to JavaScript-based clicking
            if not click_result or not click_result.get('success', False):
                print(f"{task_id}: Trying JavaScript-based clicking...")

                try:
                    # Try JavaScript clicking
                    js_click_result = await self.click_data_point_for_drilldown(page, chart_id, point_data)

                    if js_click_result.get('success', False):
                        print(f"{task_id}: JavaScript click executed, waiting for navigation...")

                        # Wait for potential navigation
                        await asyncio.sleep(3)
                        # Check if URL changed
                        current_url = page.url
                        if current_url != initial_url:                           
                            # Wait for page to fully load
                            try:
                                # await page.wait_for_load_state("networkidle", timeout=10000)
                                await asyncio.sleep(2)
                            except:
                                pass
                            click_result = js_click_result
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                        else:                           
                            # Wait a bit more and check again
                            await asyncio.sleep(2)
                            current_url = page.url
                            if current_url != initial_url:
                                print(f" {task_id}: Delayed JavaScript navigation detected to: {current_url}")
                                click_result = js_click_result
                                navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                            else:
                                click_result = {'success': False, 'error': 'JavaScript click no navigation'}
                                navigation_result = {'success': False, 'error': 'No URL change after JavaScript click'}
                    else:
                        click_result = js_click_result
                        navigation_result = {'success': False, 'error': 'JavaScript click failed'}

                except Exception as js_error:
                    print(f" {task_id}: JavaScript clicking failed: {js_error}")
                    click_result = {'success': False, 'error': str(js_error)}
                    navigation_result = {'success': False, 'error': str(js_error)}

            # Method 3: Fallback to simple click with longer wait
            if not click_result or not click_result.get('success', False):
                print(f"{task_id}: Trying simple click with extended wait...")
                try:
                    # Simple click without complex navigation detection
                    await page.mouse.click(screen_x, screen_y)
                    print(f"{task_id}: Simple click executed, waiting longer for navigation...")
                    # Wait longer for navigation
                    await asyncio.sleep(5)
                    # Check URL multiple times
                    for attempt in range(3):
                        current_url = page.url
                        if current_url != initial_url:
                            click_result = {
                                'success': True,
                                'method': 'simple_click_extended_wait',
                                'coordinates': {'x': screen_x, 'y': screen_y}
                            }
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                            break

                        if attempt < 2:  # Don't wait after last attempt
                            await asyncio.sleep(2)

                    if not click_result or not click_result.get('success', False):
                        print(f" {task_id}: Simple click also failed to trigger navigation")
                        click_result = {'success': False, 'error': 'Simple click no navigation'}
                        navigation_result = {'success': False, 'error': 'No URL change after simple click'}

                except Exception as simple_error:
                    print(f" {task_id}: Simple clicking failed: {simple_error}")
                    click_result = {'success': False, 'error': str(simple_error)}
                    navigation_result = {'success': False, 'error': str(simple_error)}

            # Check if any clicking method succeeded
            if not click_result or not click_result.get('success', False):
                error_msg = click_result.get('error', 'All clicking methods failed') if click_result else 'No click result'
                print(f" {task_id}: Failed to click data point: {error_msg}")
                return {
                    'task_id': task_id,
                    'chart_id': chart_id,
                    'error': f"Failed to click data point: {error_msg}",
                    'success': False,
                    'legend_controlled': legend_enabled
                }
            # Check navigation result
            if not navigation_result or not navigation_result.get('success', False):
                print(f"{task_id}: Navigation failed, but click was successful - attempting data extraction anyway")
                # Try to extract data from current page
                current_url = page.url
                navigation_result = {
                    'success': False,
                    'url': current_url,
                    'navigation_completed': False,
                    'error': 'Navigation failed but click succeeded'
                }

            # Step 3: Extract data from the drilldown page
            extracted_data = None
            extraction_success = False
            # Only attempt extraction if navigation was successful
            if navigation_result and navigation_result.get('success', False):
                current_url = page.url
                # Check if we're on the expected drilldown page
                if "AnalyzeData" in current_url and "chartId=drillDown" in current_url:
                    print(f" {task_id}: On correct drilldown page, extracting data...")
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year,chart_title)

                    # Check extraction success from the nested structure
                    extraction_success = False
                    if extracted_data and extracted_data.get('extraction_data'):
                        extraction_success = extracted_data['extraction_data'].get('success', False)
                    if extraction_success:
                        print(f" {task_id}: Data extraction successful")
                    else:
                        print(f"{task_id}: Data extraction failed or incomplete")
                else:
                    print(f"{task_id}: Not on expected drilldown page. URL: {current_url}")
                    extracted_data = {
                        'extraction_data': {
                            'success': False,
                            'error': f'Unexpected page URL: {current_url}',
                            'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
                        }
                    }
                    extraction_success = False
            else:
                print(f" {task_id}: Skipping data extraction due to navigation failure")
                extracted_data = {
                    'extraction_data': {
                        'success': False,
                        'error': 'Navigation failed, cannot extract data',
                        'navigation_error': navigation_result.get('error', 'Unknown navigation error') if navigation_result else 'No navigation result'
                    }
                }
                extraction_success = False
            # Determine overall success
            overall_success = (
                click_result.get('success', False) and
                navigation_result.get('success', False) and
                extraction_success
            )
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'click_result': click_result,
                'navigation_result': navigation_result,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': overall_success,
                'legend_controlled': legend_enabled,
                'drilldown_url': navigation_result.get('url', '') if navigation_result else '',
                'click_success': click_result.get('success', False) if click_result else False,
                'navigation_success': navigation_result.get('success', False) if navigation_result else False,
                'extraction_success': extraction_success
            }
            print(f" {task_id}: Successfully processed {point_label} from {dataset_label}")
            return result
        except Exception as e:
            print(f" {task_id}: Error processing point: {e}")
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': False
            }
            return error_result

    async def extract_data_from_drilldown_page(self, page, point_data, target_month_year, title):
        """Extract AG-Grid data from drilldown page and return only extracted data for specified column"""
        max_retries = 3
        retry_delay = 2
        # Skip extraction entirely if opcode is OTHER
        if point_data.get("opcode", "").upper() == "OTHER":
            log_info("Skipping extraction: opcode is OTHER")
            return {"extracted_data": [], "column_extracted": None}

        log_info(f"Extracting data for opcode: {point_data.get("opcode", "")} from drilldown page with title '{title}'")
        for attempt in range(max_retries):
            try:
                log_info(f"Extracting drill-down page data for '{title}'... (Attempt {attempt + 1}/{max_retries})")

                # Wait for page to load completely
                await asyncio.sleep(3)

                # Wait for AG-Grid to load
                await page.wait_for_selector('.ag-root-wrapper', timeout=15000)
                log_info("AG-Grid detected on page")

                # Find the target month row to expand
                target_month_found = False
                ag_rows = await page.query_selector_all('.ag-row')
                log_info(f"Found {len(ag_rows)} AG-Grid rows")
                
                for row_index, row in enumerate(ag_rows):
                    try:
                        row_text = await row.text_content()
                        log_info(f"Row text: {row_text}")
                        
                        tooltip_value = point_data["tooltipText"]
                        
                        def convert_month_label(label: str) -> str:
                            """Convert 'Oct-24' → '10/24'."""
                            return datetime.strptime(label, "%b-%y").strftime("%m/%y") 
                        
                        matches = re.findall(r"[A-Za-z]{3}-\d{2}", tooltip_value)
                        short_labels = [convert_month_label(m) for m in matches]
                        series_short1, series_short2 = short_labels
                        log_info(f"Target months: {series_short1}, {series_short2}")
                        
                        # Check if this row contains the target month/year
                        if series_short1 in row_text or series_short2 in row_text:
                            target_month_found = True
                            log_info(f"Target month found in row {row_index}")
                            
                            # Look for expand/collapse button in this row
                            expand_button = await row.query_selector('.ag-group-contracted')
                            if expand_button and await expand_button.is_visible():
                                log_info(f"Expanding row {row_index}")
                                await expand_button.click()
                                await asyncio.sleep(2)
                                log_info("Row expanded successfully")
                            else:
                                # Check if already expanded
                                expanded_button = await row.query_selector('.ag-group-expanded')
                                if expanded_button and not await expanded_button.has_class('ag-hidden'):
                                    log_info(f"Row {row_index} is already expanded")
                            break
                            
                    except Exception as row_error:
                        log_info(f"Error processing row {row_index}: {row_error}")
                        continue
                
                if not target_month_found:
                    log_info(f"Target month not found in any rows")
                    continue

                # Extract data from expanded rows
                await asyncio.sleep(1)
                updated_rows = await page.query_selector_all('.ag-row')
                log_info(f"Processing {len(updated_rows)} rows for data extraction")
                
                # Define target columns
                target_columns = {
                    'workmix': ['workmix', 'work_mix', 'work mix'],
                    'jobcount': ['jobcount', 'job_count', 'job count'],
                    'grossprofit': ['grossprofitpercentage', 'grossprofitpercentage', 'grossprofitpercentage'],
                    'partscost': ['partscost', 'partscost'],
                    'markup': ['markup', 'markup', 'markup']
                }
                
                # Find the specific column mapping based on title
                column_mapping = None
                title1 = re.sub(r"-\s*[A-Za-z]{3}-\d{2}\s+vs\s+[A-Za-z]{3}-\d{2}", "", title)
                target_column_key = title1.lower().replace(' ', '').replace('_', '')
                
                # Map common title variations to our target columns
                title_mappings = {
                    'partsworkmix%': 'workmix',
                    'work_mix': 'workmix', 
                    'workmixpercentage': 'workmix',
                    'jobcount': 'jobcount',
                    'JobCount': 'jobcount',
                    'grossprofit%': 'grossprofit',
                    'GrossProfit%': 'grossprofit',
                    'gross_profit': 'grossprofit',
                    'effectivelaborrate': 'effectivelabourrate',
                    'partscost': 'partscost',
                    'markup': 'markup',
                    'sold_hours': 'markup',
                    'partsmarkup': 'markup'
                }
                
                # Get the standardized column key
                standardized_key = title_mappings.get(target_column_key)
                log_info("target_column_key", target_column_key)
                
                if not standardized_key:
                    log_info(f"Unknown title '{title}'. Available options: {list(title_mappings.keys())}")
                    return {"extracted_data": []}
                
                log_info(f"Looking for column: {standardized_key} based on title: '{title}'")
                
                # Method 1: Look for column by col-id attribute
                search_terms = target_columns[standardized_key]
                for term in search_terms:
                    header_element = await page.query_selector(f'[col-id="{term}"], [col-id*="{term.replace(" ", "")}"]')
                    if header_element:
                        col_id = await header_element.get_attribute('col-id')
                        column_mapping = col_id
                        log_info(f"Found {standardized_key} column with col-id: {col_id}")
                        break
                
                # Method 2: Look for column by header text
                if not column_mapping:
                    headers = await page.query_selector_all('.ag-header-cell-text')
                    for header in headers:
                        header_text = await header.text_content()
                        header_text_lower = header_text.lower().strip()
                        
                        for term in search_terms:
                            if term.lower() in header_text_lower:
                                parent_header = await header.query_selector('xpath=ancestor::div[@col-id][1]')
                                if parent_header:
                                    col_id = await parent_header.get_attribute('col-id')
                                    column_mapping = col_id
                                    log_info(f"Found {standardized_key} column with col-id: {col_id} (by header text: '{header_text}')")
                                    break
                        if column_mapping:
                            break
                
                # Method 3: If column not found, try scrolling horizontally and search again
                if not column_mapping:
                    log_info(f"Column not found in current view, attempting horizontal scroll...")
                    
                    # Get the horizontal scroll container
                    scroll_viewport = await page.query_selector('.ag-body-horizontal-scroll-viewport')
                    scroll_container = await page.query_selector('.ag-body-horizontal-scroll-container')
                    
                    if scroll_viewport and scroll_container:
                        # Get scroll dimensions
                        viewport_width = await scroll_viewport.evaluate('el => el.clientWidth')
                        container_width = await scroll_container.evaluate('el => el.scrollWidth')
                        current_scroll = await scroll_viewport.evaluate('el => el.scrollLeft')
                        
                        log_info(f"Scroll info - Viewport: {viewport_width}px, Container: {container_width}px, Current: {current_scroll}px")
                        
                        # Calculate scroll steps (scroll by 200px increments)
                        scroll_step = 200
                        max_scroll = container_width - viewport_width
                        scroll_attempts = 0
                        max_scroll_attempts = 10
                        
                        while not column_mapping and scroll_attempts < max_scroll_attempts and current_scroll < max_scroll:
                            # Scroll right
                            new_scroll_position = min(current_scroll + scroll_step, max_scroll)
                            await scroll_viewport.evaluate(f'el => el.scrollLeft = {new_scroll_position}')
                            await asyncio.sleep(1)  # Wait for scroll to complete
                            
                            scroll_attempts += 1
                            current_scroll = new_scroll_position
                            log_info(f"Scroll attempt {scroll_attempts}: scrolled to position {current_scroll}px")
                            
                            # Try to find the column again after scrolling
                            # Method 1: Look for column by col-id attribute
                            for term in search_terms:
                                header_element = await page.query_selector(f'[col-id="{term}"], [col-id*="{term.replace(" ", "")}"]')
                                if header_element and await header_element.is_visible():
                                    col_id = await header_element.get_attribute('col-id')
                                    column_mapping = col_id
                                    log_info(f"Found {standardized_key} column with col-id: {col_id} after scrolling")
                                    break
                            
                            # Method 2: Look for column by header text
                            if not column_mapping:
                                headers = await page.query_selector_all('.ag-header-cell-text')
                                for header in headers:
                                    if await header.is_visible():
                                        header_text = await header.text_content()
                                        header_text_lower = header_text.lower().strip()
                                        
                                        for term in search_terms:
                                            if term.lower() in header_text_lower:
                                                parent_header = await header.query_selector('xpath=ancestor::div[@col-id][1]')
                                                if parent_header:
                                                    col_id = await parent_header.get_attribute('col-id')
                                                    column_mapping = col_id
                                                    log_info(f"Found {standardized_key} column with col-id: {col_id} (by header text: '{header_text}') after scrolling")
                                                    break
                                        if column_mapping:
                                            break
                            
                            if column_mapping:
                                break
                    else:
                        log_info("Could not find scroll elements")
                
                if not column_mapping:
                    log_info(f"Could not find column for '{title}' even after scrolling")
                    return {"extracted_data": []}
                
                log_info(f"Using column mapping: {standardized_key} -> {column_mapping}")
                
                # Extract data from all visible rows, filtering for paytypegroup = 'C'
                extracted_data = []
                
                for row_index, row in enumerate(updated_rows):
                    try:
                        # Check if this is a data row (not a group header row)
                        row_class = await row.get_attribute('class')
                        if 'ag-row-group' in row_class and 'ag-row-level-0' in row_class:
                            log_info(f"Skipping group header row {row_index}")
                            continue
                        
                        # Get the month value from the first column
                        month_cell = await row.query_selector('[col-id="ag-Grid-AutoColumn"] .ag-group-value')
                        if not month_cell:
                            continue
                        
                        month_value = await month_cell.text_content()
                        month_value = month_value.strip()
                        
                        # Check if this row matches our target months
                        tooltip_value = point_data["tooltipText"]
                        matches = re.findall(r"[A-Za-z]{3}-\d{2}", tooltip_value)
                        short_labels = [datetime.strptime(m, "%b-%y").strftime("%m/%y") for m in matches]
                        
                        if month_value not in short_labels:
                            continue
                        
                        # Check paytypegroup column
                        paytype_cell = await row.query_selector('[col-id="paytypegroup"]')
                        if not paytype_cell:
                            log_info(f"No paytypegroup cell found in row {row_index}")
                            continue
                        
                        paytype_value = await paytype_cell.text_content()
                        paytype_value = paytype_value.strip()
                        
                        # Only process rows where paytypegroup = 'C'
                        if paytype_value != 'C':
                            log_info(f"Skipping row {row_index}: paytypegroup is '{paytype_value}', not 'C'")
                            continue
                        
                        log_info(f"Processing row {row_index} with month '{month_value}' and paytypegroup 'C'")
                        
                        # Get row identifier (operation code)
                        row_identifier = ""
                        opcode_cell = await row.query_selector('[col-id="lbropcode"] a')
                        if opcode_cell:
                            row_identifier = await opcode_cell.text_content()
                            row_identifier = row_identifier.strip()
                        
                        # Initialize row data
                        row_data = {
                            "row_index": row_index,
                            "month": month_value,
                            "opcode": row_identifier,
                            "paytypegroup": paytype_value
                        }
                        
                        # Extract data for the target column
                        try:
                            cell = await row.query_selector(f'[col-id="{column_mapping}"]')
                            if cell and await cell.is_visible():
                                cell_value = await cell.text_content()
                                cell_value = cell_value.strip()
                                row_data[standardized_key] = cell_value
                                
                                # Only add row if it has data
                                if cell_value and cell_value != "":
                                    extracted_data.append(row_data)
                                    log_info(f"Extracted row {row_index} ({row_identifier}): {standardized_key} = {cell_value}")
                                
                        except Exception as cell_error:
                            log_error(f"Error extracting {standardized_key} from row {row_index}: {cell_error}")
                            continue
                            
                    except Exception as row_error:
                        log_error(f"Error processing row {row_index}: {row_error}")
                        continue
                
                log_info(f"Successfully extracted {len(extracted_data)} rows with data for column '{title}'")
                
                # Return success if we have data
                if extracted_data:
                    return {"extracted_data": extracted_data, "column_extracted": standardized_key}
                    
                # If no data extracted, continue to next attempt
                if attempt < max_retries - 1:
                    log_info(f"No data extracted on attempt {attempt + 1}, retrying...")
                    await asyncio.sleep(retry_delay)
                
            except Exception as e:
                log_error(f"Error on attempt {attempt + 1}: {e}")
                log_error(f"Error loading or extracting data from drilldown page corresponds to the OPCODE {point_data.get("opcode", "")}. Error", e)
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
        
        # Return empty result if all attempts failed
        log_info(f"All {max_retries} attempts failed")
        return {"extracted_data": [], "column_extracted": None}

    async def process_all_combinations_with_selective_legend_control(self, combinations):
        """Process all combinations with selective legend control and drilldown"""     
        # Organize combinations by chart
        chart_combinations = {}
        for combo_idx, combination in enumerate(combinations):
            chart_id = combination.get('chart_id', f'chart_{combo_idx}')
            target_month = combination.get('target_month_year', 'unknown')
            matching_points = combination.get('matching_points', [])

            chart_combinations[chart_id] = {
                'combination_index': combo_idx,
                'chart_info': combination['chart_info'],
                'target_month_year': target_month,
                'matching_points': matching_points,
                'current_point_index': 0
            }
            print(f"📋 Chart {chart_id}: {target_month} ({len(matching_points)} points)")

        max_points = max(len(chart['matching_points']) for chart in chart_combinations.values()) if chart_combinations else 0
        print(f"🎯 Maximum points in any chart: {max_points}")
        all_results = []
        target_month_year = TARGET_MONTHS_YEARS
        # Process points round by round with parallel processing
        for point_round in range(max_points):
            print(f"\nProcessing round {point_round + 1}/{max_points}")
            # Create tasks for this round
            round_tasks = []
            task_counter = 0
            for chart_id, chart_data in chart_combinations.items():
                matching_points = chart_data['matching_points']
                if point_round < len(matching_points):
                    point = matching_points[point_round]
                    task = {
                        'task_id': f"round_{point_round}_chart_{chart_id}",
                        'chart_id': chart_id,
                        'chart_info': chart_data['chart_info'],
                        'target_month_year': chart_data['target_month_year'],
                        'point_data': point,
                        'point_index': point_round,
                        'browser_id': f"browser_{task_counter % MAX_CONCURRENT_BROWSERS}"
                    }
                    round_tasks.append(task)
                    task_counter += 1
            if not round_tasks:
                continue
            # Group tasks by browser for parallel processing
            browser_task_groups = {}
            for i in range(MAX_CONCURRENT_BROWSERS):
                browser_task_groups[f'browser_{i}'] = []
            for task in round_tasks:
                browser_id = task['browser_id']
                browser_task_groups[browser_id].append(task)            
            # Process browser groups in parallel with enhanced legend control
            async def process_browser_round_tasks_with_selective_legend(browser_id, tasks):
                """Process tasks for a specific browser with selective legend control and parallel processing"""
                print(f"🚀 {browser_id}: Starting round {point_round + 1} with {len(tasks)} tasks")
                browser_results = []
                async with async_playwright() as playwright:
                    browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
                    try:
                        # Navigate to PartsWorkMixAnalysis
                        await page.goto(f"{config.site_url.rstrip('/')}/PartsWorkMixAnalysis", timeout=50000)
                        # Click the button
                        await page.click('button:has-text("2 Month Work Mix Comparison")', timeout=500000)
                        # Wait for Month 1 dropdown to be visible
                        await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
                        await page.click('label:has-text("Month 1") + div [role="button"]')
                        await page.click(f'li[role="option"]:has-text("{config.month1}")')

                        # Wait for Month 2 dropdown
                        await page.wait_for_selector('label:has-text("Month 2") + div [role="button"]', timeout=50000)
                        await page.click('label:has-text("Month 2") + div [role="button"]')
                        await page.click(f'li[role="option"]:has-text("{config.month2}")')

                        # Wait for the chart/table container to load before capture
                        for i in range(5):
                            try:
                                await page.wait_for_selector('div.highcharts-container', timeout=10000)
                                log_info("Chart loaded successfully")
                                break
                            except:
                                log_info(f"Retrying chart load... attempt {i+1}")
                        else:
                            raise Exception("Chart did not load after retries")
                        # Apply enhanced legend control
                        legend_setup_success = await self.apply_enhanced_legend_control(page)
                        await asyncio.sleep(2)
                        if not legend_setup_success:
                            print(f"{browser_id}: Legend control setup failed, attempting manual setup...")
                            await self.debug_and_setup_charts(page)
                        # Disable all legends initially
                        await self.disable_all_legends(page)
                        await asyncio.sleep(1)
                        print(f" {browser_id}: Legend control applied, ready for selective processing")
                    except Exception as e:
                        print(f" {browser_id}: Navigation error: {str(e)}")
                        return browser_results
                    # Process each task with selective legend control
                    for task_idx, task in enumerate(tasks):
                        chart_id = task['chart_id']
                        point_label = task['point_data'].get('xLabel', 'Unknown')
                        dataset_label = task['point_data'].get('datasetLabel', 'Unknown Dataset')
                        print(f"{browser_id}: Processing task {task_idx + 1}/{len(tasks)} - {chart_id} - {point_label} ({dataset_label})")
                        try:
                            # Step 1: Disable ALL legends first
                            print(f"🔒 {browser_id}: Disabling all legends before processing {chart_id}")
                            await self.disable_all_legends(page)
                            await asyncio.sleep(1)
                            # Step 2: Enable ONLY the legend for the current chart being processed
                            print(f"🔓 {browser_id}: Enabling ONLY legend for {chart_id} - {dataset_label}")
                            legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
                            await asyncio.sleep(1)
                            if legend_enabled:
                                print(f" {browser_id}: Legend control successful - ONLY {chart_id} legend is active")
                            else:
                                print(f"{browser_id}: Legend control failed, but continuing with processing")
                            
                            chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')   
                            # Step 3: Process with selective legend control
                            result = await self.process_single_point_task_with_selective_legend(page, task, target_month_year,chart_title)
                            if isinstance(result, dict):
                                result['browser_id'] = browser_id
                                result['round'] = point_round + 1
                                result['method'] = 'selective_legend_control'
                                result['task_sequence'] = task_idx + 1
                            browser_results.append(result)
                            print(f" {browser_id}: Completed task {task_idx + 1} - {chart_id} - {point_label}")
                            # Step 4: Disable ALL legends after processing and navigate back if more tasks
                            if task_idx < len(tasks) - 1:  # Not the last task
                                print(f"🔒 {browser_id}: Task completed - Disabling ALL legends before next task")
                                await self.disable_all_legends(page)
                                await page.goto(f"{config.site_url.rstrip('/')}/PartsWorkMixAnalysis", timeout=50000)
                                # Click the button
                                await page.click('button:has-text("2 Month Work Mix Comparison")', timeout=500000)
                                # Wait for Month 1 dropdown to be visible
                                await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
                                await page.click('label:has-text("Month 1") + div [role="button"]')
                                await page.click(f'li[role="option"]:has-text("{config.month1}")')

                                # Wait for Month 2 dropdown
                                await page.wait_for_selector('label:has-text("Month 2") + div [role="button"]', timeout=50000)
                                await page.click('label:has-text("Month 2") + div [role="button"]')
                                await page.click(f'li[role="option"]:has-text("{config.month2}")')

                                # Wait for the chart/table container to load before capture
                                for i in range(5):
                                    try:
                                        await page.wait_for_selector('div.highcharts-container', timeout=10000)
                                        log_info("Chart loaded successfully")
                                        break
                                    except:
                                        log_info(f"Retrying chart load... attempt {i+1}")
                                else:
                                    raise Exception("Chart did not load after retries")

                                # Re-apply legend control for next task
                                print(f"{browser_id}: Re-applying legend control for next task")
                                await self.apply_enhanced_legend_control(page)
                                await asyncio.sleep(1)
                            else:
                                print(f"🏁 {browser_id}: Final task completed - All legends will remain disabled")
                        except Exception as e:
                            print(f" {browser_id}: Error processing task {task_idx + 1} - {chart_id} - {point_label}: {str(e)}")
                            error_result = {
                                'task_id': task['task_id'],
                                'browser_id': browser_id,
                                'round': point_round + 1,
                                'error': str(e),
                                'chart_id': chart_id,
                                'point_label': point_label,
                                'status': 'failed',
                                'method': 'selective_legend_control',
                                'task_sequence': task_idx + 1
                            }
                            browser_results.append(error_result)
                    print(f" {browser_id}: Completed round {point_round + 1} - {len(browser_results)} results")
                    try:
                        await context.close()
                        await browser.close()
                    except Exception as cleanup_error:
                        print(f"{browser_id}: Cleanup error: {cleanup_error}")
                return browser_results            
            # Run browser groups in parallel
            browser_tasks = [
                asyncio.create_task(process_browser_round_tasks_with_selective_legend(browser_id, tasks))
                for browser_id, tasks in browser_task_groups.items()
                if tasks
            ]            
            round_results_list = await asyncio.gather(*browser_tasks, return_exceptions=True)
       
            # Collect results from this round
            round_results = []
            for browser_results in round_results_list:
                if isinstance(browser_results, Exception):
                    print(f"Browser processing failed in round {point_round + 1}: {str(browser_results)}")
                    continue
                round_results.extend(browser_results)            
            all_results.extend(round_results)
            print(f" Round {point_round + 1} completed: {len(round_results)} results")            
            # Add delay between rounds
            if point_round < max_points - 1:
                await asyncio.sleep(2.0)        
        # Process final results
        successful_results = []
        failed_results = []        
        for result in all_results:
            if isinstance(result, dict) and result.get('success', False):
                successful_results.append(result)
            else:
                failed_results.append(result)
        
        print(f"\n🎉 Processing completed with selective legend control!")
        print(f"Summary:")
        print(f"   - Total rounds processed: {max_points}")
        print(f"   - Total charts: {len(chart_combinations)}")
        print(f"   - Total tasks processed: {len(all_results)}")
        print(f"   - Successful: {len(successful_results)}")
        print(f"   - Failed: {len(failed_results)}")        
        return {
            'successful': successful_results,
            'failed': failed_results,
            'all_results': all_results,
            'total_processed': len(all_results),
            'total_charts': len(chart_combinations),
            'rounds_processed': max_points
        }
    
    async def enable_legend_for_chart(self, page, chart_id):
        """Enable legend for a specific chart"""
        try:
            await page.evaluate(f"""
                if (window.chartInstances && window.chartInstances.has('{chart_id}')) {{
                    const chartData = window.chartInstances.get('{chart_id}');
                    const chart = chartData.instance;
                    
                    // Enable legend
                    if (!chart.options.plugins) chart.options.plugins = {{}};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {{}};
                    chart.options.plugins.legend.display = true;
                    
                    // Update chart
                    chart.update('none');
                    
                    console.log(`Legend enabled for {chart_id}`);
                }}
            """)
            
            print(f" Legend enabled for {chart_id}")
            return True
            
        except Exception as e:
            print(f" Failed to enable legend for {chart_id}: {str(e)}")
            return False
    
    async def disable_legend_for_chart(self, page, chart_id):
        """Disable legend for a specific chart"""
        try:
            await page.evaluate(f"""
                if (window.chartInstances && window.chartInstances.has('{chart_id}')) {{
                    const chartData = window.chartInstances.get('{chart_id}');
                    const chart = chartData.instance;
                    
                    // Disable legend
                    if (!chart.options.plugins) chart.options.plugins = {{}};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {{}};
                    chart.options.plugins.legend.display = false;
                    
                    // Update chart
                    chart.update('none');
                    
                    console.log(`Legend disabled for {chart_id}`);
                }}
            """)
            
            print(f" Legend disabled for {chart_id}")
            return True
            
        except Exception as e:
            print(f" Failed to disable legend for {chart_id}: {str(e)}")
            return False

    async def debug_and_setup_charts(self, page):
        """Debug and manually setup chart instances"""
        try:
            result = await page.evaluate("""
                (function() {
                    console.log('=== CHART DEBUG AND SETUP ===');

                    // Check if Chart.js is available
                    console.log('Chart.js available:', typeof Chart !== 'undefined');

                    // Find all canvas elements
                    const allCanvases = document.querySelectorAll('canvas');
                    console.log(`Total canvas elements found: ${allCanvases.length}`);

                    const chartCanvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                    console.log(`Chart.js canvas elements found: ${chartCanvases.length}`);

                    // Initialize chart instances map
                    if (!window.chartInstances) {
                        window.chartInstances = new Map();
                    }

                    let foundCharts = 0;

                    // Try multiple methods to find charts
                    chartCanvases.forEach((canvas, index) => {
                        let chartInstance = null;

                        console.log(`Processing canvas ${index}:`);

                        // Method 1: Chart.getChart
                        if (typeof Chart !== 'undefined' && Chart.getChart) {
                            try {
                                chartInstance = Chart.getChart(canvas);
                                if (chartInstance) {
                                    console.log(`  - Found via Chart.getChart`);
                                }
                            } catch (e) {
                                console.log(`  - Chart.getChart failed:`, e.message);
                            }
                        }

                        // Method 2: Canvas chart property
                        if (!chartInstance && canvas.chart) {
                            chartInstance = canvas.chart;
                            console.log(`  - Found via canvas.chart`);
                        }

                        // Method 3: Canvas _chart property
                        if (!chartInstance && canvas._chart) {
                            chartInstance = canvas._chart;
                            console.log(`  - Found via canvas._chart`);
                        }

                        // Method 4: Search Chart.instances
                        if (!chartInstance && typeof Chart !== 'undefined' && Chart.instances) {
                            Object.values(Chart.instances).forEach(instance => {
                                if (instance.canvas === canvas) {
                                    chartInstance = instance;
                                    console.log(`  - Found via Chart.instances`);
                                }
                            });
                        }

                        if (chartInstance && chartInstance.data && chartInstance.data.datasets) {
                            const chartId = `chart_${index}`;
                            window.chartInstances.set(chartId, {
                                instance: chartInstance,
                                canvas: canvas,
                                originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                            });

                            console.log(`  - Registered as ${chartId} with ${chartInstance.data.datasets.length} datasets`);
                            chartInstance.data.datasets.forEach((dataset, dsIndex) => {
                                console.log(`    Dataset ${dsIndex}: ${dataset.label || 'Unnamed'}`);
                            });

                            foundCharts++;
                        } else {
                            console.log(`  - No valid chart instance found`);
                        }
                    });

                    console.log(`=== SETUP COMPLETE: ${foundCharts} charts registered ===`);
                    return foundCharts;
                })()
            """)

            print(f"Debug setup completed: {result} charts found and registered")
            return result > 0

        except Exception as e:
            print(f" Debug setup failed: {str(e)}")
            return False

    async def click_legend_line(self, page, chart_id, dataset_label):
        """Click on a specific legend line to enable/highlight the dataset"""
        try:
            # First, try to find and click the legend item
            legend_clicked = await page.evaluate(f"""
            (function() {{
                if (window.chartInstances && window.chartInstances.has('{chart_id}')) {{
                    const chartData = window.chartInstances.get('{chart_id}');
                    const chart = chartData.instance;
                    // Find the dataset index by label
                    let datasetIndex = -1;
                    chart.data.datasets.forEach((dataset, index) => {{
                        if (dataset.label === '{dataset_label}') {{
                            datasetIndex = index;
                        }}
                    }});
                    if (datasetIndex >= 0) {{
                        // Hide all datasets except the target one
                        chart.data.datasets.forEach((dataset, index) => {{
                            const meta = chart.getDatasetMeta(index);
                            if (meta) {{
                                meta.hidden = (index !== datasetIndex);
                            }}
                        }});
                        // Update chart
                        chart.update('none');
                        console.log('Activated dataset: {dataset_label} (index: ' + datasetIndex + ')');
                        return true;
                    }}
                    console.log('Dataset not found: {dataset_label}');
                    return false;
                }}
                return false;
            }})()
            """)
            
            if legend_clicked:
                print(f" Legend line clicked for {chart_id} - {dataset_label}")
                return True
            else:
                print(f"Could not click legend line for {chart_id} - {dataset_label}")
                return False
        except Exception as e:
            print(f" Failed to click legend line for {chart_id} - {dataset_label}: {str(e)}")
            return False
    
    async def process_single_point_task_with_legend_control(self, page, task, target_month_year):
        """Process a single point task with legend control - modified version"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        
        try:
            # Click on the specific data point and extract data
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            
            print(f"🎯 {task_id}: Clicking point {point_label} from {dataset_label}")
            
            # Wait a moment to ensure legend control is applied
            await asyncio.sleep(1)
            
            extracted_data = await self.click_and_extract_data(page, point_data, target_month_year)
            
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': extracted_data.get('click_success', False) if extracted_data else False,
                'legend_controlled': True
            }
            
            print(f" {task_id}: Successfully processed {point_label} from {dataset_label}")
            return result
            
        except Exception as e:
            print(f" {task_id}: Error processing point: {e}")
            
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': False
            }
            return error_result
    
    async def click_chart_data_point(self, page, chart_id, point_data):
        """Click on a specific chart data point directly"""
        try:
            # Extract point information
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)
            
            # Click on the specific data point
            point_clicked = await page.evaluate(f"""
            (function() {{
                if (window.chartInstances && window.chartInstances.has('{chart_id}')) {{
                    const chartData = window.chartInstances.get('{chart_id}');
                    const chart = chartData.instance;
                    
                    try {{
                        // Find the dataset by label if datasetIndex is not reliable
                        let targetDatasetIndex = {dataset_index};
                        
                        // Verify dataset index by label
                        chart.data.datasets.forEach((dataset, index) => {{
                            if (dataset.label === '{dataset_label}') {{
                                targetDatasetIndex = index;
                            }}
                        }});
                        
                        // Get the dataset meta
                        const meta = chart.getDatasetMeta(targetDatasetIndex);
                        if (!meta || !meta.data || !meta.data[{point_index}]) {{
                            console.log('Data point not found at index: ' + {point_index});
                            return false;
                        }}
                        
                        // Get the data point element
                        const pointElement = meta.data[{point_index}];
                        if (!pointElement) {{
                            console.log('Point element not found');
                            return false;
                        }}
                        
                        // Get the chart canvas
                        const canvas = chart.canvas;
                        if (!canvas) {{
                            console.log('Canvas not found');
                            return false;
                        }}
                        
                        // Get point position
                        const pointPosition = pointElement.getCenterPoint();
                        
                        // Create and dispatch click event
                        const rect = canvas.getBoundingClientRect();
                        const clickEvent = new MouseEvent('click', {{
                            clientX: rect.left + pointPosition.x,
                            clientY: rect.top + pointPosition.y,
                            bubbles: true,
                            cancelable: true
                        }});
                        
                        // Dispatch the click event
                        canvas.dispatchEvent(clickEvent);
                        
                        console.log('Clicked data point: ' + '{x_label}' + ' from dataset: ' + '{dataset_label}');
                        console.log('Point position:', pointPosition);
                        
                        return true;
                        
                    }} catch (error) {{
                        console.error('Error clicking data point:', error);
                        return false;
                    }}
                }}
                console.log('Chart instance not found: {chart_id}');
                return false;
            }})()
            """)
            
            if point_clicked:
                print(f" Data point clicked for {chart_id} - {x_label} from {dataset_label}")
                return True
            else:
                print(f"Could not click data point for {chart_id} - {x_label} from {dataset_label}")
                return False
                
        except Exception as e:
            print(f" Failed to click data point for {chart_id} - {x_label}: {str(e)}")
            return False

    async def process_single_point_task_with_direct_clicking(self, page, task, target_month_year):
        """Process a single point task with direct data point clicking"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        
        try:
            # Click on the specific data point directly
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            
            print(f"🎯 {task_id}: Clicking data point {point_label} from {dataset_label}")
            
            # Click directly on the chart data point
            click_success = await self.click_chart_data_point(page, chart_id, point_data)
            
            if not click_success:
                print(f"{task_id}: Failed to click data point, attempting fallback method")
                # Fallback to the original click_and_extract_data method
                extracted_data = await self.click_and_extract_data(page, point_data, target_month_year)
            else:
                # Wait a moment after clicking
                await asyncio.sleep(1)
                # Extract data after clicking
                extracted_data = await self.click_and_extract_data(page, point_data, target_month_year)
            
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': extracted_data.get('click_success', False) if extracted_data else False,
                'direct_click_success': click_success
            }
            
            print(f" {task_id}: Successfully processed {point_label} from {dataset_label}")
            return result
            
        except Exception as e:
            print(f" {task_id}: Error processing point: {e}")
            
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'direct_click_success': False
            }
            return error_result
    
    async def process_all_combinations_parallel(self, combinations):
        """Process charts in parallel with 3 browsers, each handling different charts"""
        
        print(f"🚀 Starting parallel processing of {len(combinations)} charts with 3 browsers")
        
        # Organize combinations by chart for easier processing
        chart_combinations = {}
        for combo_idx, combination in enumerate(combinations):
            chart_id = combination.get('chart_id', f'chart_{combo_idx}')
            target_month = combination.get('target_month_year', 'unknown')
            
            # Extract drillable points from the new structure
            processed_points = []
            total_points_count = 0
            
            # Handle the new matching_points structure with series
            matching_points = combination.get('matching_points', {})
            if isinstance(matching_points, dict) and 'series' in matching_points:
                for series in matching_points['series']:
                    series_name = series.get('name', 'Unknown')
                    series_points = series.get('points', [])
                    total_points_count += len(series_points)
                    
                    for point in series_points:
                        # Skip OTHER subcategory points as they're not drillable
                        # if point.get('opCode', '').upper() != 'OTHER':
                            # Add series information to each point for easier processing
                            enhanced_point = point.copy()
                            enhanced_point['series_name'] = series_name
                            processed_points.append(enhanced_point)
            
            # Store chart information
            chart_combinations[chart_id] = {
                'combination_index': combo_idx,
                'chart_info': combination['chart_info'],
                'target_month_year': target_month,
                'matching_points': processed_points,
                'total_points': total_points_count,
                'drillable_count': len(processed_points),
                'original_matching_points': matching_points,  # Keep original for reference
                'current_point_index': 0
            }
            
            print(f"**Chart {chart_id}: {target_month} ({total_points_count} total points, {len(processed_points)} drillable)")
        
        all_results = []
        target_month_year = TARGET_MONTHS_YEARS
        max_browsers = 3
        
        # Group charts for parallel processing (3 charts at a time)
        chart_items = list(chart_combinations.items())
        chart_batches = [chart_items[i:i + max_browsers] for i in range(0, len(chart_items), max_browsers)]
        
        print(f"Processing {len(chart_batches)} batches with up to {max_browsers} browsers per batch")
        
        # Process each batch of charts in parallel
        for batch_index, chart_batch in enumerate(chart_batches, 1):
            print(f"\n🚀 Starting Batch {batch_index}/{len(chart_batches)} with {len(chart_batch)} charts")
            
            batch_tasks = []
            for browser_index, (chart_id, chart_data) in enumerate(chart_batch):
                browser_id = f"Browser_{browser_index + 1}"
                drillable_count = chart_data['drillable_count']
                total_count = chart_data['total_points']
                
                if drillable_count == 0:
                    print(f"   📋 {browser_id}: Skipping Chart {chart_id} - no drillable points (all are OTHER subcategory)")
                    continue
                
                chart_title = chart_data['chart_info'].get('chartTitle', 'Unknown')
                print(f"   📋 {browser_id}: Will process Chart {chart_id} - {chart_title} ({drillable_count}/{total_count} drillable)")
                
                task = asyncio.create_task(
                    self.process_single_chart_parallel(chart_data, target_month_year, browser_id, chart_id)
                )
                batch_tasks.append((browser_id, chart_id, task))
            
            if batch_tasks:
                print(f"Waiting for all {len(batch_tasks)} browsers in batch {batch_index} to complete...")
                
                for browser_id, chart_id, task in batch_tasks:
                    try:
                        result = await task
                        if isinstance(result, list):
                            all_results.extend(result)
                            print(f" {browser_id} completed Chart {chart_id}: {len(result)} results")
                        else:
                            print(f"{browser_id} returned unexpected result type for Chart {chart_id}")
                    except Exception as e:
                        print(f" {browser_id} failed processing Chart {chart_id}: {str(e)}")
                        continue
                
                print(f" Batch {batch_index} completed")
                
                if batch_index < len(chart_batches):
                    print(f"Waiting before next batch...")
                    await asyncio.sleep(3)
            else:
                print(f"Batch {batch_index} had no drillable charts, skipping...")
        
        # Process final results
        successful_results = []
        failed_results = []
        
        for result in all_results:
            if isinstance(result, dict) and result.get('success', False):
                successful_results.append(result)
            else:
                failed_results.append(result)
        
        print(f"\n🎉 Parallel processing with 3 browsers completed!")
        print(f"Summary:")
        print(f"   - Total charts processed: {len(chart_combinations)}")
        print(f"   - Total batches processed: {len(chart_batches)}")
        print(f"   - Total point tasks processed: {len(all_results)}")
        print(f"   - Successful: {len(successful_results)}")
        print(f"   - Failed: {len(failed_results)}")
        print(f"   - Success rate: {(len(successful_results) / len(all_results) * 100):.1f}%" if all_results else "0%")
        
        return {
            'successful': successful_results,
            'failed': failed_results,
            'all_results': all_results,
            'total_processed': len(all_results),
            'total_charts': len(chart_combinations),
            'batches_processed': len(chart_batches),
            'success_rate': (len(successful_results) / len(all_results) * 100) if all_results else 0
        }

    async def process_single_chart_parallel(self, chart_data, target_month_year, browser_id, chart_id):
        """Process all points in a single chart in parallel (for use with multiple browsers)"""
        
        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
        matching_points = chart_data.get('matching_points', [])
        
        print(f"{browser_id}: Processing chart: {chart_title} ({chart_id}) with {len(matching_points)} drillable points")
        
        chart_results = []
        
        # Use the dedicated parallel function to check and re-login the session.
        # This ensures a valid session before a new page is even created.
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []

        # Get a NEW PAGE from the existing session.
        page = await self.auth_manager.new_page()
        try:
            # Navigate to PartsWorkMixAnalysis
            print(f"{browser_id}: Navigating to PartsWorkMixAnalysis for {chart_id}")
            await page.goto(f"{config.site_url.rstrip('/')}/PartsWorkMixAnalysis", timeout=50000)
            # Click the button
            await page.click('button:has-text("2 Month Work Mix Comparison")', timeout=500000)
            # Wait for Month 1 dropdown to be visible
            await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
            await page.click('label:has-text("Month 1") + div [role="button"]')
            await page.click(f'li[role="option"]:has-text("{config.month1}")')

            # Wait for Month 2 dropdown
            await page.wait_for_selector('label:has-text("Month 2") + div [role="button"]', timeout=50000)
            await page.click('label:has-text("Month 2") + div [role="button"]')
            await page.click(f'li[role="option"]:has-text("{config.month2}")')

            # Wait for the chart/table container to load before capture
            for i in range(5):
                try:
                    await page.wait_for_selector('div.highcharts-container', timeout=10000)
                    log_info("Chart loaded successfully")
                    break
                except:
                    log_info(f"Retrying chart load... attempt {i+1}")
            else:
                raise Exception("Chart did not load after retries")
            
            # Wait for chart to load
            await asyncio.sleep(3)
            
            # Process each drillable point in this chart sequentially within this browser
            for point_idx, point_data in enumerate(matching_points):
                # Ensure point_data is a dictionary
                if not isinstance(point_data, dict):
                    print(f"{browser_id}: Skipping point {point_idx + 1}/{len(matching_points)}: Not a dictionary - {type(point_data)}")
                    continue
                
                point_label = point_data.get('opcode', f'Point_{point_idx}')
                series_name = point_data.get('series_name', point_data.get('series', 'Unknown Dataset'))
                sub_category = point_data.get('opcode', '')
                main_category = point_data.get('opcategory', '')
                
                # Double-check that this point is drillable (not OTHER subcategory)
                # if sub_category.upper() == 'OTHER':
                #     print(f"{browser_id}: Skipping point {point_idx + 1}/{len(matching_points)}: {point_label} - OTHER subcategory not drillable")
                #     continue
                
                print(f"\n{browser_id}: Processing point {point_idx + 1}/{len(matching_points)}: {point_label} ({series_name}) - {main_category}.{sub_category}")
                
                try:
                    # Step 1: Ensure chart is interactive and data points are clickable
                    print(f"{browser_id}: Ensuring chart {chart_id} is interactive...")
                    await self.ensure_chart_interactivity(page, chart_id)
                    await asyncio.sleep(1)
                    
                    # Step 2: Create task for this point
                    task = {
                        'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_info': chart_data['chart_info'],
                        'target_month_year': chart_data['target_month_year'],
                        'point_data': point_data,
                        'point_index': point_idx,
                        'browser_id': browser_id
                    }
                    
                    # Step 3: Process this point with enhanced clicking
                    result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year,chart_title)
                    
                    if isinstance(result, dict):
                        result['chart_title'] = chart_title
                        result['point_sequence'] = point_idx + 1
                        result['method'] = 'parallel_processing'
                        result['browser_id'] = browser_id
                        result['chart_id'] = chart_id
                        result['is_drillable'] = True  # Mark as drillable since we filtered out OTHER
                        result['main_category'] = main_category
                        result['opcode'] = sub_category
                        result['series_name'] = series_name
                        result['point_data'] = point_data
                    
                    chart_results.append(result)
                    
                    # Log detailed result
                    if result.get('success', False):
                        drilldown_url = result.get('drilldown_url', 'Unknown')
                        print(f"   ✅ Success: {point_label} - Drilldown URL: {drilldown_url}")
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        print(f"   ❌ Failed: {point_label} - {error_msg}")
                    
                    # Step 4: Navigate back to PartsWorkMixAnalysis for next point (if not last point)
                    if point_idx < len(matching_points) - 1:
                        print(f"{browser_id}: Navigating back to PartsWorkMixAnalysis for next point")
                        try:
                            await page.goto(f"{config.site_url.rstrip('/')}/PartsWorkMixAnalysis", timeout=50000)
                            # Click the button
                            await page.click('button:has-text("2 Month Work Mix Comparison")', timeout=500000)
                            # Wait for Month 1 dropdown to be visible
                            await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
                            await page.click('label:has-text("Month 1") + div [role="button"]')
                            await page.click(f'li[role="option"]:has-text("{config.month1}")')

                            # Wait for Month 2 dropdown
                            await page.wait_for_selector('label:has-text("Month 2") + div [role="button"]', timeout=50000)
                            await page.click('label:has-text("Month 2") + div [role="button"]')
                            await page.click(f'li[role="option"]:has-text("{config.month2}")')

                            # Wait for the chart/table container to load before capture
                            for i in range(5):
                                try:
                                    await page.wait_for_selector('div.highcharts-container', timeout=10000)
                                    log_info("Chart loaded successfully")
                                    break
                                except:
                                    log_info(f"Retrying chart load... attempt {i+1}")
                            else:
                                raise Exception("Chart did not load after retries")
                            
                            await asyncio.sleep(2)
                            print(f"   🔄 Successfully navigated back to PartsWorkMixAnalysis")
                        except Exception as nav_back_error:
                            print(f"   ⚠️ Failed to navigate back to PartsWorkMixAnalysis: {nav_back_error}")
                            pass
                
                except Exception as e:
                    print(f"   💥 Error processing point {point_idx + 1} - {point_label}: {str(e)}")
                    error_result = {
                        'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_title': chart_title,
                        'point_label': point_label,
                        'main_category': main_category,
                        'opcode': sub_category,
                        'series_name': series_name,
                        'is_drillable': True,
                        'error': str(e),
                        'success': False,
                        'method': 'parallel_processing',
                        'browser_id': browser_id,
                        'point_sequence': point_idx + 1
                    }
                    chart_results.append(error_result)
            
            print(f"🏁 {browser_id}: Completed all drillable points for chart: {chart_title}")
        
        except Exception as e:
            print(f"💥 {browser_id}: Error setting up chart {chart_id}: {str(e)}")
            error_result = {
                'chart_id': chart_id,
                'chart_title': chart_title,
                'error': f"Chart setup failed: {str(e)}",
                'success': False,
                'method': 'parallel_processing',
                'browser_id': browser_id
            }
            chart_results.append(error_result)
        
        finally:
            try:
                await page.close()
                
                print(f"🔒 {browser_id}: Browser closed for chart {chart_id}")
            except Exception as cleanup_error:
                print(f"⚠️ {browser_id}: Cleanup error for {chart_id}: {cleanup_error}")
        
        return chart_results

    async def process_single_chart_sequential(self, chart_data, target_month_year):
        """Process all points in a single chart sequentially"""
        chart_id = chart_data.get('chart_info', {}).get('canvasId', 'unknown_chart')
        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
        matching_points = chart_data.get('matching_points', [])

        print(f"🎯 Processing chart: {chart_title} ({chart_id}) with {len(matching_points)} points")

        chart_results = []

        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)

            try:
                # Navigate to PartsWorkMixAnalysis
                print(f"Navigating to PartsWorkMixAnalysis for {chart_id}")
                await page.goto(f"{config.site_url.rstrip('/')}/PartsWorkMixAnalysis", timeout=50000)
                # Click the button
                await page.click('button:has-text("2 Month Work Mix Comparison")', timeout=500000)
                # Wait for Month 1 dropdown to be visible
                await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
                await page.click('label:has-text("Month 1") + div [role="button"]')
                await page.click(f'li[role="option"]:has-text("{config.month1}")')

                # Wait for Month 2 dropdown
                await page.wait_for_selector('label:has-text("Month 2") + div [role="button"]', timeout=50000)
                await page.click('label:has-text("Month 2") + div [role="button"]')
                await page.click(f'li[role="option"]:has-text("{config.month2}")')

                # Wait for the chart/table container to load before capture
                for i in range(5):
                    try:
                        await page.wait_for_selector('div.highcharts-container', timeout=10000)
                        log_info("Chart loaded successfully")
                        break
                    except:
                        log_info(f"Retrying chart load... attempt {i+1}")
                else:
                    raise Exception("Chart did not load after retries")

                # Apply enhanced legend control
                legend_setup_success = await self.apply_enhanced_legend_control(page)
                await asyncio.sleep(2)

                if not legend_setup_success:
                    print(f"Legend control setup failed for {chart_id}, attempting manual setup...")
                    await self.debug_and_setup_charts(page)

                # Debug legend control setup
                await self.debug_legend_control(page)

                print(f" Page setup completed for {chart_id}")

                # Process each point in this chart sequentially
                for point_idx, point_data in enumerate(matching_points):
                    point_label = point_data.get('xLabel', f'Point_{point_idx}')
                    dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')

                    print(f"\nProcessing point {point_idx + 1}/{len(matching_points)}: {point_label} ({dataset_label})")

                    try:
                        # Step 1: Disable ALL legends first
                        print(f"🔒 Disabling all legends before processing {chart_id}")
                        await self.disable_all_legends(page)
                        await asyncio.sleep(1)

                        # Step 2: Enable ONLY the legend for current chart/dataset
                        print(f"🔓 Enabling ONLY legend for {chart_id} - {dataset_label}")
                        legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
                        await asyncio.sleep(2)  # Give more time for chart to update

                        if legend_enabled:
                            print(f" Legend control successful - ONLY {chart_id} legend is active")
                        else:
                            print(f"Legend control failed, but continuing with processing")

                        # Step 2.5: Ensure chart is interactive and data points are clickable
                        print(f"Ensuring chart {chart_id} is interactive after legend control...")
                        await self.ensure_chart_interactivity(page, chart_id)
                        await asyncio.sleep(1)

                        # Step 3: Create task for this point
                        task = {
                            'task_id': f"{chart_id}_point_{point_idx}",
                            'chart_id': chart_id,
                            'chart_info': chart_data['chart_info'],
                            'target_month_year': chart_data['target_month_year'],
                            'point_data': point_data,
                            'point_index': point_idx
                        }
                        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
                        # Step 4: Process this point with enhanced clicking
                        result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year,chart_title)

                        if isinstance(result, dict):
                            result['chart_title'] = chart_title
                            result['point_sequence'] = point_idx + 1
                            result['method'] = 'sequential_processing'

                        chart_results.append(result)

                        # Log detailed result
                        if result.get('success', False):
                            click_success = result.get('click_success', False)
                            nav_success = result.get('navigation_success', False)
                            extract_success = result.get('extraction_success', False)
                            print(f" Completed point {point_idx + 1}: {point_label}")
                            print(f"   Click: {'' if click_success else ''} | Navigation: {'' if nav_success else ''} | Extraction: {'' if extract_success else ''}")
                            if nav_success:
                                drilldown_url = result.get('drilldown_url', 'Unknown')
                                print(f"   🔗 Drilldown URL: {drilldown_url}")
                        else:
                            error_msg = result.get('error', 'Unknown error')
                            print(f" Failed point {point_idx + 1}: {point_label} - {error_msg}")

                        # Step 5: Navigate back to PartsWorkMixAnalysis for next point (if not last point)
                        if point_idx < len(matching_points) - 1:
                            print(f"Navigating back to PartsWorkMixAnalysis for next point")
                            try:
                                await page.goto(f"{config.site_url.rstrip('/')}/PartsWorkMixAnalysis", timeout=50000)
                                # Click the button
                                await page.click('button:has-text("2 Month Work Mix Comparison")', timeout=500000)
                                # Wait for Month 1 dropdown to be visible
                                await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
                                await page.click('label:has-text("Month 1") + div [role="button"]')
                                await page.click(f'li[role="option"]:has-text("{config.month1}")')

                                # Wait for Month 2 dropdown
                                await page.wait_for_selector('label:has-text("Month 2") + div [role="button"]', timeout=50000)
                                await page.click('label:has-text("Month 2") + div [role="button"]')
                                await page.click(f'li[role="option"]:has-text("{config.month2}")')

                                # Wait for the chart/table container to load before capture
                                for i in range(5):
                                    try:
                                        await page.wait_for_selector('div.highcharts-container', timeout=10000)
                                        log_info("Chart loaded successfully")
                                        break
                                    except:
                                        log_info(f"Retrying chart load... attempt {i+1}")
                                else:
                                    raise Exception("Chart did not load after retries")

                                # Re-apply legend control
                                await self.apply_enhanced_legend_control(page)
                                await asyncio.sleep(1)
                                print(f" Successfully navigated back to PartsWorkMixAnalysis")
                            except Exception as nav_back_error:
                                print(f" Failed to navigate back to PartsWorkMixAnalysis: {nav_back_error}")
                                # Try to continue anyway
                                pass

                    except Exception as e:
                        print(f" Error processing point {point_idx + 1} - {point_label}: {str(e)}")
                        error_result = {
                            'task_id': f"{chart_id}_point_{point_idx}",
                            'chart_id': chart_id,
                            'chart_title': chart_title,
                            'point_label': point_label,
                            'error': str(e),
                            'success': False,
                            'method': 'sequential_processing',
                            'point_sequence': point_idx + 1
                        }
                        chart_results.append(error_result)

                print(f" Completed all points for chart: {chart_title}")

            except Exception as e:
                print(f" Error setting up chart {chart_id}: {str(e)}")
                error_result = {
                    'chart_id': chart_id,
                    'chart_title': chart_title,
                    'error': f"Chart setup failed: {str(e)}",
                    'success': False,
                    'method': 'sequential_processing'
                }
                chart_results.append(error_result)

            finally:
                try:
                    await context.close()
                    await browser.close()
                except Exception as cleanup_error:
                    print(f"Cleanup error for {chart_id}: {cleanup_error}")

        return chart_results
    
    async def ensure_chart_interactivity(self, page, chart_id):
        """Ensure chart is interactive and data points are clickable after legend control"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Ensuring chart interactivity for: {chart_id}');
                
                // Check if this is a Highcharts chart with chartid class
                const chartContainer = document.querySelector('.highcharts-container.chartid-{chart_id}');
                if (chartContainer) {{
                    console.log('Found Highcharts container for chartid-{chart_id}');
                    
                    try {{
                        // For Highcharts, ensure tooltips and hover events are enabled
                        const svgRoot = chartContainer.querySelector('.highcharts-root');
                        if (svgRoot) {{
                            // Enable pointer events on all chart elements
                            const chartPoints = chartContainer.querySelectorAll('.highcharts-point');
                            chartPoints.forEach(point => {{
                                point.style.pointerEvents = 'all';
                                point.style.cursor = 'pointer';
                            }});
                            
                            // Ensure series are interactive
                            const series = chartContainer.querySelectorAll('.highcharts-series');
                            series.forEach(serie => {{
                                serie.style.pointerEvents = 'all';
                            }});
                            
                            console.log('Highcharts interactivity ensured for: {chart_id}');
                            return true;
                        }}
                    }} catch (error) {{
                        console.error('Error ensuring Highcharts interactivity:', error);
                        return false;
                    }}
                }}
                
                // Fallback to original Chart.js logic if not Highcharts
                if (!window.chartInstances) {{
                    console.log('No chart instances found');
                    return false;
                }}
                
                // Try multiple chart ID variations for Chart.js
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;
                
                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        break;
                    }}
                }}
                
                if (!chartData) {{
                    console.log('Chart not found for interactivity check');
                    return false;
                }}
                
                const chart = chartData.instance;
                
                try {{
                    // Ensure chart is responsive to events
                    if (chart.options) {{
                        if (!chart.options.interaction) chart.options.interaction = {{}};
                        chart.options.interaction.intersect = false;
                        chart.options.interaction.mode = 'point';
                        
                        if (!chart.options.onHover) {{
                            chart.options.onHover = function(event, elements) {{
                                console.log('Chart hover detected:', elements.length, 'elements');
                            }};
                        }}
                        
                        if (!chart.options.onClick) {{
                            chart.options.onClick = function(event, elements) {{
                                console.log('Chart click detected:', elements.length, 'elements');
                                if (elements.length > 0) {{
                                    const element = elements[0];
                                    console.log('Clicked element:', element);
                                }}
                            }};
                        }}
                    }}
                    
                    // Force chart update to apply interaction settings
                    chart.update('none');
                    
                    console.log('Chart interactivity ensured for: {chart_id}');
                    return true;
                }} catch (error) {{
                    console.error('Error ensuring chart interactivity:', error);
                    return false;
                }}
            }})()
            """)
            
            if result:
                print(f"Chart {chart_id} interactivity ensured")
            else:
                print(f"Failed to ensure chart {chart_id} interactivity")
            
            return result
        
        except Exception as e:
            print(f"Error ensuring chart interactivity: {str(e)}")
            return False

    async def try_chartjs_event_click(self, page, chart_id, point_data):
        """Simple, direct chart click function using chart_id"""
        try:
            screen_x = point_data.get('adjustedScreenX', point_data.get('screenX', 0))
            screen_y = point_data.get('adjustedScreenY', point_data.get('screenY', 0))
            
            print(f"Simple click at ({screen_x}, {screen_y}) for chart {chart_id}")
            
            # Method 1: Direct Highcharts API call using chart_id
            result = await page.evaluate(f"""
                try {{
                    // Find specific chart by chart_id
                    let chart = null;
                    
                    // Try multiple ways to find the chart
                    if (window.Highcharts && window.Highcharts.charts) {{
                        // Method 1: Find by chart container ID or data attribute
                        chart = window.Highcharts.charts.find(c => {{
                            if (!c || !c.container) return false;
                            return c.container.id === '{chart_id}' || 
                                c.container.getAttribute('data-highcharts-chart') === '{chart_id}' ||
                                c.container.closest('[id*="{chart_id}"]');
                        }});
                        
                        // Method 2: If not found, try by index (extract number from chart_id)
                        if (!chart) {{
                            const chartIndex = parseInt('{chart_id}'.match(/\\d+/)?.[0] || '0');
                            chart = window.Highcharts.charts[chartIndex];
                        }}
                    }}
                    
                    if (!chart) return {{ success: false, error: 'No chart found with ID: {chart_id}' }};
                    
                    console.log('Found chart:', chart.container.id || chart.container.className);
                    
                    // Convert coordinates to chart space
                    const svg = chart.container.querySelector('svg');
                    const rect = svg.getBoundingClientRect();
                    const x = {screen_x} < rect.width ? {screen_x} : {screen_x} - rect.left;
                    const y = {screen_y} < rect.height ? {screen_y} : {screen_y} - rect.top;
                    
                    console.log('Converted coordinates:', x, y);
                    
                    // Find point at coordinates
                    const point = chart.series.flatMap(s => s.points)
                        .find(p => {{
                            if (!p.plotX || !p.plotY) return false;
                            const px = p.plotX + chart.plotLeft;
                            const py = p.plotY + chart.plotTop;
                            const distance = Math.sqrt(Math.pow(px - x, 2) + Math.pow(py - y, 2));
                            return distance < 40; // Increased tolerance
                        }});
                    
                    if (point) {{
                        console.log('Found point:', point.category, point.series.name, point.y);
                        
                        // Try multiple click methods
                        if (point.click) {{
                            point.click();
                            console.log('Called point.click()');
                        }}
                        
                        if (point.firePointEvent) {{
                            point.firePointEvent('click');
                            console.log('Called point.firePointEvent()');
                        }}
                        
                        // Trigger drilldown if available
                        if (point.doDrilldown) {{
                            point.doDrilldown();
                            console.log('Called point.doDrilldown()');
                        }}
                        
                        return {{ success: true, method: 'highcharts_api', point_found: true }};
                    }}
                    
                    return {{ success: false, error: 'No matching point found at coordinates' }};
                }} catch (e) {{
                    console.error('Chart click error:', e);
                    return {{ success: false, error: e.message }};
                }}
            """)
            
            if result.get('success'):
                return result
                
            # Method 2: Simple DOM click as fallback
            await page.mouse.click(screen_x, screen_y)
            return {'success': True, 'method': 'mouse_click'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def process_single_point_task_with_enhanced_clicking(self, page, task, target_month_year,chart_title):
        """Simplified processing with essential steps only"""
        
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        point_data = task['point_data']
        
        # Basic validation
        if not isinstance(point_data, dict):
            return {
                'task_id': task_id,
                'error': 'Invalid point data',
                'success': False,
                'timestamp': datetime.now().isoformat()
            }

        # Skip non-drillable points
        # if point_data.get('opCode', '').upper() == 'OTHER':
        #     return {
        #         'task_id': task_id,
        #         'error': 'Point with OTHER subcategory is not drillable',
        #         'success': False,
        #         'is_drillable': False,
        #         'timestamp': datetime.now().isoformat()
        #     }

        print(f"{task_id}: Processing {point_data.get('category', 'Unknown')}")
        
        try:
            initial_url = page.url
            log_info(f"Initial URL: {initial_url}")
            screen_x = point_data.get('screenX', point_data.get('screenX'))
            screen_y = point_data.get('screenY', point_data.get('screenY'))
            
            if not screen_x or not screen_y:
                return {
                    'task_id': task_id,
                    'error': 'No coordinates available',
                    'success': False,
                    'timestamp': datetime.now().isoformat()
                }
            
            # Simple click attempts
            click_methods = [
                # Method 1: Enhanced chart click
                lambda: self.try_chartjs_event_click(page, task['chart_id'], point_data),
                
                # Method 2: Direct mouse click
                lambda: page.click(
                    f".highcharts-container.chartid-{task['chart_id']} svg.highcharts-root",
                    position={"x": screen_x, "y": screen_y}
                ),
                
                lambda: page.evaluate(f"""
                    (el => el && el.click() && true || false)(document.elementFromPoint({screen_x}, {screen_y}))
                """),
                
                
            ]
            
            navigation_success = False
            click_result = None
            
            # Get initial tab state before clicking
            initial_selected_tab = await page.evaluate("""
                () => {
                    const selectedTab = document.querySelector('.MuiTab-root.Mui-selected');
                    return selectedTab ? selectedTab.id : null;
                }
            """)

            log_info(f"{task_id}: Initial selected tab: {initial_selected_tab}")
                        
            for i, method in enumerate(click_methods):
                log_info(f"{task_id}: Trying click method {i+1}")
                
                try:
                    await method()
                    
                    # Check for both URL change and tab change
                    for attempt in range(10):  # 5 seconds total
                        await asyncio.sleep(0.5)
                        
                        # Check URL change first
                        current_url = page.url
                        if current_url != initial_url:
                            navigation_success = True
                            print(f"{task_id}: URL navigation detected with method {i+1}")
                            log_info(f"{task_id}: URL changed from {initial_url} to {current_url}")
                            click_result = {
                                'success': True, 
                                'method': f'method_{i+1}',
                                'navigation_type': 'url_change',
                                'from_url': initial_url,
                                'to_url': current_url
                            }
                            break
                        
                        # Check for tab change if URL didn't change
                        current_selected_tab = await page.evaluate("""
                            () => {
                                const selectedTab = document.querySelector('.MuiTab-root.Mui-selected');
                                return selectedTab ? selectedTab.id : null;
                            }
                        """)
                        log_info(f"{task_id}: Current selected tab: {current_selected_tab}")
                        
                        if current_selected_tab != initial_selected_tab and current_selected_tab is not None:
                            navigation_success = True
                            print(f"{task_id}: Tab navigation detected with method {i+1}")
                            print(f"{task_id}: Tab changed from '{initial_selected_tab}' to '{current_selected_tab}'")
                            click_result = {
                                'success': True, 
                                'method': f'method_{i+1}',
                                'navigation_type': 'tab_change',
                                'from_tab': initial_selected_tab,
                                'to_tab': current_selected_tab
                            }
                            break
                    
                    if navigation_success:
                        break
                        
                except Exception as e:
                    print(f"{task_id}: Method {i+1} failed: {str(e)}")
                    continue
            
            if not navigation_success:
                return {
                    'task_id': task_id,
                    'error': 'No click method triggered navigation',
                    'success': False,
                    'timestamp': datetime.now().isoformat()
                }
            
            # Data extraction - handle both URL-based and tab-based navigation
            current_url = page.url
            extraction_success = False
            extracted_data = {}
            drillOpcode=point_data.get("opcode", "").upper()
            log_info("Current URL:",current_url)

            log_info("Opcode to be drilled:",drillOpcode)
            
            # Check if this is URL-based navigation to drilldown page
            if  click_result and click_result.get('navigation_type') == 'tab_change':
                print(f"{task_id}: On drilldown page, extracting data...")
                try:
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year,chart_title)
                    extraction_success = extracted_data.get('extraction_data', {}).get('success', False)
                except Exception as e:
                    extracted_data = {'extraction_data': {'success': False, 'error': str(e)}}   
            
            else:
                
                # Skip extraction entirely if opcode is OTHER
                if point_data.get("opcode", "").upper() == "OTHER":
                    log_info("Skipping extraction: opcode is OTHER")
                    extracted_data ={"extracted_data": [], "column_extracted": None}
                else:
                    extracted_data = {'extraction_data': {'success': False, 'error': f'Unexpected navigation result. URL: {current_url}, Click result: {click_result}'}}
            
            overall_success = navigation_success and extraction_success
            
            return {
                'task_id': task_id,
                'chart_id': task['chart_id'],
                'point_data': point_data,
                'is_drillable': True,
                'click_result': click_result,
                'navigation_success': navigation_success,
                'extraction_success': extraction_success,
                'extracted_data': extracted_data,
                'drilldown_url': current_url,
                'success': overall_success,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'task_id': task_id,
                'error': str(e),
                'success': False,
                'timestamp': datetime.now().isoformat()
            }
      
    async def process_single_point_task(self, task, target_month_year):
        """Process a single point task - unchanged from your original"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        browser_id = task['browser_id']
        
        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            
            try:
                # Navigate to PartsWorkMixAnalysis
                await page.goto(f"{config.site_url.rstrip('/')}/PartsWorkMixAnalysis", timeout=50000)
                # Click the button
                await page.click('button:has-text("2 Month Work Mix Comparison")', timeout=500000)
                # Wait for Month 1 dropdown to be visible
                await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
                await page.click('label:has-text("Month 1") + div [role="button"]')
                await page.click(f'li[role="option"]:has-text("{config.month1}")')

                # Wait for Month 2 dropdown
                await page.wait_for_selector('label:has-text("Month 2") + div [role="button"]', timeout=50000)
                await page.click('label:has-text("Month 2") + div [role="button"]')
                await page.click(f'li[role="option"]:has-text("{config.month2}")')

                # Wait for the chart/table container to load before capture
                for i in range(5):
                    try:
                        await page.wait_for_selector('div.highcharts-container', timeout=10000)
                        log_info("Chart loaded successfully")
                        break
                    except:
                        log_info(f"Retrying chart load... attempt {i+1}")
                else:
                    raise Exception("Chart did not load after retries")
                           
                # Click on the specific data point and extract data
                point_label = point_data.get('xLabel', 'Unknown')
                dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')                
                print(f"🎯 {task_id}: Clicking point {point_label} from {dataset_label}")                
                extracted_data = await self.click_and_extract_data(page, point_data, target_month_year)
                
                result = {
                    'task_id': task_id,
                    'chart_id': chart_id,
                    'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                    'target_month_year': task['target_month_year'],
                    'point_data': point_data,
                    'dataset_label': dataset_label,
                    'extracted_data': extracted_data,
                    'timestamp': datetime.now().isoformat(),
                    'browser_id': browser_id,
                    'success': extracted_data.get('click_success', False) if extracted_data else False
                }                
                print(f" {task_id}: Successfully processed {point_label} from {dataset_label}")
                return result                
            except Exception as e:
                print(f" {task_id}: Error processing point: {e}")                
                error_result = {
                    'task_id': task_id,
                    'chart_id': chart_id,
                    'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                    'target_month_year': task['target_month_year'],
                    'point_data': point_data,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat(),
                    'browser_id': browser_id,
                    'success': False
                }
                return error_result                
            finally:
                await context.close()
                await browser.close()
     
    async def process_chart_combination(self, combination, target_month_year, browser_id):
        """Process a single chart-point combination in its own browser"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]
        chart_id = combination.get('chart_id', 'unknown')
        target_month = combination.get('target_month_year', 'unknown')
        matching_points = combination.get('matching_points', [])        
        print(f"Browser {browser_id}: Processing {chart_id} - {target_month}")
        print(f"   Points to process: {len(matching_points)}")
        
        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            
            try:
                # Navigate to PartsWorkMixAnalysis
                await page.goto(f"{config.site_url.rstrip('/')}/PartsWorkMixAnalysis", timeout=50000)
                # Click the button
                await page.click('button:has-text("2 Month Work Mix Comparison")', timeout=500000)
                # Wait for Month 1 dropdown to be visible
                await page.wait_for_selector('label:has-text("Month 1") + div [role="button"]', timeout=50000)
                await page.click('label:has-text("Month 1") + div [role="button"]')
                await page.click(f'li[role="option"]:has-text("{config.month1}")')

                # Wait for Month 2 dropdown
                await page.wait_for_selector('label:has-text("Month 2") + div [role="button"]', timeout=50000)
                await page.click('label:has-text("Month 2") + div [role="button"]')
                await page.click(f'li[role="option"]:has-text("{config.month2}")')

                # Wait for the chart/table container to load before capture
                for i in range(5):
                    try:
                        await page.wait_for_selector('div.highcharts-container', timeout=10000)
                        log_info("Chart loaded successfully")
                        break
                    except:
                        log_info(f"Retrying chart load... attempt {i+1}")
                else:
                    raise Exception("Chart did not load after retries")
                           
                results = []                
                # Process each matching point in this combination
                for point_idx, point in enumerate(matching_points):
                    try:
                        point_label = point.get('xLabel', f'Point_{point_idx}')
                        point_value = point.get('value', 'unknown')                        
                        print(f"🎯 Browser {browser_id}: Processing point {point_idx + 1}/{len(matching_points)}")
                        print(f"   Point: {point_label} (Value: {point_value})")                        
                        # Click on the data point and extract data
                        extracted_data = await self.click_and_extract_data(page, point, target_month_year)
                        
                        result = {
                            'chart_id': chart_id,
                            'chart_title': combination['chart_info'].get('chartTitle', 'Unknown'),
                            'target_month_year': target_month,
                            'clicked_point': point,
                            'extracted_data': extracted_data,
                            'timestamp': datetime.now().isoformat(),
                            'browser_id': browser_id,
                            'point_index': point_idx,
                            'success': extracted_data.get('click_success', False) if extracted_data else False
                        }                        
                        results.append(result)
                        print(f" Browser {browser_id}: Successfully processed point {point_label}")
                        
                        # Wait before processing next point
                        await asyncio.sleep(3)
                    
                    except Exception as e:
                        print(f" Browser {browser_id}: Error processing point {point.get('xLabel', 'unknown')}: {e}")
                        
                        error_result = {
                            'chart_id': chart_id,
                            'chart_title': combination['chart_info'].get('chartTitle', 'Unknown'),
                            'target_month_year': target_month,
                            'clicked_point': point,
                            'error': str(e),
                            'timestamp': datetime.now().isoformat(),
                            'browser_id': browser_id,
                            'point_index': point_idx,
                            'success': False
                        }
                        results.append(error_result)                
                combination['processing_status'] = 'completed'
                combination['results'] = results                
                print(f" Browser {browser_id}: Completed {chart_id} - {len(results)} results")
                return combination                
            except Exception as e:
                print(f" Browser {browser_id}: Error processing combination {chart_id}: {e}")
                combination['processing_status'] = 'failed'
                combination['error'] = str(e)
                return combination                
            finally:
                await context.close()
                await browser.close()
    
    async def run_complete_process(self):
        """Run the complete chart processing workflow with enhanced legend control"""
        log_info("🚀 Starting complete chart processing workflow with enhanced legend control...")

        # Step 1: Start the AuthManager ONCE at the beginning of the entire process
        success = await self.auth_manager.start(headless=False)
        if not success:
            log_error("❌ Authentication failed. Exiting.")
            return None
        
        try:
            # Step 1: Create chart-point combinations            
            combinations = await self.create_chart_point_combinations(config.target_month_year)            
            if not combinations:
                print(" No chart-point combinations found")
                return None
            print(f" Created {len(combinations)} chart-point combinations")
            # Step 2: Process all combinations in parallel with 3 browsers
            print("Step 2: Processing combinations in parallel with 3 browsers...")
            results = await self.process_all_combinations_parallel(combinations)
            if not results:
                print(" No results from processing")
                return None
            # Step 3: Save results
            print("Step 3: Saving results...")
            await self.save_results(results)
            print(" Complete chart processing workflow finished successfully")
            print(f"Final Summary:")
            log_info(f"   - Total combinations processed: {len(combinations)}")
            log_info(f"   - Total tasks completed: {results.get('total_processed', 0)}")
            log_info(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
            return results
        except Exception as e:
            log_error(f" Error in complete process: {e}")            
            traceback.print_exc()
            return None
        finally:
            log_info("Closing AuthManager browser/session...")
            await self.auth_manager.stop()  # <-- ensures browser closes
    
    async def save_results(self, results):
        """Save processing results to files with enhanced formatting"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # Create results directory if it doesn't exist
            results_dir= create_folder_file_path(
                subfolder="chart_processing_results",                               
            )

            # Save all results (combined)
            if results.get('all_results'):
                all_results_file = os.path.join(results_dir, chart_process_json)
                with open(all_results_file, 'w', encoding='utf-8') as f:
                    json.dump(results['all_results'], f, indent=2, default=str, ensure_ascii=False)
                print(f"All results saved to {all_results_file}")
            log_info(f"Going to process chart data file: {all_results_file} {transformed_json}")
            output_file_path = os.path.join(results_dir, transformed_json)
            process_chart_data_file(all_results_file, output_file_path) 

            # Save enhanced summary
            summary = {
                'timestamp': timestamp,
                'processing_method': 'enhanced_legend_control_parallel',
                'target_months_years': TARGET_MONTHS_YEARS,
                'max_concurrent_browsers': MAX_CONCURRENT_BROWSERS,
                'total_processed': results.get('total_processed', 0),
                'total_charts': results.get('total_charts', 0),
                'rounds_processed': results.get('rounds_processed', 0),
                'successful_count': len(results.get('successful', [])),
                'failed_count': len(results.get('failed', [])),
                'success_rate': results.get('success_rate', 0),
                'processing_statistics': {
                    'charts_with_data': results.get('total_charts', 0),
                    'average_points_per_chart': results.get('total_processed', 0) / results.get('total_charts', 1) if results.get('total_charts', 0) > 0 else 0,
                    'total_rounds': results.get('rounds_processed', 0)
                }
            }

            # Perform comparison with CP overview results
            # print("\nStep 4: Performing UI vs DB comparison...")
            # await self.compare_with_parts_workmix_comparison_results(results, timestamp)

        except Exception as e:
            print(f" Error saving results: {e}")            
            traceback.print_exc()

def process_chart_data_file(input_file_path, output_file_path=None, threshold=2.0, debug=False):
    """
    Process chart data from JSON file and transform to DB format
    
    Args:
        input_file_path: Path to input JSON file with chart data
        output_file_path: Optional path to save output
        threshold: Work mix threshold for individual opcodes
        debug: Print debug information
    
    Returns:
        Transformed data in DB format
    """
    with open(input_file_path, 'r') as f:
        chart_data = json.load(f)
    
    if debug:
        print("=== DEBUG INFO ===")
        # Extract unique series names and chart titles
        series_names = set()
        chart_titles = set()
        opcodes = set()
        
        for point in chart_data:
            if isinstance(point, dict) and 'point_data' in point:
                point_data = point['point_data']
                series_name = point_data.get('series_name') or point_data.get('series', '')
                if series_name:
                    series_names.add(series_name)
                
                chart_title = point.get('chart_title', '')
                if chart_title:
                    chart_titles.add(chart_title)
                
                opcode = point_data.get('opcode') or point_data.get('category')
                if opcode and opcode != "OTHER":
                    opcodes.add(opcode)
        
        print(f"Unique series names found: {sorted(series_names)}")
        print(f"Unique chart titles found: {sorted(chart_titles)}")
        print(f"Unique opcodes found: {sorted(opcodes)}")
        print("==================")
    
    result = transform_chart_data_to_db_format(chart_data, threshold=threshold)
    
    if output_file_path:
        with open(output_file_path, 'w') as f:
            json.dump(result, f, indent=2)
        print(f"Output saved to: {output_file_path}")
    
    return result

def extract_month_key_from_series(series_name, month_mapping):
    """
    Extract month key from series name using dynamic month mapping
    """
    for month_pattern, month_key in month_mapping.items():
        if month_pattern == series_name:
            return month_key
    return None

def extract_months_from_data(chart_data_list):
    """
    Dynamically extract month information from the chart data
    """
    months = set()
    month_mapping = {}
    
    for point in chart_data_list:
        if not isinstance(point, dict) or 'point_data' not in point:
            continue
            
        point_data = point['point_data']
        series_name = point_data.get('series_name') or point_data.get('series', '')
        
        # Add series name to months set
        if series_name:
            months.add(series_name)
    
    # Sort months and create mapping
    month_list = sorted(list(months))
    for i, month in enumerate(month_list, 1):
        month_mapping[month] = f"mon{i}"
    
    return month_mapping

def identify_chart_type(chart_title):
    """
    Dynamically identify chart type from title
    """
    chart_mappings = {
        'Parts cost': 'partscost',
        'Work Mix %': 'workmix', 
        'Job Count': 'job_count',
        'Gross Profit': 'gp_percentage',
        'parts_markup': 'markup',
        'Sold Hours': 'parts_workmix_hours'
    }
    
    for key, chart_type in chart_mappings.items():
        if key in chart_title:
            return chart_type
    return None

def extract_value_from_point(point_data, chart_type):
    """
    Extract the appropriate value from point_data based on chart type
    """
    
    # Fallback to other possible fields
    fallback_fields = [
        'workmix1Value', 'workmix2Value', 
        'job_count1Value', 'job_count2Value', 'gp_percentage1Value', 
        'gp_percentage2Value', 'elr1Value', 'elr2Value', 
        'parts_workmix_hours1Value', 'parts_workmix_hours2Value'
    ]
    
    for field in fallback_fields:
        if field in point_data and point_data[field] is not None and point_data[field] != '':
            return float(point_data[field])
    
    return 0.0

def extract_month_key_from_series(series_name, month_mapping):
    """
    Extract month key from series name using dynamic month mapping
    """
    for month_pattern, month_key in month_mapping.items():
        if month_pattern == series_name:
            return month_key
    return None

def process_extracted_data(point, opcode, month_mapping):
    """
    Process extracted_data from point and return organized data by month
    """
    extracted_data = point.get('extracted_data', {})
    extracted_values = {}
    
    if isinstance(extracted_data, dict) and 'extracted_data' in extracted_data:
        for row in extracted_data['extracted_data']:
            if isinstance(row, dict):
                extracted_opcode = row.get('opcode')
                if extracted_opcode == opcode:
                    # Get month from the row
                    month_str = row.get('month', '')
                    
                    # Convert month format (e.g., "06/25" to "Jun-25")
                    month_key = None
                    for month_name, key in month_mapping.items():
                        if month_str in ['06/25', '2025-06', 'Jun-25', 'June 2025']:
                            if 'Jun' in month_name or '06' in month_name:
                                month_key = key
                                break
                        elif month_str in ['07/25', '2025-07', 'Jul-25', 'July 2025']:
                            if 'Jul' in month_name or '07' in month_name:
                                month_key = key
                                break
                    
                    if month_key:
                        # Map extracted fields to chart types
                        field_mappings = {
                            'workmix': ('workmix', lambda x: float(x.replace('%', '')) if isinstance(x, str) else float(x)),
                            'jobcount': ('job_count', lambda x: int(float(x)) if x else 0),
                            'grossprofit': ('gp_percentage', lambda x: float(x.replace('%', '')) if isinstance(x, str) else float(x)), 
                            'effectivelabourrate': ('elr', lambda x: float(x.replace('$', '')) if isinstance(x, str) else float(x)),
                            'soldhours': ('parts_workmix_hours', lambda x: float(x) if x else 0)
                        }
                        
                        for field, (chart_type_mapped, converter) in field_mappings.items():
                            if field in row and row[field] is not None:
                                try:
                                    extracted_value = converter(row[field])
                                    if chart_type_mapped not in extracted_values:
                                        extracted_values[chart_type_mapped] = {}
                                    extracted_values[chart_type_mapped][month_key] = extracted_value
                                except (ValueError, AttributeError):
                                    pass
    
    return extracted_values

def organize_ui_chart_data(chart_data_list):
    """
    Organize UI chart data by opcode and chart type dynamically
    """
    # First pass: extract month mapping
    month_mapping = extract_months_from_data(chart_data_list)
    
    organized_data = defaultdict(lambda: {
        'main_category': None,
        'opcategory': None,
        'data': defaultdict(dict),
        'extracted_data': defaultdict(dict)
    })
    
    for point in chart_data_list:
        if not isinstance(point, dict) or 'point_data' not in point:
            continue
            
        point_data = point['point_data']
        chart_title = point.get('chart_title', '')
        
        # Extract key information
        opcode = point_data.get('opcode') or point_data.get('category')
        series_name = point_data.get('series_name') or point_data.get('series', '')
        main_category = point_data.get('main_category') or point_data.get('opcategory')
        
        if not opcode or not series_name:
            continue
            
        # Skip "OTHER" entries as they will be calculated separately
        if opcode == "OTHER":
            continue
            
        # Identify chart type and month
        chart_type = identify_chart_type(chart_title)
        month_key = extract_month_key_from_series(series_name, month_mapping)
        
        if not chart_type or not month_key:
            continue
            
        # Extract value from point_data
        value = extract_value_from_point(point_data, chart_type)
        
        # Store organized data
        if organized_data[opcode]['main_category'] is None:
            organized_data[opcode]['main_category'] = main_category
            organized_data[opcode]['opcategory'] = point_data.get('opcategory', main_category)
            
        organized_data[opcode]['data'][chart_type][month_key] = value
        
        # Process extracted_data if available
        extracted_values = process_extracted_data(point, opcode, month_mapping)
        for chart_type_mapped, month_values in extracted_values.items():
            for month_key_ext, extracted_value in month_values.items():
                organized_data[opcode]['extracted_data'][chart_type_mapped][month_key_ext] = extracted_value
    
    return dict(organized_data), month_mapping

def calculate_category_totals(category_data, available_months):
    """
    Calculate total metrics for a category with dynamic months
    """
    individual_opcodes = category_data.get("individual_opcodes", [])
    others_summary = category_data.get("others_summary", {})
    
    total_metrics = {}
    
    for month_key in available_months:
        totals = {
            "workmix": 0, "job_count": 0, "parts_workmix_hours": 0,
            "parts_workmix_sale": 0, "parts_workmix_cost": 0
        }
        
        # Sum individual opcodes
        for opcode in individual_opcodes:
            totals["workmix"] += opcode.get(f"workmix_{month_key}", 0)
            totals["job_count"] += opcode.get(f"job_count_{month_key}", 0)
            totals["parts_workmix_hours"] += opcode.get(f"parts_workmix_hours_{month_key}", 0)
            totals["parts_workmix_sale"] += opcode.get(f"parts_workmix_sale_{month_key}", 0)
            totals["parts_workmix_cost"] += opcode.get(f"parts_workmix_cost_{month_key}", 0)
        
        # Add others summary
        totals["workmix"] += others_summary.get(f"workmix_{month_key}", 0)
        totals["job_count"] += others_summary.get(f"job_count_{month_key}", 0)
        totals["parts_workmix_hours"] += others_summary.get(f"parts_workmix_hours_{month_key}", 0)
        totals["parts_workmix_sale"] += others_summary.get(f"parts_workmix_sale_{month_key}", 0)
        totals["parts_workmix_cost"] += others_summary.get(f"parts_workmix_cost_{month_key}", 0)
        
        # Calculate derived metrics
        gp_percentage = ((totals["parts_workmix_sale"] - totals["parts_workmix_cost"]) / totals["parts_workmix_sale"] * 100) if totals["parts_workmix_sale"] > 0 else 0
        elr = totals["parts_workmix_sale"] / totals["parts_workmix_hours"] if totals["parts_workmix_hours"] > 0 else 0
        
        total_metrics.update({
            f"workmix_{month_key}": totals["workmix"],
            f"job_count_{month_key}": int(totals["job_count"]),
            f"gp_percentage_{month_key}": gp_percentage,
            f"elr_{month_key}": elr,
            f"parts_workmix_hours_{month_key}": totals["parts_workmix_hours"],
            f"parts_workmix_sale_{month_key}": totals["parts_workmix_sale"],
            f"parts_workmix_cost_{month_key}": totals["parts_workmix_cost"]
        })
    
    return total_metrics

def create_individual_opcode_entry(opcode, opcode_data, available_months):
    """
    Create individual opcode entry in DB format with dynamic month support
    """
    data = opcode_data['data']
    extracted_data = opcode_data.get('extracted_data', {})
    entry = {"opcode": opcode}
    
    # Process each available month
    for month_key in available_months:
        # Get base values from point_data
        workmix = data.get('workmix', {}).get(month_key, 0)
        job_count = int(data.get('job_count', {}).get(month_key, 0))
        gp_percentage = data.get('gp_percentage', {}).get(month_key, 0)
        elr = data.get('elr', {}).get(month_key, 0)
        parts_workmix_hours = data.get('parts_workmix_hours', {}).get(month_key, 0)
        
        # Get extracted values
        extracted_workmix = extracted_data.get('workmix', {}).get(month_key, 0)
        extracted_job_count = int(extracted_data.get('job_count', {}).get(month_key, 0))
        extracted_gp_percentage = extracted_data.get('gp_percentage', {}).get(month_key, 0)
        extracted_elr = extracted_data.get('elr', {}).get(month_key, 0)
        extracted_parts_workmix_hours = extracted_data.get('parts_workmix_hours', {}).get(month_key, 0)
        
        # Calculate derived values
        parts_workmix_sale = parts_workmix_hours * elr
        parts_workmix_cost = parts_workmix_sale * (1 - gp_percentage / 100) if gp_percentage > 0 else 0
        
        # Add to entry
        entry.update({
            f"workmix_{month_key}": workmix,
            f"job_count_{month_key}": job_count,
            f"gp_percentage_{month_key}": gp_percentage,
            f"elr_{month_key}": elr,
            f"parts_workmix_hours_{month_key}": parts_workmix_hours,
            f"parts_workmix_sale_{month_key}": parts_workmix_sale,
            f"parts_workmix_cost_{month_key}": parts_workmix_cost,
            f"extracted_workmix_{month_key}": extracted_workmix,
            f"extracted_job_count_{month_key}": extracted_job_count,
            f"extracted_gp_percentage_{month_key}": extracted_gp_percentage,
            f"extracted_elr_{month_key}": extracted_elr,
            f"extracted_parts_workmix_hours_{month_key}": extracted_parts_workmix_hours
        })
    
    return entry

def categorize_opcodes_by_type(organized_data, available_months, threshold=2.0):
    """
    Categorize opcodes into COMPETITIVE, MAINTENANCE, and REPAIR
    """
    categories = {
        "COMPETITIVE": {"individual_opcodes": [], "others": []},
        "MAINTENANCE": {"individual_opcodes": [], "others": []},
        "REPAIR": {"individual_opcodes": [], "others": []}
    }
    
    # Category mapping
    category_mapping = {
        "COMP": "COMPETITIVE",
        "COMPETITIVE": "COMPETITIVE",
        "MAINT": "MAINTENANCE", 
        "MAINTENANCE": "MAINTENANCE",
        "REPAIR": "REPAIR"
    }
    
    for opcode, opcode_data in organized_data.items():
        main_category = opcode_data.get('main_category', 'MAINT')
        db_category = category_mapping.get(main_category, "MAINTENANCE")
        
        # Check if opcode meets threshold for individual listing
        meets_threshold = False
        for month_key in available_months:
            workmix = opcode_data['data'].get('workmix', {}).get(month_key, 0)
            if workmix >= threshold:
                meets_threshold = True
                break
        
        if meets_threshold:
            # Individual opcode
            entry = create_individual_opcode_entry(opcode, opcode_data, available_months)
            categories[db_category]["individual_opcodes"].append(entry)
        else:
            # Add to others
            categories[db_category]["others"].append(opcode_data)
    
    # Calculate others_summary for each category
    for category_name in categories:
        if categories[category_name]["others"]:
            categories[category_name]["others_summary"] = calculate_others_summary(
                categories[category_name]["others"], available_months
            )
        else:
            # Empty others summary
            others_summary = {}
            for month_key in available_months:
                others_summary.update({
                    f"workmix_{month_key}": 0,
                    f"job_count_{month_key}": 0,
                    f"gp_percentage_{month_key}": 0,
                    f"elr_{month_key}": 0,
                    f"parts_workmix_hours_{month_key}": 0,
                    f"parts_workmix_sale_{month_key}": 0,
                    f"parts_workmix_cost_{month_key}": 0
                })
            categories[category_name]["others_summary"] = others_summary
        
        # Remove the others list as it's not needed in final output
        del categories[category_name]["others"]
    
    return categories

def generate_ui_db_comparison_html(html_path, comparison_data, timestamp, tenant="Unknown", store="Unknown", role="Unknown"):
    """Generate HTML report for UI-DB comparison results"""   
    # Calculate statistics
    total = len(comparison_data)
    passed = sum(1 for entry in comparison_data if entry.get('Match', '').upper() == 'TRUE')
    failed = total - passed    
    # Group by chart name
    grouped_data = defaultdict(list)
    for entry in comparison_data:
        chart_name = entry.get('Chart_Namewith_id', 'Unknown Chart')
        grouped_data[chart_name].append(entry)

    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>UI vs DB Comparison Report</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; }}
            .badge-fail {{ background-color: #dc3545; }}
            .card-header {{ cursor: pointer; }}
            .comparison-row {{ display: flex; justify-content: space-between; margin-bottom: 10px; }}
            .ui-value {{ background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-right: 10px; }}
            .db-value {{ background-color: #e9ecef; padding: 10px; border-radius: 5px; margin-left: 10px; }}
            .match-indicator {{ font-weight: bold; padding: 5px 10px; border-radius: 3px; }}
            .match-true {{ background-color: #d4edda; color: #155724; }}
            .match-false {{ background-color: #f8d7da; color: #721c24; }}
            .pre-json {{ background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="mb-4">UI vs DB Comparison Report</h1>
            <div class="mb-4">
                <strong>Tenant:</strong> {tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
                <strong>Report Timestamp:</strong> {timestamp}<br>
            </div>

            <div class="d-flex gap-3 mb-4">
                <span class="badge bg-success">Passed: {passed}</span>
                <span class="badge bg-danger">Failed: {failed}</span>
                <span class="badge bg-secondary">Total: {total}</span>
                <span class="badge bg-info">Match Rate: {(passed/total*100):.1f}%</span>
            </div>

            <div class="accordion" id="reportAccordion">
    """
    for chart_idx, (chart_name, entries) in enumerate(grouped_data.items()):
        # Check if all entries for this chart pass
        chart_pass = all(entry.get('Match', '').upper() == 'TRUE' for entry in entries)
        badge_class = "badge-pass" if chart_pass else "badge-fail"
        badge_text = "All Passed" if chart_pass else "Has Failures"
        chart_id = f"chart{chart_idx}"
        html_template += f"""
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-{chart_id}">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#{chart_id}" aria-expanded="false" aria-controls="{chart_id}">
                    {chart_name} <span class="ms-3 badge {badge_class}">{badge_text}</span>
                    <small class="ms-2 text-muted">({len(entries)} comparisons)</small>
                </button>
            </h2>
            <div id="{chart_id}" class="accordion-collapse collapse" aria-labelledby="heading-{chart_id}" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        """
        for idx, entry in enumerate(entries):
            match = entry.get('Match', '').upper() == 'TRUE'
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            sub_id = f"{chart_id}-entry-{idx}"
            
            # Extract values for display
            line_name = entry.get('Line_Name_Legend', 'Unknown')
            tooltip_value = entry.get('UI_Line_Data_Point_Value', 'N/A')
            extracted_field = entry.get('Drilldown_Extracted_Field', 'Unknown Field')
            db_tooltip_value = entry.get('DB_Tooltip_Value', 'N/A')
            ui_value = entry.get('UI_Extracted_Value', 'N/A')
            db_field = entry.get('DB_Calculated_Field', 'Unknown Field')
            db_value = entry.get('DB_Calculated_Value', 'N/A')
            
            html_template += f"""
            <div class="card mb-2">
                <div class="card-header" data-bs-toggle="collapse" data-bs-target="#{sub_id}" aria-expanded="false" style="cursor:pointer;">
                    <strong>{extracted_field}</strong> ({line_name}) <span class="ms-2 badge {sub_badge}">{sub_text}</span>
                </div>
                <div id="{sub_id}" class="collapse">
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6>Chart Information:</h6>
                                <p><strong>Line Name:</strong> {line_name}</p>
                                <p><strong>UI Tooltip Value:</strong> {tooltip_value}</p>
                                <p><strong>DB Tooltip Value:</strong> {db_tooltip_value}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>Match Status:</h6>
                                <span class="match-indicator {'match-true' if match else 'match-false'}">
                                    {'✓ MATCH' if match else '✗ MISMATCH'}
                                </span>
                            </div>
                        </div>
                        
                        <div class="comparison-row">
                            <div class="ui-value flex-fill">
                                <h6>UI Extracted Value:</h6>
                                <p><strong>Field:</strong> {extracted_field}</p>
                                <p><strong>Value:</strong> {ui_value}</p>
                            </div>
                            <div class="db-value flex-fill">
                                <h6>DB Calculated Value:</h6>
                                <p><strong>Field:</strong> {db_field}</p>
                                <p><strong>Value:</strong> {db_value}</p>
                            </div>
                        </div>
                        
                        
                    </div>
                </div>
            </div>
            """

        html_template += """
                </div>
            </div>
        </div>
        """

    html_template += """
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)    
    print(f"HTML report generated: {html_path}")

async def generate_final_comparison_report(timestamp):
    """Generate a final consolidated comparison report with enhanced formatting"""
    try:
        print("Generating final consolidated comparison report...")
        results_dir = "chart_processing_results"
        # Find the comparison CSV file
        comparison_csv_file = os.path.join(results_dir, f"ui_db_comparison_{timestamp}.csv")
        if not os.path.exists(comparison_csv_file):
            print(f"Comparison CSV file not found: {comparison_csv_file}")
            return
        comparison_data = []
        with open(comparison_csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            comparison_data = list(reader)
        if not comparison_data:
            print("No comparison data found")
            return
        # Calculate summary statistics
        total_comparisons = len(comparison_data)
        matches = sum(1 for row in comparison_data if row.get('Match', '').lower() == 'true')
        mismatches = total_comparisons - matches
        match_rate = (matches / total_comparisons * 100) if total_comparisons > 0 else 0

        # Generate simplified final report
        final_report_file = os.path.join(results_dir, f"final_ui_db_comparison_report_{timestamp}.csv")

        with open(final_report_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # Write simplified header with only requested columns
            writer.writerow(["UI", "DB Calculated", "Matches"])

            # Write data rows with simplified format
            for row in comparison_data:
                ui_value = row.get('UI_Value', '')
                db_value = row.get('DB_Value', '')
                match_value = row.get('Match', '')
                # Format the UI and DB values with metric names for clarity
                ui_display = f"{row.get('UI_Title', '')} ({row.get('Month', '')}): {ui_value}"
                db_display = f"{row.get('DB_Metric', '')} ({row.get('Month', '')}): {db_value}"
                writer.writerow([ui_display, db_display, match_value])

        # Also create an Excel file with conditional formatting for highlighting
        try:
            # Create Excel workbook
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "UI vs DB Comparison"
            # Create styles
            gray_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
            blue_fill = PatternFill(start_color="305496", end_color="305496", fill_type="solid")
            yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
            center_alignment = Alignment(horizontal='center', vertical='center')
            bold_font = Font(bold=True)
            # Add "Parts Work Mix" heading in first row (merged across all columns)
            ws.merge_cells('A1:I1')
            parts_workmix_comparison_cell = ws['A1']
            parts_workmix_comparison_cell.value = "Parts Work Mix"
            parts_workmix_comparison_cell.alignment = center_alignment
            parts_workmix_comparison_cell.font = bold_font
            # Add "UI" heading in second row (columns C, D, E) with gray background
            ws.merge_cells('C2:E2')
            ui_cell = ws['C2']
            ui_cell.value = "UI"
            ui_cell.fill = gray_fill
            ui_cell.alignment = center_alignment
            ui_cell.font = bold_font

            # Add "Calculated" heading in second row (columns F, G,H) with blue background
            ws.merge_cells('F2:H2')
            calculated_cell = ws['F2']
            calculated_cell.value = "Calculated"
            calculated_cell.fill = blue_fill
            calculated_cell.alignment = center_alignment
            calculated_cell.font = bold_font

            # Define custom Excel column headers as requested
            custom_headers = [
                    "Chart Name(ID)",                # Custom heading
                    "Legend Name(Date)",         # Custom heading
                    "Tooltip Value",                 # Custom heading
                    "Extracted Field Name",               # Custom heading
                    "Extracted Value",
                    "Tooltip Value",
                    "Field Name",
                    "Value",
                    "Match (True/False)"
                ]
            # Original CSV headers mapping to custom headers
            original_headers = [
                "Chart_Namewith_id",
                "Line_Name_Legend",
                "UI_Line_Data_Point_Value",
                "Drilldown_Extracted_Field",
                "UI_Extracted_Value",
                "DB_Tooltip_Value",
                "DB_Calculated_Field",
                
                "DB_Calculated_Value",
                "Match"
            ]
            # Add custom headers to Excel (row 3)
            for col_idx, header in enumerate(custom_headers, start=1):
                # ws.cell(row=3, column=col_idx, value=header)
                header_cell = ws.cell(row=3, column=col_idx, value=header)
                header_cell.font = bold_font
            # Add data and apply conditional formatting (starting from row 4)
            for row_idx, row_data in enumerate(comparison_data, start=4):
                match_value = str(row_data.get('Match', '')).upper()
                for col_idx, original_header in enumerate(original_headers, start=1):
                    cell_value = row_data.get(original_header, '')
                    ws.cell(row=row_idx, column=col_idx, value=cell_value)
                    # Highlight row in yellow if match is FALSE
                    if match_value == 'FALSE':
                        ws.cell(row=row_idx, column=col_idx).fill = yellow_fill
            # Auto-adjust column widths (handle merged cells)
            for col_idx in range(1, len(custom_headers) + 1):
                max_length = 0
                column_letter = get_column_letter(col_idx)                
                # Check all cells in this column for maximum length
                for row in ws.iter_rows(min_col=col_idx, max_col=col_idx):
                    for cell in row:
                        if cell.value and not isinstance(cell, openpyxl.cell.MergedCell):
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass                
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            # Save Excel file
            excel_file = os.path.join(results_dir, f"final_ui_db_comparison_report_{timestamp}.xlsx")
            wb.save(excel_file)
            print(f"Excel report with custom headers and highlighting saved to: {excel_file}")

        except ImportError:
            print("openpyxl not available - Excel file with highlighting not created")
            print("💡 Install openpyxl to get Excel file with yellow highlighting: pip install openpyxl")

        print(f"Final comparison report saved to: {final_report_file}")
        # Print final summary to console
        print(f"\nFINAL COMPARISON SUMMARY:")
        print(f"   Total Comparisons: {total_comparisons}")
        print(f"    Successful Matches: {matches}")
        print(f"    Mismatches: {mismatches}")
        print(f"   Match Rate: {match_rate:.1f}%")
        if mismatches > 0:
            print(f"\n Found {mismatches} mismatches - check the final report for details")
        else:
            print(f"\n🎉 Perfect match! All UI and DB values are consistent!")
        html_report_file = os.path.join(results_dir, f"ui_db_comparison_report_{timestamp}.html")
        generate_ui_db_comparison_html(html_report_file, comparison_data, timestamp)
        return final_report_file
    except Exception as e:
        print(f" Error generating final comparison report: {e}")        
        traceback.print_exc()
        return None

async def ui_capture():
    """Handles chart UI capture and runs the workflow"""
    
    try:
        
        processor = MultiChartParallelProcessor(
            max_browsers=MAX_CONCURRENT_BROWSERS            
        )

        log_info(f"   - Processing mode: Parallel ({MAX_CONCURRENT_BROWSERS}browsers, different charts)")
        log_info(f"   - Max concurrent browsers: {MAX_CONCURRENT_BROWSERS}")
        log_info(f"   - Target months/years: {TARGET_MONTHS_YEARS}")
        log_info(f"   - Browser timeout: {BROWSER_TIMEOUT}ms")
        log_info("=" * 80)

        # Run the parallel chart processing workflow
        results = await processor.run_complete_process()    
        if results:
            log_info("\n" + "=" * 80)
            log_info("Parallel processing with 3 browsers completed successfully!")
            log_info("Final Results:")
            log_info(f"   - Total tasks processed: {results.get('total_processed', 0)}")
            log_info(f"   - Charts processed: {results.get('total_charts', 0)}")
            log_info(f"   - Batches processed: {results.get('batches_processed', 0)}")
            log_info(f"   - Successful tasks: {len(results.get('successful', []))}")
            log_info(f"   - Failed tasks: {len(results.get('failed', []))}")
            log_info(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
            log_info("=" * 80)
            # Additional statistics
            if results.get('successful'):
                log_info(f" Parallel processing completed with {len(results['successful'])} successful extractions")
            if results.get('failed'):
                log_error(f" {len(results['failed'])} tasks failed - check failed results file for details")

            return True

    except Exception as e:
        log_error(f"❌ UI capture failed: {e}")
        traceback.print_exc()
        return False

# Main execution
async def main():
    """Main function to run the enhanced chart processing with legend control"""

    print("Starting Parallel Chart Processing Application with 3 Browsers")
    print("=" * 80)
    start_time = time.time()
    print(f"Start Time: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    # Initialize components
    ui_results = await ui_capture()

    if not ui_results:
        log_error("UI capture did not return results. Exiting.")
        return False
    
    if ui_results:
        print("\n" + "=" * 80)
        print(f"🎉 Parallel processing with 3 browsers completed successfully!")      
        # Generate final comparison report
        print("\n" + "=" * 80)
        print("GENERATING FINAL UI vs DB COMPARISON REPORT")
        print("=" * 80)
        try:
            #step4:
            db_calculation()
            # step5:    
            result_dir,db_json_path = create_folder_file_path(subfolder="chart_processing_results", output_file=db_json) 
            ui_json_path = os.path.join(result_dir, transformed_json)
            log_info(f" Comparing UI JSON: {ui_json_path}")
            log_info(f" with DB JSON: {db_json_path}")
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            compare_with_parts_workmix_comparison_results(ui_json_path, db_json_path,timestamp)
            log_info("Comparison completed - generating final report")
            end_time = time.time()-start_time
            log_info(f"End Time: {end_time}")
            return True
        except Exception as comparison_error:
            print(f" Error generating comparison report: {comparison_error}")            
            traceback.print_exc()
    else:
        print(" Parallel processing failed - check logs for details")
def create_folder_file_path(subfolder: str, output_file: Optional[str] = None) -> Union[str, tuple[str, str]]:
    """
    Generate a folder path. For subfolders other than 'logs', creates tenant/store specific folder.
    """
    def sanitize_filename(name: str) -> str:
        name = name.strip()
        return re.sub(r'[^\w]', '_', name)

    safe_subfolder = sanitize_filename(subfolder)

    if safe_subfolder.lower() == "logs":
        output_folder = safe_subfolder
    else:
        try:
            tenant_name = "carriageag-fopc_simt_prime"
            store_name = "Carriage_Kia_of_Woodstock"
        except AttributeError:
            raise ValueError("Config must contain 'database_name' and 'store_name'.")
        
        safe_tenant_name = sanitize_filename(tenant_name)
        safe_store_name = sanitize_filename(store_name)
        output_folder = os.path.join(tenant_name, store_name, safe_subfolder)
        
    os.makedirs(output_folder, exist_ok=True)

    if output_file:
        output_file_path = os.path.join(output_folder, output_file)
        return output_folder, output_file_path
    else:
        return output_folder

def parse_currency(value):
    """Parse currency string to float with proper handling of monetary values"""
    if value is None:
        return 0.0
    if isinstance(value, (int, float)):
        return round(float(value), 2)
    if isinstance(value, str):
        # Remove currency symbols, commas, spaces and handle parentheses for negative values
        cleaned = value.replace('$', '').replace(',', '').replace(' ', '')
        if cleaned.startswith('(') and cleaned.endswith(')'):
            cleaned = '-' + cleaned[1:-1]
        try:
            # Use Decimal for precise monetary calculations and round to 2 decimal places
            decimal_value = Decimal(cleaned)
            return float(decimal_value.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
        except (ValueError, DecimalInvalidOperation):
            return 0.0
    return 0.0

def parse_percentage(value):
    """Parse percentage string to float, handling percentages with proper decimal precision"""
    if value is None:
        return 0.0
    try:
        if isinstance(value, (int, float)):
            # Convert to Decimal for precise calculation
            decimal_value = Decimal(str(value))
        elif isinstance(value, str):
            # Remove percentage signs, commas, spaces, and handle parentheses for negative values
            cleaned = value.replace('%', '').replace(',', '').replace(' ', '')
            if cleaned.startswith('(') and cleaned.endswith(')'):
                cleaned = '-' + cleaned[1:-1]
            decimal_value = Decimal(cleaned)
        else:
            return 0.0

        # Round to 2 decimal places using proper decimal arithmetic
        return float(decimal_value.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
    except (ValueError, TypeError, DecimalInvalidOperation):
        return 0.0

def organize_chart_data(chart_data_list):
    """
    Organize chart data preserving original GP percentage values
    """
    organized = defaultdict(lambda: defaultdict(dict))
    month_mapping = extract_months_from_data(chart_data_list)
    # Initialize data structure to hold all metrics
    metric_data = defaultdict(lambda: defaultdict(lambda: defaultdict(dict)))
    
    # First pass - collect all metric data
    for item in chart_data_list:
        if not item.get('point_data'):
            continue

        point_data = item['point_data']
        opcode = point_data.get('opcode')
        category = point_data.get('opcategory')
        series = point_data.get('series_name') or point_data.get('series', '')
        chart_title = item.get('chart_title', '')
        # No extracted data handling here - using point_data as before

        if not all([opcode, category, series]):
            continue
            
        # Store category
        metric_data[opcode]['category'] = category
        
        # Map Jul-25 to mon2 and Apr-25 to mon1
        month_key = month_mapping.get(series)
        if not month_key:
            continue

        # Store value based on chart type
        if 'Parts Cost' in chart_title:
            metric_data[opcode][month_key]['parts_cost'] = parse_currency(point_data.get('value', 0))
            # Also store the parts cost from point data if available
            if 'parts_cost_1' in point_data:
                metric_data[opcode]['mon1']['parts_cost'] = parse_currency(point_data['parts_cost_1'])
            if 'parts_cost_2' in point_data:
                metric_data[opcode]['mon2']['parts_cost'] = parse_currency(point_data['parts_cost_2'])
                
        elif 'Work Mix %' in chart_title:
            metric_data[opcode][month_key]['workmix'] = parse_percentage(point_data.get('value', 0))
            # Also store workmix from point data
            if 'workmix_1' in point_data:
                metric_data[opcode]['mon1']['workmix'] = parse_percentage(point_data['workmix_1'])
            if 'workmix_2' in point_data:
                metric_data[opcode]['mon2']['workmix'] = parse_percentage(point_data['workmix_2'])
                
        elif 'Job Count' in chart_title:
            metric_data[opcode][month_key]['job_count'] = int(float(point_data.get('value', 0)))
            # Also store job count from point data
            if 'job_count_1' in point_data:
                metric_data[opcode]['mon1']['job_count'] = int(float(point_data['job_count_1']))
            if 'job_count_2' in point_data:
                metric_data[opcode]['mon2']['job_count'] = int(float(point_data['job_count_2']))
                
        elif 'Parts markup' in chart_title or 'Parts Markup' in chart_title:
            # Store the value based on the series month
            metric_data[opcode][month_key]['parts_markup'] = float(point_data.get('value', 0))
            
            # Always store both values from the complementary fields if available
            if 'parts_markup_1' in point_data:
                metric_data[opcode]['mon1']['parts_markup'] = float(point_data['parts_markup_1'])
            if 'parts_markup_2' in point_data:
                metric_data[opcode]['mon2']['parts_markup'] = float(point_data['parts_markup_2'])

        # Always check for GP percentages
        if 'gp_percentage_1' in point_data:
            metric_data[opcode]['mon1']['gp_percentage'] = parse_percentage(point_data['gp_percentage_1'])
        if 'gp_percentage_2' in point_data:
            metric_data[opcode]['mon2']['gp_percentage'] = parse_percentage(point_data['gp_percentage_2'])
            
    # Second pass - construct final structure with all metrics
    for opcode, data in metric_data.items():
        if opcode == 'category':
            continue
            
        category = data['category']
        mon1_data = data.get('mon1', {})
        mon2_data = data.get('mon2', {})
        
        organized[category][opcode] = {
            'opcode': opcode,
            'category': category,
            'data': {
                'gp_percentage': {
                    'mon1': mon1_data.get('gp_percentage', 0.0),
                    'mon2': mon2_data.get('gp_percentage', 0.0)
                }
            },
            'parts_cost_mon1': mon1_data.get('parts_cost', 0.0),
            'parts_cost_mon2': mon2_data.get('parts_cost', 0.0),
            'workmix_mon1': mon1_data.get('workmix', 0.0),
            'workmix_mon2': mon2_data.get('workmix', 0.0),
            'parts_markup_mon1': mon1_data.get('parts_markup', 0.0),
            'parts_markup_mon2': mon2_data.get('parts_markup', 0.0),
            'job_count_mon1': mon1_data.get('job_count', 0),
            'job_count_mon2': mon2_data.get('job_count', 0),
            'gp_percentage_mon1': mon1_data.get('gp_percentage', 0.0),
            'gp_percentage_mon2': mon2_data.get('gp_percentage', 0.0)
        }
            
    return organized, month_mapping
def transform_chart_data_to_db_format(chart_data_list, threshold=2.0):
    """Transform chart data to DB format preserving GP percentages"""
    organized, month_mapping = organize_chart_data(chart_data_list)
    store_id=config.store_id
    realm=config.realm
    if not organized:
        return None
    
    category_map = {
        'COMP': 'COMPETITIVE',
        'MAINT': 'MAINTENANCE',
        'REPAIR': 'REPAIR'
    }
    
    # Initialize extracted data structure
    extracted_category_data = {}
    
    # Process extracted data from chart_processing_json
    for item in chart_data_list:
        if not (item.get('extracted_data') and item['extracted_data'].get('extracted_data')):
            continue
            
        point_data = item.get('point_data', {})
        opcode = point_data.get('opcode')
        category = point_data.get('opcategory')
        
        if not opcode or not category:
            continue
            
        category_name = category_map.get(category, category)
        
        if category_name not in extracted_category_data:
            extracted_category_data[category_name] = {'individual_opcodes': []}
            
        # Find or create opcode entry
        opcode_entry = None
        for entry in extracted_category_data[category_name]['individual_opcodes']:
            if entry['opcode'] == opcode:
                opcode_entry = entry
                break
                
        if not opcode_entry:
            opcode_entry = {
                'opcode': opcode,
                'parts_cost_mon1': 0.0,
                'parts_cost_mon2': 0.0,
                'workmix_mon1': 0.0,
                'workmix_mon2': 0.0,
                'parts_markup_mon1': 0.0,
                'parts_markup_mon2': 0.0,
                'job_count_mon1': 0,
                'job_count_mon2': 0,
                'gp_percentage_mon1': 0.0,
                'gp_percentage_mon2': 0.0
            }
            extracted_category_data[category_name]['individual_opcodes'].append(opcode_entry)
            
        # Process the extracted data
        column_extracted = item['extracted_data'].get('column_extracted')
        for row in item['extracted_data']['extracted_data']:
            if row.get('opcode') != opcode:
                continue
                
            month = row.get('month')
            if not month:
                continue
                
            # Map the column type to field name
            field_mapping = {
                'partscost': 'parts_cost',
                'workmix': 'workmix',
                'markup': 'parts_markup',
                'jobcount': 'job_count',
                'grossprofit': 'gp_percentage'
            }
            
            # Map months
            month_suffix = 'mon1' if month == config.month1 else 'mon2' if month == config.month2 else None
            if not month_suffix:
                continue
                
            field_name = field_mapping.get(column_extracted)
            if not field_name:
                continue
                
            value = row.get(column_extracted)
            if value is not None:
                if column_extracted == 'jobcount':
                    parsed_value = int(float(value))
                elif column_extracted in ['partscost', 'markup']:
                    parsed_value = parse_currency(value)
                else:  # workmix and grossprofit
                    parsed_value = parse_percentage(value)
                    
                opcode_entry[f'{field_name}_{month_suffix}'] = parsed_value
    
    category_breakdown = {}
    extracted_data = {'category_breakdown': {}}
    
    for cat_key, opcodes in organized.items():
        category_name = category_map.get(cat_key, cat_key)
        individual_opcodes = []
        others_data = defaultdict(lambda: defaultdict(float))

        for opcode, metrics in opcodes.items():
            # Preserve both original and extracted data
            opcode_entry = {
                'opcode': opcode if opcode != 'OTHER' else f'OTHER_{cat_key}',
                'parts_cost_mon1': metrics.get('parts_cost_mon1', 0),  # July 
                'parts_cost_mon2': metrics.get('parts_cost_mon2', 0),  # April
                'workmix_mon1': metrics.get('workmix_mon1', 0),
                'workmix_mon2': metrics.get('workmix_mon2', 0),
                'parts_markup_mon1': metrics.get('parts_markup_mon1', 0),
                'parts_markup_mon2': metrics.get('parts_markup_mon2', 0),
                'job_count_mon1': metrics.get('job_count_mon1', 0),
                'job_count_mon2': metrics.get('job_count_mon2', 0),
                'gp_percentage_mon1': metrics.get('gp_percentage_mon1', 0), # July
                'gp_percentage_mon2': metrics.get('gp_percentage_mon2', 0)  # April
            }

            if opcode == 'OTHER':
                others_data.update(opcode_entry)
            else:
                individual_opcodes.append(opcode_entry)

        category_breakdown[category_name] = {
            'individual_opcodes': sorted(individual_opcodes, key=lambda x: x.get('parts_cost_mon2', 0), reverse=True),
            'others_summary': dict(others_data) if others_data else {},
        }

    # Process extracted data from chart_processing_json
    extracted_data = {'category_breakdown': {}}
    category_to_extracted = {}
    
    # First, collect all extracted data from chart_processing_json
    for item in chart_data_list:
        if not (item.get('extracted_data') and item['extracted_data'].get('extracted_data')):
            continue
            
        point_data = item.get('point_data', {})
        opcode = point_data.get('opcode')
        category = point_data.get('opcategory')
        
        if not opcode or not category:
            continue
            
        category_name = category_map.get(category, category)
        
        if category_name not in category_to_extracted:
            category_to_extracted[category_name] = {}
            
        if opcode not in category_to_extracted[category_name]:
            category_to_extracted[category_name][opcode] = {
                'parts_cost_mon1': 0.0,
                'parts_cost_mon2': 0.0,
                'workmix_mon1': 0.0,
                'workmix_mon2': 0.0,
                'parts_markup_mon1': 0.0,
                'parts_markup_mon2': 0.0,
                'job_count_mon1': 0,
                'job_count_mon2': 0,
                'gp_percentage_mon1': 0.0,
                'gp_percentage_mon2': 0.0
            }
        
        extracted_rows = item['extracted_data']['extracted_data']
        column_extracted = item['extracted_data'].get('column_extracted')
        
        if column_extracted:
            for row in extracted_rows:
                if row.get('opcode') != opcode:
                    continue
                    
                month = row.get('month')
                if not month:
                    continue
                    
                # Map the column_extracted to our field names
                field_mapping = {
                    'partscost': 'parts_cost',
                    'workmix': 'workmix',
                    'markup': 'parts_markup',
                    'jobcount': 'job_count',
                    'grossprofit': 'gp_percentage'  # This maps to both grossprofit and gp_percentage fields
                }
                
                # # Map months to mon1/mon2
                # month_mapping = {
                #     config.month1: 'mon1',  # April data goes into mon1
                #     config.month2: 'mon2'   # July data goes into mon2
                # }
                start_dt = datetime.strptime(config.start_date, "%Y-%m-%d")
                end_dt = datetime.strptime(config.end_date, "%Y-%m-%d")

                start_month_short = start_dt.strftime("%m/%y")  # e.g., 04/25
                end_month_short = end_dt.strftime("%m/%y")      # e.g., 07/25

                # month_mapping = {
                #     start_month_short: 'mon1',
                #     end_month_short: 'mon2'
                # }
                # Parse it as a date
                # Example extracted month from JSON
                row_month_str = row.get('month')  # e.g., "07/25"
                row_month_dt = datetime.strptime(row_month_str, "%m/%y")  # converts "07/25" -> datetime(2025,7,1)

                # month_suffix = month_mapping.get(month)
                # Parse config months
                month1_dt = datetime.strptime(config.month1, "%b-%y")  # e.g., "Apr-25"
                month2_dt = datetime.strptime(config.month2, "%b-%y")  # e.g., "Jul-25"

                # Map to mon1 / mon2
                if row_month_dt == month1_dt:
                    month_suffix = "mon1"
                elif row_month_dt == month2_dt:
                    month_suffix = "mon2"
                else:
                    continue  # skip months outside of range
                if not month_suffix:
                    continue
                    
                field_name = field_mapping.get(column_extracted)
                if not field_name:
                    continue
                    
                # Get the value from the correct field based on column_extracted
                value = row.get(column_extracted)
                if value is not None:
                    # Strip $ and % symbols but preserve the numeric value exactly
                    if isinstance(value, str):
                        cleaned_value = value.replace('$', '').replace('%', '').strip()
                        # Convert to float to remove quotes but preserve exact value
                        try:
                            numeric_value = float(cleaned_value.replace(',', ''))
                            # Format to preserve commas for thousands and exact decimal places
                            if '.' in cleaned_value:
                                decimal_places = len(cleaned_value.split('.')[-1])
                                formatted_value = '{:,.{}f}'.format(numeric_value, decimal_places)
                            else:
                                formatted_value = '{:,.0f}'.format(numeric_value)
                            value = float(formatted_value.replace(',', ''))
                        except ValueError:
                            value = cleaned_value
                            
                    field_key = f'{field_name}_{month_suffix}'
                    category_to_extracted[category_name][opcode][field_key] = value
                    
                    # Special handling for grossprofit - update gp_percentage fields
                    if column_extracted == 'grossprofit':
                        gp_key = f'gp_percentage_{month_suffix}'
                        category_to_extracted[category_name][opcode][gp_key] = value

    # Build the extracted_data structure
    for category_name, opcodes in category_to_extracted.items():
        extracted_data['category_breakdown'][category_name] = {
            'individual_opcodes': []
        }
        for opcode, metrics in opcodes.items():
            if opcode != 'OTHER':  # Skip OTHER opcodes in extracted data
                opcode_details = {
                    'opcode': opcode,
                    **metrics  # Unpack all the metrics we collected
                }
                extracted_data['category_breakdown'][category_name]['individual_opcodes'].append(opcode_details)

    result = {
        'analysis_info': {
            'target_start_month': config.start_date,
            'target_end_month': config.end_date,
            'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'analysis_type': 'parts_work_mix',
            'store_id': str(store_id),
            'realm': realm,
            'advisor_filter': ['all'],
            'technician_filter': ['all'],
            'customer_pay_types': ['M', 'C'],
            'warranty_pay_types': ['W', 'F', 'E']
        },
        'target_month_results': {
            'target_month': config.target_month_year,
            'target_month_name': config.target_month_year,
            'month1_period': config.month1, # April
            'month1_name': config.month1,
            'month2_period': config.month2, # July
            'month2_name': config.month2,
            'work_mix_analysis': {
                'category_breakdown': category_breakdown
            },
            'extracted_data': extracted_data
        }
    }

    # Write output file
    output_folder, output_path = create_folder_file_path("chart_processing_results", transformed_json)
    with open(output_path, 'w') as f:
        json.dump(result, f, indent=4)
    
    return result

def run_validation():
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(" Processing interrupted by user")
    except Exception as e:
        print(f"\n Unexpected error: {e}")
        traceback.print_exc()

def set_target():
    TARGET_MONTHS_YEARS = config.target_month_year
    log_info(f" Target months/years set to: {TARGET_MONTHS_YEARS}")

if __name__ == "__main__":
    set_target()
    run_validation()
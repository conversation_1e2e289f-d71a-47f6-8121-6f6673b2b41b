"""
Compares CP Overview UI and calculated results, and generates CSV, Excel, JSON, and HTML reports.
"""

import json
import csv
import re
import os
import logging
import traceback
from collections import defaultdict
from datetime import datetime
from openpyxl.styles import Font, Alignment, PatternFill
import openpyxl
from dotenv import load_dotenv
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.std.universal.chart_dict import VALIDATION_CHARTS
chart_key="cp_parts_overview"
dict_xlsx_file = VALIDATION_CHARTS[chart_key]["xlsx"]
db_json = VALIDATION_CHARTS[chart_key]["db_json"]
dict_json = VALIDATION_CHARTS[chart_key]["json"]
dict_html= VALIDATION_CHARTS[chart_key]["html"]
dict_csv= VALIDATION_CHARTS[chart_key]["csv"]
sub_folder="Individual_Reports"
load_dotenv()

# Configure logging

# Constants
Tenant = config.database_name
store = config.store_name
role = config.role


def clean_number(value):
    """Remove formatting and convert to float if possible."""
    if value is None or value == "":
        return "Missing"
    if isinstance(value, str):
        value = re.sub(r"[$%,]", "", value)  # Remove $, %, and ,
        if re.match(
            r"\(.*\)", value
        ):  # Check if value is in parentheses (negative number)
            value = "-" + value.strip("()")  # Convert (2) to -2
    try:
        return float(value)
    except ValueError:
        return value  # Return as is if not convertible


def extract_ui_values_from_chart_processing(ui_data):
    """Extract UI values and chart details from chart processing results"""
    ui_values = {}
    chart_details = {}

    try:
        if not isinstance(ui_data, list):
            log_error(f"Expected UI data to be a list, got {type(ui_data)}")
            return {}, {}

        log_info(f"Processing {len(ui_data)} UI charts")

        for chart_idx, chart in enumerate(ui_data):
            try:
                if not isinstance(chart, dict):
                    logging.warning(f"Chart {chart_idx} is not a dictionary, skipping")
                    continue
                log_info(f"Processing chart_idx: {chart_idx} ")
                log_info(f"Processing chart: {chart} ")
                chart_title = chart.get("chart_title", "Unknown Chart")
                chart_id = chart.get("chart_ids", "Unknown ID")
                chart_name_with_id = f"{chart_title}({chart_id})"
                target_month = chart.get("target_month_year", "Unknown")
                dataset_label = chart.get("dataset_label", "Unknown Line")
                log_info(f"Processing chart_title: {chart_title} UI charts")
                log_info(f"Processing chart_id:{chart_id} UI charts")
                log_info(f"Processing chart_name_with_id:{chart_name_with_id} UI charts")
                log_info(f"Processing target_month:{target_month} UI charts")
                log_info(f"Processing dataset_label:{dataset_label} UI charts")

                # Extract line data point value
                point_data = chart.get("point_data", {})
                line_value = point_data.get("value", "0")
                log_info(f"Processing point_data: {point_data} UI charts")
                log_info(f"Processing line_value: {line_value} UI charts")
                # Handle percentage values
                if "%" in dataset_label:
                    ui_line_value = float(line_value) * 100
                else:
                    ui_line_value = float(line_value)
                if chart_id == '966':
                    ui_line_value = float(line_value) * 100
                else:
                    ui_line_value = float(line_value)
                log_info(f"Processing ui_line_value: {ui_line_value} UI charts {chart_id}")
                # Store line value
                line_key = f"{chart_title} ({target_month})"
                ui_values[line_key] = {
                    "line_value": clean_number(ui_line_value),
                    "drilldown_values": {},
                }
                log_info(f"Processing line_key: {line_key} UI charts")
                # Extract drilldown values from mui_grid_data
                extracted_items = (
                    chart.get("extracted_data", {})
                    .get("extraction_data", {})
                    .get("mui_grid_data", [])
                )
                log_info(f"Processing extracted_items: {extracted_items} UI charts")
                for grid in extracted_items:
                    if not isinstance(grid, dict):
                        continue
                    # Only process container_index 0 or 1
                    if grid.get("container_index") in [0, 1]:
                        for item in grid.get("items", []):
                            if not isinstance(item, dict):
                                continue

                            title = item.get("title", "")
                            value = item.get("value", "")
                            if not title or not value:
                                continue

                            clean_value = (
                                str(value)
                                .replace("$", "")
                                .replace(",", "")
                                .replace("%", "")
                                .strip()
                            )
                            drilldown_key = f"{title} ({target_month})"

                            ui_values[line_key]["drilldown_values"][drilldown_key] = (
                                clean_number(clean_value)
                            )
                # Store chart details
                chart_details[line_key] = {
                    "chart_title": chart_title,
                    "chart_id": chart_id,
                    "chart_name_with_id": chart_name_with_id,
                    "dataset_label": dataset_label,
                    "target_month": target_month,
                }
            except Exception as e:
                log_error(f"Error processing chart {chart_idx}: {e}")
                continue
        
        return ui_values, chart_details

    except Exception as e:
        log_error(f"Error extracting UI values: {e}")
        return {}, {}


def extract_db_values_from_cp_parts_overview(db_data):
    """Extract DB values from CP Parts overview calculated results"""
    db_values = {}
    monthly_data = {}

    try:
        if not isinstance(db_data, dict):
            log_error(f"Expected DB data to be a dictionary, got {type(db_data)}")
            return {}, {}
        # Extract target_month_results section
        target_month_results = db_data.get("target_month_results", {})
        analysis_info = db_data.get("analysis_info", {})
        if not target_month_results:
            log_error("No target_month_results found in DB data")
            return {}, {}
        # Get target month for date formatting
        target_month = analysis_info.get("target_month", "2023-11-01")
        formatted_date = target_month
        log_info(
            f"Target month: {target_month}, Processing DB values for date: {formatted_date}"
        )
        # Get customer_pay_metrics for specific fields
        customer_pay_metrics = target_month_results.get("customer_pay_metrics", {})
        ro_counts = target_month_results.get("ro_counts", {})
        # Create monthly_data structure for line comparison
        monthly_data[formatted_date] = {
            "Labor Revenue": target_month_results.get("labor_revenue", 0),
            "Parts Revenue": target_month_results.get("parts_revenue", 0),
            "Combined Revenue": target_month_results.get("combined_revenue", 0),
            "Labor Gross Profit": target_month_results.get("labor_gross_profit", 0),
            "Parts Gross Profit": target_month_results.get("parts_gross_profit", 0),
            "Combined Gross Profit": target_month_results.get(
                "combined_gross_profit", 0
            ),
            "Labor Gross Profit %": target_month_results.get(
                "labor_gross_profit_percentage", 0
            ),
            "CP Parts Gross Profit Percentage": round(target_month_results.get("parts_gross_profit_percentage", 0)),
            "Combined Gross Profit %": target_month_results.get(
                "combined_gross_profit_percentage", 0
            ),
            "Labor Sold Hours": target_month_results.get("labor_sold_hours", 0),
            "Effective Labor Rate": customer_pay_metrics.get("effective_labor_rate", 0),
            "Parts Markup": customer_pay_metrics.get("parts_markup", 0),
            "Parts Revenue Per RO": target_month_results.get("parts_revenue_per_ro", 0),
            "RO Count": ro_counts.get("customer_pay_ros", 0),
            "Hours Per RO - Parts Only": target_month_results.get("hours_per_ro_parts_only_CP", 0),
            "RO Count - Parts Only": target_month_results.get("parts_only_ro_count_CP", 0),
            "total_labor_sold_hours": target_month_results.get("total_labor_sold_hours", 0),
            "CP Parts Markup - Repair and Competitive": target_month_results.get("cp_parts_markup_repair_competitive", 0),
        }
        # Create drilldown values mapping
        drilldown_mappings = {
            f"Labor Sale - Customer Pay ({formatted_date})": customer_pay_metrics.get(
                "labor_sale_customer_pay", 0
            ),
            f"Total Parts Sales ({formatted_date})": customer_pay_metrics.get(
                "total_parts_sale", 0
            ),
            f"Combined Revenue ({formatted_date})": target_month_results.get(
                "combined_revenue", 0
            ),
            f"Labor Gross Profit ({formatted_date})": target_month_results.get(
                "labor_gross_profit", 0
            ),
            f"Parts Gross Profit ({formatted_date})": target_month_results.get(
                "parts_gross_profit", 0
            ),
            f"Combined Gross Profit ({formatted_date})": target_month_results.get(
                "combined_gross_profit", 0
            ),
            f"Labor Gross Profit % ({formatted_date})": target_month_results.get(
                "labor_gross_profit_percentage", 0
            ),
            f"Parts Gross Profit % ({formatted_date})": target_month_results.get(
                "parts_gross_profit_percentage", 0
            ),
            f"Combined Gross Profit % ({formatted_date})": target_month_results.get(
                "combined_gross_profit_percentage", 0
            ),
            f"Labor Sold Hours ({formatted_date})": target_month_results.get(
                "labor_sold_hours", 0
            ),
            f"Total Parts Cost ({formatted_date})": customer_pay_metrics.get(
                "total_parts_cost", 0
            ),
            f"Total Labor Cost ({formatted_date})": customer_pay_metrics.get(
                "total_labor_cost", 0
            ),
            f"Effective Labor Rate ({formatted_date})": customer_pay_metrics.get(
                "effective_labor_rate", 0
            ),
            f"Parts Markup ({formatted_date})": customer_pay_metrics.get(
                "parts_markup", 0
            ),
            f"RO Count ({formatted_date})": ro_counts.get(
                "customer_pay_ros", 0
            ),  
            f"Parts Sale/RO ({formatted_date})": target_month_results.get(
                "parts_revenue_per_ro", 0
            ),
            f"Parts Hours/RO ({formatted_date})": target_month_results.get(
                "hours_per_ro_parts_only_CP", 0
            ),
            f"RO Count - Parts Only ({formatted_date})": target_month_results.get(
                "parts_only_ro_count_CP", 0
            ),
            f"Parts Hours ({formatted_date})": target_month_results.get(
                "total_labor_sold_hours", 0
            ),
            f"Parts Markup - Repair and Competitive ({formatted_date})": target_month_results.get(
                "cp_parts_markup_repair_competitive", 0
            ),
            f"Total Parts Sale - Repair and Competitive ({formatted_date})": target_month_results.get(
                "CP_parts_revenue_Comp_and_Rep", 0
            ),
            f"Total Parts Cost - Repair and Competitive ({formatted_date})": target_month_results.get(
                "CP_parts_cost_Comp_and_Rep", 0
            ),
            
            
        }

        # Structure DB values to match UI format
        for line_name, line_value in monthly_data[formatted_date].items():
            line_key = f"{line_name} ({formatted_date})"
            db_values[line_key] = {
                "line_value": clean_number(line_value),
                "drilldown_values": {},
            }
            # Add relevant drilldown values for this line
            for drilldown_key, drilldown_value in drilldown_mappings.items():
                db_values[line_key]["drilldown_values"][drilldown_key] = clean_number(
                    drilldown_value
                )

        log_info(f"Extracted {len(db_values)} DB values from CP overview data")
        return db_values, monthly_data
    except Exception as e:
        log_error(f"Error extracting DB values: {e}")
        return {}, {}


def compare_values(ui_value, db_value, tolerance=0.01):
    """Compare two values with tolerance for floating point numbers"""
    if ui_value == "Missing" or db_value == "Missing":
        return ui_value == db_value
    try:
        ui_float = float(ui_value) if ui_value != "Missing" else 0
        db_float = float(db_value) if db_value != "Missing" else 0
        return abs(ui_float - db_float) < tolerance
    except (ValueError, TypeError):
        return str(ui_value) == str(db_value)

# Helpers
def sanitize(name):
    """Sanitize name for use in file paths"""
    return name.replace(" ", "-")


def get_output_folder():
    """Get output folder path"""
    return f"CP-Overview-report-{sanitize(Tenant)}_{sanitize(store)}_{sanitize(role)}"


def generate_ui_db_comparison_html(
    html_path, comparison_data, tenant="Unknown", store="Unknown", role="Unknown"
):
    """Generate HTML report for UI-DB comparison results matching the desired format"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    # Convert comparison_data to the expected format if it's a list of lists
    if comparison_data and isinstance(comparison_data[0], list):
        # Convert list format to dict format
        formatted_data = []
        for entry in comparison_data:
            if len(entry) >= 9:
                formatted_entry = {
                    "Chart_Namewith_id": entry[0],
                    "Line_Name_Legend": entry[1],
                    "UI_Line_Data_Point_Value": entry[2],
                    "Drilldown_Extracted_Field": entry[3],
                    "UI_Extracted_Value": entry[4],
                    "DB_Line_Value": entry[5],
                    "DB_Calculated_Field": entry[6],
                    "DB_Calculated_Value": entry[7],
                    "Match": str(entry[8]).upper(),
                }
                formatted_data.append(formatted_entry)
        comparison_data = formatted_data

    # Calculate statistics
    total = len(comparison_data)
    passed = sum(
        1 for entry in comparison_data if str(entry.get("match", "")).upper() == "TRUE"
    )
    failed = total - passed
    match_rate = (passed / total * 100) if total > 0 else 0
    # Group by chart name
    grouped_data = defaultdict(list)
    for entry in comparison_data:
        chart_name = entry.get("chart_name_with_id", "Unknown Chart")
        grouped_data[chart_name].append(entry)
    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>UI vs DB Comparison Report- CP Parts Overview</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; color: white; }}
            .badge-fail {{ background-color: #dc3545; color: white; }}
            .card-header {{ cursor: pointer; background-color: #cfe2f3; }}
            .comparison-section {{ display: flex; justify-content: space-between; margin-bottom: 15px; }}
            .chart-info {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin-right: 10px; }}
            .match-status {{ background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 0 0 200px; }}
            .value-comparison {{ display: flex; justify-content: space-between; margin-top: 15px; }}
            .ui-extracted {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin-right: 10px; }}
            .db-calculated {{ background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 1; }}
            .match-indicator {{ font-weight: bold; padding: 8px 15px; border-radius: 5px; display: inline-block; }}
            .match-true {{ background-color: #d4edda; color: #155724; }}
            .match-false {{ background-color: #f8d7da; color: #721c24; }}
            .section-title {{ font-weight: bold; margin-bottom: 8px; color: #333; }}
            .field-value {{ margin-bottom: 5px; }}
            .badge-all-passed {{ background-color: #28a745; color: white; }}
            .badge-has-failures {{ background-color: #dc3545; color: white; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="mb-4">UI vs DB Comparison Report- CP Parts Overview</h1>
            <div class="mb-4">
                <strong>Tenant:</strong> {tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
                <strong>Report Timestamp:</strong> {timestamp}<br>
            </div>

            <div class="d-flex gap-3 mb-4">
                <span class="badge bg-success">Passed: {passed}</span>
                <span class="badge bg-danger">Failed: {failed}</span>
                <span class="badge bg-secondary">Total: {total}</span>
                <span class="badge bg-info">Match Rate: {match_rate:.1f}%</span>
            </div>

            <div class="accordion" id="reportAccordion">
    """
    for chart_idx, (chart_name, entries) in enumerate(grouped_data.items()):
        # Check if all entries for this chart pass
        chart_pass = all(
            str(entry.get("match", "")).upper() == "TRUE" for entry in entries
        )
        badge_class = "badge-all-passed" if chart_pass else "badge-has-failures"
        badge_text = "All Passed" if chart_pass else "Has Failures"
        chart_id = f"chart{chart_idx}"

        html_template += f"""
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-{chart_id}">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#{chart_id}" aria-expanded="false" aria-controls="{chart_id}">
                    {chart_name} <span class="ms-3 badge {badge_class}">{badge_text}</span>
                    <small class="ms-2 text-muted">({len(entries)} comparisons)</small>
                </button>
            </h2>
            <div id="{chart_id}" class="accordion-collapse collapse" aria-labelledby="heading-{chart_id}" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        """
        for _idx, entry in enumerate(entries):
            match = str(entry.get("match", "")).upper() == "TRUE"
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            # Extract values for display
            line_name = entry.get("line_name_legend", "Unknown")
            ui_tooltip_value = entry["ui"].get("line_value", "N/A")
            db_tooltip_value = entry["db"].get("line_value", "N/A")
            extracted_field = entry.get("drilldown_field", "Unknown Field")
            ui_value = entry["ui"].get("drilldown_value", "N/A")
            db_field = entry.get(
                "drilldown_field", "Unknown Field"
            )  # Same as extracted_field
            db_value = entry["db"].get("drilldown_value", "N/A")

            html_template += f"""
            <div class="card mb-3">
                <div class="card-header">
                    <strong>{extracted_field}</strong> ({line_name}) <span class="ms-2 badge {sub_badge}">{sub_text}</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> {line_name}</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> {ui_tooltip_value}</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> {db_tooltip_value}</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator {"match-true" if match else "match-false"}">
                                {"✓ MATCH" if match else "✗ MISMATCH"}
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> {extracted_field}</div>
                            <div class="field-value"><strong>Value:</strong> {ui_value}</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> {db_field}</div>
                            <div class="field-value"><strong>Value:</strong> {db_value}</div>
                        </div>
                    </div>
                </div>
            </div>
            """
        html_template += """
                </div>
            </div>
        </div>
        """
    html_template += """
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)
    print(f"HTML report generated: {html_path}")


def compare_cp_parts_overview_results(ui_json_path, db_json_path):
    """Compare UI chart processing results with DB calculated values for CP Parts Overview"""
    try:
        log_info("Starting CP Parts Overview comparison...")
        log_info(f"Loading UI data from: {ui_json_path}")
        log_info(f"Loading DB data from: {db_json_path}")
        # Load JSON files
        with open(ui_json_path, "r", encoding="utf-8") as f:
            ui_data = json.load(f)
        with open(db_json_path, "r", encoding="utf-8") as f:
            db_data = json.load(f)
        # Extract values from both sources
        ui_values, ui_chart_details = extract_ui_values_from_chart_processing(ui_data)
        db_values, _db_monthly_data = extract_db_values_from_cp_parts_overview(db_data)
        
        if not ui_values:
            log_error("No UI values extracted")
            return []
        if not db_values:
            log_error("No DB values extracted")
            return []
        comparison_results = []
        json_report_data = []
        # Compare each UI chart with corresponding DB values
        for ui_line_key, ui_line_data in ui_values.items():
            chart_details = ui_chart_details.get(ui_line_key, {})
            chart_name_with_id = chart_details.get(
                "chart_name_with_id", "Unknown Chart"
            )
            log_info(f"chart_details: {chart_details}")
            log_info(f"chart_name_with_id: {chart_name_with_id}")
            # Find corresponding DB line data
            db_line_data = db_values.get(
                ui_line_key, {"line_value": "Missing", "drilldown_values": {}}
            )
            log_info(f"ui_line_key: {ui_line_key}")
            log_info(f"ui_line_data: {ui_line_data}")
            log_info(f"db_line_data: {db_line_data}")
            # Compare line values
            ui_line_value = ui_line_data.get("line_value", "Missing")
            db_line_value = db_line_data.get("line_value", "Missing")
            line_match = compare_values(ui_line_value, db_line_value)
            # Compare drilldown values
            ui_drilldowns = ui_line_data.get("drilldown_values", {})
            db_drilldowns = db_line_data.get("drilldown_values", {})
            # Create comparison entries for each drilldown field
            for ui_drilldown_key, ui_drilldown_value in ui_drilldowns.items():
                # Try to find matching DB drilldown value
                db_drilldown_value = "Missing"
                for db_drilldown_key, db_drilldown_val in db_drilldowns.items():
                    # Match by field name (ignoring date part for flexibility)
                    ui_field = ui_drilldown_key.split(" (")[0]
                    db_field = db_drilldown_key.split(" (")[0]
                    # Handle field name variations
                    field_mappings = {
                        "Total Parts Sale": "Total Parts Sales",
                        "Effective Labor Rate": "Effective Labor Rate",
                    }
                    if ui_field == db_field or field_mappings.get(ui_field) == db_field:
                        db_drilldown_value = db_drilldown_val
                        break
                drilldown_match = compare_values(ui_drilldown_value, db_drilldown_value)
                # Overall match: both line and drilldown must match
                overall_match = line_match and drilldown_match
                # Create comparison result
                result = [
                    chart_name_with_id,
                    ui_line_key,
                    ui_line_value,
                    ui_drilldown_key.split(" (")[0],  # Drilldown field name
                    ui_drilldown_value,
                    db_line_value,
                    db_drilldown_key.split(" (")[0]
                    if db_drilldown_value != "Missing"
                    else "Not Found",
                    db_drilldown_value,
                    overall_match,
                ]
                comparison_results.append(result)
                log_info(f"comparison_results: {comparison_results}")
                # Create JSON report entry
                json_entry = {
                    "chart_name_with_id": chart_name_with_id,
                    "line_name_legend": ui_line_key,
                    "drilldown_field": ui_drilldown_key.split(" (")[0],
                    "match": overall_match,
                    "ui": {
                        "line_value": ui_line_value,
                        "drilldown_value": ui_drilldown_value,
                    },
                    "db": {
                        "line_value": db_line_value,
                        "drilldown_value": db_drilldown_value,
                    },
                }
                json_report_data.append(json_entry)
                log_info(f"json_report_data: {json_report_data}")
        
        # Save comparison results as CSV
        _output_folder, output_csv = create_folder_file_path(
            subfolder=sub_folder,
            output_file=dict_csv,            
        )
        with open(output_csv, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(
                [
                    "Chart Name with ID",
                    "Line Name (Legend)",
                    "UI Line Value",
                    "Drilldown Field",
                    "UI Drilldown Value",
                    "DB Line Value",
                    "DB Drilldown Field",
                    "DB Drilldown Value",
                    "Match (True/False)",
                ]
            )
            writer.writerows(comparison_results)
        log_info(f"CSV comparison results saved to {output_csv}")
        # Create Excel file with highlighting
        # _folder, xlsx_file = create_folder_file_path(
        #     base_folder_name="Individual_Reports",
        #     output_file="cp_overview_comparison_highlighted.xlsx",
        #     tenant_name=config.database_name,
        # )
        xlsx_file = os.path.join(_output_folder, dict_xlsx_file)
        # Convert CSV to Excel with formatting
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "CP Overview Comparison"
        # Title
        ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=9)
        ws.cell(row=1, column=1).value = "CP Overview Comparison Report"
        ws.cell(row=1, column=1).font = Font(bold=True, size=14)
        ws.cell(row=1, column=1).alignment = Alignment(
            horizontal="center", vertical="center"
        )

        # Section headers
        ui_fill = PatternFill(
            start_color="D9E1F2", end_color="D9E1F2", fill_type="solid"
        )
        db_fill = PatternFill(
            start_color="305496", end_color="305496", fill_type="solid"
        )

        ws.merge_cells(start_row=3, start_column=2, end_row=3, end_column=5)
        ui_cell = ws.cell(row=3, column=2, value="UI")
        ui_cell.font = Font(bold=True, size=12)
        ui_cell.alignment = Alignment(horizontal="center", vertical="center")
        ui_cell.fill = ui_fill
        for col in range(2, 6):
            ws.cell(row=3, column=col).fill = ui_fill

        ws.merge_cells(start_row=3, start_column=6, end_row=3, end_column=8)
        db_cell = ws.cell(row=3, column=6, value="DB")
        db_cell.font = Font(bold=True, size=12)
        db_cell.alignment = Alignment(horizontal="center", vertical="center")
        db_cell.fill = db_fill
        for col in range(6, 9):
            ws.cell(row=3, column=col).fill = db_fill
        # Headers
        headers = [
            "Chart Name with ID",
            "Line Name (Legend)",
            "UI Line Value",
            "Drilldown Field",
            "UI Drilldown Value",
            "DB Line Value",
            "DB Drilldown Field",
            "DB Drilldown Value",
            "Match (True/False)",
        ]

        for col_idx, header in enumerate(headers, start=1):
            cell = ws.cell(row=4, column=col_idx, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal="center", vertical="center")
        # Data
        for row_idx, row_data in enumerate(comparison_results, start=5):
            for col_idx, value in enumerate(row_data, start=1):
                ws.cell(row=row_idx, column=col_idx, value=value)
        # Highlight mismatches
        yellow_fill = PatternFill(
            start_color="FFFF00", end_color="FFFF00", fill_type="solid"
        )
        match_column_index = 9  # "Match (True/False)" column

        for row in ws.iter_rows(
            min_row=5,
            max_row=ws.max_row,
            min_col=match_column_index,
            max_col=match_column_index,
        ):
            for cell in row:
                if cell.value == False:  # Check if match is False
                    for cell_to_fill in ws[cell.row]:
                        cell_to_fill.fill = yellow_fill
        try:
            wb.save(xlsx_file)
            log_info(f"Excel file with highlighted mismatches saved as {xlsx_file}")
        except PermissionError:
            # Try with a timestamp suffix if file is locked
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_name = xlsx_file.rsplit(".", 1)[0]
            new_xlsx_file = f"{base_name}_{timestamp}.xlsx"
            wb.save(new_xlsx_file)
            log_info(
                f"Excel file with highlighted mismatches saved as {new_xlsx_file} (original file was locked)"
            )

        # Save JSON report
        # _folder, json_path = create_folder_file_path(
        #     base_folder_name="Individual_Reports",
        #     output_file="cp_overview_comparison.json",
        #     tenant_name=config.database_name,
        # )
        json_path = os.path.join(_output_folder, dict_json)
        with open(json_path, "w", encoding="utf-8") as jf:
            json.dump(
                {
                    "tenant": Tenant,
                    "store": store,
                    "role": role,
                    "generatedAt": datetime.now().isoformat(),
                    "results": json_report_data,
                },
                jf,
                indent=2,
            )

        log_info(f"JSON report saved to {json_path}")
        # Save HTML report
        # _folder, html_path = create_folder_file_path(
        #     base_folder_name="Individual_Reports",
        #     output_file="cp_overview_comparison.html",
        #     tenant_name=config.database_name,
        # )
        html_path = os.path.join(_output_folder, dict_html)  

        generate_ui_db_comparison_html(html_path, json_report_data, Tenant, store, role)
        log_info(f"HTML report saved to {html_path}")
        log_info("CP Overview comparison completed successfully")
        return comparison_results
    except Exception as e:
        log_error(f"Error in CP Overview comparison: {e}")
        traceback.print_exc()
        return []

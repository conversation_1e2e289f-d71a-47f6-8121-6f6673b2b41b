"""
Compares KPI dashboard values between UI and calculated results, and generates CSV, Excel, JSON, and HTML reports.
"""

import os
import json
import csv
import re
import logging
from collections import defaultdict
from datetime import datetime
from typing import Any, Dict, List, Union
import numpy as np
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from dotenv import load_dotenv

# Assuming these imports are correctly set up in your project
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import log_info,log_error,log_warn
from lib.std.universal.chart_dict import VALIDATION_CHARTS
from lib.std.universal.constants import kpi_scorecard_ignored_headers,get_kpi_placeholder

chart_key="kpi_scorecard"
dict_html = VALIDATION_CHARTS[chart_key]["html"]
dict_xlsx=VALIDATION_CHARTS[chart_key]["xlsx"]
dict_csv=VALIDATION_CHARTS[chart_key]["csv"]
dict_json=VALIDATION_CHARTS[chart_key]["json"]
sub_folder="Individual_Reports"


load_dotenv()

# Constants
# NOTE: Using getattr here for safety, though they should be present if config is loaded
Tenant = getattr(config, "database_name", "UNKNOWN_TENANT")
store = getattr(config, "store_name", "UNKNOWN_STORE")
role = getattr(config, "role", "UNKNOWN_ROLE")

# 🛑 DELETED: The 'clean_value' function has been removed because it incorrectly mapped missing values to 0.

def normalize_and_compare_single_value(value: Any) -> Any:
    """
    Cleans a single value by stripping units/commas and converting it to a
    normalized number (int if whole, otherwise float), or returns the cleaned string.
    """
    if isinstance(value, (int, float)):
        # Handle numpy NaN/inf
        if isinstance(value, float) and (np.isnan(value) or np.isinf(value)):
            return str(value).upper()
        if isinstance(value, float) and value.is_integer():
            return int(value)
        return value

    if isinstance(value, str):
        if value.strip().upper() in ["NAN", "NA", "NONE", ""]:
            return value.strip().upper()
        
        match = re.search(r'[-+]?\d*\.?\d+', value.replace(',', ''))
        
        if match:
            extracted_number_str = match.group(0)
            try:
                float_value = float(extracted_number_str)
                if float_value.is_integer():
                    return int(float_value)
                return float_value
            except ValueError:
                return value.strip()
        else:
            return value.replace('$', '').replace(',', '').replace('%', '').strip()
            
    return value

def standardize_missing(val: Any) -> Union[str, Any]:
    """
    Standardizes various null, NaN, or empty forms to the sentinel string 'MISSING'.
    """
    # Handles values already processed by normalize_and_compare_single_value
    if val in [None, "MISSING", "NaN", "NAN", ""]:
        return 'MISSING'
    if isinstance(val, str) and val.strip().upper() in ["NAN", "NA", "NONE"]:
        return 'MISSING'
    # Check for floating point NaN/Inf from numpy
    if isinstance(val, float) and (np.isnan(val) or np.isinf(val)):
        return 'MISSING'
    return val

def standardize_zero_ratio_parts(value: Any) -> Any:
    """
    Normalizes ratio strings that represent zero (e.g., '0.0 / 0.0%', '$0 / 0') 
    to a standard '0 / 0' for comparison.
    """
    if isinstance(value, str) and "/" in value:
        # Use a cleaner regex replacement here to handle all units before splitting
        parts = [p.strip() for p in re.sub(r'[,\$,%]', '', value).split('/', 1)]
        
        is_zero_ratio = True
        for p in parts:
            try:
                # Check if part is a number and is not zero (within a tolerance)
                if p and re.search(r'\d', p) and abs(float(p)) >= 1e-6:
                    is_zero_ratio = False
                    break
            except ValueError:
                # Non-numeric part, so don't treat as a zero ratio
                is_zero_ratio = False
                break

        if is_zero_ratio:
            return "0 / 0"
    return value

# NOTE: The provided extract_kpis function needs to return the *original raw string*
# of the ratio for the detailed parsing in step 2c below. Assuming your current
# implementation of extract_kpis has been adjusted to handle this, or we will
# rely on the normalized string being close enough.


def extract_kpis(d: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extracts KPI fields from a nested dictionary, ignoring section headers,
    using the robust normalization function.
    """
    kpis = {}
    ignored_headers = kpi_scorecard_ignored_headers
    for category, sub_dict in d.items():
        if category in ignored_headers:
            continue
        if isinstance(sub_dict, dict):
            for k, v in sub_dict.items():
                # 🟢 CORRECTION: Use normalize_and_compare_single_value
                kpis[k.strip()] = normalize_and_compare_single_value(v)
        else:
            # 🟢 CORRECTION: Use normalize_and_compare_single_value
            kpis[category.strip()] = normalize_and_compare_single_value(sub_dict)
    return kpis


# Helpers
def sanitize(name): return name.replace(" ", "-")


def get_output_folder(): return f"Playwright-report-{sanitize(Tenant)}_{sanitize(store)}_{sanitize(role)}"


def generate_playwright_style_html(html_path, json_report_data):
    passed = sum(1 for entry in json_report_data if entry['match'])
    failed = len(json_report_data) - passed
    total = len(json_report_data)

    # Group by section prefix
    sectioned_data = defaultdict(list)
    for entry in json_report_data:
        kpi = entry['kpi']
        if ' - ' in kpi:
            section, name = kpi.split(' - ', 1)
        else:
            section, name = 'KPI', kpi
        entry['clean_kpi'] = name
        sectioned_data[section].append(entry)

    html_template = f"""
    <!DOCTYPE html>
    <html lang=\"en\">
    <head>
        <meta charset=\"UTF-8\">
        <title>KPI Dashboard Report</title>
        <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; }}
            .badge-fail {{ background-color: #dc3545; }}
            .card-header {{ cursor: pointer; }}
        </style>
    </head>
    <body>
        <div class=\"container\">
            <h1 class=\"mb-4\">KPI Dashboard Report</h1>
            <div class=\"mb-4\">
                <strong>Tenant:</strong> {Tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
            </div>

            <div class=\"d-flex gap-3 mb-4\">
                <span class=\"badge bg-success\">Passed: {passed}</span>
                <span class=\"badge bg-danger\">Failed: {failed}</span>
                <span class=\"badge bg-secondary\">Total: {total}</span>
            </div>

            <div class=\"accordion\" id=\"reportAccordion\">
    """

    for s_idx, (section, entries) in enumerate(sectioned_data.items()):
        section_pass = all(entry['match'] for entry in entries)
        badge_class = "badge-pass" if section_pass else "badge-fail"
        badge_text = "Passed" if section_pass else "Failed"
        section_id = f"section{s_idx}"

        html_template += f"""
        <div class=\"accordion-item\">
            <h2 class=\"accordion-header\" id=\"heading-{section_id}\">
                <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#{section_id}\" aria-expanded=\"false\" aria-controls=\"{section_id}\">
                    {section} <span class=\"ms-3 badge {badge_class}\">{badge_text}</span>
                </button>
            </h2>
            <div id=\"{section_id}\" class=\"accordion-collapse collapse\" aria-labelledby=\"heading-{section_id}\" data-bs-parent=\"#reportAccordion\">
                <div class=\"accordion-body\">
        """

        for idx, entry in enumerate(entries):
            match = entry['match']
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            sub_id = f"{section_id}-entry-{idx}"
            html_template += f"""
            <div class=\"card mb-2\">
                <div class=\"card-header\" data-bs-toggle=\"collapse\" data-bs-target=\"#{sub_id}\" aria-expanded=\"false\" style=\"cursor:pointer;\">
                    {entry['clean_kpi']} <span class=\"ms-2 badge {sub_badge}\">{sub_text}</span>
                </div>
                <div id=\"{sub_id}\" class=\"collapse\">
                    <div class=\"card-body\">
                        <strong>UI:</strong>
                        <pre>{json.dumps(entry['ui'], indent=2)}</pre>
                        <strong>Calculated:</strong>
                        <pre>{json.dumps(entry['calculated'], indent=2)}</pre>
                    </div>
                </div>
            </div>
            """

        html_template += """
                </div>
            </div>
        </div>
        """

    html_template += """
            </div>
        </div>
        <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>
    </body>
    </html>
    """

    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)


def compare_dashboard_kpis(
    kpi_dashboard_file,
    result_set_file,
):
    comparison_results = []
    error_msg = None

    try:
        # --- 1. Load KPI dashboard file (UI JSON/MD converted) ---
        if not os.path.exists(kpi_dashboard_file):
            error_msg = f"❌ KPI dashboard file not found: {kpi_dashboard_file}"
            log_error(error_msg)
            data1_kpis = {}
        else:
            with open(kpi_dashboard_file, "r", encoding="utf-8") as f1:
                try:
                    file_content = f1.read()
                    json_match = re.search(r'\{[\s\S]*\}', file_content)
                    if json_match:
                        data1 = json.loads(json_match.group(0))
                    else:
                        data1 = json.loads(file_content)
                except json.JSONDecodeError:
                    error_msg = f"❌ Invalid JSON in KPI dashboard file: {kpi_dashboard_file}"
                    log_error(error_msg)
                    data1 = {}
            data1_kpis = extract_kpis(data1.get("KPI Scorecard", {})) if data1 else {}
            log_info(f"UI KPIs Extracted: {len(data1_kpis)} items") # Added log
            
        # --- 2. Load DB result set file ---
        if not os.path.exists(result_set_file):
            error_msg = f"❌ Result set file not found: {result_set_file}"
            log_error(error_msg)
            data2_kpis = {}
        else:
            with open(result_set_file, "r", encoding="utf-8") as f2:
                try:
                    data2 = json.load(f2)
                except json.JSONDecodeError:
                    error_msg = f"❌ Invalid JSON in result set file: {result_set_file}"
                    log_error(error_msg)
                    data2 = {}
            data2_combined = {}
            if isinstance(data2, list):
                for entry in data2:
                    if isinstance(entry, dict):
                        data2_combined.update(entry)
            elif isinstance(data2, dict):
                data2_combined = data2
            
            data2_kpis = extract_kpis(data2_combined) if data2_combined else {}
            log_info(f"Calculated KPIs Extracted: {len(data2_kpis)} items") # Added log


        # ----------------------------------------------------------------------
        # --- 3. Compare values (The corrected core logic) ---
        # ----------------------------------------------------------------------
        
        all_keys = set(data1_kpis.keys()) | set(data2_kpis.keys())

        if all_keys:
            for key in all_keys:
                value1_raw = data1_kpis.get(key, 'MISSING')
                value2_raw = data2_kpis.get(key, 'MISSING')
                
                # 🟢 LOGGING ADDITION 1: Show the extracted values and types before comparison
                log_info(f"--------------------------------------------------")
                log_info(f"Comparing KPI: '{key}'")
                log_info(f"  UI Value:        '{value1_raw}' ({type(value1_raw).__name__})")
                log_info(f"  Calculated Value: '{value2_raw}' ({type(value2_raw).__name__})")
                
                # Apply standardization for missing values
                val1_standard = standardize_missing(value1_raw)
                val2_standard = standardize_missing(value2_raw)

                is_match = False

                # Case 1: Simple Equality Check
                if val1_standard == val2_standard:
                    is_match = True

                # --- Dynamic Compound Key Check (Non-Hardcoded) ---
                is_compound_key = "/" in key 

                if not is_match and is_compound_key:
                    
                    # 2a: Zero-Ratio Standardization (Checks '0.0 / 0.0%' == '0 / 0')
                    value1_zero_normalized = standardize_zero_ratio_parts(str(value1_raw))
                    value2_zero_normalized = standardize_zero_ratio_parts(str(value2_raw))

                    if value1_zero_normalized == value2_zero_normalized:
                        is_match = True

                    # 2b: Missing vs. Zero-Normalized Ratio (The ONLY exception: Handles MISSING vs. "0 / 0")
                    elif (val1_standard == 'MISSING' and value2_zero_normalized == "0 / 0") or \
                         (val2_standard == 'MISSING' and value1_zero_normalized == "0 / 0"):
                        is_match = True

                    # 2c: Numerical Ratio Check (Detailed comparison for non-zero, non-missing ratios)
                    if not is_match and isinstance(value1_raw, str) and isinstance(value2_raw, str):
                        try:
                            # Use nested functions for clean parsing (defined for local scope)
                            def clean_part(p): return re.sub(r'[,\$,%]', '', p).strip()
                            def safe_float(s):
                                try:
                                    match = re.search(r'[-+]?\d*\.?\d+', s.replace(',', ''))
                                    return float(match.group(0)) if match else None
                                except ValueError: return None
                            def get_comparable(n):
                                if n is None: return n
                                return int(n) if n == round(n) else n
                            
                            num1_str, den1_str = [clean_part(p) for p in value1_raw.split('/', 1)]
                            num2_str, den2_str = [clean_part(p) for p in value2_raw.split('/', 1)]

                            num1_comp = get_comparable(safe_float(num1_str))
                            den1_comp = get_comparable(safe_float(den1_str))
                            num2_comp = get_comparable(safe_float(num2_str))
                            den2_comp = get_comparable(safe_float(den2_str))

                            if (num1_comp == num2_comp) and (den1_comp == den2_comp):
                                is_match = True

                        except (ValueError, TypeError, IndexError) as e:
                            log_warn(f"Error parsing ratio KPI '{key}' ('{value1_raw}' vs '{value2_raw}'): {e}. Mismatch assumed.")
                            is_match = False

                # All other mismatches fall through
                comparison_results.append([key, value1_raw, value2_raw, is_match])
                
                # 🟢 LOGGING ADDITION 2: Show the final result
                log_info(f"  -> FINAL RESULT: {'MATCH' if is_match else 'MISMATCH'}")
                log_info(f"--------------------------------------------------\n")

        else:
            log_warn("⚠ No KPIs extracted from one or both files.")
    except Exception as e:
        error_msg = f"❌ Unexpected error in compare_dashboard_kpis: {e}"
        logging.exception(error_msg)

    # --- 4. Report Generation (CSV, Excel, JSON, HTML) ---
    
    # --- Always write CSV ---
    folder, csv_file = create_folder_file_path(
        subfolder=sub_folder,
        output_file=dict_csv,
    )
    with open(csv_file, mode="w", newline="", encoding="utf-8") as file:
        writer = csv.writer(file)
        writer.writerow(["Field Name", "UI Value", "Calculated Value", "Match (True/False)"])
        writer.writerows(comparison_results)
    log_info(f"Comparison results saved to {csv_file}")

    # --- Excel output (with full styling) ---
    folder, xlsx_file = create_folder_file_path(
        subfolder=sub_folder,
        output_file=dict_xlsx
    )
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Comparison Results"

    # Title
    ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=4)
    ws.cell(row=1, column=1).value = "KPI Dashboard"
    ws.cell(row=1, column=1).font = Font(bold=True, size=14)
    ws.cell(row=1, column=1).alignment = Alignment(horizontal="center", vertical="center")

    # Background fills
    ui_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    calc_fill = PatternFill(start_color="305496", end_color="305496", fill_type="solid")

    # UI header
    ws.merge_cells(start_row=3, start_column=2, end_row=3, end_column=2)
    ui_cell = ws.cell(row=3, column=2, value="UI")
    ui_cell.font = Font(bold=True, size=12)
    ui_cell.alignment = Alignment(horizontal="center", vertical="center")
    ui_cell.fill = ui_fill
    for col in range(2, 3):
        ws.cell(row=3, column=col).fill = ui_fill

    # Calculated header
    ws.merge_cells(start_row=3, start_column=3, end_row=3, end_column=3)
    calc_cell = ws.cell(row=3, column=3, value="Calculated")
    calc_cell.font = Font(bold=True, size=12)
    calc_cell.alignment = Alignment(horizontal="center", vertical="center")
    calc_cell.fill = calc_fill
    for col in range(3, 4):
        ws.cell(row=3, column=col).fill = calc_fill

    # Load CSV to write into Excel
    with open(csv_file, "r", encoding="utf-8") as file:
        reader = csv.reader(file)
        headers = next(reader)

        # Write headers on row 4
        for col_idx, header in enumerate(headers, start=1):
            cell = ws.cell(row=4, column=col_idx, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal="center", vertical="center")

        # Write data starting row 5
        for row_idx, row in enumerate(reader, start=5):
            ws.append(row)

    # Highlight mismatches
    yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
    for row in ws.iter_rows(min_row=5, max_row=ws.max_row, min_col=4, max_col=4):
        for cell in row:
            if str(cell.value) == "False":
                for cell_to_fill in ws[cell.row]:
                    cell_to_fill.fill = yellow_fill

    wb.save(xlsx_file)
    log_info(f"Excel file with highlighted mismatches saved as {xlsx_file}")

    # --- JSON report ---
    folder, json_path = create_folder_file_path(
        subfolder=sub_folder,
        output_file=dict_json, 
    )
    json_results = []
    for row in comparison_results:
        # Convert non-serializable objects (like numpy types) to string before JSON dump
        ui_val = str(row[1]) if not isinstance(row[1], (str, int, float, bool, type(None))) else row[1]
        calc_val = str(row[2]) if not isinstance(row[2], (str, int, float, bool, type(None))) else row[2]
        
        json_results.append({
            "kpi": row[0],
            "ui": {"Value": ui_val},
            "calculated": {"Value": calc_val},
            "match": row[3]
        })
        
    with open(json_path, "w") as jf:
        json.dump({
            "tenant": Tenant,
            "store": store,
            "role": role,
            "generatedAt": datetime.now().isoformat(),
            "error": error_msg,
            "results": json_results
        }, jf, indent=2)
    log_info(f"✔ JSON report saved to {json_path}")

    # --- HTML report ---
    folder, html_path = create_folder_file_path(
        subfolder=sub_folder,
        output_file=dict_html, 
    )
    html_report_data = []
    for row in comparison_results:
        kpi = row[0]
        match = row[-1]
        html_report_data.append({
            "kpi": kpi,
            "match": match,
            "ui": {"Value": str(row[1])},
            "calculated": {"Value": str(row[2])}
        })
    generate_playwright_style_html(html_path, html_report_data) 


    log_info(f"✔ Report generated in: {html_path}")

    # Logs
    log_info(f"✔ JSON report saved to {json_path}")
    log_info(f"✔ HTML report saved to {html_path}")
import asyncio
import json
import os
import time
import glob
import math
import traceback
import pandas as pd
import numpy as np
from datetime import datetime
from math import isfinite, isnan
from playwright.async_api import async_playwright
from concurrent.futures import ThreadPoolExecutor
import sys
sys.path.append('../')
import pandas as pd
import numpy as np
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime
import threading
from datetime import datetime
from collections import defaultdict   
import re
from decimal import Decimal, ROUND_HALF_UP
import openpyxl
import openpyxl.cell
from openpyxl.styles import PatternFill, Alignment, Font
from openpyxl.utils import get_column_letter
import csv
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.pattern.config import config
from lib.pattern.qa_auto_core.compare_13_month_trend import compare_discount_metrics_results
from dateutil.relativedelta import relativedelta
from datetime import timedelta
from lib.pattern.qa_auto_core.db_handler.db_connector import (
    getCustomerPayTypeGroupsList, menuMasterTableResult, menuServiceTypeTableResult,
    assignedMenuModelsTableResult, assignedMenuOpcodesTableResult, MPISetupTableResult,
    MPIOpcodesTableResult, allRevenueDetailsCPOverview, allRevenueDetailsForShopSuppliesTable,
    paytypeRetailFlagSettingTable, shopSuppliesDataTable,allRevenueDetailsTable,DiscountPartsDetails,DiscountLaborDetails
)

from lib.std.universal.authmanager import AuthManager
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.std.universal.chart_dict import VALIDATION_CHARTS
chart_key="discount_metrics_13_month_trend"
chart_process_json = VALIDATION_CHARTS[chart_key]["chart_process_json"]
db_json = VALIDATION_CHARTS[chart_key]["db_json"]

#  Target months-years for drilling down (modify as needed)
TARGET_MONTHS_YEARS = config.target_month_year
# Configuration constants
MAX_CONCURRENT_BROWSERS = 3
BROWSER_TIMEOUT = 30000

namespace = {
    'ns': 'http://www.dmotorworks.com/service-repair-order-history'
}

# def round_off(n, decimals=0):
#     """Round off numbers with proper decimal handling"""
#     multiplier = Decimal(10) ** decimals
#     if isinstance(n, float):
#         n = Decimal(str(n))
#     return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

# def zero_sales_check(df, columns):
#     """Function used for checking zero sales"""
#     total_sum = df[columns].sum().sum()
#     return total_sum == 0

# def get_month_date_range_from_target(target_date_str):
#     """Get the start and end date for the target month"""
#     # Parse the target date string
#     target_date = datetime.strptime(target_date_str, "%Y-%m-%d")
#     # Get first day of the target month
#     month_start = target_date.replace(day=1)
#     # Get last day of the target month
#     month_end = month_start + relativedelta(months=1) - timedelta(days=1)
#     return month_start, month_end

# def get_database_tables():
#     """Fetch all required database tables"""
#     log_info("Fetching database tables...")

#     # Get retail flag
#     retail_flag_DB_connect = getCustomerPayTypeGroupsList()
#     retail_flag_df = retail_flag_DB_connect.getCustomerPayTypeList()
#     retail_flag = set(retail_flag_df['source_paytype'].tolist()) if not retail_flag_df.empty else {'C'}
#     log_info(f"Retail flag from database: {retail_flag}")

#     # Get menu master table
#     menu_master_db_connect = menuMasterTableResult()
#     menu_master_df = menu_master_db_connect.getTableResult()

#     # Get menu service type table
#     menu_service_type_db_connect = menuServiceTypeTableResult()
#     menu_service_type_df = menu_service_type_db_connect.getTableResult()

#     # Get assigned menu models table
#     assigned_menu_models_db_connect = assignedMenuModelsTableResult()
#     assigned_menu_models_df = assigned_menu_models_db_connect.getTableResult()

#     # Get assigned menu opcodes table
#     assigned_menu_opcodes_db_connect = assignedMenuOpcodesTableResult()
#     assigned_menu_opcodes_df = assigned_menu_opcodes_db_connect.getTableResult()

#     # Get MPI setup table
#     MPI_setup_db_connect = MPISetupTableResult()
#     MPI_setup_df = MPI_setup_db_connect.getTableResult()

#     # Get MPI opcodes table
#     MPI_opcodes_db_connect = MPIOpcodesTableResult()
#     mpi_opcodes = MPI_opcodes_db_connect.getTableResult()

#     return {
#         'retail_flag': retail_flag,
#         'menu_master_df': menu_master_df,
#         'menu_service_type_df': menu_service_type_df,
#         'assigned_menu_models_df': assigned_menu_models_df,
#         'assigned_menu_opcodes_df': assigned_menu_opcodes_df,
#         'MPI_setup_df': MPI_setup_df,
#         'mpi_opcodes': mpi_opcodes
#     }

# def process_advisor_tech_filters(advisor_set, tech_set):
#     """Process advisor and technician filter configurations"""
#     # Process advisor configuration
#     if isinstance(advisor_set, str):
#         if advisor_set.lower() == 'all':
#             advisor = {'all'}
#         elif ',' in advisor_set:
#             advisor = {x.strip() for x in advisor_set.split(',')}
#         else:
#             advisor = {advisor_set.strip()}
#     else:
#         advisor = {'all'}

#     # Process technician configuration
#     if isinstance(tech_set, str):
#         if tech_set.lower() == 'all':
#             tech = {'all'}
#         elif ',' in tech_set:
#             tech = {x.strip() for x in tech_set.split(',')}
#         else:
#             tech = {tech_set.strip()}
#     else:
#         tech = {'all'}

#     return advisor, tech

# def round_off(n, decimals=0):
#     """Round off numbers with proper decimal handling"""
#     multiplier = Decimal(10) ** decimals
#     if isinstance(n, float):
#         n = Decimal(str(n))
#     return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

# def zero_sales_check(df, columns):
#     """Function used for checking zero sales"""
#     total_sum = df[columns].sum().sum()
#     return total_sum == 0

# def convert_and_sum(df, column_name):
#     """Convert column to numeric and return sum"""
#     return pd.to_numeric(df[column_name], errors='coerce').fillna(0).sum()

# def convert_and_sum_labor_discount(df, column_name):
#     """Convert labor discount column to numeric and return sum"""
#     return pd.to_numeric(df[column_name], errors='coerce').fillna(0).sum()

# def convert_and_sum_parts_discount(df, column_name):
#     """Convert parts discount column to numeric and return sum"""
#     return pd.to_numeric(df[column_name], errors='coerce').fillna(0).sum()

# def get_discount_database_tables():
#     """Fetch discount-related database tables"""
#     log_info("Fetching discount database tables...")

#     # Get retail flag
#     retail_flag_DB_connect = getCustomerPayTypeGroupsList()
#     retail_flag_df = retail_flag_DB_connect.getCustomerPayTypeList()
#     retail_flag = set(retail_flag_df['source_paytype'].tolist()) if not retail_flag_df.empty else {'C'}
#     log_info(f"Retail flag from database: {retail_flag}")

#     # Get labor discount details
#     labor_discount_table_db_connect = DiscountLaborDetails()
#     labor_discount_details_df = labor_discount_table_db_connect.getTableResult()
#     log_info(f"Labor discount records retrieved: {len(labor_discount_details_df)}")

#     # Get parts discount details
#     parts_discount_table_db_connect = DiscountPartsDetails()
#     parts_discount_details_df = parts_discount_table_db_connect.getTableResult()
#     log_info(f"Parts discount records retrieved: {len(parts_discount_details_df)}")

#     return {
#         'retail_flag': retail_flag,
#         'labor_discount_details_df': labor_discount_details_df,
#         'parts_discount_details_df': parts_discount_details_df
#     }

# def prepare_revenue_data_for_discounts(all_revenue_details_df, start_date, end_date):
#     """Prepare and filter revenue data for discount calculations"""
#     log_info("Preparing revenue data for discount calculations...")

#     # Filter by date range
#     filtered_df = all_revenue_details_df[
#         (all_revenue_details_df['closeddate'] >= start_date) &
#         (all_revenue_details_df['closeddate'] <= end_date)
#     ]

#     # Filter by department and category
#     filtered_df = filtered_df[
#         (filtered_df['department'] == 'Service') & 
#         (filtered_df['opcategory'].isin(['REPAIR','COMPETITIVE','MAINTENANCE'])) &
#         (filtered_df['hide_ro'].astype(int) != 1)
#     ]

#     # Convert numeric columns
#     columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
#     filtered_df = filtered_df.copy()
#     filtered_df[columns_to_convert] = filtered_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)
#     filtered_df[columns_to_convert] = filtered_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))

#     # Create unique RO number
#     filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)

#     log_info(f"Filtered revenue records: {len(filtered_df)}")
#     return filtered_df

# def filter_customer_pay_data_for_discounts(revenue_df, customer_pay_types):
#     """Filter revenue data for customer pay types"""
#     log_info("Filtering customer pay data for discounts...")

#     total_CP_revenue_details_df = revenue_df[revenue_df['paytypegroup'].isin(customer_pay_types)]
#     total_CP_revenue_details_df = total_CP_revenue_details_df.copy()

#     # Convert numeric columns
#     columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
#     total_CP_revenue_details_df[columns_to_convert] = total_CP_revenue_details_df[columns_to_convert].apply(pd.to_numeric, errors='coerce').fillna(0)

#     # Remove zero sales rows
#     total_CP_revenue_details_df = total_CP_revenue_details_df[
#         ~((total_CP_revenue_details_df['lbrsale'].fillna(0) == 0) &
#           (total_CP_revenue_details_df['lbrsoldhours'].fillna(0) == 0) &
#           (total_CP_revenue_details_df['prtextendedsale'].fillna(0) == 0) &
#           (total_CP_revenue_details_df['prtextendedcost'].fillna(0) == 0))
#     ]

#     log_info(f"Customer pay records after filtering: {len(total_CP_revenue_details_df)}")
#     return total_CP_revenue_details_df

# def apply_advisor_tech_filters_for_discounts(total_CP_revenue_details_df, advisor, tech, columns_to_check):
#     """Apply advisor and technician filters to discount data"""
#     log_info("Applying advisor and tech filters for discount calculations...")

#     if advisor == {'all'} and tech == {'all'}:
#         matching_ro_numbers = total_CP_revenue_details_df['unique_ro_number'].unique()
#     elif advisor != {'all'} and tech == {'all'}:
#         matching_ro_numbers = total_CP_revenue_details_df.loc[
#             total_CP_revenue_details_df['serviceadvisor'].astype(str).isin(advisor), 
#             'unique_ro_number'
#         ].unique()
#     elif advisor == {'all'} and tech != {'all'}:
#         matching_ro_numbers = total_CP_revenue_details_df.loc[
#             total_CP_revenue_details_df['lbrtechno'].astype(str).isin(tech), 
#             'unique_ro_number'
#         ].unique()
#     elif advisor != {'all'} and tech != {'all'}:
#         matching_ro_numbers = total_CP_revenue_details_df.loc[
#             (total_CP_revenue_details_df['serviceadvisor'].astype(str).isin(advisor)) & 
#             (total_CP_revenue_details_df['lbrtechno'].astype(str).isin(tech)), 
#             'unique_ro_number'
#         ].unique()

#     # Apply filter
#     filtered_df = total_CP_revenue_details_df[total_CP_revenue_details_df['unique_ro_number'].isin(matching_ro_numbers)]
#     filtered_df = filtered_df.reset_index(drop=True)
#     filtered_df.loc[filtered_df['opcategory'] == 'N/A', columns_to_check] = 0

#     log_info(f"Records after advisor/tech filter: {len(filtered_df)}")
#     return filtered_df

# def prepare_discount_data(labor_discount_details_df, parts_discount_details_df, month_start, month_end):
#     """Prepare labor and parts discount data with date filtering"""
#     log_info("Preparing discount data with date filtering...")

#     columns_to_convert_labor_discount = ['apportionedlbrdiscount','lbrsale','lbrcost']
#     columns_to_convert_parts_discount = ['apportionedlbrdiscount','prtssale','prtcost']

#     # Prepare labor discount data
#     labor_discount_details_df = labor_discount_details_df.copy()
    
#     # Convert closeddate to datetime if not already
#     if 'closeddate' in labor_discount_details_df.columns:
#         labor_discount_details_df['closeddate'] = pd.to_datetime(labor_discount_details_df['closeddate'], errors='coerce')
        
#         # Filter by date range
#         labor_discount_details_df = labor_discount_details_df[
#             (labor_discount_details_df['closeddate'] >= month_start) &
#             (labor_discount_details_df['closeddate'] <= month_end)
#         ]
#         log_info(f"Labor discount records after date filtering: {len(labor_discount_details_df)}")
    
#     labor_discount_details_df[columns_to_convert_labor_discount] = labor_discount_details_df[columns_to_convert_labor_discount].apply(pd.to_numeric, errors='coerce').fillna(0)

#     # Prepare parts discount data
#     parts_discount_details_df = parts_discount_details_df.copy()
    
#     # Convert closeddate to datetime if not already
#     if 'closeddate' in parts_discount_details_df.columns:
#         parts_discount_details_df['closeddate'] = pd.to_datetime(parts_discount_details_df['closeddate'], errors='coerce')
        
#         # Filter by date range
#         parts_discount_details_df = parts_discount_details_df[
#             (parts_discount_details_df['closeddate'] >= month_start) &
#             (parts_discount_details_df['closeddate'] <= month_end)
#         ]
#         log_info(f"Parts discount records after date filtering: {len(parts_discount_details_df)}")
    
#     parts_discount_details_df[columns_to_convert_parts_discount] = parts_discount_details_df[columns_to_convert_parts_discount].apply(pd.to_numeric, errors='coerce').fillna(0)

#     return labor_discount_details_df, parts_discount_details_df

# def calculate_discount_metrics(total_CP_revenue_details_df, labor_discount_details_df, parts_discount_details_df, customer_pay_types):
#     """Calculate all discount metrics"""
#     log_info("Calculating discount metrics...")

 
#     # Create a composite key for matching in labor_discount_details_df
#     # Matching on: ronumber, closeddate, and lbrsequenceno
#     labor_discount_details_df = labor_discount_details_df.copy()
#     labor_discount_details_df['match_key'] = (
#         labor_discount_details_df['ronumber'].astype(str) + '_' + 
#         labor_discount_details_df['closeddate'].astype(str) + '_' + 
#         labor_discount_details_df['lbrsequenceno'].astype(str)
#     )
    
#     # Create the same composite key in total_CP_revenue_details_df
#     total_CP_revenue_details_df = total_CP_revenue_details_df.copy()
#     total_CP_revenue_details_df['match_key'] = (
#         total_CP_revenue_details_df['ronumber'].astype(str) + '_' + 
#         total_CP_revenue_details_df['closeddate'].astype(str) + '_' + 
#         total_CP_revenue_details_df['lbrsequenceno'].astype(str)
#     )
    
#     # Get unique match keys from labor_discount_details_df
#     discount_match_keys = labor_discount_details_df['match_key'].unique()
    
#     # Filter total_CP_revenue_details_df for matching records and sum lbrsale
#     # This represents "Labor Sale - Customer Pay" for discounted ROs
#     labor_sale_customer_pay_discounted = labor_discount_details_df[
#         labor_discount_details_df['match_key'].isin(discount_match_keys)
#     ]['lbrsale'].sum()
#     # Revenue calculations (original logic)
#     Labor_Sales = convert_and_sum(total_CP_revenue_details_df, 'lbrsale')
    
#     competitive_df = total_CP_revenue_details_df[
#         (total_CP_revenue_details_df['paytypegroup'].isin(customer_pay_types))
#     ]

#     all_revenue_C = total_CP_revenue_details_df[total_CP_revenue_details_df['paytypegroup'] == 'C']
    
#     Labor_Cost = convert_and_sum(total_CP_revenue_details_df, 'lbrcost') 
#     Total_Hours = convert_and_sum(total_CP_revenue_details_df, 'lbrsoldhours')
#     Parts_Sales = convert_and_sum(total_CP_revenue_details_df, 'prtextendedsale')
#     Parts_Cost = convert_and_sum(total_CP_revenue_details_df, 'prtextendedcost')
#     overall_RO_Count = total_CP_revenue_details_df['unique_ro_number'].nunique()
#     total_Sales = Labor_Sales + Parts_Sales

#     # Labor discount calculations
#     apportioned_Lbr_Discount = convert_and_sum_labor_discount(labor_discount_details_df, 'apportionedlbrdiscount')
#     discount_Sale_Labor = convert_and_sum_labor_discount(labor_discount_details_df, 'lbrsale')
#     discount_Cost_Labor = convert_and_sum_labor_discount(labor_discount_details_df, 'lbrcost')
#     discounted_RO_Count_Labor = labor_discount_details_df['ronumber'].nunique()

#     # Parts discount calculations
#     apportioned_Parts_Discount = convert_and_sum_parts_discount(parts_discount_details_df, 'apportionedlbrdiscount')
#     discount_Sale_Parts = convert_and_sum_parts_discount(parts_discount_details_df, 'prtssale')
#     discount_Cost_Parts = convert_and_sum_parts_discount(parts_discount_details_df, 'prtcost')
#     discounted_RO_Count_Parts = parts_discount_details_df['ronumber'].nunique()

#     # Total discount calculations
#     total_Discount = apportioned_Lbr_Discount + apportioned_Parts_Discount
#     total_Discount_Sales = discount_Sale_Labor + discount_Sale_Parts

#     # Discount by level
#     discounted_RO_Count_RO_Level = labor_discount_details_df[labor_discount_details_df['dislevel'] == "RO"]['ronumber'].nunique()
#     discounted_RO_Count_LINE_Level = labor_discount_details_df[labor_discount_details_df['dislevel'] == "LINE"]['ronumber'].nunique()
#     discounted_RO_Count_LOP_Level = labor_discount_details_df[labor_discount_details_df['dislevel'] == "LOP"]['ronumber'].nunique()

#     # CP Discounted RO % (1123)
#     cp_Discounted_RO_percent = (discounted_RO_Count_Labor / overall_RO_Count) * 100 if overall_RO_Count != 0 else 0

#     # CP % Disc of Total $ Sold (1115)
#     total_Sale_percent = (total_Discount * 100 / total_Sales) if total_Sales != 0 else 0
#     labor_Sale_percent = (apportioned_Lbr_Discount * 100 / Labor_Sales) if Labor_Sales != 0 else 0
#     parts_Sale_percent = (apportioned_Parts_Discount * 100 / Parts_Sales) if Parts_Sales != 0 else 0

#     # CP % Disc Per Discounted CP ROs (1232)
#     if total_Discount_Sales != 0:
#         cp_percent_Disc_Per_Discounted_CP_ROs = (total_Discount * 100 / total_Discount_Sales)
#         cp_percent_Disc_Per_Discounted_CP_ROs_labor = (apportioned_Lbr_Discount * 100 / total_Discount_Sales) 
#         cp_percent_Disc_Per_Discounted_CP_ROs_parts = (apportioned_Parts_Discount * 100 / total_Discount_Sales)
#     else:
#         cp_percent_Disc_Per_Discounted_CP_ROs = 0
#         cp_percent_Disc_Per_Discounted_CP_ROs_labor = 0
#         cp_percent_Disc_Per_Discounted_CP_ROs_parts = 0

#     # Discounts Per Total CP ROs (1236)
#     if overall_RO_Count != 0:
#         labor_Discounts_Per_Total_CP_ROs = apportioned_Lbr_Discount / overall_RO_Count
#         parts_Discounts_Per_Total_CP_ROs = apportioned_Parts_Discount / overall_RO_Count
#         total_Discounts_Per_Total_CP_ROs = total_Discount / overall_RO_Count
#     else:
#         labor_Discounts_Per_Total_CP_ROs = 0
#         parts_Discounts_Per_Total_CP_ROs = 0
#         total_Discounts_Per_Total_CP_ROs = 0

#     # CP Total Disc $ Avg of Disc ROs (1165)
#     labor_CP_Total_Disc_Avg_Disc_ROs = apportioned_Lbr_Discount / discounted_RO_Count_Labor if discounted_RO_Count_Labor != 0 else 0
#     parts_CP_Total_Disc_Avg_Disc_ROs = apportioned_Parts_Discount / discounted_RO_Count_Parts if discounted_RO_Count_Parts != 0 else 0
#     total_CP_Total_Disc_Avg_Disc_ROs = labor_CP_Total_Disc_Avg_Disc_ROs + parts_CP_Total_Disc_Avg_Disc_ROs

#     # Drilldown results structure
#     discount_drilldown_results = {
#         "1234": {  # CP Discounts - Labor & Parts
#             "Total Labor Discount": {
#                 "Total Labor Discount": round_off(apportioned_Lbr_Discount, 2)
#             },
#             "Total Parts Discount": {
#                 "Total Parts Discount": round_off(apportioned_Parts_Discount, 2)
#             },
#             "Total Discount": {
#                 "Total Discount": round_off(total_Discount, 2)
#             }
#         },
#         "1113": {  # CP RO Count for Disc by Disc Level
#             "RO": {
#                 "RO Count": discounted_RO_Count_RO_Level,
#                 "RO - Discounts": discounted_RO_Count_RO_Level,
#                 "Labor Sale - Customer Pay": round_off(labor_sale_customer_pay_discounted, 2)
#             },
#             "Line": {
#                 "RO Count": discounted_RO_Count_LINE_Level,
#                 "Line - Discounts": discounted_RO_Count_LINE_Level
#             },
#             "LOP": {
#                 "Line - Discounts": discounted_RO_Count_LOP_Level
#             }
#         },
#         "1123": {  # CP Discounted RO %
#             "CP Discounted RO %": {
#                 "Overall RO Count": overall_RO_Count,
#                 "Discounted RO Count": discounted_RO_Count_Labor,
#                 "Discounted RO %": round_off(cp_Discounted_RO_percent, 2)
#             }
#         },
#         "1115": {  # CP % Disc of Total $ Sold
#             "Total": {
#                 "Overall Discount Sale %": round_off(total_Sale_percent, 2)
#             },
#         },
#         "1232": {  # CP % Disc Per Discounted CP ROs
#             "Total": {
#                 "Overall Discount %": round_off(cp_percent_Disc_Per_Discounted_CP_ROs, 2)
#             },
#         },
#         "1236": {  # Discounts Per Total CP ROs
#             "Labor Discount": {
#                "Labor Sale - Customer Pay": round_off(labor_sale_customer_pay_discounted, 2),
#                 "Overall RO Count": overall_RO_Count,
#                 "Total Labor Discount": round_off(apportioned_Lbr_Discount, 2),
#                 "$Discounted per Total CP ROs": round_off(labor_Discounts_Per_Total_CP_ROs, 2)
#             },
#             "Parts Discount": {
#                 "Overall RO Count": overall_RO_Count,
#                 "Total Parts Discount": round_off(apportioned_Parts_Discount, 2),
#                 "$Discounted per Total CP ROs": round_off(parts_Discounts_Per_Total_CP_ROs, 2)
#             },
#             "Total Discount": {
#                 "$Discounted per Total CP ROs": round_off(total_Discounts_Per_Total_CP_ROs, 2)
#             }
#         },
#         "1165": {  # CP Total Disc $ Avg of Disc ROs
#             "Total": {
#                 "Overall Discount per CP RO": round_off(total_CP_Total_Disc_Avg_Disc_ROs, 2)
#             },
#         }
#     }

#     return {
#         # CP Discounts - Labor & Parts (1234)
#         'total_labor_discount': round_off(apportioned_Lbr_Discount, 2),
#         'total_parts_discount': round_off(apportioned_Parts_Discount, 2),
#         'total_discount': round_off(total_Discount, 2),
        
#         # CP RO Count for Disc by Disc Level (1113)
#         'discounted_ro_count_ro_level': discounted_RO_Count_RO_Level,
#         'discounted_ro_count_line_level': discounted_RO_Count_LINE_Level,
#         'discounted_ro_count_lop_level': discounted_RO_Count_LOP_Level,
        
#         # CP Discounted RO % (1123)
#         'cp_discounted_ro_percent': round_off(cp_Discounted_RO_percent, 2),
        
#         # CP % Disc of Total $ Sold (1115)
#         'total_sale_percent': round_off(total_Sale_percent, 2),
        
#         # CP % Disc Per Discounted CP ROs (1232)
#         'cp_percent_disc_per_discounted_cp_ros': round_off(cp_percent_Disc_Per_Discounted_CP_ROs, 2),
        
#         # Discounts Per Total CP ROs (1236)
#         'labor_sale_customer_pay_discounted': round_off(labor_sale_customer_pay_discounted, 2),
#         'labor_discounts_per_total_cp_ros': round_off(labor_Discounts_Per_Total_CP_ROs, 2),
#         'parts_discounts_per_total_cp_ros': round_off(parts_Discounts_Per_Total_CP_ROs, 2),
#         'total_discounts_per_total_cp_ros': round_off(total_Discounts_Per_Total_CP_ROs, 2),
        
#         # CP Total Disc $ Avg of Disc ROs (1165)
#         'total_cp_total_disc_avg_disc_ros': round_off(total_CP_Total_Disc_Avg_Disc_ROs, 2),
#     }, discount_drilldown_results
# def define_customer_warranty_pay_types_for_discounts(retail_flag):
#     """Define customer and warranty pay types dynamically based on retail flag for discount calculations"""
#     log_info(f"Defining pay types for discount calculations with retail_flag: {retail_flag}")

#     if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
#         customer_pay_types = {'C'}
#         warranty_pay_types = {'W', 'F', 'M', 'E'}
#     elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
#         customer_pay_types = {'C', 'M'}
#         warranty_pay_types = {'W', 'F', 'E'}
#     elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
#         customer_pay_types = {'C', 'E'}
#         warranty_pay_types = {'W', 'F', 'M'}
#     elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
#         customer_pay_types = {'C', 'E', 'M'}
#         warranty_pay_types = {'W', 'F'}
#     else:
#         customer_pay_types = {'C'}
#         warranty_pay_types = {'W', 'F', 'M', 'E'}

#     log_info(f"Customer pay types: {customer_pay_types}")
#     log_info(f"Warranty pay types: {warranty_pay_types}")

#     return customer_pay_types, warranty_pay_types

# def get_month_date_range_from_target_for_discounts(target_date_str):
#     """Get the start and end date for the target month for discount calculations"""
#     from dateutil.relativedelta import relativedelta
#     from datetime import timedelta
    
#     # Parse the target date string
#     target_date = datetime.strptime(target_date_str, "%Y-%m-%d")
#     # Get first day of the target month
#     month_start = target_date.replace(day=1)
#     # Get last day of the target month
#     month_end = month_start + relativedelta(months=1) - timedelta(days=1)
#     return month_start, month_end

# def process_discount_metrics(all_revenue_details_df, month_start, month_end, advisor, tech, customer_pay_types, discount_db_tables):
#     """Process discount metrics for a specific month"""
#     log_info(f"Processing discount metrics for {month_start.strftime('%Y-%m')}")
    
#     columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
    
#     # Prepare revenue data
#     revenue_df = prepare_revenue_data_for_discounts(all_revenue_details_df, month_start, month_end)
    
#     if revenue_df.empty:
#         log_info("No revenue data found for the period")
#         return None
    
#     # Filter customer pay data
#     cp_revenue_df = filter_customer_pay_data_for_discounts(revenue_df, customer_pay_types)
    
#     if cp_revenue_df.empty:
#         log_info("No customer pay data found for the period")
#         return None
#     # Apply advisor/tech filters
#     filtered_cp_df = apply_advisor_tech_filters_for_discounts(cp_revenue_df, advisor, tech, columns_to_check)
    
#     # Prepare discount data WITH DATE FILTERING
#     labor_discount_df, parts_discount_df = prepare_discount_data(
#         discount_db_tables['labor_discount_details_df'],
#         discount_db_tables['parts_discount_details_df'],
#         month_start,  # ✅ Pass month_start
#         month_end     # ✅ Pass month_end
#     )
    
#     # Calculate metrics
#     discount_metrics, discount_drilldown = calculate_discount_metrics(
#         filtered_cp_df, 
#         labor_discount_df, 
#         parts_discount_df,customer_pay_types
#     )
    
#     return {
#         "month": month_start.strftime('%Y-%m'),
#         "month_name": month_start.strftime('%B %Y'),
#         "discount_metrics": discount_metrics,
#         "drilldown_results": discount_drilldown
#     }
# def discount_db_execution(target_date_str, advisor, tech, columns_to_check):
#     """
#     Handle database operations and execute discount metrics processing
#     """
#     try:
#         # Get target month date range
#         month_start, month_end = get_month_date_range_from_target_for_discounts(target_date_str)
#         log_info(f"Target month range for discounts: {month_start.date()} to {month_end.date()}")

#         # Fetch all data from database
#         log_info("Fetching discount data from database...")
        
#         all_revenue_details_table_db_connect = allRevenueDetailsTable()
#         all_revenue_details_df = all_revenue_details_table_db_connect.getTableResult()
        
#         if all_revenue_details_df.empty:
#             log_error("ERROR: No data retrieved from database for discounts!")
#             return None, None

#         # Convert date column
#         all_revenue_details_df['closeddate'] = pd.to_datetime(all_revenue_details_df['closeddate'], errors='coerce')

#         # Get discount database tables
#         discount_db_tables = get_discount_database_tables()

#         # Define customer and warranty pay types based on retail_flag
#         customer_pay_types, warranty_pay_types = define_customer_warranty_pay_types_for_discounts(
#             discount_db_tables['retail_flag']
#         )

#         # Process discount metrics for target month
#         target_month_result = process_discount_metrics(
#             all_revenue_details_df,
#             month_start,
#             month_end,
#             advisor,
#             tech,
#             customer_pay_types,
#             discount_db_tables
#         )
        
#         return target_month_result, customer_pay_types
        
#     except Exception as e:
#         log_error(f"ERROR in discount_db_execution: {str(e)}")
#         log_error("=" * 60)
#         log_error("DISCOUNT DATABASE EXECUTION FAILED")
#         log_error("=" * 60)
#         return None, None

# def db_calculation():
#     """
#     Main execution function for discount metrics calculation
#     """
#     # Configuration variables
#     columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']

#     storeid = config.store_id
#     realm = config.database_name
#     advisor_set = config.advisor
#     tech_set = config.technician

#     # Process advisor and technician configurations
#     advisor, tech = process_advisor_tech_filters(advisor_set, tech_set)

#     if advisor != {'all'}:
#         advisor_id = next(iter(advisor))
#     else:
#         advisor_id = 'all'

#     log_info("\n" + "=" * 80)
#     log_info("DISCOUNT METRICS ANALYSIS - STARTING")
#     log_info("=" * 80)

#     # Execute database operations and processing
#     if not TARGET_MONTHS_YEARS or len(TARGET_MONTHS_YEARS) == 0:
#         log_error("No target month specified for discount metrics!")
#         return
    
#     target_date_str = TARGET_MONTHS_YEARS[0]
#     target_month_result, customer_pay_types = discount_db_execution(
#         target_date_str, advisor, tech, columns_to_check
#     )

#     # Process results
#     if target_month_result:
#         log_info("\n" + "=" * 80)
#         log_info("DISCOUNT METRICS RESULTS PROCESSING")
#         log_info("=" * 80)

#         # Create the final result set for the target month only
#         final_result_set = {
#             "analysis_info": {
#                 "target_month": target_date_str,
#                 "analysis_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
#                 "store_id": storeid,
#                 "realm": realm,
#                 "advisor_filter": list(advisor),
#                 "technician_filter": list(tech),
#                 "customer_pay_types": list(customer_pay_types) if customer_pay_types else []
#             },
#             "target_month_results": target_month_result
#         }

#         # Write results to JSON file
#         result_dir,output_filename = create_folder_file_path(subfolder="chart_processing_results", output_file=db_json)  
#         with open(output_filename, 'w', encoding='utf-8') as json_file:
#             json.dump(final_result_set, json_file, indent=4, ensure_ascii=False)

#         log_info(f"\nTarget month 13 month tredn data written successfully to {output_filename}")  
        

#         # Log key metrics
#         log_info("\n" + "=" * 80)
#         log_info("KEY DISCOUNT METRICS")
#         log_info("=" * 80)
#         metrics = target_month_result['discount_metrics']
#         log_info(f"Total Labor Discount: ${metrics['total_labor_discount']}")
#         log_info(f"Total Parts Discount: ${metrics['total_parts_discount']}")
#         log_info(f"Total Discount: ${metrics['total_discount']}")
#         log_info(f"CP Discounted RO %: {metrics['cp_discounted_ro_percent']}%")
#         log_info(f"Total Sale Discount %: {metrics['total_sale_percent']}%")

#     else:
#         log_info("\n" + "=" * 80)
#         log_info("NO DISCOUNT DATA RESULTS PROCESSING")
#         log_info("=" * 80)
#         log_info(f"No discount data available for target month {target_date_str}")

#     log_info("\n" + "=" * 80)
#     log_info("DISCOUNT METRICS ANALYSIS - MAIN EXECUTION COMPLETED")
#     log_info("=" * 80)
def round_off(n, decimals=0):
    """Round off numbers with proper decimal handling"""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def zero_sales_check(df, columns):
    """Check if all sales columns sum to zero"""
    total_sum = df[columns].sum().sum()
    return total_sum == 0

def convert_and_sum(df, column_name):
    """Convert column to numeric and sum values"""
    return pd.to_numeric(df[column_name], errors='coerce').fillna(0).sum()

def get_month_date_range_from_target(target_date_str):
    """Get the start and end date for the target month"""
    # Parse the target date string
    target_date = datetime.strptime(target_date_str, "%Y-%m-%d")    
    # Get first day of the target month
    month_start = target_date.replace(day=1)    
    # Get last day of the target month
    month_end = month_start + relativedelta(months=1) - timedelta(days=1)    
    return month_start, month_end

def process_target_month_discount_data(all_revenue_details_df, labor_discount_details_df, parts_discount_details_df, 
                                       month_start, month_end, advisor, tech, retail_flag, 
                                       customer_pay_types, columns_to_check):
    """Process discount metrics data for the target month and return results"""
    
    month_start = month_start.date()
    month_end = month_end.date()
    
    # Filter data for the specific target month
    month_data = all_revenue_details_df[
        (all_revenue_details_df['closeddate'] >= month_start) &
        (all_revenue_details_df['closeddate'] <= month_end)
    ]
    
    if month_data.empty:
        print("No data found for the target month")
        return None
    
    # Apply filtering logic
    all_revenue_details_df_filtered = month_data[
        (month_data['department'] == 'Service') & 
        (month_data['opcategory'].isin(['REPAIR','COMPETITIVE','MAINTENANCE'])) &
        (month_data['hide_ro'].astype(int) != 1)
    ]
    
    if all_revenue_details_df_filtered.empty:
        print("No service department data found for the target month")
        return None
    
    all_revenue_details_df_filtered = all_revenue_details_df_filtered.copy()
    
    # Convert columns to numeric
    columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
    all_revenue_details_df_filtered[columns_to_convert] = all_revenue_details_df_filtered[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)
    all_revenue_details_df_filtered[columns_to_convert] = all_revenue_details_df_filtered[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))
    
    # Create unique RO number
    all_revenue_details_df_filtered['unique_ro_number'] = all_revenue_details_df_filtered['ronumber'].astype(str) + '_' + all_revenue_details_df_filtered['closeddate'].astype(str)
    
    # Filter by customer pay types
    total_CP_revenue_details_df = all_revenue_details_df_filtered[all_revenue_details_df_filtered['paytypegroup'].isin(customer_pay_types)]
    
    total_CP_revenue_details_df = total_CP_revenue_details_df.copy()
    total_CP_revenue_details_df[columns_to_convert] = total_CP_revenue_details_df[columns_to_convert].apply(pd.to_numeric, errors='coerce').fillna(0)
    
    # Remove rows with all zero values
    total_CP_revenue_details_df = total_CP_revenue_details_df[
        ~((total_CP_revenue_details_df['lbrsale'].fillna(0) == 0) &
          (total_CP_revenue_details_df['lbrsoldhours'].fillna(0) == 0) &
          (total_CP_revenue_details_df['prtextendedsale'].fillna(0) == 0) &
          (total_CP_revenue_details_df['prtextendedcost'].fillna(0) == 0))
    ]
    
    # Apply advisor and tech filters
    if advisor == {'all'} and tech == {'all'}:
        matching_ro_numbers = total_CP_revenue_details_df['unique_ro_number'].unique()
    elif advisor != {'all'} and tech == {'all'}:
        matching_ro_numbers = total_CP_revenue_details_df.loc[total_CP_revenue_details_df['serviceadvisor'].astype(str).isin(advisor), 'unique_ro_number'].unique()
    elif advisor == {'all'} and tech != {'all'}:
        matching_ro_numbers = total_CP_revenue_details_df.loc[total_CP_revenue_details_df['lbrtechno'].astype(str).isin(tech), 'unique_ro_number'].unique()
    elif advisor != {'all'} and tech != {'all'}:
        matching_ro_numbers = total_CP_revenue_details_df.loc[(total_CP_revenue_details_df['serviceadvisor'].astype(str).isin(advisor)) & 
            (total_CP_revenue_details_df['lbrtechno'].astype(str).isin(tech)), 'unique_ro_number'].unique()
    
    # Apply the filters
    total_CP_revenue_details_df = total_CP_revenue_details_df[total_CP_revenue_details_df['unique_ro_number'].isin(matching_ro_numbers)]
    total_CP_revenue_details_df = total_CP_revenue_details_df.reset_index(drop=True)
    total_CP_revenue_details_df.loc[total_CP_revenue_details_df['opcategory'] == 'N/A', columns_to_check] = 0
    
    # Calculate revenue metrics
    Labor_Sales = convert_and_sum(total_CP_revenue_details_df, 'lbrsale')
    Labor_Cost = convert_and_sum(total_CP_revenue_details_df, 'lbrcost')
    Total_Hours = convert_and_sum(total_CP_revenue_details_df, 'lbrsoldhours')
    Parts_Sales = convert_and_sum(total_CP_revenue_details_df, 'prtextendedsale')
    Parts_Cost = convert_and_sum(total_CP_revenue_details_df, 'prtextendedcost')
    overall_RO_Count = total_CP_revenue_details_df['unique_ro_number'].nunique()
    total_Sales = Labor_Sales + Parts_Sales
    
    # Filter discount data for the target month
    labor_discount_month = labor_discount_details_df[
        (labor_discount_details_df['closeddate'] >= month_start) &
        (labor_discount_details_df['closeddate'] <= month_end)
    ].copy()
    
    parts_discount_month = parts_discount_details_df[
        (parts_discount_details_df['closeddate'] >= month_start) &
        (parts_discount_details_df['closeddate'] <= month_end)
    ].copy()
    
    # Convert discount columns to numeric
    columns_to_convert_labor_discount = ['apportionedlbrdiscount', 'lbrsale', 'lbrcost']
    columns_to_convert_parts_discount = ['apportionedlbrdiscount', 'prtssale', 'prtcost']
    
    labor_discount_month[columns_to_convert_labor_discount] = labor_discount_month[columns_to_convert_labor_discount].apply(pd.to_numeric, errors='coerce').fillna(0)
    parts_discount_month[columns_to_convert_parts_discount] = parts_discount_month[columns_to_convert_parts_discount].apply(pd.to_numeric, errors='coerce').fillna(0)
    
    # Calculate discount metrics - Labor
    apportioned_Lbr_Discount = convert_and_sum(labor_discount_month, 'apportionedlbrdiscount')
    discount_Sale_Labor = convert_and_sum(labor_discount_month, 'lbrsale')
    discount_Cost_Labor = convert_and_sum(labor_discount_month, 'lbrcost')
    discounted_RO_Count_Labor = labor_discount_month['ronumber'].nunique()
    
    # Calculate discount metrics - Parts
    apportioned_Parts_Discount = convert_and_sum(parts_discount_month, 'apportionedlbrdiscount')
    discount_Sale_Parts = convert_and_sum(parts_discount_month, 'prtssale')
    discount_Cost_Parts = convert_and_sum(parts_discount_month, 'prtcost')
    discounted_RO_Count_Parts = parts_discount_month['ronumber'].nunique()
    
    # Total discount metrics
    total_Discount = apportioned_Lbr_Discount + apportioned_Parts_Discount
    total_Discount_Sales = discount_Sale_Labor + discount_Sale_Parts
    
    # RO counts by discount level
    discounted_RO_Count_RO_Level = labor_discount_month[labor_discount_month['dislevel'] == "RO"]['ronumber'].nunique()
    discounted_RO_Count_LINE_Level = labor_discount_month[labor_discount_month['dislevel'] == "LINE"]['ronumber'].nunique()
    discounted_RO_Count_LOP_Level = labor_discount_month[labor_discount_month['dislevel'] == "LOP"]['ronumber'].nunique()
    
    # Calculate percentages and averages
    cp_Discounted_RO_percent = (discounted_RO_Count_Labor / overall_RO_Count) * 100 if overall_RO_Count != 0 else 0
    
    total_Sale_percent = (total_Discount * 100 / total_Sales) if total_Sales != 0 else 0
    labor_Sale_percent = (apportioned_Lbr_Discount * 100 / Labor_Sales) if Labor_Sales != 0 else 0
    parts_Sale_percent = (apportioned_Parts_Discount * 100 / Parts_Sales) if Parts_Sales != 0 else 0
    
    if total_Discount_Sales != 0:
        cp_percent_Disc_Per_Discounted_CP_ROs = (total_Discount * 100 / total_Discount_Sales)
        cp_percent_Disc_Per_Discounted_CP_ROs_labor = (apportioned_Lbr_Discount * 100 / total_Discount_Sales)
        cp_percent_Disc_Per_Discounted_CP_ROs_parts = (apportioned_Parts_Discount * 100 / total_Discount_Sales)
    else:
        cp_percent_Disc_Per_Discounted_CP_ROs = 0
        cp_percent_Disc_Per_Discounted_CP_ROs_labor = 0
        cp_percent_Disc_Per_Discounted_CP_ROs_parts = 0
    
    if overall_RO_Count != 0:
        labor_Discounts_Per_Total_CP_ROs = apportioned_Lbr_Discount / overall_RO_Count
        parts_Discounts_Per_Total_CP_ROs = apportioned_Parts_Discount / overall_RO_Count
        total_Discounts_Per_Total_CP_ROs = total_Discount / overall_RO_Count
    else:
        labor_Discounts_Per_Total_CP_ROs = 0
        parts_Discounts_Per_Total_CP_ROs = 0
        total_Discounts_Per_Total_CP_ROs = 0
    
    labor_CP_Total_Disc_Avg_Disc_ROs = apportioned_Lbr_Discount / discounted_RO_Count_Labor if discounted_RO_Count_Labor != 0 else 0
    parts_CP_Total_Disc_Avg_Disc_ROs = apportioned_Parts_Discount / discounted_RO_Count_Parts if discounted_RO_Count_Parts != 0 else 0
    total_CP_Total_Disc_Avg_Disc_ROs = labor_CP_Total_Disc_Avg_Disc_ROs + parts_CP_Total_Disc_Avg_Disc_ROs
    
    # Return comprehensive result set
    return {
        "target_month": month_start.strftime('%Y-%m'),
        "target_month_name": month_start.strftime('%B %Y'),
        
        # CP Discounts - Labor & Parts
        "cp_discounts": {
            "total_labor_discount": round_off(apportioned_Lbr_Discount, 2),
            "total_parts_discount": round_off(apportioned_Parts_Discount, 2),
            "total_discount": round_off(total_Discount, 2)
        },
        
        # CP RO Count for Disc by Disc Level
        "ro_count_by_discount_level": {
            "discounted_ro_count_ro_level": discounted_RO_Count_RO_Level,
            "discounted_ro_count_line_level": discounted_RO_Count_LINE_Level,
            "discounted_ro_count_lop_level": discounted_RO_Count_LOP_Level
        },
        
        # CP Discounted RO %
        "discounted_ro_percentage": {
            "cp_discounted_ro_percent": round_off(cp_Discounted_RO_percent, 2)
        },
        
        # CP % Disc of Total $ Sold
        "percentage_disc_of_total_sold": {
            "total_sales_percent": round_off(total_Sale_percent, 2),
            "labor_sales_percent": round_off(labor_Sale_percent, 2),
            "parts_sales_percent": round_off(parts_Sale_percent, 2)
        },
        
        # CP % Disc Per Discounted CP ROs
        "percentage_disc_per_discounted_ro": {
            "total_cp_percent": round_off(cp_percent_Disc_Per_Discounted_CP_ROs, 2),
            "labor_cp_percent": round_off(cp_percent_Disc_Per_Discounted_CP_ROs_labor, 2),
            "parts_cp_percent": round_off(cp_percent_Disc_Per_Discounted_CP_ROs_parts, 2)
        },
        
        # Discounts Per Total CP ROs
        "discounts_per_total_cp_ros": {
            "labor_discounts": round_off(labor_Discounts_Per_Total_CP_ROs, 2),
            "parts_discounts": round_off(parts_Discounts_Per_Total_CP_ROs, 2),
            "total_discounts": round_off(total_Discounts_Per_Total_CP_ROs, 2)
        },
        
        # CP Total Disc $ Avg of Disc ROs
        "avg_discount_per_discounted_ro": {
            "total_avg": round_off(total_CP_Total_Disc_Avg_Disc_ROs, 2),
            "labor_avg": round_off(labor_CP_Total_Disc_Avg_Disc_ROs, 2),
            "parts_avg": round_off(parts_CP_Total_Disc_Avg_Disc_ROs, 2)
        },
        
        # Additional context
        "overall_metrics": {
            "overall_ro_count": overall_RO_Count,
            "total_sales": round_off(total_Sales, 2),
            "labor_sales": round_off(Labor_Sales, 2),
            "parts_sales": round_off(Parts_Sales, 2),
            "discounted_ro_count_labor": discounted_RO_Count_Labor,
            "discounted_ro_count_parts": discounted_RO_Count_Parts
        }
    }

def db_execution_discount_metrics(target_date_str, advisor, tech, retail_flag, columns_to_check, 
                                  all_revenue_details_df, labor_discount_details_df, parts_discount_details_df):
    """
    Handle database operations and execute discount metrics processing for target month
    """
    try:
        # Get target month date range
        month_start, month_end = get_month_date_range_from_target(target_date_str)
        
        print(f"Target month range: {month_start.date()} to {month_end.date()}")
        
        if all_revenue_details_df.empty:
            print("ERROR: No revenue data provided!")
            return None, None
        
        # Convert date column and other preprocessing
        columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))
        
        # Define customer pay types based on retail_flag
        if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C'}
        elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'M'}
        elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C', 'E'}
        elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'E', 'M'}
        else:
            customer_pay_types = {'C'}
        
        target_month_result = process_target_month_discount_data(
            all_revenue_details_df,
            labor_discount_details_df,
            parts_discount_details_df,
            month_start,
            month_end,
            advisor,
            tech,
            retail_flag,
            customer_pay_types,
            columns_to_check
        )
        
        return target_month_result, customer_pay_types
        
    except Exception as e:
        print(f"ERROR in db_execution_discount_metrics: {str(e)}")
        print("=" * 60)
        print("DISCOUNT METRICS DATABASE EXECUTION FAILED")
        print("=" * 60)
        return None, None

def db_calculation():
    """
    Main execution function for discount metrics DB calculation
    """
    
    # Configuration variables
    columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
    retail_flag = {'C', 'M'}  # Adjust as needed
    
    storeid = config.store_id
    realm = config.database_name
    advisor_set = config.advisor
    tech_set = config.technician
    
    # Process advisor configuration
    if isinstance(advisor_set, str):
        if advisor_set.lower() == 'all':
            advisor = {'all'}
        elif ',' in advisor_set:
            advisor = {x.strip() for x in advisor_set.split(',')}
        else:
            advisor = {advisor_set.strip()}
    else:
        advisor = {'all'}
    
    # Process technician configuration
    if isinstance(tech_set, str):
        if tech_set.lower() == 'all':
            tech = {'all'}
        elif ',' in tech_set:
            tech = {x.strip() for x in tech_set.split(',')}
        else:
            tech = {tech_set.strip()}
    else:
        tech = {'all'}
    
    log_info("\n" + "=" * 80)
    log_info("DISCOUNT METRICS ANALYSIS - STARTING")
    log_info("=" * 80)

    # Execute database operations and processing
    if not TARGET_MONTHS_YEARS or len(TARGET_MONTHS_YEARS) == 0:
        log_error("No target month specified for discount metrics!")
        return

    # Get retail flag from config
    retail_flag_df = config.retail_flag_all
    if retail_flag_df is not None and not retail_flag_df.empty:
        retail_flag = set(retail_flag_df['source_paytype'].tolist())
    else:
        retail_flag = {'C'}  # Default fallback

    log_info(f"Retail flag from config: {retail_flag}")

    # Get all revenue details from config
    all_revenue_details_df = config.all_revenue_details
    if all_revenue_details_df is None or all_revenue_details_df.empty:
        log_error("No revenue details data available in config!")
        return

    # Fetch discount-specific data
    log_info("Fetching discount-specific data...")
    try:
        labor_discount_table_db_connect = DiscountLaborDetails()
        labor_discount_details_df = labor_discount_table_db_connect.getTableResult()
        log_info(f"Labor discount records retrieved: {len(labor_discount_details_df)}")

        parts_discount_table_db_connect = DiscountPartsDetails()
        parts_discount_details_df = parts_discount_table_db_connect.getTableResult()
        log_info(f"Parts discount records retrieved: {len(parts_discount_details_df)}")
    except Exception as e:
        log_error(f"Failed to fetch discount data: {e}")
        return

    # Execute database operations and processing
    target_date_str = TARGET_MONTHS_YEARS[0]
    target_month_result, customer_pay_types = db_execution_discount_metrics(
        target_date_str, advisor, tech, retail_flag, columns_to_check,
        all_revenue_details_df, labor_discount_details_df, parts_discount_details_df
    )
    
    # Process results
    if target_month_result:
        print("\n" + "=" * 80)
        print("DISCOUNT METRICS RESULTS PROCESSING")
        print("=" * 80)
        
        # Create the final result set for the target month in the format expected by comparison function
        final_result_set = {
            "analysis_info": {
                "target_month": target_date_str,
                "analysis_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "store_id": storeid,
                "realm": realm,
                "advisor_filter": list(advisor),
                "technician_filter": list(tech),
                "customer_pay_types": list(customer_pay_types)
            },
            "target_month_results": {
                "discount_metrics": {
                    # Chart 1234: CP Discounts - Labor & Parts
                    "total_labor_discount": target_month_result["cp_discounts"]["total_labor_discount"],
                    "total_parts_discount": target_month_result["cp_discounts"]["total_parts_discount"],
                    "total_discount": target_month_result["cp_discounts"]["total_discount"],

                    # Chart 1113: RO Count by Disc Level
                    "discounted_ro_count_ro_level": target_month_result["ro_count_by_discount_level"]["discounted_ro_count_ro_level"],
                    "discounted_ro_count_line_level": target_month_result["ro_count_by_discount_level"]["discounted_ro_count_line_level"],
                    "discounted_ro_count_lop_level": target_month_result["ro_count_by_discount_level"]["discounted_ro_count_lop_level"],

                    # Chart 1123: CP Discounted RO %
                    "cp_discounted_ro_percent": target_month_result["discounted_ro_percentage"]["cp_discounted_ro_percent"],

                    # Chart 1115: CP % Disc of Total $ Sold
                    "total_sales_percent": target_month_result["percentage_disc_of_total_sold"]["total_sales_percent"],
                    "labor_sales_percent": target_month_result["percentage_disc_of_total_sold"]["labor_sales_percent"],
                    "parts_sales_percent": target_month_result["percentage_disc_of_total_sold"]["parts_sales_percent"],

                    # Chart 1232: CP % Disc Per Discounted CP ROs
                    "total_cp_percent": target_month_result["percentage_disc_per_discounted_ro"]["total_cp_percent"],
                    "labor_cp_percent": target_month_result["percentage_disc_per_discounted_ro"]["labor_cp_percent"],
                    "parts_cp_percent": target_month_result["percentage_disc_per_discounted_ro"]["parts_cp_percent"],

                    # Chart 1236: Discounts Per Total CP ROs
                    "labor_discounts_per_total_cp_ros": target_month_result["discounts_per_total_cp_ros"]["labor_discounts"],
                    "parts_discounts_per_total_cp_ros": target_month_result["discounts_per_total_cp_ros"]["parts_discounts"],
                    "total_discounts_per_total_cp_ros": target_month_result["discounts_per_total_cp_ros"]["total_discounts"],

                    # Chart 1165: CP Total Disc $ Avg of Disc ROs
                    "total_avg_discount_per_discounted_ro": target_month_result["avg_discount_per_discounted_ro"]["total_avg"],
                    "labor_avg_discount_per_discounted_ro": target_month_result["avg_discount_per_discounted_ro"]["labor_avg"],
                    "parts_avg_discount_per_discounted_ro": target_month_result["avg_discount_per_discounted_ro"]["parts_avg"]
                },
                "drilldown_results": {
                    # Add drilldown data structure if needed by comparison function
                },
                "original_data": target_month_result  # Keep original structure for reference
            }
        }
        
        # Write results to JSON file using proper path creation
        result_dir, db_json_path = create_folder_file_path(subfolder="chart_processing_results", output_file=db_json)

        with open(db_json_path, 'w', encoding='utf-8') as json_file:
            json.dump(final_result_set, json_file, indent=4, ensure_ascii=False)

        log_info(f"Target month discount metrics data written successfully to {db_json_path}")
        
        # Display summary
        print(f"\nTarget Month Summary for {target_month_result['target_month_name']}:")
        print(f"  Total Discount: ${target_month_result['cp_discounts']['total_discount']:,.2f}")
        print(f"    - Labor Discount: ${target_month_result['cp_discounts']['total_labor_discount']:,.2f}")
        print(f"    - Parts Discount: ${target_month_result['cp_discounts']['total_parts_discount']:,.2f}")
        print(f"  Discounted RO %: {target_month_result['discounted_ro_percentage']['cp_discounted_ro_percent']}%")
        print(f"  Total RO Count: {target_month_result['overall_metrics']['overall_ro_count']}")
        print(f"  Discount Levels:")
        print(f"    - RO Level: {target_month_result['ro_count_by_discount_level']['discounted_ro_count_ro_level']}")
        print(f"    - LINE Level: {target_month_result['ro_count_by_discount_level']['discounted_ro_count_line_level']}")
        print(f"    - LOP Level: {target_month_result['ro_count_by_discount_level']['discounted_ro_count_lop_level']}")
        
    else:
        print("\n" + "=" * 80)
        print("NO DISCOUNT METRICS DATA FOUND")
        print("=" * 80)
        print(f"No data available for target month {target_date_str}")
    
    print("\n" + "=" * 80)
    print("DISCOUNT METRICS ANALYSIS - MAIN EXECUTION COMPLETED")
    print("=" * 80)
    
    return final_result_set if target_month_result else None
class MultiChartParallelProcessor:
    """Process multiple charts in parallel with separate browsers"""
    
    # def __init__(self, max_browsers=4, auth_manager=None):
    #     self.max_browsers = max_browsers
    #     self.auth_manager = auth_manager or AuthManager()
    def __init__(self, max_browsers=MAX_CONCURRENT_BROWSERS, auth_manager=None):
        self.max_browsers = max_browsers
        self.auth_manager = auth_manager or AuthManager(config)
        self.charts_info = None

    async def discover_charts(self):
        """Discover all charts on the new component page with 12 bar charts"""
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []
        
        page = await self.auth_manager.new_page()
        try:
            await page.goto(f"{config.site_url.rstrip('/')}/Discounts", timeout=50000)
            await asyncio.sleep(2)
            # Try to activate the '13 Month Trend' tab if present
            try:
                await page.locator("role=tab[name=\"13 Month Trend\"]").click(timeout=3000)
                await asyncio.sleep(1)
            except Exception:
                try:
                    tabs = page.locator('.MuiTabs-flexContainer button')
                    for i in range(await tabs.count()):
                        txt = (await tabs.nth(i).inner_text()).strip()
                        if '13 Month' in txt:
                            await tabs.nth(i).click()
                            await asyncio.sleep(1)
                            break
                except Exception:
                    log_warn("Could not select '13 Month Trend' tab; continuing")
            chart_found = False

            # Wait for the page to load and charts to be rendered
            selectors_to_try = [
                'canvas.chartjs-render-monitor',
                'canvas[class*="chartjs"]',
                'canvas',
                '[id*="chart"]',
                '.react-grid-item canvas',
                '[id*="chartContainterId"]'
            ]

            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    chart_found = True
                    print(f"✓ Found charts using selector: {selector}")
                    break
                except:
                    continue

            if not chart_found:
                print("❌ No chart elements found with any selector")
                return False

            # Enhanced waiting strategy for chart loading
            print("⏳ Waiting for charts to fully load...")
            await asyncio.sleep(3)  # Initial wait

            # Wait for chart containers to be present (flexible approach)
            print("⏳ Waiting for chart containers to load...")
            await page.wait_for_function("""
                () => {
                    const containers = document.querySelectorAll('[id*="chartContainterId"]');
                    console.log(`Found ${containers.length} chart containers`);
                    return containers.length > 0;  // Wait for at least one chart
                }
            """, timeout=60000)

            # Additional wait for chart rendering
            await asyncio.sleep(5)

            # Check how many chart containers we found
            container_count = await page.evaluate("""
                () => {
                    const containers = document.querySelectorAll('[id*="chartContainterId"]');
                    return containers.length;
                }
            """)
            print(f"✓ Found {container_count} chart containers on page")

            # Wait for canvas elements to be rendered (flexible approach)
            await page.wait_for_function("""
                () => {
                    const canvases = document.querySelectorAll('canvas');
                    console.log(`Found ${canvases.length} canvas elements`);
                    return canvases.length > 0;  // Wait for at least one canvas
                }
            """, timeout=60000)

            # Check how many canvases we found
            canvas_count = await page.evaluate("""
                () => {
                    const canvases = document.querySelectorAll('canvas');
                    return canvases.length;
                }
            """)
            print(f"✓ Found {canvas_count} canvas elements - charts appear to be loaded")
            
            charts_info = await page.evaluate("""
                () => {
                    console.log('Starting enhanced chart discovery...');

                    // First, try to find all chart containers
                    const chartContainers = document.querySelectorAll('[id*="chartContainterId"]');
                    console.log('Found chart containers:', chartContainers.length);

                    const chartsInfo = [];
                    const processedContainers = new Set();

                    // Method 1: Find charts by container ID (Enhanced)
                    chartContainers.forEach((container, containerIndex) => {
                        const containerId = container.id;
                        const chartId = containerId.replace('chartContainterId-', '');

                        // Skip if already processed
                        if (processedContainers.has(containerId)) {
                            return;
                        }
                        processedContainers.add(containerId);

                        // Look for canvas in this container
                        const canvas = container.querySelector('canvas');
                        if (canvas) {
                            const rect = canvas.getBoundingClientRect();

                            // Get chart title from card header
                            let chartTitle = `Chart ${chartId}`;
                            const cardHeader = container.querySelector(`#card-header-${chartId}`);
                            if (cardHeader) {
                                const titleElement = cardHeader.querySelector('.MuiCardHeader-title');
                                if (titleElement && titleElement.textContent.trim()) {
                                    chartTitle = titleElement.textContent.trim();
                                }
                            }

                            // Check if chart has data (no "divNoData" element)
                            const hasData = !container.querySelector('.divNoData');

                            // Check if canvas is actually rendered and visible
                            const isVisible = rect.width > 0 && rect.height > 0 &&
                                            window.getComputedStyle(canvas).display !== 'none' &&
                                            window.getComputedStyle(container).display !== 'none';

                            chartsInfo.push({
                                canvasIndex: containerIndex,
                                chartTitle: chartTitle,
                                canvasId: canvas.id || `canvas-${chartId}`,
                                canvasClass: canvas.className,
                                containerId: containerId,
                                chartId: chartId,
                                position: {
                                    x: Math.round(rect.left),
                                    y: Math.round(rect.top),
                                    width: Math.round(rect.width),
                                    height: Math.round(rect.height)
                                },
                                visible: isVisible,
                                isChartJs: canvas.classList.contains('chartjs-render-monitor') ||
                                        canvas.classList.contains('chartjs-render'),
                                hasData: hasData,
                                detectionMethod: 'container-based',
                                isFullyLoaded: canvas.width > 0 && canvas.height > 0
                            });

                            console.log(`Processed chart ${chartId}: ${chartTitle}, visible: ${isVisible}, hasData: ${hasData}`);
                        } else {
                            console.log(`No canvas found in container ${containerId}`);
                        }
                    });
                    
                    console.log('Charts found by container method:', chartsInfo.length);

                    // Method 2: Find any remaining canvases that might have been missed
                    const allCanvases = document.querySelectorAll('canvas');
                    console.log('Total canvases found:', allCanvases.length);
                    
                    allCanvases.forEach((canvas, canvasIndex) => {
                        // Skip if this canvas is already in our list
                        const alreadyFound = chartsInfo.some(chart => 
                            chart.canvasId === canvas.id || 
                            (chart.canvasId.includes('canvas-') && canvas.closest(`#${chart.containerId}`))
                        );
                        
                        if (!alreadyFound) {
                            const rect = canvas.getBoundingClientRect();
                            
                            // Only include visible canvases with reasonable dimensions
                            if (rect.width > 50 && rect.height > 50) {
                                // Try to find chart title
                                let chartTitle = `Chart ${canvasIndex + 1}`;
                                let chartId = null;
                                let containerId = null;
                                
                                // Look for parent container
                                let container = canvas.closest('.react-grid-item, .MuiCard-root, [id*="chart"], [class*="chart"]');
                                if (container) {
                                    // Try to extract ID from container
                                    if (container.id && container.id.includes('chartContainterId')) {
                                        containerId = container.id;
                                        chartId = container.id.replace('chartContainterId-', '');
                                    }
                                    
                                    // Look for title
                                    const titleElement = container.querySelector('h1, h2, h3, h4, h5, h6, .title, [class*="title"], .MuiCardHeader-title');
                                    if (titleElement && titleElement.textContent.trim()) {
                                        chartTitle = titleElement.textContent.trim();
                                    }
                                }
                                
                                // Check for data
                                const hasData = !container || !container.querySelector('.divNoData');
                                
                                chartsInfo.push({
                                    canvasIndex: canvasIndex,
                                    chartTitle: chartTitle,
                                    canvasId: canvas.id || `canvas-fallback-${canvasIndex}`,
                                    canvasClass: canvas.className,
                                    containerId: containerId,
                                    chartId: chartId || `fallback-${canvasIndex}`,
                                    position: {
                                        x: Math.round(rect.left),
                                        y: Math.round(rect.top),
                                        width: Math.round(rect.width),
                                        height: Math.round(rect.height)
                                    },
                                    visible: rect.width > 0 && rect.height > 0,
                                    isChartJs: canvas.classList.contains('chartjs-render-monitor') || canvas.classList.contains('chartjs-render'),
                                    hasData: hasData,
                                    detectionMethod: 'canvas-based'
                                });
                            }
                        }
                    });
                    
                    // Method 3: Look for react-grid-items that might contain charts
                    const gridItems = document.querySelectorAll('.react-grid-item');
                    console.log('Grid items found:', gridItems.length);
                    
                    gridItems.forEach((item, itemIndex) => {
                        const canvas = item.querySelector('canvas');
                        if (canvas) {
                            // Check if we already have this chart
                            const alreadyFound = chartsInfo.some(chart => 
                                chart.canvasId === canvas.id || 
                                chart.containerId === item.id
                            );
                            
                            if (!alreadyFound) {
                                const rect = canvas.getBoundingClientRect();
                                
                                if (rect.width > 0 && rect.height > 0) {
                                    let chartTitle = `Grid Chart ${itemIndex + 1}`;
                                    let chartId = null;
                                    
                                    if (item.id && item.id.includes('chartContainterId')) {
                                        chartId = item.id.replace('chartContainterId-', '');
                                        
                                        const cardHeader = item.querySelector(`#card-header-${chartId}`);
                                        if (cardHeader) {
                                            const titleElement = cardHeader.querySelector('.MuiCardHeader-title');
                                            if (titleElement && titleElement.textContent.trim()) {
                                                chartTitle = titleElement.textContent.trim();
                                            }
                                        }
                                    }
                                    
                                    const hasData = !item.querySelector('.divNoData');
                                    
                                    chartsInfo.push({
                                        canvasIndex: itemIndex,
                                        chartTitle: chartTitle,
                                        canvasId: canvas.id || `grid-canvas-${itemIndex}`,
                                        canvasClass: canvas.className,
                                        containerId: item.id,
                                        chartId: chartId || `grid-${itemIndex}`,
                                        position: {
                                            x: Math.round(rect.left),
                                            y: Math.round(rect.top),
                                            width: Math.round(rect.width),
                                            height: Math.round(rect.height)
                                        },
                                        visible: rect.width > 0 && rect.height > 0,
                                        isChartJs: canvas.classList.contains('chartjs-render-monitor') || canvas.classList.contains('chartjs-render'),
                                        hasData: hasData,
                                        detectionMethod: 'grid-based'
                                    });
                                }
                            }
                        }
                    });
                    
                    console.log(`Total charts discovered: ${chartsInfo.length}`);

                    // Re-index canvasIndex to ensure proper ordering
                    chartsInfo.forEach((chart, index) => {
                        chart.canvasIndex = index;
                    });

                    // Sort by position (top to bottom, left to right)
                    chartsInfo.sort((a, b) => {
                        if (Math.abs(a.position.y - b.position.y) < 50) {
                            return a.position.x - b.position.x;
                        }
                        return a.position.y - b.position.y;
                    });

                    // Final re-indexing after sorting
                    chartsInfo.forEach((chart, index) => {
                        chart.canvasIndex = index;
                    });

                    console.log(`Final chart count: ${chartsInfo.length}`);
                    console.log('Chart IDs found:', chartsInfo.map(c => c.chartId));

                    return chartsInfo;
                }
            """)
            
            print(f"📊 Found {len(charts_info)} charts on the new component page")

            # If we don't have any charts, try one more time with additional wait
            if len(charts_info) == 0:
                print(f"⚠️ No charts found. Retrying with longer wait...")
                await asyncio.sleep(5)  # Additional wait

                # Try discovery again
                retry_charts_info = await page.evaluate("""
                    () => {
                        const containers = document.querySelectorAll('[id*="chartContainterId"]');
                        const retryChartsInfo = [];

                        containers.forEach((container, index) => {
                            const containerId = container.id;
                            const chartId = containerId.replace('chartContainterId-', '');
                            const canvas = container.querySelector('canvas');

                            if (canvas) {
                                const rect = canvas.getBoundingClientRect();
                                let chartTitle = `Chart ${chartId}`;
                                const cardHeader = container.querySelector(`#card-header-${chartId}`);
                                if (cardHeader) {
                                    const titleElement = cardHeader.querySelector('.MuiCardHeader-title');
                                    if (titleElement && titleElement.textContent.trim()) {
                                        chartTitle = titleElement.textContent.trim();
                                    }
                                }

                                retryChartsInfo.push({
                                    canvasIndex: index,
                                    chartTitle: chartTitle,
                                    canvasId: canvas.id || `canvas-${chartId}`,
                                    canvasClass: canvas.className,
                                    containerId: containerId,
                                    chartId: chartId,
                                    position: {
                                        x: Math.round(rect.left),
                                        y: Math.round(rect.top),
                                        width: Math.round(rect.width),
                                        height: Math.round(rect.height)
                                    },
                                    visible: rect.width > 0 && rect.height > 0,
                                    isChartJs: canvas.classList.contains('chartjs-render-monitor'),
                                    hasData: !container.querySelector('.divNoData'),
                                    detectionMethod: 'retry-attempt',
                                    isFullyLoaded: canvas.width > 0 && canvas.height > 0
                                });
                            }
                        });

                        return retryChartsInfo;
                    }
                """)

                if len(retry_charts_info) > len(charts_info):
                    print(f"✓ Retry successful! Found {len(retry_charts_info)} charts")
                    charts_info = retry_charts_info
                else:
                    print(f"⚠️ Retry didn't improve results. Continuing with {len(charts_info)} charts")

            # If still no charts found, this might be a page loading issue
            if len(charts_info) == 0:
                print("❌ No charts found after retry. This might indicate a page loading issue or changed page structure.")
                return False

            # Group charts by their data availability
            charts_with_data = [chart for chart in charts_info if chart['hasData']]
            charts_no_data = [chart for chart in charts_info if not chart['hasData']]

            print(f"✅ Charts with data: {len(charts_with_data)}")
            print(f"❌ Charts with no data: {len(charts_no_data)}")

            # Display detailed info for each chart
            for i, chart in enumerate(charts_info):
                status = "✅ HAS DATA" if chart['hasData'] else "❌ NO DATA"
                method = chart.get('detectionMethod', 'unknown')
                print(f"  {i+1}. Chart ID: {chart['chartId']} - {chart['chartTitle']} {status}")
                print(f"     Container: {chart['containerId']}")
                print(f"     Canvas: {chart['canvasId']} (Method: {method})")
                print(f"     Position: {chart['position']}")
                print(f"     Visible: {chart['visible']}, ChartJS: {chart['isChartJs']}")
                print()

            # Debug: Also log all chart container IDs found on page
            container_ids = await page.evaluate("""
                () => {
                    const containers = document.querySelectorAll('[id*="chartContainterId"]');
                    return Array.from(containers).map(c => c.id);
                }
            """)
            print(f"All chart container IDs on page: {container_ids}")

            return charts_info
            
        except Exception as e:
            print(f"❌ Error discovering charts: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            log_info("Finished discovering chart-point combinations")
            await page.close()                         
    async def find_matching_points_in_chart(self, page, chart_index, target_month_year,chart_id):
        """Find matching data points for a specific chart and target month-year"""
        try:
            log_info(f"Finding points in chart {chart_index} for target: {target_month_year}")            
            matching_points = await page.evaluate("""
                (args) => {
                    const { chartIndex, targetMonthYear } = args;
                    const matchingPoints = [];
                    
                    try {
                        // Find all canvas elements
                        const canvases = document.querySelectorAll('canvas');
                        console.log(`Found ${canvases.length} canvas elements`);
                        
                        // Check if the requested chart index exists
                        if (chartIndex >= canvases.length) {
                            console.log(`Chart index ${chartIndex} out of range (max: ${canvases.length - 1})`);
                            return [];
                        }
                        
                        const canvas = canvases[chartIndex];
                        let chart = null;
                        
                        console.log(`Processing canvas ${chartIndex} for target: ${targetMonthYear}`);
                        
                        // Multiple methods to get chart instance
                        try {
                            // Method 1: Chart.getChart (Chart.js v3+)
                            if (typeof Chart !== 'undefined' && Chart.getChart) {
                                chart = Chart.getChart(canvas);
                                console.log(`Method 1 - Chart.getChart: ${chart ? 'Found' : 'Not found'}`);
                            }
                            
                            // Method 2: Chart.instances (Chart.js v2)
                            if (!chart && typeof Chart !== 'undefined' && Chart.instances) {
                                const instances = Object.values(Chart.instances);
                                chart = instances.find(instance => instance.canvas === canvas);
                                console.log(`Method 2 - Chart.instances: ${chart ? 'Found' : 'Not found'}`);
                            }
                            
                            // Method 3: Canvas._chart property (older versions)
                            if (!chart && canvas._chart) {
                                chart = canvas._chart;
                                console.log(`Method 3 - canvas._chart: Found`);
                            }
                            
                            // Method 4: Check for chart instance in canvas properties
                            if (!chart) {
                                const keys = Object.keys(canvas);
                                for (const key of keys) {
                                    if (key.includes('chart') || key.includes('Chart')) {
                                        chart = canvas[key];
                                        if (chart && chart.data) {
                                            console.log(`Method 4 - Found chart via property: ${key}`);
                                            break;
                                        }
                                    }
                                }
                            }
                            
                        } catch (e) {
                            console.warn(`Error getting chart instance for canvas ${chartIndex}:`, e);
                        }
                        
                        if (!chart) {
                            console.log(`No chart found for canvas ${chartIndex}`);
                            return [];
                        }
                        
                        // Validate chart structure
                        if (!chart.data || !chart.data.datasets) {
                            console.log(`Invalid chart data structure for canvas ${chartIndex}`);
                            return [];
                        }
                        
                        console.log(`Processing chart with ${chart.data.datasets.length} datasets`);
                        
                        const canvasRect = canvas.getBoundingClientRect();
                        console.log(`Canvas rect:`, canvasRect);
                        
                        // Get x-axis labels
                        const xLabels = chart.data.labels || [];
                        console.log(`X-axis labels:`, xLabels);
                        
                        // Helper function to check if label matches target
                        const isLabelMatch = (label, target) => {
                            if (!label || !target) return false;
                            
                            const labelStr = String(label).toLowerCase().trim();
                            const targetStr = String(target).toLowerCase().trim();
                            
                            // Direct match
                            if (labelStr === targetStr) return true;
                            
                            // Contains match
                            if (labelStr.includes(targetStr) || targetStr.includes(labelStr)) return true;
                            
                            // Month-year pattern matching (e.g., "Jan 2024", "January 2024", "01/2024")
                            const monthYearRegex = /(\\w+)[\\/\\-\\s]+(\\d{4})/;
                            const labelMatch = labelStr.match(monthYearRegex);
                            const targetMatch = targetStr.match(monthYearRegex);
                            
                            if (labelMatch && targetMatch) {
                                const labelMonth = labelMatch[1];
                                const labelYear = labelMatch[2];
                                const targetMonth = targetMatch[1];
                                const targetYear = targetMatch[2];
                                
                                // Check if years match and months match (partial match allowed)
                                if (labelYear === targetYear && 
                                    (labelMonth.includes(targetMonth) || targetMonth.includes(labelMonth))) {
                                    return true;
                                }
                            }
                            
                            return false;
                        };
                        
                        // Process each dataset
                        for (let datasetIndex = 0; datasetIndex < chart.data.datasets.length; datasetIndex++) {
                            const dataset = chart.data.datasets[datasetIndex];
                            
                            if (!dataset.data || !Array.isArray(dataset.data)) {
                                console.log(`No data in dataset ${datasetIndex}`);
                                continue;
                            }
                            
                            console.log(`Processing dataset ${datasetIndex} with ${dataset.data.length} points`);
                            
                            // Process each data point
                            for (let pointIndex = 0; pointIndex < dataset.data.length; pointIndex++) {
                                const value = dataset.data[pointIndex];
                                const xLabel = xLabels[pointIndex] || `Point ${pointIndex}`;
                                
                                // Only process points that match the target month-year
                                if (!isLabelMatch(xLabel, targetMonthYear)) {
                                    continue;
                                }
                                
                                console.log(`Found matching point: ${xLabel} matches ${targetMonthYear}`);
                                
                                try {
                                    let screenX = null;
                                    let screenY = null;
                                    let canvasX = null;
                                    let canvasY = null;
                                    
                                    // Enhanced coordinate extraction
                                    try {
                                        const meta = chart.getDatasetMeta(datasetIndex);
                                        if (meta && meta.data && meta.data[pointIndex]) {
                                            const element = meta.data[pointIndex];
                                            
                                            // Check if coordinates are valid numbers
                                            if (typeof element.x === 'number' && !isNaN(element.x) && 
                                                typeof element.y === 'number' && !isNaN(element.y)) {
                                                canvasX = element.x;
                                                canvasY = element.y;
                                                screenX = canvasRect.left + element.x;
                                                screenY = canvasRect.top + element.y;
                                                console.log(`Element coordinates: canvas(${canvasX}, ${canvasY}) -> screen(${screenX}, ${screenY})`);
                                            }
                                        }
                                    } catch (e) {
                                        console.warn(`Could not get element position for point ${pointIndex}:`, e);
                                    }
                                    
                                    // Fallback: Use chart scales to calculate coordinates
                                    if ((canvasX === null || isNaN(canvasX)) && chart.scales) {
                                        try {
                                            // Find x and y scales
                                            const xScale = chart.scales.x || chart.scales['x-axis-0'] || chart.scales.xAxes?.[0] ||
                                                        Object.values(chart.scales).find(s => s.axis === 'x' || s.type === 'category' || s.type === 'time');
                                            const yScale = chart.scales.y || chart.scales['y-axis-0'] || chart.scales.yAxes?.[0] ||
                                                        Object.values(chart.scales).find(s => s.axis === 'y' || s.position === 'left');
                                            
                                            if (xScale && yScale && xScale.getPixelForValue && yScale.getPixelForValue) {
                                                // Get the actual y value
                                                let yValue = value;
                                                if (typeof value === 'object' && value !== null) {
                                                    yValue = value.y || value.value || value.data;
                                                }
                                                if (typeof yValue === 'string') {
                                                    yValue = parseFloat(yValue);
                                                }
                                                
                                                if (!isNaN(yValue)) {
                                                    canvasX = xScale.getPixelForValue(pointIndex);
                                                    canvasY = yScale.getPixelForValue(yValue);
                                                    
                                                    if (!isNaN(canvasX) && !isNaN(canvasY)) {
                                                        screenX = canvasRect.left + canvasX;
                                                        screenY = canvasRect.top + canvasY;
                                                        console.log(`Scale-based coordinates: canvas(${canvasX}, ${canvasY}) -> screen(${screenX}, ${screenY})`);
                                                    }
                                                }
                                            }
                                        } catch (e) {
                                            console.warn(`Error in scale-based calculation:`, e);
                                        }
                                    }
                                    
                                    // Final validation of coordinates
                                    const coordsValid = screenX !== null && screenY !== null && 
                                                    !isNaN(screenX) && !isNaN(screenY) &&
                                                    isFinite(screenX) && isFinite(screenY);
                                    
                                    // Handle different value formats
                                    let displayValue = value;
                                    if (typeof value === 'object' && value !== null) {
                                        displayValue = value.y || value.value || value.data || JSON.stringify(value);
                                    }
                                    
                                    const pointData = {
                                        canvasIndex: chartIndex,
                                        datasetIndex: datasetIndex,
                                        pointIndex: pointIndex,
                                        value: displayValue,
                                        xLabel: xLabel,
                                        screenX: screenX,
                                        screenY: screenY,
                                        canvasX: canvasX,
                                        canvasY: canvasY,
                                        datasetLabel: dataset.label || `Dataset ${datasetIndex}`,
                                        chartType: chart.config ? chart.config.type : 'unknown',
                                        coordinatesValid: coordsValid,
                                        targetMonthYear: targetMonthYear
                                    };
                                    
                                    matchingPoints.push(pointData);
                                    console.log(`Added matching point:`, pointData);
                                    
                                } catch (e) {
                                    console.warn(`Error processing matching point ${pointIndex}:`, e);
                                }
                            }
                        }
                        
                    } catch (e) {
                        console.error('Error in chart point extraction:', e);
                        return [];
                    }
                    
                    console.log(`Found ${matchingPoints.length} matching points for chart ${chartIndex} and target ${targetMonthYear}`);
                    return matchingPoints;
                }
            """, {'chartIndex': chart_index, 'targetMonthYear': target_month_year})
            
            log_info(f" Found {len(matching_points)} matching points in chart {chart_index} for {target_month_year}")
            log_info(f"Matching points: {matching_points}")
            return matching_points            
        except Exception as e:
            log_error(f"Error finding matching points in chart {chart_index}: {str(e)}")
            return []      
    
    async def create_chart_point_combinations(self, target_months_years):
        """Create combinations of charts and their matching points"""
        log_info("Creating chart-point combinations...")
        
        # Use the dedicated parallel function to check and re-login the session.
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []

        # Get a NEW PAGE from the existing session.
        page = await self.auth_manager.new_page()
        try:
            # Navigate to Discounts and activate 13 Month Trend tab
            await page.goto(f"{config.site_url.rstrip('/')}/Discounts", timeout=60000)
            await asyncio.sleep(5)
            try:
                await page.locator("role=tab[name=\"13 Month Trend\"]").click(timeout=3000)
                await asyncio.sleep(1)
            except Exception:
                log_warn("Failed to click '13 Month Trend' tab on navigation")

            # Discover all charts 
            charts_info = await self.discover_charts()
            
            if not charts_info:
                log_warn(" No charts found======================")
                # Delete auth_state.json file when no charts are found
                auth_state_path = "auth_state.json"
                try:
                    if os.path.exists(auth_state_path):
                        os.remove(auth_state_path)
                        log_warn(f"Deleted {auth_state_path} due to no charts found")
                    else:
                        log_info(f"{auth_state_path} not found to delete")
                except Exception as e:
                    log_error(f"Error deleting {auth_state_path}: {e}")
                return []

            chart_point_combinations = []
            charts_with_points = []

            # Process each chart and its points
            for chart_info in charts_info:
                chart_index = chart_info['canvasIndex']
                container_id = chart_info.get('containerId', '')
                chart_id = container_id.split('-')[-1] if container_id else None
                chart_title = chart_info.get('chartTitle', f'Chart {chart_id}')
                
                log_info(f"Processing Chart {chart_id}: {chart_title}")
                chart_total_points = 0
                chart_combinations = []

                # Process each target month-year
                for target_month_year in target_months_years:
                    log_info(f"Looking for data points matching: {target_month_year}")
                    matching_points = await self.find_matching_points_in_chart(
                        page, chart_index, target_month_year, chart_id)
                    
                    log_info(f"Found {len(matching_points) if matching_points else 0} matching points for {target_month_year}")
                    
                    if matching_points:
                        combination = {
                            'chart_index': f"chart_{chart_index}",
                            'chart_id': chart_id,
                            'chart_info': chart_info,
                            'target_month_year': target_month_year,
                            'matching_points': matching_points,
                            'processing_status': 'pending',
                            'points_count': len(matching_points)
                        }
                        chart_combinations.append(combination)
                        chart_total_points += len(matching_points)
                        log_info(f"   Added combination: Chart {chart_index} - {target_month_year} ({len(matching_points)} points)")
                    else:
                        log_info(f"   No matching points found for Chart {chart_index} - {target_month_year}")

                # Track charts with their point counts
                if chart_combinations:
                    charts_with_points.append({
                        'chart_index': chart_index,
                        'chart_id': chart_id,
                        'chart_title': chart_title,
                        'total_points': chart_total_points,
                        'combinations': chart_combinations
                    })

            # Sort and process final combinations
            charts_with_points.sort(key=lambda x: x['total_points'], reverse=True)
            
            for chart_data in charts_with_points:
                chart_point_combinations.extend(chart_data['combinations'])
                log_info(f"  Added Chart {chart_data['chart_index']}: {chart_data['chart_title']} ({chart_data['total_points']} points)")

            # Create summary
            log_info(f"\nSummary: Created {len(chart_point_combinations)} chart-point combinations from {len(charts_with_points)} charts")
            
            chart_summary = {}
            for combo in chart_point_combinations:
                chart_id = combo['chart_id']
                if chart_id not in chart_summary:
                    chart_summary[chart_id] = 0
                chart_summary[chart_id] += 1

            for chart_id, count in chart_summary.items():
                log_info(f"  {chart_id}: {count} combinations")

            return chart_point_combinations

        except Exception as e:
            log_error(f" Error creating chart-point combinations: {str(e)}")
            return []

        finally:
            log_info("Finished creating chart-point combinations")
            await page.close()                
   
    async def apply_enhanced_legend_control(self, page):
        """Apply the enhanced legend control script to the page with robust chart detection"""
        try:
            # Wait for charts to be loaded with multiple attempts
            log_info("Waiting for charts to load...")

            # Try multiple selectors to find charts
            chart_found = False
            selectors_to_try = [
                'canvas.chartjs-render-monitor',
                'canvas[class*="chartjs"]',
                'canvas',
                '[id*="chart"]'
            ]
            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    chart_found = True
                    log_info(f" Found charts using selector: {selector}")
                    break
                except Exception:
                    continue

            if not chart_found:
                log_warn("No chart elements found with any selector")
                return False
            await asyncio.sleep(3)  # Give charts time to fully initialize
            # Execute the comprehensive chart detection script
            result = await page.evaluate("""
                (function() {
                    console.log('=== COMPREHENSIVE CHART DETECTION ===');

                    // Check Chart.js availability
                    const chartJsAvailable = typeof Chart !== 'undefined';
                    console.log('Chart.js available:', chartJsAvailable);

                    if (!chartJsAvailable) {
                        // Try to find Chart.js in window object
                        const chartKeys = Object.keys(window).filter(key => key.toLowerCase().includes('chart'));
                        console.log('Potential chart objects:', chartKeys);
                        return false;
                    }

                    // Initialize chart instances map
                    window.legendControlInitialized = true;
                    window.chartInstances = new Map();

                    // Find all possible canvas elements
                    const allCanvases = document.querySelectorAll('canvas');
                    const chartCanvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                    const anyChartCanvas = document.querySelectorAll('canvas[class*="chart"]');

                    console.log(`Canvas elements found:`);
                    console.log(`  - Total canvas: ${allCanvases.length}`);
                    console.log(`  - chartjs-render-monitor: ${chartCanvases.length}`);
                    console.log(`  - chart-related: ${anyChartCanvas.length}`);

                    // Use the most specific selector that found canvases
                    let canvasesToProcess = chartCanvases.length > 0 ? chartCanvases :
                                          anyChartCanvas.length > 0 ? anyChartCanvas : allCanvases;

                    console.log(`Processing ${canvasesToProcess.length} canvas elements`);

                    let successfullyRegistered = 0;

                    canvasesToProcess.forEach((canvas, index) => {
                        console.log(`\\n--- Processing Canvas ${index} ---`);

                        let chartInstance = null;
                        let detectionMethod = 'none';

                        // Method 1: Chart.getChart (Chart.js v3+)
                        try {
                            if (Chart.getChart) {
                                chartInstance = Chart.getChart(canvas);
                                if (chartInstance) {
                                    detectionMethod = 'Chart.getChart';
                                    console.log(`✓ Found via Chart.getChart`);
                                }
                            }
                        } catch (e) {
                            console.log(`✗ Chart.getChart failed:`, e.message);
                        }

                        // Method 2: Canvas chart property
                        if (!chartInstance) {
                            try {
                                if (canvas.chart) {
                                    chartInstance = canvas.chart;
                                    detectionMethod = 'canvas.chart';
                                    console.log(`✓ Found via canvas.chart`);
                                }
                            } catch (e) {
                                console.log(`✗ canvas.chart failed:`, e.message);
                            }
                        }

                        // Method 3: Canvas _chart property
                        if (!chartInstance) {
                            try {
                                if (canvas._chart) {
                                    chartInstance = canvas._chart;
                                    detectionMethod = 'canvas._chart';
                                    console.log(`✓ Found via canvas._chart`);
                                }
                            } catch (e) {
                                console.log(`✗ canvas._chart failed:`, e.message);
                            }
                        }

                        // Method 4: Search Chart.instances
                        if (!chartInstance) {
                            try {
                                if (Chart.instances) {
                                    Object.values(Chart.instances).forEach(instance => {
                                        if (instance && instance.canvas === canvas) {
                                            chartInstance = instance;
                                            detectionMethod = 'Chart.instances';
                                            console.log(`✓ Found via Chart.instances`);
                                        }
                                    });
                                }
                            } catch (e) {
                                console.log(`✗ Chart.instances search failed:`, e.message);
                            }
                        }

                        // Method 5: Check canvas data attributes or properties
                        if (!chartInstance) {
                            try {
                                // Look for any property that might contain a chart instance
                                const props = Object.getOwnPropertyNames(canvas);
                                for (const prop of props) {
                                    if (prop.includes('chart') || prop.includes('Chart')) {
                                        const value = canvas[prop];
                                        if (value && typeof value === 'object' && value.data && value.data.datasets) {
                                            chartInstance = value;
                                            detectionMethod = `canvas.${prop}`;
                                            console.log(`✓ Found via canvas.${prop}`);
                                            break;
                                        }
                                    }
                                }
                            } catch (e) {
                                console.log(`✗ Property search failed:`, e.message);
                            }
                        }

                        // Validate chart instance
                        if (chartInstance) {
                            console.log(`Chart instance found via: ${detectionMethod}`);

                            if (chartInstance.data && chartInstance.data.datasets && chartInstance.data.datasets.length > 0) {
                                // Use canvas ID or create consistent ID mapping
                                let chartId = canvas.id || `canvas-${index}`;

                                // Also register with alternative IDs for compatibility
                                const alternativeIds = [`chart_${index}`, `chart-${index}`, `canvas_${index}`];

                                window.chartInstances.set(chartId, {
                                    instance: chartInstance,
                                    canvas: canvas,
                                    detectionMethod: detectionMethod,
                                    originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                                });

                                // Register with alternative IDs
                                alternativeIds.forEach(altId => {
                                    window.chartInstances.set(altId, {
                                        instance: chartInstance,
                                        canvas: canvas,
                                        detectionMethod: detectionMethod,
                                        originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                                    });
                                });

                                console.log(` Registered ${chartId} (and alternatives: ${alternativeIds.join(', ')}):`);
                                console.log(`   - Datasets: ${chartInstance.data.datasets.length}`);
                                console.log(`   - Detection: ${detectionMethod}`);

                                chartInstance.data.datasets.forEach((dataset, dsIndex) => {
                                    console.log(`   - Dataset ${dsIndex}: "${dataset.label || 'Unnamed'}"`);
                                });

                                successfullyRegistered++;
                            } else {
                                console.log(`✗ Chart instance invalid (no datasets)`);
                            }
                        } else {
                            console.log(`✗ No chart instance found for canvas ${index}`);
                        }
                    });

                    console.log(`\\n=== DETECTION COMPLETE ===`);
                    console.log(`Successfully registered: ${successfullyRegistered} charts`);
                    console.log(`Chart IDs: [${Array.from(window.chartInstances.keys()).join(', ')}]`);

                    return successfullyRegistered > 0;
                })()
            """)

            if result:
                log_info(" Enhanced legend control applied successfully with comprehensive detection")
                return True
            else:
                log_info(" No chart instances found even with comprehensive detection")
                return False

        except Exception as e:
            log_error(f" Failed to apply enhanced legend control: {str(e)}")
            return False
    async def disable_all_legends(self, page):
        """Disable legends for all charts"""
        try:
            await page.evaluate("""
                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        
                        // Disable legend
                        if (!chart.options.plugins) chart.options.plugins = {};
                        if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                        chart.options.plugins.legend.display = false;
                        
                        // Update chart
                        chart.update('none');
                        
                        console.log(`Legend disabled for ${chartId}`);
                    });
                }
            """)
            
            log_info(" All legends disabled")
            return True
            
        except Exception as e:
            log_error(f" Failed to disable legends: {str(e)}")
            return False
     
    async def enable_only_target_legend(self, page, chart_id, target_dataset_label):
        """Enable only the target dataset legend and disable all others"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to enable legend for chart: {chart_id}, dataset: {target_dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found, attempting to reinitialize...');
                    return false;
                }}

                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;
                let actualChartId = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        actualChartId = id;
                        console.log(`Found chart with ID: ${{id}}`);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found with any ID variation');
                    console.log('Available charts:', Array.from(window.chartInstances.keys()));
                    return false;
                }}

                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure for', actualChartId);
                    return false;
                }}

                // Find target dataset index with fuzzy matching
                let targetDatasetIndex = -1;
                chart.data.datasets.forEach((dataset, index) => {{
                    const datasetLabel = dataset.label || '';
                    const targetLabel = '{target_dataset_label}';

                    // Exact match first
                    if (datasetLabel === targetLabel) {{
                        targetDatasetIndex = index;
                        return;
                    }}

                    // Fuzzy match (contains or similar)
                    if (datasetLabel.includes(targetLabel) || targetLabel.includes(datasetLabel)) {{
                        targetDatasetIndex = index;
                        console.log(`Fuzzy match found: "${{datasetLabel}}" matches "${{targetLabel}}"`);
                    }}
                }});

                if (targetDatasetIndex === -1) {{
                    console.log('Target dataset not found: {target_dataset_label}');
                    console.log('Available datasets:', chart.data.datasets.map(d => d.label));

                    // Try to find any dataset and use the first one as fallback
                    if (chart.data.datasets.length > 0) {{
                        targetDatasetIndex = 0;
                        console.log('Using first dataset as fallback:', chart.data.datasets[0].label);
                    }} else {{
                        return false;
                    }}
                }}

                try {{
                    // Show only the target dataset
                    chart.data.datasets.forEach((dataset, index) => {{
                        const meta = chart.getDatasetMeta(index);
                        if (meta) {{
                            meta.hidden = (index !== targetDatasetIndex);
                        }}
                    }});

                    // Enable legend but filter to show only target dataset
                    if (!chart.options.plugins) chart.options.plugins = {{}};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {{}};

                    chart.options.plugins.legend.display = true;
                    chart.options.plugins.legend.labels = {{
                        filter: function(legendItem) {{
                            return legendItem.datasetIndex === targetDatasetIndex;
                        }}
                    }};

                    // Update chart
                    chart.update('none');

                    const actualDatasetLabel = chart.data.datasets[targetDatasetIndex].label;
                    console.log('Successfully enabled legend for:', actualDatasetLabel, '(index:', targetDatasetIndex, ')');
                    return true;
                }} catch (error) {{
                    console.error('Error updating chart:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                log_info(f" Enabled only legend for {target_dataset_label} in chart {chart_id}")
                return True
            else:
                log_error(f"Failed to enable legend for {target_dataset_label} in chart {chart_id}")
                return False

        except Exception as e:
            log_error(f" Error enabling legend for {target_dataset_label}: {str(e)}")
            return False

    async def debug_legend_control(self, page):
        """Debug function to check legend control setup"""
        try:
            debug_info = await page.evaluate("""
            (function() {
                const info = {
                    chartInstancesExists: !!window.chartInstances,
                    chartInstancesSize: window.chartInstances ? window.chartInstances.size : 0,
                    chartIds: window.chartInstances ? Array.from(window.chartInstances.keys()) : [],
                    chartDetails: []
                };

                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        info.chartDetails.push({
                            id: chartId,
                            hasInstance: !!chart,
                            hasData: !!(chart && chart.data),
                            datasetCount: chart && chart.data && chart.data.datasets ? chart.data.datasets.length : 0,
                            datasetLabels: chart && chart.data && chart.data.datasets ? chart.data.datasets.map(d => d.label) : []
                        });
                    });
                }

                return info;
            })()
            """)
            
            for chart_detail in debug_info.get('chartDetails', []):
                chart_id = chart_detail.get('id', 'Unknown')
                dataset_count = chart_detail.get('datasetCount', 0)
                dataset_labels = chart_detail.get('datasetLabels', [])                
            return debug_info

        except Exception as e:
            log_error(f" Error debugging legend control: {str(e)}")
            return None

    async def click_data_point_for_drilldown(self, page, chart_id, point_data):
        """Click on a specific data point to trigger drilldown navigation"""
        try:
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')

            # First try using the pre-calculated screen coordinates
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    log_info(f"Clicking at pre-calculated coordinates: ({screen_x}, {screen_y})")
                    await page.mouse.click(screen_x, screen_y)
                    await asyncio.sleep(1)
                    return {
                        'success': True,
                        'method': 'pre_calculated_coordinates',
                        'position': {'x': screen_x, 'y': screen_y},
                        'dataset_label': dataset_label,
                        'x_label': x_label
                    }
                except Exception as coord_error:
                    log_error(f"Pre-calculated coordinates failed: {coord_error}")

            # Fallback to JavaScript-based clicking
            click_result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to click data point for chart: {chart_id}, dataset: {dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found');
                    return {{ success: false, error: 'Chart instances not initialized' }};
                }}

                if (!window.chartInstances.has('{chart_id}')) {{
                    console.log('Chart {chart_id} not found');
                    console.log('Available charts:', Array.from(window.chartInstances.keys()));
                    return {{ success: false, error: 'Chart instance not found' }};
                }}

                const chartData = window.chartInstances.get('{chart_id}');
                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure');
                    return {{ success: false, error: 'Invalid chart data' }};
                }}

                try {{
                    // Find target dataset index by label
                    let targetDatasetIndex = {dataset_index};
                    chart.data.datasets.forEach((dataset, index) => {{
                        if (dataset.label === '{dataset_label}') {{
                            targetDatasetIndex = index;
                        }}
                    }});

                    // Get dataset meta
                    const meta = chart.getDatasetMeta(targetDatasetIndex);
                    if (!meta || !meta.data || !meta.data[{point_index}]) {{
                        console.log('Data point not found at index: {point_index}');
                        return {{ success: false, error: 'Data point not found' }};
                    }}

                    // Get point element and position
                    const pointElement = meta.data[{point_index}];
                    const pointPosition = pointElement.getCenterPoint();
                    const canvas = chart.canvas;

                    // Create click event
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {{
                        clientX: rect.left + pointPosition.x,
                        clientY: rect.top + pointPosition.y,
                        bubbles: true,
                        cancelable: true
                    }});

                    // Dispatch click event
                    canvas.dispatchEvent(clickEvent);

                    console.log('Successfully clicked data point: {x_label} from {dataset_label}');
                    return {{
                        success: true,
                        method: 'javascript_click',
                        position: pointPosition,
                        dataset_label: '{dataset_label}',
                        x_label: '{x_label}'
                    }};

                }} catch (error) {{
                    console.error('Error clicking data point:', error);
                    return {{ success: false, error: error.message }};
                }}
            }})()
            """)

            if click_result.get('success', False):
                log_info(f" Data point clicked for drilldown: {x_label} from {dataset_label}")
                return click_result
            else:
                log_error(f"Failed to click data point: {click_result.get('error', 'Unknown error')}")
                return click_result

        except Exception as e:
            log_error(f" Error clicking data point for drilldown: {str(e)}")
            return {'success': False, 'error': str(e)}

 
    async def process_single_point_task_with_selective_legend(self, page, task, target_month_year):
        """Process a single point task with selective legend control and drilldown"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        try:
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')         
            # Step 1: Enable only the target legend, disable all others
            legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
            if not legend_enabled:
                log_error(f"{task_id}: Legend control failed, continuing anyway")
            # Wait for legend update
            await asyncio.sleep(1)
            # Debug: Check if the chart area is visible and clickable
            await self.debug_chart_clickability(page, chart_id, point_data)
            # Step 2: Click data point and wait for drilldown navigation
            click_result = None
            navigation_result = None
            # Get current URL before clicking
            initial_url = page.url
            log_info(f"{task_id}: Current URL before click: {initial_url}")
            # Method 1: Use pre-calculated coordinates (most reliable)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    await page.mouse.click(screen_x, screen_y)                   
                    await asyncio.sleep(3)  # Give time for navigation to start
                    # Check if URL changed
                    current_url = page.url
                    if current_url != initial_url:
                        log_info(f" {task_id}: Navigation detected to: {current_url}")
                        # Wait for page to fully load
                        try:
                            # await page.wait_for_load_state("networkidle", timeout=10000)
                            await asyncio.sleep(2)
                        except Exception:
                            pass  # Continue even if load state fails

                        click_result = {
                            'success': True,
                            'method': 'pre_calculated_coordinates',
                            'coordinates': {'x': screen_x, 'y': screen_y}
                        }

                        navigation_result = {
                            'success': True,
                            'url': current_url,
                            'navigation_completed': True,
                            'initial_url': initial_url
                        }

                    else:
                        log_info(f"{task_id}: No navigation detected after coordinate click")
                        # Wait a bit more and check again
                        await asyncio.sleep(2)
                        current_url = page.url

                        if current_url != initial_url:
                            log_info(f" {task_id}: Delayed navigation detected to: {current_url}")
                            click_result = {'success': True, 'method': 'pre_calculated_coordinates'}
                            navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                        else:
                            log_info(f" {task_id}: No navigation after coordinate click")
                            click_result = {'success': False, 'error': 'No navigation detected after coordinate click'}
                            navigation_result = {'success': False, 'error': 'No URL change detected'}

                except Exception as coord_error:
                    log_error(f"{task_id}: Coordinate clicking failed: {coord_error}")
                    click_result = {'success': False, 'error': str(coord_error)}

            # Method 2: Fallback to JavaScript-based clicking
            if not click_result or not click_result.get('success', False):
                log_info(f"{task_id}: Trying JavaScript-based clicking...")

                try:
                    # Try JavaScript clicking
                    js_click_result = await self.click_data_point_for_drilldown(page, chart_id, point_data)

                    if js_click_result.get('success', False):
                        log_info(f"{task_id}: JavaScript click executed, waiting for navigation...")

                        # Wait for potential navigation
                        await asyncio.sleep(3)
                        # Check if URL changed
                        current_url = page.url
                        if current_url != initial_url:                           
                            # Wait for page to fully load
                            try:
                                # await page.wait_for_load_state("networkidle", timeout=10000)
                                await asyncio.sleep(2)
                            except Exception:
                                pass
                            click_result = js_click_result
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                        else:                           
                            # Wait a bit more and check again
                            await asyncio.sleep(2)
                            current_url = page.url
                            if current_url != initial_url:
                                log_info(f" {task_id}: Delayed JavaScript navigation detected to: {current_url}")
                                click_result = js_click_result
                                navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                            else:
                                click_result = {'success': False, 'error': 'JavaScript click no navigation'}
                                navigation_result = {'success': False, 'error': 'No URL change after JavaScript click'}
                    else:
                        click_result = js_click_result
                        navigation_result = {'success': False, 'error': 'JavaScript click failed'}

                except Exception as js_error:
                    log_error(f" {task_id}: JavaScript clicking failed: {js_error}")
                    click_result = {'success': False, 'error': str(js_error)}
                    navigation_result = {'success': False, 'error': str(js_error)}

            # Method 3: Fallback to simple click with longer wait
            if not click_result or not click_result.get('success', False):
                log_info(f"{task_id}: Trying simple click with extended wait...")
                try:
                    # Simple click without complex navigation detection
                    await page.mouse.click(screen_x, screen_y)
                    log_info(f"{task_id}: Simple click executed, waiting longer for navigation...")
                    # Wait longer for navigation
                    await asyncio.sleep(5)
                    # Check URL multiple times
                    for attempt in range(3):
                        current_url = page.url
                        if current_url != initial_url:
                            click_result = {
                                'success': True,
                                'method': 'simple_click_extended_wait',
                                'coordinates': {'x': screen_x, 'y': screen_y}
                            }
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                            break

                        if attempt < 2:  # Don't wait after last attempt
                            await asyncio.sleep(2)

                    if not click_result or not click_result.get('success', False):
                        log_info(f" {task_id}: Simple click also failed to trigger navigation")
                        click_result = {'success': False, 'error': 'Simple click no navigation'}
                        navigation_result = {'success': False, 'error': 'No URL change after simple click'}
                except Exception as simple_error:
                    log_error(f" {task_id}: Simple clicking failed: {simple_error}")
                    click_result = {'success': False, 'error': str(simple_error)}
                    navigation_result = {'success': False, 'error': str(simple_error)}

            # Check if any clicking method succeeded
            if not click_result or not click_result.get('success', False):
                error_msg = click_result.get('error', 'All clicking methods failed') if click_result else 'No click result'
                log_error(f" {task_id}: Failed to click data point: {error_msg}")
                return {
                    'task_id': task_id,
                    'chart_id': chart_id,
                    'error': f"Failed to click data point: {error_msg}",
                    'success': False,
                    'legend_controlled': legend_enabled
                }
            # Check navigation result
            if not navigation_result or not navigation_result.get('success', False):
                log_info(f"{task_id}: Navigation failed, but click was successful - attempting data extraction anyway")
                # Try to extract data from current page
                current_url = page.url
                navigation_result = {
                    'success': False,
                    'url': current_url,
                    'navigation_completed': False,
                    'error': 'Navigation failed but click succeeded'
                }

            # Step 3: Extract data from the drilldown page
            extracted_data = None
            extraction_success = False
            # Only attempt extraction if navigation was successful
            if navigation_result and navigation_result.get('success', False):
                current_url = page.url
                # Check if we're on the expected drilldown page
                if "AnalyzeData" in current_url and "chartId=drillDown" in current_url:
                    log_info(f" {task_id}: On correct drilldown page, extracting data...")
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year, chart_id, dataset_label)
                    # Check extraction success from the nested structure
                    extraction_success = False
                    if extracted_data and extracted_data.get('extraction_data'):
                        extraction_success = extracted_data['extraction_data'].get('success', False)
                    if extraction_success:
                        log_info(f" {task_id}: Data extraction successful")
                    else:
                        log_error(f"{task_id}: Data extraction failed or incomplete")
                else:
                    log_info(f"{task_id}: Not on expected drilldown page. URL: {current_url}")
                    extracted_data = {
                        'extraction_data': {
                            'success': False,
                            'error': f'Unexpected page URL: {current_url}',
                            'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
                        }
                    }
                    extraction_success = False
            else:
                log_error(f" {task_id}: Skipping data extraction due to navigation failure")
                extracted_data = {
                    'extraction_data': {
                        'success': False,
                        'error': 'Navigation failed, cannot extract data',
                        'navigation_error': navigation_result.get('error', 'Unknown navigation error') if navigation_result else 'No navigation result'
                    }
                }
                extraction_success = False
            # Determine overall success
            overall_success = (
                click_result.get('success', False) and
                navigation_result.get('success', False) and
                extraction_success
            )
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'click_result': click_result,
                'navigation_result': navigation_result,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': overall_success,
                'legend_controlled': legend_enabled,
                'drilldown_url': navigation_result.get('url', '') if navigation_result else '',
                'click_success': click_result.get('success', False) if click_result else False,
                'navigation_success': navigation_result.get('success', False) if navigation_result else False,
                'extraction_success': extraction_success
            }
            log_info(f" {task_id}: Successfully processed {point_label} from {dataset_label}")
            return result
        except Exception as e:
            log_info(f" {task_id}: Error processing point: {e}")
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': False
            }
            return error_result

    async def extract_data_from_drilldown_page(self, page, point_data, target_month_year, chart_id=None, dataset_label=None):
        """Extract AG-Grid data from drilldown page with chart-specific field extraction"""
        log_info(f"🔍 Starting data extraction - Chart ID: {chart_id}, Dataset: {dataset_label}")
        max_retries = 3
        retry_delay = 2

        async def extract_from_ag_grid_table(page, target_month_year, chart_id):
            """Helper function to extract data directly from AG-Grid table for specific chart"""
            try:
                # Wait for AG-Grid to load
                await page.wait_for_selector('.ag-root-wrapper', timeout=10000)
                log_info("AG-Grid detected on page")
                await asyncio.sleep(2)
                
                # Format target month for comparison (e.g., "07/25")
                target_dt = datetime.strptime(target_month_year, "%Y-%m-%d")
                target_month_short = target_dt.strftime("%m/%y")
                
                log_info(f"Looking for month: {target_month_short} in AG-Grid table for chart {chart_id}")
                
                # Get all rows from AG-Grid
                rows = await page.query_selector_all('.ag-center-cols-container .ag-row')
                log_info(f"Found {len(rows)} rows in AG-Grid")
                
                target_row_data = None
                
                # Define all column mappings
                all_column_mappings = {
                    '1115': {
                        'display_name': 'Overall Discount Sale %',
                        'field_key': 'percentagepertotalcpsale'
                    },
                    '1234': {
                        'display_names': ['Total Labor Discount', 'Total Parts Discount', 'Total Discount'],
                        'field_keys': ['totallabordiscount', 'totalpartsdiscount', 'totaldiscount']
                    },
                    '1232': {
                        'display_name': 'Overall Discount %',
                        'field_key': 'discountpercentageperdiscountcpsale'
                    },
                    '1165': {
                        'display_name': 'Overall Discount per CP RO',
                        'field_key': 'totaldiscountsperdiscountedcpro'
                    }
                }
                
                # Get the mapping for the specific chart_id
                chart_mapping = all_column_mappings.get(str(chart_id))
                if not chart_mapping:
                    log_error(f"No column mapping found for chart_id: {chart_id}")
                    return None
                
                for row_index, row in enumerate(rows):
                    try:
                        # Scroll row into view
                        await row.scroll_into_view_if_needed()
                        await asyncio.sleep(0.1)
                        
                        # Get the month cell (first column)
                        month_cell = await row.query_selector('[col-id="monthYear"]')
                        if not month_cell:
                            continue
                        
                        month_value = (await month_cell.text_content()).strip()
                        
                        # Check if this is the target month
                        if month_value == target_month_short:
                            log_info(f"✅ Found target month: {target_month_short} in row {row_index}")
                            
                            # Extract data from this row based on chart_id
                            row_data = {}
                            
                            # Handle both single and multiple field keys
                            if 'field_keys' in chart_mapping:
                                # Multiple columns case (like chart '1234')
                                display_names = chart_mapping['display_names']
                                field_keys = chart_mapping['field_keys']
                                
                                for i, field_key in enumerate(field_keys):
                                    cell = await row.query_selector(f'[col-id="{field_key}"]')
                                    if cell:
                                        cell_value = (await cell.text_content()).strip()
                                        row_data[display_names[i]] = cell_value
                                        log_info(f"   {display_names[i]}: {cell_value}")
                            else:
                                # Single column case (like charts '1115', '1232', '1165')
                                display_name = chart_mapping['display_name']
                                field_key = chart_mapping['field_key']
                                
                                cell = await row.query_selector(f'[col-id="{field_key}"]')
                                if cell:
                                    cell_value = (await cell.text_content()).strip()
                                    row_data[display_name] = cell_value
                                    log_info(f"   {display_name}: {cell_value}")
                            
                            target_row_data = row_data
                            break
                                
                    except Exception as row_error:
                        log_error(f"Error processing row {row_index}: {row_error}")
                        continue
                
                return target_row_data
                
            except Exception as e:
                log_error(f"Error extracting from AG-Grid table: {e}")
                return None
        async def extract_from_ag_grid(page):
            """Helper function to extract data from AG-Grid"""
            try:
                # Wait for AG-Grid to load
                grid_found = False
                grid_selectors = ['.ag-root-wrapper', '.ag-theme-balham', '[class*="ag-theme"]', '.ag-grid-container']
                
                for selector in grid_selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=5000)
                        log_info(f"Found AG-Grid with selector: {selector}")
                        grid_found = True
                        break
                    except:
                        continue
                
                if not grid_found:
                    return None
                
                # Get grid data using JavaScript
                grid_data = await page.evaluate("""
                    () => {
                        try {
                            // Find all grid divs
                            const gridDivs = document.querySelectorAll('.ag-root-wrapper');
                            const results = [];
                            
                            for (const gridDiv of gridDivs) {
                                // Get grid instance
                                const gridInstance = gridDiv.__agComponent;
                                if (!gridInstance || !gridInstance.api) continue;
                                
                                const api = gridInstance.api;
                                
                                // Get column definitions
                                const columnDefs = api.getColumnDefs();
                                
                                // Get row data
                                const rowData = [];
                                api.forEachNode(node => {
                                    if (node.data) {
                                        rowData.push(node.data);
                                    }
                                });
                                
                                results.push({
                                    columns: columnDefs,
                                    rows: rowData,
                                    columnState: api.getColumnState()
                                });
                            }
                            
                            return results;
                        } catch (e) {
                            console.error('Error getting grid data:', e);
                            return null;
                        }
                    }
                """)
                
                return grid_data
                
            except Exception as e:
                log_error(f"Error extracting from AG-Grid: {str(e)}")
                return None
        
        # Define chart-specific field mappings with additional selectors
        
        # Normalize chart ID by removing any prefix
        normalized_chart_id = str(chart_id).replace('chart_', '') if chart_id else ''
        log_info(f"Original chart_id: {chart_id}, Normalized chart_id: {normalized_chart_id}")
        
        # Special handling for chart 1115 - extract from table
        # Define column mappings for each chart ID
        CHART_COLUMN_MAPPINGS = {
            '1115': {
                'columns': ['percentagepertotalcpsale'],
                'display_name': 'Overall Discount Sale %',
                'field_key': 'percentagepertotalcpsale'
            },
            '1234': {
                'columns': ['totallabordiscount', 'totalpartsdiscount', 'totaldiscount'],
                'display_names': ['Total Labor Discount', 'Total Parts Discount', 'Total Discount'],
                'field_keys': ['totallabordiscount', 'totalpartsdiscount', 'totaldiscount']
            },
            '1232': {
                'columns': ['discountpercentageperdiscountcpsale'],
                'display_name': 'Overall Discount %',
                'field_key': 'discountpercentageperdiscountcpsale'
            },
            '1165': {
                'columns': ['totaldiscountsperdiscountedcpro'],
                'display_name': 'Overall Discount per CP RO',
                'field_key': 'totaldiscountsperdiscountedcpro'
            }
        }


        if normalized_chart_id in ['1115', '1234', '1232', '1165']:
            log_info(f"🎯 Chart {normalized_chart_id} detected! Extracting data from AG-Grid table for month: {target_month_year}")
            
            # Get column configuration for this chart
            chart_config = CHART_COLUMN_MAPPINGS.get(normalized_chart_id)
            
            if not chart_config:
                log_error(f"No column mapping found for chart {normalized_chart_id}")
                return {
                    'success': False,
                    'error': f'No column mapping configured for chart {normalized_chart_id}'
                }
            
            for attempt in range(max_retries):
                try:
                    log_info(f"Extraction attempt {attempt + 1}/{max_retries}")
                    
                    # Wait for page to load
                    await asyncio.sleep(3)
                    
                    # Extract data from AG-Grid table
                    table_data = await extract_from_ag_grid_table(page, target_month_year, normalized_chart_id)
                    
                    if table_data:
                        # Handle single column extraction (1115, 1232, 1165)
                        if len(chart_config['columns']) == 1:
                            column_name = chart_config['columns'][0]
                            display_name = chart_config['display_name']
                            
                            # Get the extracted value from table_data
                            extracted_value = table_data.get(display_name, '0.00')  # ✅ USE THE DISPLAY NAME KEY
                            
                            # Format the extraction data
                            extraction_data = {
                                "extraction_timestamp": datetime.now().isoformat(),
                                "page_url": page.url,
                                "success": True,
                                "chart_id": normalized_chart_id,
                                "target_month_year": target_month_year,
                                "dataset_label": display_name,
                                "extracted_value": extracted_value,  # ✅ USE EXTRACTED VALUE
                                "extracted_field": chart_config['field_key'],
                                "full_row_data": table_data,
                                "mui_grid_data": [
                                    {
                                        "container_index": 0,
                                        "selector_used": "ag-grid-table",
                                        "items": [
                                            {
                                                "item_index": 0,
                                                "title": display_name,
                                                "value": extracted_value,  # ✅ FIX: Use actual extracted value, not "0.00"
                                                "html_structure": {
                                                    "h5_html": display_name,
                                                    "h6_html": extracted_value  # ✅ FIX: Use actual extracted value
                                                }
                                            }
                                        ]
                                    }
                                ],
                                "total_items_found": 1
                            }
                            
                            log_info(f"✅ Successfully extracted {display_name}: {extracted_value}")
                        
                        # ✅ FIX: Handle multiple column extraction (1234) with dataset_label filtering
                        else:
                            extracted_values = {}
                            mui_items = []
                            
                            # ✅ NEW: Filter based on dataset_label if provided
                            if dataset_label and normalized_chart_id == '1234':
                                # Only extract the specific column that matches the clicked dataset
                                if dataset_label in table_data:
                                    # Find the index of the matching dataset
                                    try:
                                        dataset_idx = chart_config['display_names'].index(dataset_label)
                                        field_key = chart_config['field_keys'][dataset_idx]
                                        value = table_data.get(dataset_label, '0.00')
                                        
                                        extracted_values[field_key] = value
                                        mui_items.append({
                                            "item_index": 0,
                                            "title": dataset_label,
                                            "value": value,
                                            "html_structure": {
                                                "h5_html": dataset_label,
                                                "h6_html": value
                                            }
                                        })
                                        log_info(f"✅ Extracted specific dataset: {dataset_label} = {value}")
                                    except ValueError:
                                        log_error(f"Dataset label '{dataset_label}' not found in display_names")
                                else:
                                    log_error(f"Dataset label '{dataset_label}' not found in table_data")
                            else:
                                # Extract all columns if no specific dataset_label or not chart 1234
                                for idx, display_name in enumerate(chart_config['display_names']):
                                    value = table_data.get(display_name, '0.00')
                                    extracted_values[chart_config['field_keys'][idx]] = value
                                    
                                    mui_items.append({
                                        "item_index": idx,
                                        "title": display_name,
                                        "value": value,
                                        "html_structure": {
                                            "h5_html": display_name,
                                            "h6_html": value
                                        }
                                    })
                            
                            extraction_data = {
                                "extraction_timestamp": datetime.now().isoformat(),
                                "page_url": page.url,
                                "success": True,
                                "chart_id": normalized_chart_id,
                                "target_month_year": target_month_year,
                                "dataset_labels": [dataset_label] if dataset_label and len(mui_items) == 1 else chart_config['display_names'],
                                "extracted_values": extracted_values,
                                "extracted_fields": list(extracted_values.keys()),
                                "full_row_data": table_data,
                                "mui_grid_data": [
                                    {
                                        "container_index": 0,
                                        "selector_used": "ag-grid-table",
                                        "items": mui_items
                                    }
                                ],
                                "total_items_found": len(mui_items)
                            }
                            
                            log_info(f"✅ Successfully extracted {len(mui_items)} value(s) for chart {normalized_chart_id}")
                            for field_key, value in extracted_values.items():
                                log_info(f"   - {field_key}: {value}")               
                        return {
                            'success': True,
                            'chart_id': normalized_chart_id,
                            'point_data': point_data,
                            'click_success': True,
                            'navigation_success': True,
                            'extraction_data': extraction_data,
                            'error': None
                        }
                    else:
                        log_warn(f"No data extracted for {target_month_year} on attempt {attempt + 1}")
                        
                        if attempt < max_retries - 1:
                            await asyncio.sleep(retry_delay)
                            continue
                        
                except Exception as e:
                    log_error(f"Error in extraction attempt {attempt + 1}: {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        continue
            
            # All retries failed
            log_error(f"All {max_retries} attempts failed for chart {normalized_chart_id}")
            return {
                'target_month_year': target_month_year,
                'point_data': point_data,
                'click_success': True,
                'navigation_success': True,
                'extraction_data': {
                    "extraction_timestamp": datetime.now().isoformat(),
                    "success": False,
                    "error": f"Failed to extract data from AG-Grid table after all retries",
                    "chart_id": normalized_chart_id,
                    "attempt": max_retries
                },
                'error': f"Maximum retries exceeded for chart {normalized_chart_id}"
            }
        # For other charts, continue with existing logic
        for attempt in range(max_retries):
            try:
                log_info(f"Extracting drill-down page data... (Attempt {attempt + 1}/{max_retries})")

                # Add wait for page load
                await asyncio.sleep(3)
                
                extraction_data = {
                    "extraction_timestamp": datetime.now().isoformat(),
                    "page_url": page.url,
                    "mui_grid_data": [],
                    "success": False,
                    "error": None,
                    "attempt": attempt + 1,
                    "chart_id": normalized_chart_id,
                    "dataset_label": dataset_label,
                    "target_month_year": target_month_year,
                    "extracted_value": None
                }
                
                log_info(f"Chart ID: {normalized_chart_id}, Target Month: {target_month_year}, Dataset Label: {dataset_label}")
                
                # Wait for page elements to load
                selectors = [
                    '.MuiGrid-container',
                    '[class*="MuiGrid-container"]',
                    'h5.MuiTypography-h5',
                    'h6.MuiTypography-subtitle1',
                    '.ag-root-wrapper'
                ]
                for selector in selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=5000)
                    except:
                        continue
                
               
                # Fallback: Extract MUI Grid data only if AG-Grid extraction failed
                log_info("AG-Grid extraction failed, attempting MUI Grid extraction as fallback...")
                mui_grid_selectors = [
                    '.MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3',
                    '.MuiGrid-root.MuiGrid-container',
                    '[class*="MuiGrid-container"]'
                ]

                for selector in mui_grid_selectors:
                    try:
                        grid_containers = await page.query_selector_all(selector)
                        log_info(f"Found {len(grid_containers)} grid containers with selector: {selector}")
                        
                        for container_index, container in enumerate(grid_containers):
                            if await container.is_visible():
                                grid_items = await container.query_selector_all('.MuiGrid-item')
                                
                                container_data = {
                                    "container_index": container_index,
                                    "selector_used": selector,
                                    "items": []
                                }

                                for item_index, item in enumerate(grid_items):
                                    h5_element = await item.query_selector('h5.MuiTypography-root.MuiTypography-h5')
                                    h6_element = await item.query_selector('h6.MuiTypography-root.MuiTypography-subtitle1')
                                    
                                    if h5_element and h6_element:
                                        title = (await h5_element.text_content()).strip()
                                        value = (await h6_element.text_content()).strip()
                                        
                                        # Skip unwanted header items
                                        if "Fixed Ops" in title or "Version" in title:
                                            continue
                                            
                                        # For charts with specific mappings, check if title matches expected titles
                                        
                                        item_data = {
                                            "item_index": item_index,
                                            "title": title,
                                            "value": value,
                                            "html_structure": {
                                                "h5_html": await h5_element.inner_html(),
                                                "h6_html": await h6_element.inner_html()
                                            }
                                        }
                                        container_data["items"].append(item_data)
                                        log_info(f"Added item - Title: {title}, Value: {value}")

                                if container_data["items"]:
                                    extraction_data["mui_grid_data"].append(container_data)

                    except Exception as selector_error:
                        log_error(f"Error with selector {selector}: {selector_error}")
                        continue

                # Also try direct h5/h6 extraction
                try:
                    h5_elements = await page.query_selector_all('h5.MuiTypography-h5')
                    h6_elements = await page.query_selector_all('h6.MuiTypography-subtitle1')

                    direct_data = {
                        "container_index": "direct",
                        "selector_used": "direct_elements",
                        "items": []
                    }

                    for i, (h5, h6) in enumerate(zip(h5_elements, h6_elements)):
                        if h5 and h6:
                            title = (await h5.text_content()).strip()
                            value = (await h6.text_content()).strip()

                            item_data = {
                                "item_index": i,
                                "title": title,
                                "value": value,
                                "html_structure": {
                                    "h5_html": await h5.inner_html(),
                                    "h6_html": await h6.inner_html()
                                }
                            }
                            direct_data["items"].append(item_data)

                    if direct_data["items"]:
                        extraction_data["mui_grid_data"].append(direct_data)

                except Exception as direct_error:
                    log_error(f"Error in direct element extraction: {direct_error}")

                # Determine success
                extraction_data["success"] = (
                    len(extraction_data["mui_grid_data"]) > 0 or 
                    extraction_data["extracted_value"] is not None
                )
                extraction_data["total_items_found"] = sum(
                    len(container["items"]) for container in extraction_data["mui_grid_data"]
                )
                
                log_info(f"Extraction success: {extraction_data['success']}")
                log_info(f"Total items found: {extraction_data['total_items_found']}")

                # If successful, return result immediately
                if extraction_data["success"]:
                    log_info(f"Data extraction successful on attempt {attempt + 1}")
                    return {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': extraction_data,
                        'error': None
                    }

                # If not successful and not the last attempt, wait and retry
                if attempt < max_retries - 1:
                    log_error(f"Attempt {attempt + 1} failed, retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)

            except Exception as e:
                log_error(f"Error in attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                    continue
        
        # Return failure result after all retries
        log_error(f"All {max_retries} attempts failed")
        return {
            'target_month_year': target_month_year,
            'point_data': point_data,
            'click_success': True,
            'navigation_success': True,
            'extraction_data': {
                "extraction_timestamp": datetime.now().isoformat(),
                "success": False,
                "error": "Maximum retries exceeded",
                "attempt": max_retries
            },
            'error': "Maximum retries exceeded"
        }

    async def extract_ag_grid_field_for_month(self, page, target_month_year, col_variations):
        """
        Extract a specific field value from AG-Grid for the target month
        with horizontal scrolling support
        
        Args:
            page: Playwright page object
            target_month_year: Target month in format like "09/25"
            col_variations: List of possible column IDs to search for
        
        Returns:
            The extracted value as a string, or None if not found
        """
        try:
            log_info(f"🔍 Searching for columns {col_variations} in month {target_month_year}")
            
            # Wait for AG-Grid to be visible
            await page.wait_for_selector('.ag-center-cols-container', timeout=5000)
            await asyncio.sleep(1)
            
            # Helper function to scroll AG-Grid horizontally
            async def scroll_to_find_column(col_variations):
                """Scroll horizontally to find column"""
                scroll_viewport = await page.query_selector('.ag-body-horizontal-scroll-viewport')
                if not scroll_viewport:
                    scroll_viewport = await page.query_selector('.ag-center-cols-viewport')
                
                if not scroll_viewport:
                    log_error("Could not find scroll viewport")
                    return None
                
                # Reset scroll to start
                await page.evaluate('document.querySelector(".ag-body-horizontal-scroll-viewport")?.scrollTo(0, 0) || document.querySelector(".ag-center-cols-viewport")?.scrollTo(0, 0)')
                await asyncio.sleep(0.5)
                
                max_attempts = 20
                scroll_step = 200
                
                for attempt in range(max_attempts):
                    # Check if any column variation is visible
                    for col_id in col_variations:
                        header = await page.query_selector(f'[col-id="{col_id}"]')
                        if header:
                            try:
                                is_visible = await header.is_visible()
                                if is_visible:
                                    log_info(f"✅ Found column {col_id} after {attempt} scroll attempts")
                                    return col_id
                            except:
                                continue
                    
                    # Scroll right
                    await scroll_viewport.evaluate(f'el => el.scrollLeft += {scroll_step}')
                    await asyncio.sleep(0.3)
                    log_info(f"Scrolling right to find column (attempt {attempt + 1}/{max_attempts})")
                
                log_error(f"Column not found after {max_attempts} scroll attempts")
                return None
            
            # Find the target column
            found_col_id = await scroll_to_find_column(col_variations)
            
            if not found_col_id:
                log_error(f"Could not find any of the column variations: {col_variations}")
                return None
            
            log_info(f"Using column ID: {found_col_id}")
            
            # Now find the target month row
            # Reset scroll to see month column
            await page.evaluate('document.querySelector(".ag-body-horizontal-scroll-viewport")?.scrollTo(0, 0) || document.querySelector(".ag-center-cols-viewport")?.scrollTo(0, 0)')
            await asyncio.sleep(0.5)
            
            # Get all rows
            rows = await page.query_selector_all('.ag-center-cols-container .ag-row')
            log_info(f"Found {len(rows)} rows in AG-Grid")
            
            target_row = None
            for row_index, row in enumerate(rows):
                try:
                    # Scroll row into view
                    await row.scroll_into_view_if_needed()
                    await asyncio.sleep(0.1)
                    
                    # Get the month cell (first column with col-id="monthYear")
                    month_cell = await row.query_selector('[col-id="monthYear"]')
                    if not month_cell:
                        continue
                    
                    month_value = (await month_cell.text_content()).strip()
                    log_info(f"Row {row_index}: Month = {month_value}")
                    
                    # Check if this is the target month
                    if month_value == target_month_year:
                        log_info(f"✅ Found target month: {target_month_year} in row {row_index}")
                        target_row = row
                        break
                        
                except Exception as row_error:
                    log_error(f"Error processing row {row_index}: {row_error}")
                    continue
            
            if not target_row:
                log_error(f"❌ Target month {target_month_year} not found in AG-Grid")
                return None
            
            # Now scroll to make the target column visible and extract value
            log_info(f"Scrolling to make column {found_col_id} visible...")
            found_col_id_visible = await scroll_to_find_column(col_variations)
            
            if not found_col_id_visible:
                log_error("Could not make target column visible")
                return None
            
            # Scroll the target row into view again
            await target_row.scroll_into_view_if_needed()
            await asyncio.sleep(0.3)
            
            # Get the target field cell
            field_cell = await target_row.query_selector(f'[col-id="{found_col_id}"]')
            if field_cell:
                try:
                    is_visible = await field_cell.is_visible()
                    if is_visible:
                        field_value = (await field_cell.text_content()).strip()
                        log_info(f"✅ Extracted value: {field_value}")
                        return field_value
                    else:
                        log_error("Field cell found but not visible")
                except Exception as e:
                    log_error(f"Error getting cell value: {e}")
            else:
                log_error(f"❌ Field column {found_col_id} not found in target row")
            
            return None
            
        except Exception as e:
            log_error(f"Error extracting AG-Grid field: {e}")
            return None
    

    async def debug_and_setup_charts(self, page):
        """Debug and manually setup chart instances"""
        try:
            result = await page.evaluate("""
                (function() {
                    console.log('=== CHART DEBUG AND SETUP ===');

                    // Check if Chart.js is available
                    console.log('Chart.js available:', typeof Chart !== 'undefined');

                    // Find all canvas elements
                    const allCanvases = document.querySelectorAll('canvas');
                    console.log(`Total canvas elements found: ${allCanvases.length}`);

                    const chartCanvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                    console.log(`Chart.js canvas elements found: ${chartCanvases.length}`);

                    // Initialize chart instances map
                    if (!window.chartInstances) {
                        window.chartInstances = new Map();
                    }

                    let foundCharts = 0;

                    // Try multiple methods to find charts
                    chartCanvases.forEach((canvas, index) => {
                        let chartInstance = null;

                        console.log(`Processing canvas ${index}:`);

                        // Method 1: Chart.getChart
                        if (typeof Chart !== 'undefined' && Chart.getChart) {
                            try {
                                chartInstance = Chart.getChart(canvas);
                                if (chartInstance) {
                                    console.log(`  - Found via Chart.getChart`);
                                }
                            } catch (e) {
                                console.log(`  - Chart.getChart failed:`, e.message);
                            }
                        }

                        // Method 2: Canvas chart property
                        if (!chartInstance && canvas.chart) {
                            chartInstance = canvas.chart;
                            console.log(`  - Found via canvas.chart`);
                        }

                        // Method 3: Canvas _chart property
                        if (!chartInstance && canvas._chart) {
                            chartInstance = canvas._chart;
                            console.log(`  - Found via canvas._chart`);
                        }

                        // Method 4: Search Chart.instances
                        if (!chartInstance && typeof Chart !== 'undefined' && Chart.instances) {
                            Object.values(Chart.instances).forEach(instance => {
                                if (instance.canvas === canvas) {
                                    chartInstance = instance;
                                    console.log(`  - Found via Chart.instances`);
                                }
                            });
                        }

                        if (chartInstance && chartInstance.data && chartInstance.data.datasets) {
                            const chartId = `chart_${index}`;
                            window.chartInstances.set(chartId, {
                                instance: chartInstance,
                                canvas: canvas,
                                originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                            });

                            console.log(`  - Registered as ${chartId} with ${chartInstance.data.datasets.length} datasets`);
                            chartInstance.data.datasets.forEach((dataset, dsIndex) => {
                                console.log(`    Dataset ${dsIndex}: ${dataset.label || 'Unnamed'}`);
                            });

                            foundCharts++;
                        } else {
                            console.log(`  - No valid chart instance found`);
                        }
                    });

                    console.log(`=== SETUP COMPLETE: ${foundCharts} charts registered ===`);
                    return foundCharts;
                })()
            """)

            log_info(f"Debug setup completed: {result} charts found and registered")
            return result > 0

        except Exception as e:
            log_error(f" Debug setup failed: {str(e)}")
            return False
   
    async def click_chart_data_point(self, page, chart_id, point_data):
        """Click on a specific chart data point directly"""
        try:
            # Extract point information
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)            
            # Click on the specific data point
            point_clicked = await page.evaluate(f"""
            (function() {{
                if (window.chartInstances && window.chartInstances.has('{chart_id}')) {{
                    const chartData = window.chartInstances.get('{chart_id}');
                    const chart = chartData.instance;
                    
                    try {{
                        // Find the dataset by label if datasetIndex is not reliable
                        let targetDatasetIndex = {dataset_index};
                        
                        // Verify dataset index by label
                        chart.data.datasets.forEach((dataset, index) => {{
                            if (dataset.label === '{dataset_label}') {{
                                targetDatasetIndex = index;
                            }}
                        }});
                        
                        // Get the dataset meta
                        const meta = chart.getDatasetMeta(targetDatasetIndex);
                        if (!meta || !meta.data || !meta.data[{point_index}]) {{
                            console.log('Data point not found at index: ' + {point_index});
                            return false;
                        }}
                        
                        // Get the data point element
                        const pointElement = meta.data[{point_index}];
                        if (!pointElement) {{
                            console.log('Point element not found');
                            return false;
                        }}                        
                        // Get the chart canvas
                        const canvas = chart.canvas;
                        if (!canvas) {{
                            console.log('Canvas not found');
                            return false;
                        }}                        
                        // Get point position
                        const pointPosition = pointElement.getCenterPoint();                        
                        // Create and dispatch click event
                        const rect = canvas.getBoundingClientRect();
                        const clickEvent = new MouseEvent('click', {{
                            clientX: rect.left + pointPosition.x,
                            clientY: rect.top + pointPosition.y,
                            bubbles: true,
                            cancelable: true
                        }});                        
                        // Dispatch the click event
                        canvas.dispatchEvent(clickEvent);                        
                        console.log('Clicked data point: ' + '{x_label}' + ' from dataset: ' + '{dataset_label}');
                        console.log('Point position:', pointPosition);                        
                        return true;                        
                    }} catch (error) {{
                        console.error('Error clicking data point:', error);
                        return false;
                    }}
                }}
                console.log('Chart instance not found: {chart_id}');
                return false;
            }})()
            """)
            
            if point_clicked:
                log_info(f" Data point clicked for {chart_id} - {x_label} from {dataset_label}")
                return True
            else:
                log_error(f"Could not click data point for {chart_id} - {x_label} from {dataset_label}")
                return False               
        except Exception as e:
            log_error(f"Failed to click data point for {chart_id} - {x_label}: {str(e)}")
            return False
   
    async def process_all_combinations_parallel(self, combinations):
        """Process charts in parallel with multiple browsers, each handling different charts"""

        log_info(f"🚀 Starting parallel processing of {len(combinations)} charts with 3 browsers")       
        # Organize combinations by chart for easier processing
        chart_combinations = {}
        for combo_idx, combination in enumerate(combinations):
            chart_id = combination.get('chart_id', f'chart_{combo_idx}')
            target_month = combination.get('target_month_year', 'unknown')
            matching_points = combination.get('matching_points', [])

            chart_combinations[chart_id] = {
                'combination_index': combo_idx,
                'chart_info': combination['chart_info'],
                'target_month_year': target_month,
                'matching_points': matching_points,
                'current_point_index': 0
            }
            log_info(f"**Chart {chart_id}: {target_month} ({(len(matching_points))} points)")

        all_results = []
        target_month_year = TARGET_MONTHS_YEARS
        max_browsers = 3

        # Group charts for parallel processing (3 charts at a time)
        chart_items = list(chart_combinations.items())
        chart_batches = []

        # Create batches of 3 charts each
        for i in range(0, len(chart_items), max_browsers):
            batch = chart_items[i:i + max_browsers]
            chart_batches.append(batch)

        log_info(f"Processing {len(chart_batches)} batches with up to {max_browsers} browsers per batch")
        # Process each batch of charts in parallel
        for batch_index, chart_batch in enumerate(chart_batches, 1):
            log_info(f"\n🚀 Starting Batch {batch_index}/{len(chart_batches)} with {len(chart_batch)} charts")

            # Create parallel tasks for this batch
            batch_tasks = []
            for browser_index, (chart_id, chart_data) in enumerate(chart_batch):
                browser_id = f"Browser_{browser_index + 1}"
                log_info(f"   📋 {browser_id}: Will process Chart {chart_id} - {chart_data['chart_info'].get('chartTitle', 'Unknown')}")
               
                task = asyncio.create_task(
                    self.process_single_chart_parallel(chart_data, target_month_year, browser_id,chart_id)
                )
                batch_tasks.append((browser_id, chart_id, task))
            # Wait for all browsers in this batch to complete
            log_info(f"Waiting for all {len(batch_tasks)} browsers in batch {batch_index} to complete...")
            for browser_id, chart_id, task in batch_tasks:
                try:
                    result = await task
                    if isinstance(result, list):
                        all_results.extend(result)
                        log_info(f" {browser_id} completed Chart {chart_id}: {len(result)} results")
                    else:
                        log_info(f"{browser_id} returned unexpected result type for Chart {chart_id}")
                except Exception as e:
                    log_error(f" {browser_id} failed processing Chart {chart_id}: {str(e)}")
                    continue
            log_info(f" Batch {batch_index} completed")
            # Add delay between batches to avoid overwhelming the system
            if batch_index < len(chart_batches):
                log_info("Waiting before next batch...")
                await asyncio.sleep(3)

        # Process final results
        successful_results = []
        failed_results = []

        for result in all_results:
            if isinstance(result, dict) and result.get('success', False):
                successful_results.append(result)
            else:
                failed_results.append(result)

        log_info(f"\n🎉 Parallel processing with {max_browsers} browsers completed!")
        log_info("Summary:")
        log_info(f"   - Total charts processed: {len(chart_combinations)}")
        log_info(f"   - Total batches processed: {len(chart_batches)}")
        log_info(f"   - Total point tasks processed: {len(all_results)}")
        log_info(f"   - Successful: {len(successful_results)}")
        log_info(f"   - Failed: {len(failed_results)}")
        log_info(f"   - Success rate: {(len(successful_results) / len(all_results) * 100):.1f}%" if all_results else "0%")
        log_info(all_results,"all_results+++++++++++")
        return {
            'successful': successful_results,
            'failed': failed_results,
            'all_results': all_results,
            'total_processed': len(all_results),
            'total_charts': len(chart_combinations),
            'batches_processed': len(chart_batches),
            'success_rate': (len(successful_results) / len(all_results) * 100) if all_results else 0
        }

    async def process_single_chart_parallel(self, chart_data, target_month_year, browser_id, chart_id):
        """Process all points in a single chart in parallel (for use with multiple browsers)"""
        
        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
        matching_points = chart_data.get('matching_points', [])
        log_info(f"{browser_id}: Processing chart: {chart_title} :({chart_id}) with {len(matching_points)} points")
        chart_results = []

        # Use the dedicated parallel function to check and re-login the session.
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []

        # Get a NEW PAGE from the existing session.
        page = await self.auth_manager.new_page()
        
        try:
            # Navigate to Discounts and ensure the 13 Month Trend tab is active
            log_info(f"{browser_id}: Navigating to Discounts for {chart_id}")
            await page.goto(f"{config.site_url.rstrip('/')}/Discounts", timeout=60000)
            await asyncio.sleep(10)
            try:
                await page.locator("role=tab[name=\"13 Month Trend\"]").click(timeout=5000)
                await asyncio.sleep(2)
            except Exception:
                log_warn(f"{browser_id}: Could not activate '13 Month Trend' tab; continuing")

            # Apply enhanced legend control
            legend_setup_success = await self.apply_enhanced_legend_control(page)
            await asyncio.sleep(2)

            if not legend_setup_success:
                log_info(f"{browser_id}: Legend control setup failed for {chart_id}, attempting manual setup...")
                await self.debug_and_setup_charts(page)
            
            # Debug legend control setup
            await self.debug_legend_control(page)
            
            # Process each point in this chart sequentially within this browser
            for point_idx, point_data in enumerate(matching_points):
                point_label = point_data.get('xLabel', f'Point_{point_idx}')
                dataset_label = point_data.get('datasetLabel', 'Unknown Dataset') 
                
                try:
                    # Step 1: Disable ALL legends first                        
                    await self.disable_all_legends(page)
                    await asyncio.sleep(1)
                    
                    # Step 2: Enable ONLY the legend for current chart/dataset                        
                    legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
                    await asyncio.sleep(2)  # Give more time for chart to update
                    
                    if legend_enabled:
                        log_info(f" {browser_id}: Legend control successful - ONLY {chart_id} legend is active")
                    else:
                        log_error(f"{browser_id}: Legend control failed, but continuing with processing")
                    
                    # Step 2.5: Ensure chart is interactive and data points are clickable
                    log_info(f"{browser_id}: Ensuring chart {chart_id} is interactive after legend control...")
                    await self.ensure_chart_interactivity(page, chart_id)
                    await asyncio.sleep(1)
                    
                    # Step 3: Create task for this point
                    task = {
                        'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_info': chart_data['chart_info'],
                        'target_month_year': chart_data['target_month_year'],
                        'point_data': point_data,
                        'point_index': point_idx
                    }

                    # Step 4: Process this point with enhanced clicking
                    result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year)
                    
                    if isinstance(result, dict):
                        result['chart_title'] = chart_title
                        result['point_sequence'] = point_idx + 1
                        result['method'] = 'parallel_processing'
                        result['browser_id'] = browser_id
                        result['chart_id'] = chart_id

                    chart_results.append(result)

                    # Log detailed result
                    if result.get('success', False):
                        click_success = result.get('click_success', False)
                        nav_success = result.get('navigation_success', False)
                        extract_success = result.get('extraction_success', False)                           
                        
                        if nav_success:
                            drilldown_url = result.get('drilldown_url', 'Unknown')
                            log_info(f"   🔗 Drilldown URL: {drilldown_url}")
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        log_error(f" {browser_id}: Failed point {point_idx + 1}: {point_label} - {error_msg}")

                    # Step 5: Navigate back to 13 Month Trend for next point (if not last point)
                    if point_idx < len(matching_points) - 1:
                        log_info(f"{browser_id}: Navigating back to 13 Month Trend for next point")
                        try:
                            await page.goto(f"{config.site_url.rstrip('/')}/Discounts", timeout=60000)
                            await asyncio.sleep(2)

                            # Re-apply legend control
                            await self.apply_enhanced_legend_control(page)
                            await asyncio.sleep(1)
                            log_info(f" {browser_id}: Successfully navigated back to 13 Month Trend")
                        except Exception as nav_back_error:
                            log_error(f" {browser_id}: Failed to navigate back to 13 Month Trend: {nav_back_error}")
                            # Try to continue anyway
                            pass

                except Exception as e:
                    log_error(f" {browser_id}: Error processing point {point_idx + 1} - {point_label}: {str(e)}")
                    error_result = {
                        'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_title': chart_title,
                        'point_label': point_label,
                        'error': str(e),
                        'success': False,
                        'method': 'parallel_processing',
                        'browser_id': browser_id,
                        'point_sequence': point_idx + 1
                    }
                    chart_results.append(error_result)

            log_info(f" {browser_id}: Completed all points for chart: {chart_title}")            

        except Exception as e:
            log_error(f" {browser_id}: Error setting up chart {chart_id}: {str(e)}")
            error_result = {
                'chart_id': chart_id,
                'chart_title': chart_title,
                'error': f"Chart setup failed: {str(e)}",
                'success': False,
                'method': 'parallel_processing',
                'browser_id': browser_id
            }
            chart_results.append(error_result)

        finally:
            try:
                await page.close()
                log_info(f"🔒 {browser_id}: Browser closed for chart {chart_id}")
            except Exception as cleanup_error:
                log_error(f"{browser_id}: Cleanup error for {chart_id}: {cleanup_error}")

        return chart_results
    async def ensure_chart_interactivity(self, page, chart_id):
        """Ensure chart is interactive and data points are clickable after legend control"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Ensuring chart interactivity for: {chart_id}');

                if (!window.chartInstances) {{
                    console.log('No chart instances found');
                    return false;
                }}

                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;
                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found for interactivity check');
                    return false;
                }}

                const chart = chartData.instance;

                try {{
                    // Ensure chart is responsive to events
                    if (chart.options) {{
                        if (!chart.options.interaction) chart.options.interaction = {{}};
                        chart.options.interaction.intersect = false;
                        chart.options.interaction.mode = 'point';

                        if (!chart.options.onHover) {{
                            chart.options.onHover = function(event, elements) {{
                                console.log('Chart hover detected:', elements.length, 'elements');
                            }};
                        }}

                        if (!chart.options.onClick) {{
                            chart.options.onClick = function(event, elements) {{
                                console.log('Chart click detected:', elements.length, 'elements');
                                if (elements.length > 0) {{
                                    const element = elements[0];
                                    console.log('Clicked element:', element);
                                }}
                            }};
                        }}
                    }}

                    // Force chart update to apply interaction settings
                    chart.update('none');

                    console.log('Chart interactivity ensured for: {chart_id}');
                    return true;
                }} catch (error) {{
                    console.error('Error ensuring chart interactivity:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                log_info(f" Chart {chart_id} interactivity ensured")
            else:
                log_error(f"Failed to ensure chart {chart_id} interactivity")

            return result

        except Exception as e:
            log_error(f" Error ensuring chart interactivity: {str(e)}")
            return False

    async def process_single_point_task_with_enhanced_clicking(self, page, task, target_month_year):
        """Process a single point task with enhanced clicking methods for drilldown"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        try:
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            # Get current URL before clicking
            initial_url = page.url
            log_info(f"{task_id}: Current URL before click: {initial_url}")
            if chart_id == 'chart_1316':
                await asyncio.sleep(20)
            # Method 1: Try Chart.js event-based clicking first
            click_result = await self.try_chartjs_event_click(page, chart_id, point_data)
            navigation_result = None

            if click_result.get('success', False):
                log_info(f" {task_id}: Chart.js event click successful, checking for navigation...")

                # Wait for navigation
                await asyncio.sleep(3)
                current_url = page.url

                if current_url != initial_url:
                    log_info(f" {task_id}: Navigation successful to: {current_url}")
                    navigation_result = {'success': True, 'url': current_url, 'method': 'chartjs_event'}
                else:
                    log_error(f"{task_id}: Chart.js event click didn't trigger navigation")
                    click_result = {'success': False, 'error': 'No navigation after Chart.js event click'}

            # Method 2: Fallback to coordinate clicking if Chart.js event didn't work
            if not click_result or not click_result.get('success', False):
                log_info(f"{task_id}: Trying coordinate-based clicking...")

                screen_x = point_data.get('screenX')
                screen_y = point_data.get('screenY')

                if screen_x and screen_y:
                    # Try multiple click methods
                    for click_method in ['single', 'double', 'with_delay']:
                        log_info(f"{task_id}: Trying {click_method} click at ({screen_x}, {screen_y})")

                        if click_method == 'single':
                            await page.mouse.click(screen_x, screen_y)
                        elif click_method == 'double':
                            await page.mouse.click(screen_x, screen_y, click_count=2)
                        elif click_method == 'with_delay':
                            await page.mouse.move(screen_x, screen_y)
                            await asyncio.sleep(0.5)
                            await page.mouse.down()
                            await asyncio.sleep(0.1)
                            await page.mouse.up()

                        # Check for navigation after each method
                        await asyncio.sleep(3)
                        current_url = page.url

                        if current_url != initial_url:
                            log_info(f" {task_id}: {click_method} click triggered navigation to: {current_url}")
                            click_result = {'success': True, 'method': f'coordinate_{click_method}'}
                            navigation_result = {'success': True, 'url': current_url, 'method': f'coordinate_{click_method}'}
                            break
                        else:
                            log_error(f"{task_id}: {click_method} click didn't trigger navigation")

                    if not navigation_result or not navigation_result.get('success', False):
                        click_result = {'success': False, 'error': 'All coordinate click methods failed'}
                        navigation_result = {'success': False, 'error': 'No navigation with any click method'}
                else:
                    click_result = {'success': False, 'error': 'No coordinates available'}
                    navigation_result = {'success': False, 'error': 'No coordinates for clicking'}


            if navigation_result and navigation_result.get('success', False):
                current_url = page.url
                # Modified URL check to handle different chart IDs
                if "AnalyzeData" in current_url:
                    # Add delay to ensure page loads completely
                    await asyncio.sleep(3)
                    
                    log_info(f"Chart ID-----------------55-->: {chart_id}")
                        
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year, chart_id, dataset_label)
                    # Check extraction success
                    extraction_success = False
                    if extracted_data and extracted_data.get('extraction_data'):
                        extraction_success = extracted_data['extraction_data'].get('success', False)
                        
                    if extraction_success:
                        log_info(f" {task_id}: Data extraction successful")
                    else:
                        log_error(f"{task_id}: Data extraction failed or incomplete")
                else:
                    log_info(f"{task_id}: Not on expected drilldown page. URL: {current_url}")
                    extracted_data = {
                        'extraction_data': {
                            'success': False,
                            'error': f'Unexpected page URL: {current_url}',
                            'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
                        }
                    }
                    extraction_success = False

            # Determine overall success
            overall_success = (
                click_result.get('success', False) and
                navigation_result.get('success', False) and
                extraction_success
            )
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'click_result': click_result,
                'navigation_result': navigation_result,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': overall_success,
                'legend_controlled': True,  # We know legend control was attempted
                'drilldown_url': navigation_result.get('url', '') if navigation_result else '',
                'click_success': click_result.get('success', False) if click_result else False,
                'navigation_success': navigation_result.get('success', False) if navigation_result else False,
                'extraction_success': extraction_success
            }
            log_info(f" {task_id}: Enhanced processing completed for {point_label} from {dataset_label}")
            return result

        except Exception as e:
            log_error(f" {task_id}: Error in enhanced processing: {e}")
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': True
            }
            return error_result

    async def try_chartjs_event_click(self, page, chart_id, point_data):
        """Try to trigger Chart.js click event programmatically"""
        try:
            print(f"Chart ID-----------------3333--> {chart_id}")
            if chart_id == 'chart_1316':
                await asyncio.sleep(10)
            result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting Chart.js event click for chart: {chart_id}');

                if (!window.chartInstances) {{
                    console.log('No chart instances found');
                    return {{ success: false, error: 'No chart instances' }};
                }}
                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        break;
                    }}
                }}
                if (!chartData) {{
                    console.log('Chart not found for event click');
                    return {{ success: false, error: 'Chart not found' }};
                }}

                const chart = chartData.instance;
                const canvas = chartData.canvas;

                try {{
                    // Get point data
                    const pointIndex = {point_data.get('pointIndex', 0)};
                    const datasetIndex = {point_data.get('datasetIndex', 0)};

                    // Get the data point element
                    const meta = chart.getDatasetMeta(datasetIndex);
                    if (!meta || !meta.data || !meta.data[pointIndex]) {{
                        console.log('Data point not found');
                        return {{ success: false, error: 'Data point not found' }};
                    }}

                    const pointElement = meta.data[pointIndex];
                    const pointPosition = pointElement.getCenterPoint();

                    // Create a synthetic click event
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {{
                        clientX: rect.left + pointPosition.x,
                        clientY: rect.top + pointPosition.y,
                        bubbles: true,
                        cancelable: true,
                        view: window
                    }});

                    // Trigger the click event
                    canvas.dispatchEvent(clickEvent);

                    // Also try Chart.js onClick if available
                    if (chart.options && chart.options.onClick) {{
                        const elements = chart.getElementsAtEventForMode(clickEvent, 'nearest', {{ intersect: true }}, false);
                        chart.options.onClick(clickEvent, elements, chart);
                    }}

                    console.log('Chart.js event click executed successfully');
                    return {{ success: true, method: 'chartjs_event', position: pointPosition }};

                }} catch (error) {{
                    console.error('Error in Chart.js event click:', error);
                    return {{ success: false, error: error.message }};
                }}
            }})()
            """)
            return result
        except Exception as e:
            log_error(f" Error in Chart.js event click: {str(e)}")
            return {'success': False, 'error': str(e)}

    async def debug_chart_clickability(self, page, chart_id, point_data):
        """Debug function to check if chart area is clickable"""
        try:
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')

            if screen_x and screen_y:
                # Check what element is at the click coordinates
                element_info = await page.evaluate(f"""
                    () => {{
                        const element = document.elementFromPoint({screen_x}, {screen_y});
                        if (element) {{
                            return {{
                                tagName: element.tagName,
                                className: element.className,
                                id: element.id,
                                isCanvas: element.tagName === 'CANVAS',
                                boundingRect: element.getBoundingClientRect(),
                                visible: element.offsetParent !== null
                            }};
                        }}
                        return null;
                    }}
                """)

                if element_info:
                    log_info(f"Debug - Element at ({screen_x}, {screen_y}):")
                    log_info(f"   Tag: {element_info.get('tagName', 'Unknown')}")
                    log_info(f"   Class: {element_info.get('className', 'None')}")
                    log_info(f"   Canvas: {element_info.get('isCanvas', False)}")
                    log_info(f"   Visible: {element_info.get('visible', False)}")
                else:
                    log_info(f"Debug - No element found at coordinates ({screen_x}, {screen_y})")

        except Exception as e:
            log_error(f"Debug function failed: {e}")
    async def process_single_chart_sequential(self, chart_data, target_month_year):
        """Process all points in a single chart sequentially"""
        chart_id = chart_data.get('chart_info', {}).get('canvasId', 'unknown_chart')
        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
        matching_points = chart_data.get('matching_points', [])

        print(f"🎯 Processing chart: {chart_title} ({chart_id}) with {len(matching_points)} points")

        chart_results = []
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []
    
        page = await self.auth_manager.new_page()

        try:
            # Navigate to Discounts and select 13 Month Trend
            print(f"Navigating to Discounts for {chart_id}")
            await page.goto(f"{config.site_url.rstrip('/')}/Discounts", timeout=60000)
            # await page.wait_for_load_state("networkidle", timeout=15000)
            await asyncio.sleep(2)
            try:
                await page.locator("role=tab[name=\"13 Month Trend\"]").click(timeout=3000)
                await asyncio.sleep(1)
            except Exception:
                log_warn("Could not activate '13 Month Trend' tab in sequential processing; continuing")

            # Apply enhanced legend control
            legend_setup_success = await self.apply_enhanced_legend_control(page)
            await asyncio.sleep(2)

            if not legend_setup_success:
                print(f"Legend control setup failed for {chart_id}, attempting manual setup...")
                await self.debug_and_setup_charts(page)

            # Debug legend control setup
            await self.debug_legend_control(page)

            print(f" Page setup completed for {chart_id}")

            # Process each point in this chart sequentially
            for point_idx, point_data in enumerate(matching_points):
                point_label = point_data.get('xLabel', f'Point_{point_idx}')
                dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')

                print(f"\nProcessing point {point_idx + 1}/{len(matching_points)}: {point_label} ({dataset_label})")

                try:
                    # Step 1: Disable ALL legends first
                    print(f"🔒 Disabling all legends before processing {chart_id}")
                    await self.disable_all_legends(page)
                    await asyncio.sleep(1)

                    # Step 2: Enable ONLY the legend for current chart/dataset
                    print(f"🔓 Enabling ONLY legend for {chart_id} - {dataset_label}")
                    legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
                    await asyncio.sleep(2)  # Give more time for chart to update

                    if legend_enabled:
                        print(f" Legend control successful - ONLY {chart_id} legend is active")
                    else:
                        print(f"Legend control failed, but continuing with processing")

                    # Step 2.5: Ensure chart is interactive and data points are clickable
                    print(f"Ensuring chart {chart_id} is interactive after legend control...")
                    await self.ensure_chart_interactivity(page, chart_id)
                    await asyncio.sleep(1)

                    # Step 3: Create task for this point
                    task = {
                        'task_id': f"{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_info': chart_data['chart_info'],
                        'target_month_year': chart_data['target_month_year'],
                        'point_data': point_data,
                        'point_index': point_idx
                    }

                    # Step 4: Process this point with enhanced clicking
                    result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year)

                    if isinstance(result, dict):
                        result['chart_title'] = chart_title
                        result['point_sequence'] = point_idx + 1
                        result['method'] = 'sequential_processing'

                    chart_results.append(result)

                    # Log detailed result
                    if result.get('success', False):
                        click_success = result.get('click_success', False)
                        nav_success = result.get('navigation_success', False)
                        extract_success = result.get('extraction_success', False)
                        print(f" Completed point {point_idx + 1}: {point_label}")
                        print(f"   Click: {'' if click_success else ''} | Navigation: {'' if nav_success else ''} | Extraction: {'' if extract_success else ''}")
                        if nav_success:
                            drilldown_url = result.get('drilldown_url', 'Unknown')
                            print(f"   🔗 Drilldown URL: {drilldown_url}")
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        print(f" Failed point {point_idx + 1}: {point_label} - {error_msg}")

                    # Step 5: Navigate back to 13 Month Trend for next point (if not last point)
                    if point_idx < len(matching_points) - 1:
                        print(f"Navigating back to 13 Month Trend for next point")
                        try:
                            await page.goto(f"{config.site_url.rstrip('/')}/Discounts", timeout=60000)
                            # await page.wait_for_load_state("networkidle", timeout=15000)
                            await asyncio.sleep(10)

                            # Re-apply legend control
                            await self.apply_enhanced_legend_control(page)
                            await asyncio.sleep(1)
                            print(f" Successfully navigated back to 13 Month Trend")
                        except Exception as nav_back_error:
                            print(f" Failed to navigate back to 13 Month Trend: {nav_back_error}")
                            # Try to continue anyway
                            pass

                except Exception as e:
                    print(f" Error processing point {point_idx + 1} - {point_label}: {str(e)}")
                    error_result = {
                        'task_id': f"{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_title': chart_title,
                        'point_label': point_label,
                        'error': str(e),
                        'success': False,
                        'method': 'sequential_processing',
                        'point_sequence': point_idx + 1
                    }
                    chart_results.append(error_result)

            print(f" Completed all points for chart: {chart_title}")

        except Exception as e:
            print(f" Error setting up chart {chart_id}: {str(e)}")
            error_result = {
                'chart_id': chart_id,
                'chart_title': chart_title,
                'error': f"Chart setup failed: {str(e)}",
                'success': False,
                'method': 'sequential_processing'
            }
            chart_results.append(error_result)

        finally:
            try:
                # await context.close()
                # await browser.close()
                await page.close()
            except Exception as cleanup_error:
                print(f"Cleanup error for {chart_id}: {cleanup_error}")

        return chart_results               
       
    async def run_complete_process(self):
        # """Run the complete chart processing workflow with sequential processing"""
        """"
        Run the complete chart processing workflow.
        This function now orchestrates the entire session lifecycle.
        """
        log_info("🚀 Starting complete chart processing workflow with enhanced legend control...")

        # Step 1: Start the AuthManager ONCE at the beginning of the entire process
        success = await self.auth_manager.start(headless=False)
        if not success:
            log_error("❌ Authentication failed. Exiting.")
            return None

        try:
            # Step 1: Create chart-point combinations            
            combinations = await self.create_chart_point_combinations(TARGET_MONTHS_YEARS)            
            if not combinations:
                print(" No chart-point combinations found")
                return None
            print(f" Created {len(combinations)} chart-point combinations")

            # Step 2: Process combinations sequentially with single browser
            print("Step 2: Processing combinations sequentially with single browser...")
            all_results = []
            for combination in combinations:
                chart_results = await self.process_single_chart_sequential(combination, TARGET_MONTHS_YEARS)
                if chart_results:
                    all_results.extend(chart_results)

            results = {
                'successful': [r for r in all_results if r.get('success', False)],
                'failed': [r for r in all_results if not r.get('success', False)],
                'all_results': all_results,
                'total_processed': len(all_results),
                'total_charts': len(combinations),
                'batches_processed': 1,
                'success_rate': (sum(1 for r in all_results if r.get('success', False)) / len(all_results) * 100) if all_results else 0
            }

            # Step 3: Save results
            print("Step 3: Saving results...")
            await self.save_results(results)
            print(" Complete chart processing workflow finished successfully")
            print(f"Final Summary:")
            print(f"   - Total combinations processed: {len(combinations)}")
            print(f"   - Total tasks completed: {results.get('total_processed', 0)}")
            print(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
            return results

        except Exception as e:
            print(f" Error in complete process: {e}")            
            traceback.print_exc()
            return None
        finally:
            log_info("Closing AuthManager browser/session...")
            await self.auth_manager.stop()  # <-- ensures browser closes

    async def save_results(self, results):
        """Save processing results to files with enhanced formatting"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # Create results directory if it doesn't exist
            results_dir= create_folder_file_path(
                subfolder="chart_processing_results",                               
            )  

            # Save all results (combined)
            if results.get('all_results'):
                all_results_file = os.path.join(results_dir, chart_process_json)
                with open(all_results_file, 'w', encoding='utf-8') as f:
                    json.dump(results['all_results'], f, indent=2, default=str, ensure_ascii=False)
                print(f"All results saved to {all_results_file}")

            # Save enhanced summary
            summary = {
                'timestamp': timestamp,
                'processing_method': 'enhanced_legend_control_parallel',
                'target_months_years': TARGET_MONTHS_YEARS,
                'max_concurrent_browsers': MAX_CONCURRENT_BROWSERS,
                'total_processed': results.get('total_processed', 0),
                'total_charts': results.get('total_charts', 0),
                'rounds_processed': results.get('rounds_processed', 0),
                'successful_count': len(results.get('successful', [])),
                'failed_count': len(results.get('failed', [])),
                'success_rate': results.get('success_rate', 0),
                'processing_statistics': {
                    'charts_with_data': results.get('total_charts', 0),
                    'average_points_per_chart': results.get('total_processed', 0) / results.get('total_charts', 1) if results.get('total_charts', 0) > 0 else 0,
                    'total_rounds': results.get('rounds_processed', 0)
                }
            }

           
            # print("\nStep 4: Performing UI vs DB comparison...")
            # await self.compare_with_cp_overview_results(results, timestamp)

        except Exception as e:
            print(f" Error saving results: {e}")            
            traceback.print_exc()
            
async def ui_capture():
    """Handles chart UI capture and runs the workflow"""
    
    try:
        
        processor = MultiChartParallelProcessor(
            max_browsers=MAX_CONCURRENT_BROWSERS            
        )

        log_info(f"   - Processing mode: Parallel ({MAX_CONCURRENT_BROWSERS}browsers, different charts)")
        log_info(f"   - Max concurrent browsers: {MAX_CONCURRENT_BROWSERS}")
        log_info(f"   - Target months/years: {TARGET_MONTHS_YEARS}")
        log_info(f"   - Browser timeout: {BROWSER_TIMEOUT}ms")
        log_info("=" * 80)

        # Run the parallel chart processing workflow
        results = await processor.run_complete_process()    
        if results:
            log_info("\n" + "=" * 80)
            log_info("Parallel processing with 3 browsers completed successfully!")
            log_info("Final Results:")
            log_info(f"   - Total tasks processed: {results.get('total_processed', 0)}")
            log_info(f"   - Charts processed: {results.get('total_charts', 0)}")
            log_info(f"   - Batches processed: {results.get('batches_processed', 0)}")
            log_info(f"   - Successful tasks: {len(results.get('successful', []))}")
            log_info(f"   - Failed tasks: {len(results.get('failed', []))}")
            log_info(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
            log_info("=" * 80)
            # Additional statistics
            if results.get('successful'):
                log_info(f" Parallel processing completed with {len(results['successful'])} successful extractions")
            if results.get('failed'):
                log_error(f" {len(results['failed'])} tasks failed - check failed results file for details")

            return True

    except Exception as e:
        log_error(f"❌ UI capture failed: {e}")
        traceback.print_exc()
        return False
    


# Main execution
async def main():
    """Main function: orchestrates UI capture, DB calculation, and comparison. Also run the enhanced chart processing with legend control"""

    start_time = time.time()
    log_info(f"Start Time: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    # Initialize components
    # ui_results = await ui_capture()

    # if not ui_results:
    #     log_error("UI capture did not return results. Exiting.")
    #     return False
    
    # if ui_results:   
    #     # Generate final comparison report
    #     log_info("\n" + "=" * 80)
    #     log_info("GENERATING FINAL UI vs DB COMPARISON REPORT")
    #     log_info("=" * 80)
    #     try:
            #step4:
    db_calculation()
    # step5: 

    log_info("DB calculation completed successfully")   
    result_dir,db_json_path = create_folder_file_path(subfolder="chart_processing_results", output_file=db_json) 
    ui_json_path = os.path.join(result_dir, chart_process_json)
    log_info(f"UI JSON Path: {ui_json_path}")
    log_info(f"DB JSON Path: {db_json_path}")
    compare_discount_metrics_results(ui_json_path, db_json_path)
    end_time = time.time()-start_time
    log_info(f"End Time: {end_time}")
    return True
    #     except Exception as comparison_error:
    #         log_error(f" Error generating comparison report: {comparison_error}")            
    #         traceback.print_exc()
    #         return False
    # else:
    #     # log_error(" Parallel processing failed - check logs for details")
    #     return False
def run_validation():
    """Run the all process"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        log_error(" Processing interrupted by user")
    except Exception as e:
        log_error(f"\n Unexpected error: {e}")        
        traceback.print_exc()

if __name__ == "__main__":
    run_validation()

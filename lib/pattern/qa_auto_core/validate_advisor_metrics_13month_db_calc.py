import math
import sys
import json
sys.path.append('../')
from collections import Counter
import pandas as pd
import os
import numpy as np
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from decimal import Decimal, ROUND_HALF_UP
import json
 
from decimal import Decimal, ROUND_HALF_UP
# from db_handler.db_connector import (
#     allRevenueDetailsTable
# )
from lib.pattern.qa_auto_core.db_handler.db_connector import (
    allRevenueDetailsTable
)
from lib.pattern.config import config
import argparse

# Parse command line arguments
parser = argparse.ArgumentParser()
parser.add_argument('--store_id', required=True)
parser.add_argument('--store_name', required=True)
parser.add_argument('--start_date', required=True)
parser.add_argument('--end_date', required=True)
parser.add_argument('--fopc_month', required=True)
parser.add_argument('--pre_fopc_month', required=True)
parser.add_argument('--database_name', required=True)
parser.add_argument('--working_days', type=float, required=True)
parser.add_argument('--advisor', required=True)
parser.add_argument('--technician', required=True)
parser.add_argument('--last_month', required=True)
parser.add_argument('--site_url', required=True)
parser.add_argument('--role', required=True)
parser.add_argument('--target_month_year', required=True)

args = parser.parse_args()

# Set config values from command line arguments
config.store_id = args.store_id
config.store_name = args.store_name
config.start_date = args.start_date
config.end_date = args.end_date
config.fopc_month = args.fopc_month
config.pre_fopc_month = args.pre_fopc_month
config.database_name = args.database_name
config.working_days = args.working_days
config.advisor = args.advisor
config.technician = args.technician
config.last_month = args.last_month
config.site_url = args.site_url
config.role = args.role
config.target_month_year = [args.target_month_year]

def round_off(n, decimals=0):
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal('1'), rounding=ROUND_HALF_UP) / multiplier)

def zero_sales_check(df, columns):
    total_sum = df[columns].sum().sum()
    return total_sum == 0

def get_month_date_range_from_target(target_date_str):
    """Get the start and end date for the target month"""
    # Parse the target date string
    target_date = datetime.strptime(target_date_str, "%Y-%m-%d")    
    # Get first day of the target month
    month_start = target_date.replace(day=1)    
    # Get last day of the target month
    month_end = month_start + relativedelta(months=1) - timedelta(days=1)    
    return month_start, month_end

def process_advisor_metrics_month_data(all_revenue_details_df, month_start, month_end, advisor, tech, retail_flag, customer_pay_types, warranty_pay_types, columns_to_check):
    """Process advisor metrics data for the target month and return results"""
    month_start = month_start.date()
    month_end = month_end.date()
    
    # Filter data for the specific target month
    month_data = all_revenue_details_df[
        (all_revenue_details_df['closeddate'] >= month_start) &
        (all_revenue_details_df['closeddate'] <= month_end)
    ]
    
    print(f"Advisor metrics target month data shape: {month_data.shape}")
    
    if month_data.empty:
        print("No data found for the target month")
        return None
    
    # Apply existing filtering logic
    filtered_df = month_data[
        (month_data['department'] == 'Service') & 
        (month_data['hide_ro'] != True)
    ]
    
    if filtered_df.empty:
        print("No service department data found for the target month")
        return None
    
    filtered_df = filtered_df.copy()
    filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)
    
    # Initialize variables
    total_revenue = 0
    labor_revenue = 0
    parts_revenue = 0
    labor_sold_hours = 0
    ro_count = 0
    job_count = 0
    labor_gross_profit = 0
    labor_gross_profit_percentage = 0
    parts_gross_profit = 0
    parts_gross_profit_percentage = 0
    effective_labor_rate = 0
    parts_markup = 0
    
    combined_revenue_details = filtered_df.copy()
    combined_revenue_details['group'] = pd.Series(dtype="string")
    
    # Set specific columns to 0 if opcategory is 'N/A'
    combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    
    # Iterate through each unique RO number for group assignment
    for ro_number in combined_revenue_details['unique_ro_number'].unique():
        ro_specific_rows = combined_revenue_details[combined_revenue_details['unique_ro_number'] == ro_number]
        
        ro_specific_rows_C = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(customer_pay_types)]
        ro_specific_rows_W = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(warranty_pay_types)]
        
        zero_sales_C = zero_sales_check(ro_specific_rows_C, columns_to_check)
        zero_sales_W = zero_sales_check(ro_specific_rows_W, columns_to_check)
        
        if not ro_specific_rows_C.empty and not zero_sales_C:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'C'
        elif not ro_specific_rows_W.empty and not zero_sales_W:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'W'
        else:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'I'
    
    # Apply filters based on advisor and tech conditions
    if advisor == {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details['unique_ro_number'].unique()
    elif advisor != {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['serviceadvisor'].astype(str).isin(advisor), 'unique_ro_number'].unique()
    elif advisor == {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['lbrtechno'].astype(str).isin(tech), 'unique_ro_number'].unique()
    elif advisor != {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[(combined_revenue_details['serviceadvisor'].astype(str).isin(advisor)) & 
            (combined_revenue_details['lbrtechno'].astype(str).isin(tech)), 'unique_ro_number'].unique()
    
    # Apply the advisor and tech filter conditions
    combined_revenue_details = combined_revenue_details[combined_revenue_details['unique_ro_number'].isin(matching_ro_numbers)]
    combined_revenue_details = combined_revenue_details.reset_index(drop=True)
    combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    
    # RO Counts
    Scorecard_10_CP = combined_revenue_details.loc[combined_revenue_details['group'] == 'C', 'unique_ro_number'].nunique()
    Scorecard_10_Wty = combined_revenue_details.loc[combined_revenue_details['group'] == 'W', 'unique_ro_number'].nunique()
    Scorecard_10_Int = combined_revenue_details.loc[combined_revenue_details['group'] == 'I', 'unique_ro_number'].nunique()
    all_unique_ros = Scorecard_10_CP + Scorecard_10_Wty + Scorecard_10_Int
    
    # Filtering only CP job details
    list_of_paytypegroup_C = combined_revenue_details[combined_revenue_details['paytypegroup'].isin(customer_pay_types) & (combined_revenue_details['group'] == 'C')].to_dict('records')
    # Converting it to data frame
    total_revenue_CP = pd.DataFrame(list_of_paytypegroup_C)
    
    if not total_revenue_CP.empty:
        total_revenue_CP.loc[total_revenue_CP['opcategory'] == 'N/A', columns_to_check] = 0
        total_revenue_CP = total_revenue_CP[
            ~((total_revenue_CP['lbrsale'].fillna(0) == 0) &
              (total_revenue_CP['lbrsoldhours'].fillna(0) == 0) &
              (total_revenue_CP['prtextendedsale'].fillna(0) == 0) &
              (total_revenue_CP['prtextendedcost'].fillna(0) == 0))
        ]
        
        # Calculate metrics - following advisor_metrics pattern
        # 1276 - Total Revenue
        labor_revenue_value = pd.to_numeric(total_revenue_CP['lbrsale']).fillna(0).sum()
        parts_revenue_value = pd.to_numeric(total_revenue_CP['prtextendedsale']).fillna(0).sum()
        total_revenue = round_off((labor_revenue_value + parts_revenue_value), 2)
        
        # 1277 - Labor Revenue
        labor_revenue = round_off(labor_revenue_value, 2)
        
        # 1278 - Parts Revenue
        parts_revenue = round_off(parts_revenue_value, 2)
        
        # 1279 - Labor Sold Hours
        labor_sold_hours_value = pd.to_numeric(total_revenue_CP['lbrsoldhours']).fillna(0).sum()
        labor_sold_hours = round_off(labor_sold_hours_value, 2)
        
        # 1280 - RO Count
        ro_count = Scorecard_10_CP
        
        # 1281 - CP Job Count
        job_count = len(total_revenue_CP[total_revenue_CP['paytypegroup'].isin(customer_pay_types)]['ronumber'])
        
        # 1282 - Labor Gross Profit
        labor_cost = pd.to_numeric(total_revenue_CP['lbrcost']).fillna(0).sum()
        labor_gross_profit_value = labor_revenue_value - labor_cost
        labor_gross_profit = round_off(labor_gross_profit_value, 2)
        
        # 1283 - Labor Gross Profit %
        labor_gross_profit_percentage = round_off(((labor_gross_profit_value / labor_revenue_value) * 100), 1) if labor_revenue_value != 0 else 0
        
        # 1284 - Parts Gross Profit
        parts_cost = pd.to_numeric(total_revenue_CP['prtextendedcost']).fillna(0).sum()
        parts_gross_profit_value = parts_revenue_value - parts_cost
        parts_gross_profit = round_off(parts_gross_profit_value, 2)
        
        # 1285 - Parts Gross Profit %
        parts_gross_profit_percentage = round_off(((parts_gross_profit_value / parts_revenue_value) * 100), 1) if parts_revenue_value != 0 else 0
        
        # 1286 - Effective Labor Rate
        effective_labor_rate = round_off(labor_revenue_value / labor_sold_hours_value, 2) if labor_sold_hours_value != 0 else 0
        
        # 1315 - Parts Markup
        parts_markup = round_off(parts_revenue_value / parts_cost, 4) if parts_cost != 0 else 0
    
    else:
        print("No Customer Pay data available for advisor metrics calculations")
    
    # Return result set
    return {
        "target_month": month_start.strftime('%Y-%m'),
        "target_month_name": month_start.strftime('%B %Y'),
        "total_ros": all_unique_ros,
        "ro_counts": {
            "customer_pay_ros": Scorecard_10_CP,
            "warranty_ros": Scorecard_10_Wty,
            "internal_ros": Scorecard_10_Int
        },
        # Advisor metrics specific data
        "advisor_metrics": {
            "total_revenue": total_revenue,
            "labor_revenue": labor_revenue,
            "parts_revenue": parts_revenue,
            "labor_sold_hours": labor_sold_hours,
            "ro_count": ro_count,
            "job_count": job_count,
            "labor_gross_profit": labor_gross_profit,
            "labor_gross_profit_percentage": labor_gross_profit_percentage,
            "parts_gross_profit": parts_gross_profit,
            "parts_gross_profit_percentage": parts_gross_profit_percentage,
            "effective_labor_rate": effective_labor_rate,
            "parts_markup": parts_markup
        }
    }

def advisor_metrics_db_execution(target_date_str, advisor, tech, retail_flag, columns_to_check):
    """
    Handle database operations and execute advisor metrics month processing
    """
    try:
        customer_pay_types = {}
        warranty_pay_types = {}
        
        # Get target month date range
        month_start, month_end = get_month_date_range_from_target(target_date_str)
        
        print(f"Advisor metrics target month range: {month_start.date()} to {month_end.date()}")
        
        # Fetch all data from database
        print("Fetching advisor metrics data from database...")
        all_revenue_details_table_db_connect = allRevenueDetailsTable()
        all_revenue_details_df = all_revenue_details_table_db_connect.getTableResult()
        
        if all_revenue_details_df.empty:
            print("ERROR: No advisor metrics data retrieved from database!")
            return None
        
        # Convert date column and other preprocessing
        columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))
        
        # Define customer and warranty pay types based on retail_flag
        if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C'}
            warranty_pay_types = {'W', 'F', 'M', 'E'}
        elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'M'}
            warranty_pay_types = {'W', 'F', 'E'}
        elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C', 'E'}
            warranty_pay_types = {'W', 'F', 'M'}
        elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'E', 'M'}
            warranty_pay_types = {'W', 'F'}
        
        target_month_result = process_advisor_metrics_month_data(
            all_revenue_details_df, 
            month_start, 
            month_end,
            advisor, 
            tech, 
            retail_flag, 
            customer_pay_types, 
            warranty_pay_types, 
            columns_to_check
        )
        
        return target_month_result, customer_pay_types, warranty_pay_types
        
    except Exception as e:
        print(f"ERROR in advisor_metrics_db_execution: {str(e)}")
        print("=" * 60)
        print("ADVISOR METRICS DATABASE EXECUTION FAILED")
        print("=" * 60)
        return None, None, None

def advisor_metrics_db_calculation():
    """
    Main execution function for advisor metrics db calculation
    """
    # Configuration variables
    columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
    retail_flag = {'C','M'}
    
    storeid = config.store_id
    advisor_set = 'all'
    tech_set = 'all'
    #  Target months-years for drilling down (modify as needed)
    TARGET_MONTHS_YEARS = config.target_month_year

    # Process advisor configuration
    if isinstance(advisor_set, str):
        if advisor_set.lower() == 'all':
            advisor = {'all'}
        elif ',' in advisor_set:
            advisor = {x.strip() for x in advisor_set.split(',')}
        else:
            advisor = {advisor_set.strip()}
    else:
        advisor = {'all'}
    
    if advisor != {'all'}:
        advisor_id = next(iter(advisor))
    else:
        advisor_id = 'all'
    
    # Process technician configuration
    if isinstance(tech_set, str):
        if tech_set.lower() == 'all':
            tech = {'all'}
        elif ',' in tech_set:
            tech = {x.strip() for x in tech_set.split(',')}
        else:
            tech = {tech_set.strip()}
    else:
        tech = {'all'}
    
    # Execute database operations and processing
    target_date_str = TARGET_MONTHS_YEARS[0]
    target_month_result, customer_pay_types, warranty_pay_types = advisor_metrics_db_execution(
        target_date_str, advisor, tech, retail_flag, columns_to_check
    )
    
    # Process results
    if target_month_result:
        print("\n" + "=" * 80)
        print("ADVISOR METRICS RESULTS PROCESSING")
        print("=" * 80)
        
        # Create the final result set for the target month only
        final_result_set = {
            "analysis_info": {
                "target_month": target_date_str,
                "analysis_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "store_id": storeid,
                "advisor_filter": list(advisor),
                "technician_filter": list(tech),
                "customer_pay_types": list(customer_pay_types),
                "warranty_pay_types": list(warranty_pay_types)
            },
            "target_month_results": target_month_result
        }
        
        # Write results to JSON file
        output_filename = "chart_processing_results/advisor_metrics_calculated_value.json"
        with open(output_filename, 'w', encoding='utf-8') as json_file:
            json.dump(final_result_set, json_file, indent=4, ensure_ascii=False)
        
        print(f"\nTarget month advisor metrics data written successfully to {output_filename}")
        
        # Display summary
        print(f"\nAdvisor Metrics Summary for {target_month_result['target_month_name']}:")
        print(f"  Total Revenue: ${target_month_result['advisor_metrics']['total_revenue']:,.2f}")
        print(f"  Labor Revenue: ${target_month_result['advisor_metrics']['labor_revenue']:,.2f}")
        print(f"  Parts Revenue: ${target_month_result['advisor_metrics']['parts_revenue']:,.2f}")
        print(f"  Labor Gross Profit: ${target_month_result['advisor_metrics']['labor_gross_profit']:,.2f}")
        print(f"  Labor GP Percentage: {target_month_result['advisor_metrics']['labor_gross_profit_percentage']}%")
        print(f"  Parts Gross Profit: ${target_month_result['advisor_metrics']['parts_gross_profit']:,.2f}")
        print(f"  Parts GP Percentage: {target_month_result['advisor_metrics']['parts_gross_profit_percentage']}%")
        print(f"  Total ROs: {target_month_result['total_ros']}")
        print(f"    - Customer Pay ROs: {target_month_result['ro_counts']['customer_pay_ros']}")
        print(f"    - Warranty ROs: {target_month_result['ro_counts']['warranty_ros']}")
        print(f"    - Internal ROs: {target_month_result['ro_counts']['internal_ros']}")
        print(f"  Job Count: {target_month_result['advisor_metrics']['job_count']}")
        print(f"  Labor sold hours: {target_month_result['advisor_metrics']['labor_sold_hours']}")
        print(f"  Effective Labor Rate: ${target_month_result['advisor_metrics']['effective_labor_rate']}")
        print(f"  Parts Markup: {target_month_result['advisor_metrics']['parts_markup']}")
        
    else:
        print("\n" + "=" * 80)
        print("NO ADVISOR METRICS DATA RESULTS PROCESSING")
        print("=" * 80)
        
        print(f"No advisor metrics data available for target month {target_date_str}")
    
    print("\n" + "=" * 80)
    print("ADVISOR METRICS ANALYSIS - MAIN EXECUTION COMPLETED")
    print("=" * 80)

# Example usage
if __name__ == "__main__":
    advisor_metrics_db_calculation()
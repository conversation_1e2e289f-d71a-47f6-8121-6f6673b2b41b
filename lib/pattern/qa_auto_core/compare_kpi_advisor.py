# import json
# import csv
# import re
# import openpyxl
# from openpyxl.styles import PatternFill
# import os
# from dotenv import load_dotenv
# load_dotenv()
# advisor_set = os.environ.get('advisor')
# log_info(advisor_set,"advisor_set.....................")
# def extract_numeric(val):
#     if val is None:
#         return None
#     if isinstance(val, (int, float)):
#         return float(val)
#     match = re.search(r"[-+]?\d*\.\d+|\d+", str(val).replace(",", ""))
#     return float(match.group()) if match else None

# def flatten_kpi_blocks(json_array):
#     flat_data = {}
#     for item in json_array:
#         if not isinstance(item, dict):
#             continue
#         for key, value in item.items():
#             if key in ("Goal", "Variance"):
#                 continue  # skip goal/variance here
#             flat_data[key.strip()] = value
#     return flat_data

# def extract_goal_variance(json_array, key_name):
#     for item in json_array:
#         if key_name in item:
#             log_info(key_name,item[key_name],"key_name....")
#             return item[key_name]
            
#     return {}

# if advisor_set=='all':
# # Load the first JSON file
#     with open("Json-files/results_set.json", "r", encoding="utf-8") as file1:
#         data1 = json.load(file1)
#         # log_info(data1,"first file.....................")
#     with open("Json-files/kpi_advisor_complete.md", "r", encoding="utf-8") as file2:
#         content2 = file2.read().strip()
# else:
#     with open("Json-files/results_set_advisor.json", "r", encoding="utf-8") as file1:
#         data1 = json.load(file1)
#         # log_info(data1,"first file.....................")
#     with open("Json-files/kpi_advisor.md", "r", encoding="utf-8") as file2:
#         content2 = file2.read().strip()
#     # Try to wrap in square brackets and add commas between top-level objects
# try:
#         # Ensure all objects are properly comma-separated
#     # Add brackets if they are missing
#     if not content2.startswith("["):
#         fixed_content = "[" + re.sub(r"}\s*,?\s*{", "},\n{", content2.strip()) + "]"
#         data2 = json.loads(fixed_content)
#     else:
#         data2 = json.loads(content2)
# except json.JSONDecodeError as e:
#     raise ValueError(f"kpi-advisor.md does not contain valid JSON: {e}")


# # Flatten KPI values
# kpi_file1 = flatten_kpi_blocks(data1)
# kpi_file2 = flatten_kpi_blocks(data2)

# # Extract goals and variance separately
# goal_file1 = extract_goal_variance(data1, "Goal")
# log_info(goal_file1,"goal_file1")
# goal_file2 = extract_goal_variance(data2, "Goal")
# log_info(goal_file2,"goal_file2")
# # variance_file1 = extract_goal_variance(data1, "Variance")
# # log_info(variance_file1,"variance_file1")
# variance_file2 = extract_goal_variance(data2, "Variance")
# # Manually calculate variance for file1

# # Custom variance logic for file1 based on special KPI string parsing
# # Custom variance logic for file1 based on special KPI string parsing
# variance_file1 = {}
# for key in kpi_file1:
#     kpi_raw = str(kpi_file1.get(key, "")).strip()
#     goal_val = extract_numeric(goal_file1.get(key))

#     # Default variance
#     variance = ""

#     if " / " in kpi_raw:
#         parts = kpi_raw.split(" / ")

#         if "Maintenance / Repair Work Mix" in key:
#             # Use the FIRST part for Maintenance / Repair Work Mix
#             try:
#                 numeric_part = extract_numeric(parts[0])
#                 if numeric_part is not None and goal_val is not None:
#                     variance = round(numeric_part - goal_val, 4)
#             except:
#                 variance = ""
#         else:
#             # Use the LAST part for all others (supporting 2-part or 3-part formats)
#             try:
#                 numeric_part = extract_numeric(parts[-1])  # last part (e.g. 43%, or 32.1%)
#                 if numeric_part is not None and goal_val is not None:
#                     variance = round(numeric_part - goal_val, 4)
#             except:
#                 variance = ""
#     else:
#         # fallback: regular KPI - Goal
#         kpi_val = extract_numeric(kpi_raw)
#         if kpi_val is not None and goal_val is not None:
#             variance = round(kpi_val - goal_val, 4)

#     variance_file1[key] = variance



# log_info(variance_file1,"variance_file1=============")
# # Get all keys involved
# all_keys = set(kpi_file1.keys()).union(kpi_file2.keys()).union(goal_file1.keys()).union(goal_file2.keys()).union(variance_file1.keys()).union(variance_file2.keys())

# # Compare and collect results
# comparison_result = []
# def get_all_numeric_parts_rounded(s):
#     if not s:
#         return []
#     raw_parts = re.findall(r"[-+]?\d*\.\d+|\d+", str(s).replace(",", "").replace("$", "").replace("%", ""))
#     return [str(int(round(float(part)))) for part in raw_parts]
# for key in sorted(all_keys):
#     kpi1 = kpi_file1.get(key, "")
#     goal1 = goal_file1.get(key, "")
#     var1 = variance_file1.get(key, "")

#     kpi2 = kpi_file2.get(key, "")
#     goal2 = goal_file2.get(key, "")
#     var2 = variance_file2.get(key, "")
   
#     def all_numeric_parts_match(val1, val2):
#         parts1 = get_all_numeric_parts_rounded(val1)
#         parts2 = get_all_numeric_parts_rounded(val2)
#         return parts1 == parts2

#     is_match = (
#     all_numeric_parts_match(kpi1, kpi2) and
#     all_numeric_parts_match(goal1, goal2) and
#     all_numeric_parts_match(var1, var2)
#     )



#     # is_match = (
#     #     extract_numeric(kpi1) == extract_numeric(kpi2) and
#     #     extract_numeric(goal1) == extract_numeric(goal2) and
#     #     extract_numeric(var1) == extract_numeric(var2)
#     # )

#     comparison_result.append([
#         key, goal1, kpi1, var1, goal2, kpi2, var2, is_match
#     ])

# # Save to CSV
# csv_file = "comparison_kpi_advisor_detailed.csv"
# with open(csv_file, "w", newline="", encoding="utf-8") as file:
#     writer = csv.writer(file)
#     writer.writerow([
#         "Field Name", "Goal (File1)", "KPI (File1)", "Variance (File1)",
#         "Goal (File2)", "KPI (File2)", "Variance (File2)", "Match (True/False)"
#     ])
#     writer.writerows(comparison_result)

# # Save to Excel with highlights
# xlsx_file = "comparison_kpi_advisor_detailed_highlighted.xlsx"
# wb = openpyxl.Workbook()
# ws = wb.active
# ws.title = "Comparison Results"

# with open(csv_file, "r", encoding="utf-8") as file:
#     reader = csv.reader(file)
#     for row in reader:
#         ws.append(row)

# yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
# match_col = ws.max_column

# for row in ws.iter_rows(min_row=2, max_row=ws.max_row, min_col=match_col, max_col=match_col):
#     for cell in row:
#         if str(cell.value).lower() == "false":
#             for c in ws[cell.row]:
#                 c.fill = yellow_fill

# wb.save(xlsx_file)
# log_info(f"Excel file with mismatches highlighted saved as '{xlsx_file}'")




# # Define filenames
# # csv_file = "comparison_results_cleaned.csv"
# # xlsx_file = "comparison_kpi_advisor_detailed_highlighted.xlsx"

# # # Determine sheet name based on advisor_set
# # sheet_name = "All Advisors" if os.environ.get("advisor") == "all" else "Advisor"

# # # Create or open existing workbook
# # if os.path.exists(xlsx_file):
# #     wb = load_workbook(xlsx_file)
# #     # Remove existing sheet with the same name to avoid duplicates
# #     if sheet_name in wb.sheetnames:
# #         std = wb[sheet_name]
# #         wb.remove(std)
# # else:
# #     wb = openpyxl.Workbook()
# #     default_sheet = wb.active
# #     wb.remove(default_sheet)

# # # Add new sheet
# # ws = wb.create_sheet(title=sheet_name)

# # # Read CSV and populate Excel sheet
# # with open(csv_file, "r", encoding="utf-8") as file:
# #     reader = csv.reader(file)
# #     for row in reader:
# #         ws.append(row)

# # # Define yellow fill for highlighting
# # yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

# # # Get column index of "Match (True/False)" (assumed to be in the last column)
# # match_column_index = ws.max_column

# # # Apply highlighting for mismatched rows
# # for row in ws.iter_rows(min_row=2, max_row=ws.max_row, min_col=match_column_index, max_col=match_column_index):
# #     for cell in row:
# #         if str(cell.value).strip().lower() == "false":
# #             for cell_to_fill in ws[cell.row]:
# #                 cell_to_fill.fill = yellow_fill

# # # Save updated workbook
# # wb.save(xlsx_file)
# # log_info(f"Excel file with sheet '{sheet_name}' saved as '{xlsx_file}'")
import json
import csv
import re
import openpyxl
from openpyxl.styles import PatternFill
import os
from dotenv import load_dotenv
from lib.std.universal.logger import logger, log_info, log_warn, log_error

load_dotenv()
advisor_set = os.environ.get('advisor')
# log_info(advisor_set, "advisor_set.....................")
# def clean_value(value):
#     if isinstance(value, str):
#         value = value.replace("$", "").replace(",", "").replace("%", "").strip()
#         try:
#             return float(value)  # Convert to float if possible
#         except ValueError:
#             return 0 if value in [None, "nan", "NaN", ""] else value 
#     return value  # Return numeric values as is

def extract_numeric(val):
    if val is None:
        return None
    if isinstance(val, (int, float)):
        value = value.replace("$", "").replace(",", "").replace("%", "").strip()
        return float(val)
    match = re.search(r"[-+]?\d*\.\d+|\d+", str(val).replace(",", ""))
    return float(match.group()) if match else None

def flatten_kpi_blocks(json_array):
    flat_data = {}
    for item in json_array:
        if not isinstance(item, dict):
            continue
        for key, value in item.items():
            if key in ("Goal", "Variance"):
                continue
            flat_data[key.strip()] = value
    return flat_data

def extract_goal_variance(json_array, key_name):
    for item in json_array:
        if key_name in item:
            return item[key_name]
    return {}

if advisor_set == 'all':
    with open("Json-files/results_set.json", "r", encoding="utf-8") as file1:
        data1 = json.load(file1)
    with open("Json-files/kpi-advisor.md", "r", encoding="utf-8") as file2:
        content2 = file2.read().strip()
else:
    with open("Json-files/results_set_advisor.json", "r", encoding="utf-8") as file1:
        data1 = json.load(file1)
    with open("Json-files/kpi_advisor_single.md", "r", encoding="utf-8") as file2:
        content2 = file2.read().strip()

try:
    if not content2.startswith("["):
        fixed_content = "[" + re.sub(r"}\s*,?\s*{", "},\n{", content2.strip()) + "]"
        data2 = json.loads(fixed_content)
    else:
        data2 = json.loads(content2)
except json.JSONDecodeError as e:
    log_error("kpi-advisor.md does not contain valid JSON:", e, "content snippet:", content2[:200])
    raise ValueError(f"kpi-advisor.md does not contain valid JSON: {e}")

kpi_file1 = flatten_kpi_blocks(data1)
kpi_file2 = flatten_kpi_blocks(data2)

goal_file1 = extract_goal_variance(data1, "Goal")
goal_file2 = extract_goal_variance(data2, "Goal")
variance_file2 = extract_goal_variance(data2, "Variance")

variance_file1 = extract_goal_variance(data1, "Variance")

all_keys = set(kpi_file1.keys()).union(kpi_file2.keys(), goal_file1.keys(), goal_file2.keys(), variance_file1.keys(), variance_file2.keys())
comparison_result = []

def get_all_numeric_parts_rounded(s):
    # log_info(s,"sssssssssssssss")
    if s == 0.0:
        return ['0.0']
    if not s:
        return []
    # s = s.replace("$", "").replace(",", "").replace("%", "").strip()
    raw_parts = re.findall(r"[-+]?\d*\.\d+|\d+", str(s).replace(",", "").replace("$", "").replace("%", ""))
    return [str(float(part)) for part in raw_parts]


def numeric_match(val1, val2, kpi_name=None):
    parts1 = get_all_numeric_parts_rounded(val1)    
    parts2 = get_all_numeric_parts_rounded(val2)
    log_info(parts1, "parts1", parts2, "parts2..........")
    if kpi_name == "Parts to Labor Ratio":        
        try:
            part1 = float(parts1[0]) if parts1 else None
            part2 = float(parts2[0]) if parts2 else None
            log_info(part1, part2, "part1, part2..............")
            match = part1 == part2
            log_info(match, "part1 == part2...............")
            return match
        except (ValueError, IndexError):
            log_error(f"[WARN] Could not convert to float: {parts1} or {parts2}")
            return False

    return parts1 == parts2        



for key in sorted(all_keys):
    kpi1 = kpi_file1.get(key, "")
    goal1 = goal_file1.get(key, "")
    var1 = variance_file1.get(key, "")

    kpi2 = kpi_file2.get(key, "")
    goal2 = goal_file2.get(key, "")
    var2 = variance_file2.get(key, "")
    nmatch=numeric_match(kpi1, kpi2, kpi_name=key) 
    log_info(nmatch,"numeric_match....kpi",kpi1)
    is_match = (
    numeric_match(kpi1, kpi2, kpi_name=key) and    
    numeric_match(goal1, goal2, kpi_name=key) and
    numeric_match(var1, var2, kpi_name=key)
    )


    comparison_result.append([
        key, goal1, kpi1, var1, goal2, kpi2, var2, is_match
    ])

csv_file = "comparison_kpi_advisor_detailed.csv"
with open(csv_file, "w", newline="", encoding="utf-8") as file:
    writer = csv.writer(file)
    writer.writerow([
        "Field Name", "Goal (File1)", "KPI (File1)", "Variance (File1)",
        "Goal (File2)", "KPI (File2)", "Variance (File2)", "Match (True/False)"
    ])
    writer.writerows(comparison_result)

xlsx_file = "comparison_kpi_advisor_detailed_highlighted.xlsx"

# Load or create workbook
if os.path.exists(xlsx_file):
    wb = openpyxl.load_workbook(xlsx_file)
else:
    wb = openpyxl.Workbook()
    default_sheet = wb.active
    wb.remove(default_sheet)

# Determine sheet name based on advisor_set
sheet_name = "all_advisors_comparision" if advisor_set == 'all' else "single_advisor_comparision"

# If sheet already exists, remove it to update data
if sheet_name in wb.sheetnames:
    del wb[sheet_name]

# Create new sheet
ws = wb.create_sheet(title=sheet_name)

# Write header
ws.append([
    "Field Name", "Goal (File1)", "KPI (File1)", "Variance (File1)",
    "Goal (File2)", "KPI (File2)", "Variance (File2)", "Match (True/False)"
])

# Write data
for row in comparison_result:
    ws.append(row)

# Highlight mismatches
yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
match_col = ws.max_column

for row in ws.iter_rows(min_row=2, max_row=ws.max_row, min_col=match_col, max_col=match_col):
    for cell in row:
        if str(cell.value).lower() == "false":
            for c in ws[cell.row]:
                c.fill = yellow_fill

# Save workbook
wb.save(xlsx_file)
log_info(f"Data written to sheet '{sheet_name}' in '{xlsx_file}'")

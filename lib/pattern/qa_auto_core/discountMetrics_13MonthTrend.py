import math
import sys
sys.path.append('../')
from datetime import datetime
from collections import Counter
import pandas as pd
import os
import openpyxl
import numpy as np
from decimal import Decimal, ROUND_HALF_UP
from db_handler.db_connector import getCustomerPayTypeGroupsList, DiscountLaborDetails, DiscountPartsDetails

import math

def round_off(n, decimals=0):
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal('1'), rounding=ROUND_HALF_UP) / multiplier)
#Function used for checking zero sales
def zero_sales_check(df, columns):
    total_sum = df[columns].sum().sum()
    return total_sum == 0
def convert_and_sum(df, column_name):
    return pd.to_numeric(df[column_name], errors='coerce').fillna(0).sum()

def convert_and_sum_labor_discount(df, column_name):
    return pd.to_numeric(df[column_name], errors='coerce').fillna(0).sum()

def convert_and_sum_parts_discount(df, column_name):
    return pd.to_numeric(df[column_name], errors='coerce').fillna(0).sum()

columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
columns_to_convert_labor_discount = ['apportionedlbrdiscount','lbrsale','lbrcost']
columns_to_convert_parts_discount = ['apportionedlbrdiscount','prtssale','prtcost']


retail_flag_DB_connect = getCustomerPayTypeGroupsList()
retail_flag = retail_flag_DB_connect.getCustomerPayTypeList()

#Get the required environment variables
store_id = os.environ.get('store_id')
realm = os.environ.get('realm') 
s_date_env = os.environ.get('start_date')
e_date_env = os.environ.get('end_date')

advisor_set = os.environ.get('advisor')
tech_set = os.environ.get('technician')

if ',' in advisor_set:
    advisor = set(x.strip() for x in advisor_set.split(','))
else:
    advisor = {advisor_set.strip()}

if ',' in tech_set:
    tech = set(x.strip() for x in tech_set.split(','))
else:
    tech = {tech_set.strip()}

# Coverting the start and end date with required format
s_year, s_month, s_date = map(int, s_date_env.split('-'))
e_year, e_month, e_date = map(int, e_date_env.split('-'))
s_date_f = (s_year, s_month, s_date)
e_date_f = (e_year, e_month, e_date)


start_date = datetime(*s_date_f)
end_date = datetime(*e_date_f)

labor_discount_table_db_connect = DiscountLaborDetails()
labor_discount_details_df = labor_discount_table_db_connect.getTableResult()
labor_discount_details_df.to_csv('../Output/labor_discount_details.csv')
parts_discount_table_db_connect = DiscountPartsDetails()
parts_discount_details_df = parts_discount_table_db_connect.getTableResult()
parts_discount_details_df.to_csv('../Output/parts_discount_details.csv')

all_revenue_details = pd.read_csv('../Output/all_revenue_details.csv', na_values=[], keep_default_na=False)
all_revenue_details['closeddate'] = pd.to_datetime(all_revenue_details['closeddate'], errors= 'coerce')

all_revenue_details_df = all_revenue_details[
    (all_revenue_details['closeddate'] >= start_date) &
    (all_revenue_details['closeddate'] <= end_date)
    ]
all_revenue_details_df = all_revenue_details_df[
    (all_revenue_details_df['department'] == 'Service') & 
    # (all_revenue_details_df['opcategory'] != 'N/A') & 
    # (all_revenue_details_df['opcategory'] != 'SHOP SUPPLIES') &
    (all_revenue_details_df['opcategory'].isin(['REPAIR','COMPETITIVE','MAINTENANCE'])) &
    (all_revenue_details_df['hide_ro'].astype(int) != 1)
    ]

columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']

all_revenue_details_df = all_revenue_details_df.copy()
# Replace empty strings or spaces with NaN
all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)

# Convert to numeric
all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))

all_revenue_details_df.to_csv('../Output/all_revenue_details_df123.csv')
#all_revenue_details_df = all_revenue_details_df[all_revenue_details_df['department'] == 'Service']

all_revenue_details_df = all_revenue_details_df.copy()  # Create a deep copy of all_revenue_details_df to avoid the warning

# RO number with different closeddate will be considered as different RO, joining RO number and closeddate to find out the unique RO number
all_revenue_details_df['unique_ro_number'] = all_revenue_details_df['ronumber'].astype(str) + '_' + all_revenue_details_df['closeddate'].astype(str)

# Define customer and warranty pay types dynamically
if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
    customer_pay_types = {'C'}
    warranty_pay_types = {'W', 'F', 'M', 'E'}
elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
    customer_pay_types = {'C', 'M'}
    warranty_pay_types = {'W', 'F', 'E'}
elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
    customer_pay_types = {'C', 'E'}
    warranty_pay_types = {'W', 'F', 'M'}
elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
    customer_pay_types = {'C', 'E', 'M'}
    warranty_pay_types = {'W', 'F'}

total_CP_revenue_details_df = all_revenue_details_df[all_revenue_details_df['paytypegroup'].isin(customer_pay_types)]
# Coverting it to data frame
#total_CP_revenue_details_df = pd.DataFrame(list_of_paytypegroup_C)
#total_CP_revenue_details_df.to_csv('../Output/Paytype_C_list.csv')

total_CP_revenue_details_df = total_CP_revenue_details_df.copy()

total_CP_revenue_details_df[columns_to_convert] = total_CP_revenue_details_df[columns_to_convert].apply(pd.to_numeric, errors='coerce').fillna(0)

total_CP_revenue_details_df = total_CP_revenue_details_df[
~((total_CP_revenue_details_df['lbrsale'].fillna(0) == 0) &
    (total_CP_revenue_details_df['lbrsoldhours'].fillna(0) == 0) &
    (total_CP_revenue_details_df['prtextendedsale'].fillna(0) == 0) &
    (total_CP_revenue_details_df['prtextendedcost'].fillna(0) == 0))
]

total_CP_revenue_details_df.to_csv('../Output/Total_Revenue_Details.csv')
filtered_df = total_CP_revenue_details_df.copy()

labor_discount_details_df = labor_discount_details_df.copy()
labor_discount_details_df[columns_to_convert_labor_discount] = labor_discount_details_df[columns_to_convert_labor_discount].apply(pd.to_numeric, errors='coerce').fillna(0)

if not filtered_df.empty:
    
    # Apply filters based on the conditions
    if advisor == {'all'} and tech == {'all'}:
        # No filtering needed
        matching_ro_numbers = total_CP_revenue_details_df['unique_ro_number'].unique()
    elif advisor != {'all'} and tech == {'all'}:
        # Filter based on advisor only
        matching_ro_numbers =  total_CP_revenue_details_df.loc[total_CP_revenue_details_df['serviceadvisor'].astype(str).isin(advisor), 'unique_ro_number'].unique()
    elif advisor == {'all'} and tech != {'all'}:
        # Filter based on tech only
        matching_ro_numbers = total_CP_revenue_details_df.loc[total_CP_revenue_details_df['lbrtechno'].astype(str).isin(tech), 'unique_ro_number'].unique()
    elif advisor != {'all'} and tech != {'all'}:
        # Filter based on both advisor and tech
        matching_ro_numbers = total_CP_revenue_details_df.loc[(total_CP_revenue_details_df['serviceadvisor'].astype(str).isin(advisor)) & 
            (total_CP_revenue_details_df['lbrtechno'].astype(str).isin(tech)), 'unique_ro_number'].unique()
    # Applying the Advisor and tech filter conditions
    total_CP_revenue_details_df = total_CP_revenue_details_df[total_CP_revenue_details_df['unique_ro_number'].isin(matching_ro_numbers)]
    total_CP_revenue_details_df = total_CP_revenue_details_df.reset_index(drop=True)
    total_CP_revenue_details_df.loc[total_CP_revenue_details_df['opcategory'] == 'N/A', columns_to_check] = 0

    total_CP_revenue_details_df.to_csv('../Output/total_CP_revenue_details_df_new.csv')
    
    Labor_Sales = convert_and_sum(total_CP_revenue_details_df,'lbrsale')
    Labor_Cost  = convert_and_sum(total_CP_revenue_details_df, 'lbrcost') 
    Total_Hours = convert_and_sum(total_CP_revenue_details_df, 'lbrsoldhours')
    Parts_Sales = convert_and_sum(total_CP_revenue_details_df, 'prtextendedsale')
    Parts_Cost  = convert_and_sum(total_CP_revenue_details_df, 'prtextendedcost')
    overall_RO_Count = total_CP_revenue_details_df['unique_ro_number'].nunique()
    total_Sales = Labor_Sales + Parts_Sales

    #Labor
    apportioned_Lbr_Discount = convert_and_sum_labor_discount(labor_discount_details_df,'apportionedlbrdiscount')
    discount_Sale_Labor =   convert_and_sum_labor_discount(labor_discount_details_df,'lbrsale')
    discount_Cost_Labor =   convert_and_sum_labor_discount(labor_discount_details_df,'lbrcost')
    discounted_RO_Count_Labor = labor_discount_details_df['ronumber'].nunique()

    #Parts
    apportioned_Parts_Discount = convert_and_sum_parts_discount(parts_discount_details_df,'apportionedlbrdiscount')
    discount_Sale_Parts =   convert_and_sum_parts_discount(parts_discount_details_df,'prtssale')
    discount_Cost_Parts =   convert_and_sum_parts_discount(parts_discount_details_df,'prtcost')
    discounted_RO_Count_Parts = parts_discount_details_df['ronumber'].nunique()
       
    total_Discount = apportioned_Lbr_Discount + apportioned_Parts_Discount
    total_Discount_Sales = discount_Sale_Labor + discount_Sale_Parts
    discounted_RO_Count_RO_Level = labor_discount_details_df[labor_discount_details_df['dislevel'] == "RO"]['ronumber'].nunique()
    discounted_RO_Count_LINE_Level = labor_discount_details_df[labor_discount_details_df['dislevel'] == "LINE"]['ronumber'].nunique()
    discounted_RO_Count_LOP_Level = labor_discount_details_df[labor_discount_details_df['dislevel'] == "LOP"]['ronumber'].nunique()
    
    cp_Discounted_RO_percent = (discounted_RO_Count_Labor / overall_RO_Count) * 100 if overall_RO_Count != 0 else 0
  
    total_Sale_percent = (total_Discount*100/total_Sales) if total_Sales !=0 else 0
    labor_Sale_percent = (apportioned_Lbr_Discount*100/Labor_Sales) if Labor_Sales!=0 else 0
    parts_Sale_percent = (apportioned_Parts_Discount*100/Parts_Sales) if Parts_Sales!=0 else 0
   
    if total_Discount_Sales !=0:
        cp_percent_Disc_Per_Discounted_CP_ROs = (total_Discount*100/total_Discount_Sales)
        cp_percent_Disc_Per_Discounted_CP_ROs_labor = (apportioned_Lbr_Discount*100/total_Discount_Sales) 
        cp_percent_Disc_Per_Discounted_CP_ROs_parts = (apportioned_Parts_Discount*100/total_Discount_Sales)
    else:
        cp_percent_Disc_Per_Discounted_CP_ROs =0
        cp_percent_Disc_Per_Discounted_CP_ROs_labor =0
        cp_percent_Disc_Per_Discounted_CP_ROs_parts =0
    if overall_RO_Count !=0:
        labor_Discounts_Per_Total_CP_ROs = apportioned_Lbr_Discount/overall_RO_Count
        parts_Discounts_Per_Total_CP_ROs = apportioned_Parts_Discount/overall_RO_Count
        total_Discounts_Per_Total_CP_ROs = total_Discount/overall_RO_Count
    else:
        labor_Discounts_Per_Total_CP_ROs = 0
        parts_Discounts_Per_Total_CP_ROs = 0
        total_Discounts_Per_Total_CP_ROs = 0
    
    # # discount_details = DiscountDetails()
    # # discounted_RO_Count_total = discount_details.getTableResult()
    # print("ronumber",discounted_RO_Count_total)
    # total_CP_Total_Disc_Avg_Disc_ROs = float(total_Discount/discounted_RO_Count_total) 
    # if discounted_RO_Count_total !=0 else 0
    labor_CP_Total_Disc_Avg_Disc_ROs = apportioned_Lbr_Discount/discounted_RO_Count_Labor if discounted_RO_Count_Labor!=0 else 0
    parts_CP_Total_Disc_Avg_Disc_ROs = apportioned_Parts_Discount/discounted_RO_Count_Parts if discounted_RO_Count_Parts!=0 else 0
    total_CP_Total_Disc_Avg_Disc_ROs = labor_CP_Total_Disc_Avg_Disc_ROs + parts_CP_Total_Disc_Avg_Disc_ROs
    

    print("***CP Discounts - Labor & Parts 1234 ***")    
    print("Total Labor Discount:",round_off(apportioned_Lbr_Discount,2))      
    print("Total Parts Discount:",round_off(apportioned_Parts_Discount,2))     
    print("Total Discount:",total_Discount)   

    print("\n***CP RO Count for Disc by Disc Level 1113***")
    print("Discounted RO Counts at RO Level: ",discounted_RO_Count_RO_Level)
    print("Discounted RO Counts at Line Level: ",discounted_RO_Count_LINE_Level)
    print("Discounted RO Counts at LOP Level: ",discounted_RO_Count_LOP_Level)
    
    print("\n***CP Discounted RO % 1123***")
    print("CP_Discounted_RO_percent: ",round_off(cp_Discounted_RO_percent,2),"%")

    print("\n***CP % Disc of Total $ Sold 1115***")
    print("Total Sales %: ", round_off(total_Sale_percent,2),"%")
    print("Labor Sales %", round_off(labor_Sale_percent,2),"%")
    print("Parts Sales %", round_off(parts_Sale_percent,2),"%")

    print("\n***CP % Disc Per Discounted CP ROs 1232***")
    print("CP % Disc Per Discounted CP ROs", round_off(cp_percent_Disc_Per_Discounted_CP_ROs,2))
    print("Labor CP % Disc Per Discounted CP ROs",round_off(cp_percent_Disc_Per_Discounted_CP_ROs_labor,2))
    print("Parts CP % Disc Per Discounted CP ROs",round_off(cp_percent_Disc_Per_Discounted_CP_ROs_parts,2))
    
    print("\n***Discounts Per Total CP ROs 1236***")
    print("Labor Discounts Per Total CP ROs",round_off(labor_Discounts_Per_Total_CP_ROs,2))
    print("Parts Discounts Per Total CP ROs",round_off(parts_Discounts_Per_Total_CP_ROs,2))
    print("Total Discounts Per Total CP ROs",round_off(total_Discounts_Per_Total_CP_ROs,2))

    print("\n***CP Total Disc $ Avg of Disc ROs 1165***")
    print("Total CP Total Disc $ Avg of Disc ROs",round_off(total_CP_Total_Disc_Avg_Disc_ROs,2))
    print("Labor CP Total Disc $ Avg of Disc ROs",round_off(labor_CP_Total_Disc_Avg_Disc_ROs,2))
    print("Parts CP Total Disc $ Avg of Disc ROs",round_off(parts_CP_Total_Disc_Avg_Disc_ROs,2))

path = "../Output/results_set.xlsx"
# Define your sheet name
sheet_name = "DiscountMetrics_13MonthTrend"

result_set = [
    ["CP Discounts - Labor & Parts 1234", None],
    ["Total Labor Discount:", round_off(apportioned_Lbr_Discount, 2)],
    ["Total Parts Discount:", round_off(apportioned_Parts_Discount, 2)],
    ["Total Discount:", round_off(total_Discount,2)],

    ["CP RO Count for Disc by Disc Level 1113", None],
    ["Discounted RO Counts at RO Level:", discounted_RO_Count_RO_Level],
    ["Discounted RO Counts at Line Level:", discounted_RO_Count_LINE_Level],
    ["Discounted RO Counts at LOP Level:", discounted_RO_Count_LOP_Level],

    ["CP Discounted RO % 1123***", None],
    ["CP_Discounted_RO_percent:", round_off(cp_Discounted_RO_percent, 2)],

    ["CP % Disc of Total $ Sold 1115", None],
    ["Total Sales %:", round_off(total_Sale_percent, 2)],
    ["Labor Sales %:", round_off(labor_Sale_percent, 2)],
    ["Parts Sales %:", round_off(parts_Sale_percent, 2)],

    ["CP % Disc Per Discounted CP ROs 1232", None],
    ["CP % Disc Per Discounted CP ROs:", round_off(cp_percent_Disc_Per_Discounted_CP_ROs, 2)],
    ["Labor CP % Disc Per Discounted CP ROs:", round_off(cp_percent_Disc_Per_Discounted_CP_ROs_labor, 2)],
    ["Parts CP % Disc Per Discounted CP ROs:", round_off(cp_percent_Disc_Per_Discounted_CP_ROs_parts, 2)],

    ["Discounts Per Total CP ROs 1236", None],
    ["Labor Discounts Per Total CP ROs:", round_off(labor_Discounts_Per_Total_CP_ROs, 2)],
    ["Parts Discounts Per Total CP ROs:", round_off(parts_Discounts_Per_Total_CP_ROs, 2)],
    ["Total Discounts Per Total CP ROs:", round_off(total_Discounts_Per_Total_CP_ROs, 2)],

    ["CP Total Disc $ Avg of Disc ROs 1165", None],
    ["Total CP Total Disc $ Avg of Disc ROs:", round_off(total_CP_Total_Disc_Avg_Disc_ROs, 2)],
    ["Labor CP Total Disc $ Avg of Disc ROs:", round_off(labor_CP_Total_Disc_Avg_Disc_ROs, 2)],
    ["Parts CP Total Disc $ Avg of Disc ROs:", round_off(parts_CP_Total_Disc_Avg_Disc_ROs, 2)]
]

output_df = pd.DataFrame(result_set)

try:
    # Try to load the workbook
    with pd.ExcelWriter(path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
        output_df.to_excel(writer, sheet_name=sheet_name, index=False, header=False)

except FileNotFoundError:
    # If file doesn't exist, create a new one
    with pd.ExcelWriter(path, engine='openpyxl', mode='w') as writer:
        output_df.to_excel(writer, sheet_name=sheet_name, index=False, header=False)

print("Discount Metrics 13 Month Trend completed successfully")
   







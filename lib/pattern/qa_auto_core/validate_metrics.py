import asyncio
import json
import os
import time
import glob
import math
import traceback
import pandas as pd
import numpy as np
from datetime import datetime
from math import isfinite, isnan
from playwright.async_api import async_playwright
from concurrent.futures import ThreadPoolExecutor
import threading
from datetime import datetime
from collections import defaultdict   
import re
from decimal import Decimal, ROUND_HALF_UP
import openpyxl
import openpyxl.cell
from openpyxl.styles import <PERSON><PERSON><PERSON>ill, Alignment, Font
from openpyxl.utils import get_column_letter
import csv
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.pattern.config import config
from lib.pattern.qa_auto_core.compare_special_metrics import compare_special_metrics_results
from dateutil.relativedelta import relativedelta
from datetime import timedelta
from lib.pattern.qa_auto_core.db_handler.db_connector import (
    getCustomerPayTypeGroupsList, menuMasterTableResult, menuServiceTypeTableResult,
    assignedMenuModelsTableResult, assignedMenuOpcodesTableResult, MPISetupTableResult,
    MPIOpcodesTableResult, allRevenueDetailsCPOverview, allRevenueDetailsForShopSuppliesTable,
    paytypeRetailFlagSettingTable, shopSuppliesDataTable,allRevenueDetailsTable
)

from lib.std.universal.authmanager import AuthManager
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.std.universal.chart_dict import VALIDATION_CHARTS
chart_key="special_metrics"
chart_process_json = VALIDATION_CHARTS[chart_key]["chart_process_json"]
db_json = VALIDATION_CHARTS[chart_key]["db_json"]

#  Target months-years for drilling down (modify as needed)
TARGET_MONTHS_YEARS = config.target_month_year
# Configuration constants
MAX_CONCURRENT_BROWSERS = 3
BROWSER_TIMEOUT = 30000

namespace = {
    'ns': 'http://www.dmotorworks.com/service-repair-order-history'
}

def round_off(n, decimals=0):
    """Round off numbers with proper decimal handling"""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def zero_sales_check(df, columns):
    """Function used for checking zero sales"""
    total_sum = df[columns].sum().sum()
    return total_sum == 0

def get_month_date_range_from_target(target_date_str):
    """Get the start and end date for the target month"""
    # Parse the target date string
    target_date = datetime.strptime(target_date_str, "%Y-%m-%d")
    # Get first day of the target month
    month_start = target_date.replace(day=1)
    # Get last day of the target month
    month_end = month_start + relativedelta(months=1) - timedelta(days=1)
    return month_start, month_end

def get_database_tables():
    """Fetch all required database tables"""
    log_info("Fetching database tables...")

    # Get retail flag
    retail_flag_DB_connect = getCustomerPayTypeGroupsList()
    retail_flag_df = retail_flag_DB_connect.getCustomerPayTypeList()
    retail_flag = set(retail_flag_df['source_paytype'].tolist()) if not retail_flag_df.empty else {'C'}
    log_info(f"Retail flag from database: {retail_flag}")

    # Get menu master table
    menu_master_db_connect = menuMasterTableResult()
    menu_master_df = menu_master_db_connect.getTableResult()

    # Get menu service type table
    menu_service_type_db_connect = menuServiceTypeTableResult()
    menu_service_type_df = menu_service_type_db_connect.getTableResult()

    # Get assigned menu models table
    assigned_menu_models_db_connect = assignedMenuModelsTableResult()
    assigned_menu_models_df = assigned_menu_models_db_connect.getTableResult()

    # Get assigned menu opcodes table
    assigned_menu_opcodes_db_connect = assignedMenuOpcodesTableResult()
    assigned_menu_opcodes_df = assigned_menu_opcodes_db_connect.getTableResult()

    # Get MPI setup table
    MPI_setup_db_connect = MPISetupTableResult()
    MPI_setup_df = MPI_setup_db_connect.getTableResult()

    # Get MPI opcodes table
    MPI_opcodes_db_connect = MPIOpcodesTableResult()
    mpi_opcodes = MPI_opcodes_db_connect.getTableResult()

    return {
        'retail_flag': retail_flag,
        'menu_master_df': menu_master_df,
        'menu_service_type_df': menu_service_type_df,
        'assigned_menu_models_df': assigned_menu_models_df,
        'assigned_menu_opcodes_df': assigned_menu_opcodes_df,
        'MPI_setup_df': MPI_setup_df,
        'mpi_opcodes': mpi_opcodes
    }

def process_advisor_tech_filters(advisor_set, tech_set):
    """Process advisor and technician filter configurations"""
    # Process advisor configuration
    if isinstance(advisor_set, str):
        if advisor_set.lower() == 'all':
            advisor = {'all'}
        elif ',' in advisor_set:
            advisor = {x.strip() for x in advisor_set.split(',')}
        else:
            advisor = {advisor_set.strip()}
    else:
        advisor = {'all'}

    # Process technician configuration
    if isinstance(tech_set, str):
        if tech_set.lower() == 'all':
            tech = {'all'}
        elif ',' in tech_set:
            tech = {x.strip() for x in tech_set.split(',')}
        else:
            tech = {tech_set.strip()}
    else:
        tech = {'all'}

    return advisor, tech

def define_customer_warranty_pay_types(retail_flag):
    """Define customer and warranty pay types dynamically based on retail flag"""
    log_info(f"Defining pay types for retail_flag: {retail_flag}")

    if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
        customer_pay_types = {'C'}
        warranty_pay_types = {'W', 'F', 'M', 'E'}
    elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
        customer_pay_types = {'C', 'M'}
        warranty_pay_types = {'W', 'F', 'E'}
    elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
        customer_pay_types = {'C', 'E'}
        warranty_pay_types = {'W', 'F', 'M'}
    elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
        customer_pay_types = {'C', 'E', 'M'}
        warranty_pay_types = {'W', 'F'}
    else:
        customer_pay_types = {'C'}
        warranty_pay_types = {'W', 'F', 'M', 'E'}

    log_info(f"Customer pay types: {customer_pay_types}")
    log_info(f"Warranty pay types: {warranty_pay_types}")

    return customer_pay_types, warranty_pay_types

def assign_ro_groups(combined_revenue_details, customer_pay_types, warranty_pay_types, columns_to_check):
    """Assign groups (C, W, I) to ROs based on pay types and zero sales check"""
    log_info("Assigning RO groups...")

    # Create a temporary version for zero check without modifying the original data
    temp_revenue_details = combined_revenue_details.copy()
    temp_revenue_details.loc[temp_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0

    # Iterate through each unique RO number for group assignment
    for ro_number in combined_revenue_details['unique_ro_number'].unique():
        ro_specific_rows = temp_revenue_details[temp_revenue_details['unique_ro_number'] == ro_number]
        ro_specific_rows_C = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(customer_pay_types)]
        ro_specific_rows_W = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(warranty_pay_types)]
        zero_sales_C = zero_sales_check(ro_specific_rows_C, columns_to_check)
        zero_sales_W = zero_sales_check(ro_specific_rows_W, columns_to_check)

        if not ro_specific_rows_C.empty and not zero_sales_C:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'C'
        elif not ro_specific_rows_W.empty and not zero_sales_W:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'W'
        else:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'I'

    return combined_revenue_details

def apply_advisor_tech_filters(combined_revenue_details, advisor, tech, columns_to_check, original_filtered_df):
    """Apply filters based on advisor and tech conditions"""
    log_info("Applying advisor and tech filters...")

    if advisor == {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details['unique_ro_number'].unique()
    elif advisor != {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['serviceadvisor'].astype(str).isin(advisor), 'unique_ro_number'].unique()
    elif advisor == {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['lbrtechno'].astype(str).isin(tech), 'unique_ro_number'].unique()
    elif advisor != {'all'} and tech != {'all'}:
        # This replicates the bug in the original code where it uses original_filtered_df instead of combined_revenue_details
        matching_ro_numbers = combined_revenue_details.loc[(original_filtered_df['serviceadvisor'].astype(str).isin(advisor)) &
            (original_filtered_df['lbrtechno'].astype(str).isin(tech)), 'unique_ro_number'].unique()

    # Apply the advisor and tech filter conditions
    combined_revenue_details = combined_revenue_details[combined_revenue_details['unique_ro_number'].isin(matching_ro_numbers)]
    combined_revenue_details = combined_revenue_details.reset_index(drop=True)
    combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0

    return combined_revenue_details

def calculate_ro_counts(combined_revenue_details):
    """Calculate RO counts by group"""
    Scorecard_10_CP = combined_revenue_details.loc[combined_revenue_details['group'] == 'C', 'unique_ro_number'].nunique()
    Scorecard_10_Wty = combined_revenue_details.loc[combined_revenue_details['group'] == 'W', 'unique_ro_number'].nunique()
    Scorecard_10_Int = combined_revenue_details.loc[combined_revenue_details['group'] == 'I', 'unique_ro_number'].nunique()
    all_unique_ros = Scorecard_10_CP + Scorecard_10_Wty + Scorecard_10_Int

    return Scorecard_10_CP, Scorecard_10_Wty, Scorecard_10_Int, all_unique_ros

def process_customer_pay_data(combined_revenue_details, customer_pay_types, menu_master_df, assigned_menu_models_df, menu_service_type_df, assigned_menu_opcodes_df):
    """Process customer pay data and calculate metrics"""
    log_info("Processing customer pay data...")

    # Convert the data frame to dictionary for applying conditions
    all_revenue_details_list = combined_revenue_details.to_dict('records')

    # Remove jobs with paytypegroup other than C, M, E and exclude ROs with mileage as None
    total_revenue_details_C = [
        row for row in all_revenue_details_list
        if (row['group'] == 'C') and (row['paytypegroup'] in {'C', 'E', 'M'}) and not
            (row['mileage'] is None)  # Use 'is None' for checking None type
    ]

    total_revenue_details_C_df = pd.DataFrame(total_revenue_details_C)
    log_info(f"Customer Pay ROs before zero sales filter: {total_revenue_details_C_df['unique_ro_number'].nunique() if not total_revenue_details_C_df.empty else 0}")

    total_revenue_details_C_df = total_revenue_details_C_df[
        ~((total_revenue_details_C_df['lbrsale'].fillna(0) == 0) &
        (total_revenue_details_C_df['lbrsoldhours'].fillna(0) == 0) &
        (total_revenue_details_C_df['prtextendedsale'].fillna(0) == 0) &
        (total_revenue_details_C_df['prtextendedcost'].fillna(0) == 0))
    ]
    log_info(f"Customer Pay ROs after zero sales filter: {total_revenue_details_C_df['unique_ro_number'].nunique() if not total_revenue_details_C_df.empty else 0}")

    # Identify available menus from menu master table
    menu_names = set(menu_master_df['menu_name'])

    # Store the list of available default menu
    default_menu_series = menu_master_df[menu_master_df['is_default'].astype(int) == 1]['menu_name']

    # Identify the default menu name
    if not default_menu_series.empty:
        default_menu = default_menu_series.iloc[0]
    else:
        default_menu = np.nan

    # Check whether models are assigned to the available menu
    if not assigned_menu_models_df.empty:
        # Create a mapping from 'model' to 'menu_name'
        model_to_menu_map = assigned_menu_models_df.set_index('model')['menu_name'].to_dict()
        # Map the 'model' column to the 'mapped_menu'
        total_revenue_details_C_df['mapped_menu'] = total_revenue_details_C_df['model'].map(
            model_to_menu_map).fillna(default_menu)
    else:
        # If no model mapping available all models mapped to default menu
        total_revenue_details_C_df['mapped_menu'] = default_menu

    # Initialize the menu impacted ROs as set
    ro_with_item_count_more_than_1 = set()

    # If no menus are added, all values under menu sales will be 0
    if menu_names:
        for name in menu_names:
            total_revenue_details_C_df[name] = ''

        # Create a dictionary to map service_type_id to service_type using menu_service_type table
        service_type_mapping = menu_service_type_df.set_index('id')['service_type'].to_dict()

        # Create a set of menu names from the menu_master table only if it is available on the total_revenue_details_C_df columns
        menu_names = [name for name in menu_names if name in total_revenue_details_C_df.columns]

        # Iterate over each row in total_revenue_details_C_df
        for i, row in total_revenue_details_C_df.iterrows():
            mileage = float(row['mileage']) if row['mileage'] is not None else 0

            # Initialize menu_names columns to NaN
            total_revenue_details_C_df.loc[i, menu_names] = np.nan

            # Find rows in menu_master_df where mileage is within the range
            matching_menus = menu_master_df[(pd.to_numeric(menu_master_df['range_from'], errors='coerce') <= mileage) & (
                pd.to_numeric(menu_master_df['range_to'], errors='coerce') >= mileage)]

            for _, menu_row in matching_menus.iterrows():
                menu_name = menu_row['menu_name']
                service_type_id = menu_row['service_type_id']
                item_count = menu_row['items']

                # Check if the menu_name column exists in total_revenue_details_C_df
                if menu_name in total_revenue_details_C_df.columns:
                    # Map service_type_id to service_type
                    service_type = service_type_mapping.get(service_type_id, None)

                    # Update the value of the column with service_type
                    if service_type:
                        total_revenue_details_C_df.at[i, menu_name] = service_type
                        total_revenue_details_C_df.at[i, menu_name + '_items'] = int(item_count)

    # Convert columns to numeric
    total_revenue_details_C_df['lbrsale'] = pd.to_numeric(total_revenue_details_C_df['lbrsale'], errors='coerce')
    total_revenue_details_C_df['lbrsoldhours'] = pd.to_numeric(total_revenue_details_C_df['lbrsoldhours'], errors='coerce')
    total_revenue_details_C_df['prtextendedsale'] = pd.to_numeric(total_revenue_details_C_df['prtextendedsale'], errors='coerce')
    total_revenue_details_C_df['prtextendedcost'] = pd.to_numeric(total_revenue_details_C_df['prtextendedcost'], errors='coerce')

    total_revenue_details_C_df = total_revenue_details_C_df[
        ~((total_revenue_details_C_df['lbrsale'].fillna(0) == 0) &
        (total_revenue_details_C_df['lbrsoldhours'].fillna(0) == 0) &
        (total_revenue_details_C_df['prtextendedsale'].fillna(0) == 0) &
        (total_revenue_details_C_df['prtextendedcost'].fillna(0) == 0))
    ]

    return total_revenue_details_C_df, service_type_mapping

def calculate_mileage_based_metrics(total_revenue_details_C_df):
    """Calculate metrics based on mileage (below/above 60k)"""
    log_info("Calculating mileage-based metrics...")

    # Split customer pay ROs into two lists based on mileage for calculating the total RO count
    total_revenue_details_C_df_Below_60k = total_revenue_details_C_df[
        (total_revenue_details_C_df['mileage'].astype(int) < 60000)]
    total_revenue_details_C_df_Above_60k = total_revenue_details_C_df[
        (total_revenue_details_C_df['mileage'].astype(int) >= 60000)]

    total_ro_count_below_60k = total_revenue_details_C_df_Below_60k['unique_ro_number'].nunique()
    total_ro_count_above_60k = total_revenue_details_C_df_Above_60k['unique_ro_number'].nunique()

    perc_of_business_below_60k = round_off((total_ro_count_below_60k / (total_ro_count_below_60k + total_ro_count_above_60k)) * 100) if (total_ro_count_below_60k + total_ro_count_above_60k) > 0 else 0
    perc_of_business_above_60k = round_off((total_ro_count_above_60k / (total_ro_count_below_60k + total_ro_count_above_60k)) * 100) if (total_ro_count_below_60k + total_ro_count_above_60k) > 0 else 0

    return total_ro_count_below_60k, total_ro_count_above_60k, perc_of_business_below_60k, perc_of_business_above_60k

def process_one_line_multi_line_ros(total_revenue_details_C_df, service_type_mapping, assigned_menu_opcodes_df):
    """Process One Line and Multi Line ROs"""
    log_info("Processing One Line and Multi Line ROs...")

    # Split Customer Pay ROs into One Line RO and Multi Line RO based on jobs on each RO
    value_counts = total_revenue_details_C_df['unique_ro_number'].value_counts()
    one_line_ROs = value_counts[value_counts == 1].index
    One_Line_RO_Details = total_revenue_details_C_df[(total_revenue_details_C_df['unique_ro_number'].isin(one_line_ROs))]
    Multi_Line_RO_Details = total_revenue_details_C_df[~total_revenue_details_C_df['unique_ro_number'].isin(one_line_ROs)]

    log_info(f"Initial One Line ROs count: {len(one_line_ROs)}")
    log_info(f"Initial Multi Line ROs count: {Multi_Line_RO_Details['unique_ro_number'].nunique()}")
    log_info(f"Total Customer Pay ROs: {len(one_line_ROs) + Multi_Line_RO_Details['unique_ro_number'].nunique()}")

    available_menu = set(One_Line_RO_Details['mapped_menu'])

    # Check if any menu is available and process menu impacted One Line RO
    if any(value and (not isinstance(value, float) or not math.isnan(value)) for value in available_menu):
        # Identify the menu impacted One Line RO. If the item count more than 1 remove it from One Line list and add them into Multi Line
        for menu_map in available_menu:
            One_Line_RO_Details_with_menu = One_Line_RO_Details[One_Line_RO_Details[menu_map].notna()]
            for i, row in One_Line_RO_Details_with_menu.iterrows():
                opcode = row['lbropcode']
                mapped_menu = row['mapped_menu']
                service_type_name = row[mapped_menu]
                service_id = next(key for key, value in service_type_mapping.items() if value == service_type_name)

                current_menu_opcodes = assigned_menu_opcodes_df[assigned_menu_opcodes_df['menu_name'].astype(str) == mapped_menu]
                current_menu_opcodes_set = set(current_menu_opcodes['menu_opcode'])

                if mapped_menu in One_Line_RO_Details_with_menu.columns:
                    item_count = row[mapped_menu + '_items']

                    if int(item_count) > 1:
                        matching_opcode_row = assigned_menu_opcodes_df[(assigned_menu_opcodes_df['menu_opcode'] == opcode) &
                                                                    (assigned_menu_opcodes_df['service_type'] == service_id) &
                                                                    (assigned_menu_opcodes_df['menu_name'] == mapped_menu)]
                        if not matching_opcode_row.empty:
                            Multi_Line_RO_Details = pd.concat([Multi_Line_RO_Details, pd.DataFrame([row])], ignore_index=True)
                            One_Line_RO_Details = One_Line_RO_Details.drop(index=i)

    return One_Line_RO_Details, Multi_Line_RO_Details

def calculate_one_line_multi_line_metrics(One_Line_RO_Details, Multi_Line_RO_Details, total_ro_count_below_60k, total_ro_count_above_60k, Scorecard_10_CP):
    """Calculate One Line and Multi Line RO metrics"""
    log_info("Calculating One Line and Multi Line RO metrics...")

    # Split One Line and Multi Line ROs based on mileage
    One_Line_RO_Details_below60k = One_Line_RO_Details[
        (One_Line_RO_Details['mileage'].astype(int) < 60000)]
    One_Line_RO_Details_above60k = One_Line_RO_Details[
        (One_Line_RO_Details['mileage'].astype(int) >= 60000)]
    Multi_Line_RO_Details_below60k = Multi_Line_RO_Details[
        (Multi_Line_RO_Details['mileage'].astype(int) < 60000)]
    Multi_Line_RO_Details_above60k = Multi_Line_RO_Details[
        (Multi_Line_RO_Details['mileage'].astype(int) >= 60000)]

    # One Line RO count calculation
    one_line_ro_count_below_60k = One_Line_RO_Details_below60k.shape[0]
    one_line_ro_count_above_60k = One_Line_RO_Details_above60k.shape[0]

    # Multi_line RO count calculation
    multi_line_ro_count_below_60k = Multi_Line_RO_Details_below60k['unique_ro_number'].nunique()
    multi_line_ro_count_above_60k = Multi_Line_RO_Details_above60k['unique_ro_number'].nunique()

    # Calculate One Line RO percentage
    perc_of_one_line_below_60k = round_off((one_line_ro_count_below_60k / total_ro_count_below_60k) * 100, 2) if total_ro_count_below_60k > 0 else 0
    perc_of_one_line_above_60k = round_off((one_line_ro_count_above_60k / total_ro_count_above_60k) * 100, 2) if total_ro_count_above_60k > 0 else 0

    # CP 1 Line RO count - FOPC_DV_0156
    One_Line_Mileage_Under_60k = one_line_ro_count_below_60k
    One_Line_Mileage_Over_60k = one_line_ro_count_above_60k
    One_Line_Total_Shop = One_Line_Mileage_Under_60k + One_Line_Mileage_Over_60k

    # CP 1-Line-RO Count Percentage
    perc_of_one_line_below_60k = round_off(((one_line_ro_count_below_60k / total_ro_count_below_60k) * 100), 2) if total_ro_count_below_60k > 0 else 0
    perc_of_one_line_above_60k = round_off(((one_line_ro_count_above_60k / total_ro_count_above_60k) * 100), 2) if total_ro_count_above_60k > 0 else 0
    perc_of_one_line_total_shop = round_off(((One_Line_Total_Shop / Scorecard_10_CP) * 100), 2) if Scorecard_10_CP > 0 else 0

    # CP Multi Line RO Count - FOPC_DV_0159
    Multi_Line_Mileage_Under_60k = multi_line_ro_count_below_60k
    Multi_Line_Mileage_Over_60k = multi_line_ro_count_above_60k
    Multi_Line_Total_Shop = Multi_Line_Mileage_Under_60k + Multi_Line_Mileage_Over_60k

    # Multi-Line-RO Count Percentage
    perc_of_multi_line_below_60k = round_off(((Multi_Line_Mileage_Under_60k / total_ro_count_below_60k) * 100), 2) if total_ro_count_below_60k > 0 else 0
    perc_of_multi_line_above_60k = round_off(((Multi_Line_Mileage_Over_60k / total_ro_count_above_60k) * 100), 2) if total_ro_count_above_60k > 0 else 0
    perc_of_multi_line_total_shop = round_off(((Multi_Line_Total_Shop / Scorecard_10_CP) * 100), 2) if Scorecard_10_CP > 0 else 0

    #948 summary calculation
    one_line_below60k_lbrsale = One_Line_RO_Details_below60k['lbrsale'].sum()
    one_line_below60k_lbrsoldhours = One_Line_RO_Details_below60k['lbrsoldhours'].sum()

    one_line_above60k_lbrsale = One_Line_RO_Details_above60k['lbrsale'].sum()
    one_line_above60k_lbrsoldhours = One_Line_RO_Details_above60k['lbrsoldhours'].sum()

    one_line_total_shop_lbrsale = one_line_below60k_lbrsale + one_line_above60k_lbrsale
    one_line_total_shop_lbrsoldhours = one_line_below60k_lbrsoldhours + one_line_above60k_lbrsoldhours

    one_line_count_below_60k_prec_ro_count = round_off((One_Line_Mileage_Under_60k / total_ro_count_below_60k) * 100, 2) if total_ro_count_below_60k > 0 else 0
    one_line_count_above_60k_prec_ro_count = round_off((One_Line_Mileage_Over_60k / total_ro_count_above_60k) * 100, 2) if total_ro_count_above_60k > 0 else 0
    one_line_total_shop_prec_ro_count = round_off((One_Line_Total_Shop / Scorecard_10_CP) * 100, 2) if Scorecard_10_CP > 0 else 0

    # Multi-Line-RO Count 1354
    multi_line_count_below_60k_prec_ro_count = round_off((Multi_Line_Mileage_Under_60k / total_ro_count_below_60k) * 100, 2) if total_ro_count_below_60k > 0 else 0
    multi_line_count_above_60k_prec_ro_count = round_off((Multi_Line_Mileage_Over_60k / total_ro_count_above_60k) * 100, 2) if total_ro_count_above_60k > 0 else 0
    multi_line_total_shop_prec_ro_count = round_off((Multi_Line_Total_Shop / Scorecard_10_CP) * 100, 2) if Scorecard_10_CP > 0 else 0
    
    # 948
    one_line_drilldown_result = {
        "Mileage Under 60K": {
            "Mileage Under 60K": One_Line_Mileage_Under_60k,
            "Labor Sale": one_line_below60k_lbrsale,
            "Labor Sold Hours": one_line_below60k_lbrsoldhours
        },
        "Mileage Over 60K": {
            "Mileage Over 60K": One_Line_Mileage_Over_60k,
            "Labor Sale": one_line_above60k_lbrsale,
            "Labor Sold Hours": one_line_above60k_lbrsoldhours
        },
        "Total Shop": {
            "Total Shop": One_Line_Total_Shop,
            "Labor Sale": one_line_total_shop_lbrsale,
            "Labor Sold Hours": one_line_total_shop_lbrsoldhours
        }
    }

    # 923
    perc_of_one_line_drilldown_result = {
        "Mileage Under 60K": {
            "Total RO Count": total_ro_count_below_60k,
            "1 Line Count":One_Line_Mileage_Under_60k ,
            "Mileage Under 60K": one_line_count_below_60k_prec_ro_count
        },
        "Mileage Over 60K": {
            "Total RO Count": total_ro_count_above_60k,
            "1 Line Count":One_Line_Mileage_Over_60k,
            "Mileage Over 60K": one_line_count_above_60k_prec_ro_count
        },
        "Total Shop": {
            "Total RO Count": Scorecard_10_CP,
            "1 Line Count":One_Line_Total_Shop ,
            "Total Shop": one_line_total_shop_prec_ro_count
        }
    }
    
    # 1354
    multi_line_drilldown_result = {
        "Mileage Under 60K": {
            "Mileage Under 60K": Multi_Line_Mileage_Under_60k,
            
        },
        "Mileage Over 60K": {
            "Mileage Over 60K": Multi_Line_Mileage_Over_60k,
           
        },
        "Total Shop": {
            "Total Shop": Multi_Line_Total_Shop,
        }
    }
    
    # 1355
    perc_of_multi_line_drilldown_result = {
        "Mileage Under 60K": {
            "Total RO Count": total_ro_count_below_60k,
            "1 Line Count": Multi_Line_Mileage_Under_60k,
            "Mileage Under 60K": multi_line_count_below_60k_prec_ro_count
        },
        "Mileage Over 60K": {
            "Total RO Count": total_ro_count_above_60k,
            "1 Line Count": Multi_Line_Mileage_Over_60k,
            "Mileage Over 60K": multi_line_count_above_60k_prec_ro_count
        },
        "Total Shop": {
            "Total RO Count": Scorecard_10_CP,
            "1 Line Count": Multi_Line_Total_Shop,
            "Total Shop": multi_line_total_shop_prec_ro_count
        }
    }

    return {
        'One_Line_Mileage_Under_60k': One_Line_Mileage_Under_60k,
        'One_Line_Mileage_Over_60k': One_Line_Mileage_Over_60k,
        'One_Line_Total_Shop': One_Line_Total_Shop,
        'perc_of_one_line_below_60k': perc_of_one_line_below_60k,
        'perc_of_one_line_above_60k': perc_of_one_line_above_60k,
        'perc_of_one_line_total_shop': perc_of_one_line_total_shop,
        'Multi_Line_Mileage_Under_60k': Multi_Line_Mileage_Under_60k,
        'Multi_Line_Mileage_Over_60k': Multi_Line_Mileage_Over_60k,
        'Multi_Line_Total_Shop': Multi_Line_Total_Shop,
        'perc_of_multi_line_below_60k': perc_of_multi_line_below_60k,
        'perc_of_multi_line_above_60k': perc_of_multi_line_above_60k,
        'perc_of_multi_line_total_shop': perc_of_multi_line_total_shop
    },one_line_drilldown_result,perc_of_one_line_drilldown_result,multi_line_drilldown_result,perc_of_multi_line_drilldown_result

def calculate_average_ro_open_days(combined_revenue_details, filtered_df, columns_to_check):
    """Calculate Average RO Open Days"""
    log_info("Calculating Average RO Open Days...")

    # Filter the DataFrame to keep only those rows with the minimum 'opendate' for each 'unique_ro_number'
    combined_revenue_details['min_opendate'] = combined_revenue_details.groupby('unique_ro_number')['opendate'].transform('min')
    combined_revenue_details['open_days'] = (pd.to_datetime(combined_revenue_details['closeddate']) - pd.to_datetime(combined_revenue_details['min_opendate'])).dt.days

    # Use the original filtered_df for sales filtering (as in the original code)
    filtered_df_with_sales = filtered_df[
        ~((pd.to_numeric(filtered_df['lbrsale'], errors='coerce').fillna(0) == 0) &
            (pd.to_numeric(filtered_df['lbrsoldhours'], errors='coerce').fillna(0) == 0) &
            (pd.to_numeric(filtered_df['prtextendedsale'], errors='coerce').fillna(0) == 0) &
            (pd.to_numeric(filtered_df['prtextendedcost'], errors='coerce').fillna(0) == 0))
    ]

    filtered_df_with_sales['min_opendate'] = filtered_df_with_sales.groupby('unique_ro_number')['opendate'].transform('min')
    filtered_df_with_sales['open_days'] = (pd.to_datetime(filtered_df_with_sales['closeddate']) - pd.to_datetime(filtered_df_with_sales['min_opendate'])).dt.days

    # Split by pay type groups
    all_revenue_C = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'C']
    all_revenue_M = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'M']
    all_revenue_E = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'E']
    all_revenue_W = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'W']
    all_revenue_F = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'F']
    all_revenue_I = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'I']

    # Calculate RO counts for each pay type
    ro_count_for_C = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'C']['unique_ro_number']))
    ro_count_for_M = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'M']['unique_ro_number']))
    ro_count_for_E = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'E']['unique_ro_number']))
    ro_count_for_W = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'W']['unique_ro_number']))
    ro_count_for_F = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'F']['unique_ro_number']))
    ro_count_for_I = len(set(filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'I']['unique_ro_number']))

    # Get unique records for open days calculation
    all_revenue_without_duplicates_for_open_days_C = all_revenue_C.loc[all_revenue_C.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True)
    all_revenue_without_duplicates_for_open_days_W = all_revenue_W.loc[all_revenue_W.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True)
    all_revenue_without_duplicates_for_open_days_I = all_revenue_I.loc[all_revenue_I.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True)
    all_revenue_without_duplicates_for_open_days_E = all_revenue_E.loc[all_revenue_E.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True)
    all_revenue_without_duplicates_for_open_days_M = all_revenue_M.loc[all_revenue_M.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True)
    all_revenue_without_duplicates_for_open_days_F = all_revenue_F.loc[all_revenue_F.groupby('unique_ro_number')['open_days'].idxmin()].reset_index(drop=True)

    # Calculate sum of open days for each pay type
    open_days_sum_C = all_revenue_without_duplicates_for_open_days_C['open_days'].sum()
    open_days_sum_W = all_revenue_without_duplicates_for_open_days_W['open_days'].sum()
    open_days_sum_I = all_revenue_without_duplicates_for_open_days_I['open_days'].sum()
    open_days_sum_E = all_revenue_without_duplicates_for_open_days_E['open_days'].sum()
    open_days_sum_M = all_revenue_without_duplicates_for_open_days_M['open_days'].sum()
    open_days_sum_F = all_revenue_without_duplicates_for_open_days_F['open_days'].sum()

    # Calculate average open days for each pay type
    avg_days_open_C = round_off((open_days_sum_C / ro_count_for_C), 2) if ro_count_for_C != 0 else 0
    avg_days_open_W = round_off((open_days_sum_W / ro_count_for_W), 2) if ro_count_for_W != 0 else 0
    avg_days_open_I = round_off((open_days_sum_I / ro_count_for_I), 2) if ro_count_for_I != 0 else 0
    avg_days_open_E = round_off((open_days_sum_E / ro_count_for_E), 2) if ro_count_for_E != 0 else 0
    avg_days_open_F = round_off((open_days_sum_F / ro_count_for_F), 2) if ro_count_for_F != 0 else 0
    avg_days_open_M = round_off((open_days_sum_M / ro_count_for_M), 2) if ro_count_for_M != 0 else 0

    avg_open_days_drilldown_result ={
        "Customer Pay": {
            "Customer Pay": avg_days_open_C,            
        },
        "Extended Service": {
            "Extended Service": avg_days_open_E,   
        },
        "Internal": {
            "Internal": avg_days_open_I,    
        },
        "Maintenance": {
            "Maintenance Plan": avg_days_open_M,   
        },
        "Warranty": {
            "Warranty": avg_days_open_W,   
        },
        "Factory Service Contract": {
            "Factory Service Contract": avg_days_open_F,    
        }
    }
    return {
        'avg_days_open_C': avg_days_open_C,
        'avg_days_open_W': avg_days_open_W,
        'avg_days_open_I': avg_days_open_I,
        'avg_days_open_E': avg_days_open_E,
        'avg_days_open_F': avg_days_open_F,
        'avg_days_open_M': avg_days_open_M
    },avg_open_days_drilldown_result

def calculate_cp_parts_to_labor_ratio(combined_revenue_details, customer_pay_types):
    """Calculate CP Parts to Labor Ratio - FOPC_DV_0162"""
    log_info("Calculating CP Parts to Labor Ratio...")

    # Filter only CP job details
    list_of_paytypegroup_C = combined_revenue_details[combined_revenue_details['paytypegroup'].isin(['C', 'M', 'E']) & (combined_revenue_details['group'] == 'C')].to_dict('records')
    # Convert it to data frame
    total_CP_revenue_details_df = pd.DataFrame(list_of_paytypegroup_C)
    total_CP_revenue_details_df = total_CP_revenue_details_df[
        ~((total_CP_revenue_details_df['lbrsale'].fillna(0) == 0) &
        (total_CP_revenue_details_df['lbrsoldhours'].fillna(0) == 0) &
        (total_CP_revenue_details_df['prtextendedsale'].fillna(0) == 0) &
        (total_CP_revenue_details_df['prtextendedcost'].fillna(0) == 0))
    ]

    lbr_sale_total = 0
    prt_ext_sale_total = 0

    if not total_CP_revenue_details_df.empty:
        lbr_sale_total = pd.to_numeric(total_CP_revenue_details_df['lbrsale'], errors='coerce').fillna(0).sum()
        prt_ext_sale_total = pd.to_numeric(total_CP_revenue_details_df['prtextendedsale'], errors='coerce').fillna(0).sum()
    else:
        log_info("No data available for KPI Scorecard A calculation")

    CP_Parts_to_Labor_Ratio = round_off((prt_ext_sale_total / lbr_sale_total), 2) if lbr_sale_total != 0 else 0

    return CP_Parts_to_Labor_Ratio, total_CP_revenue_details_df,lbr_sale_total,prt_ext_sale_total

def calculate_labor_sold_hours_percentage(combined_revenue_details, filtered_df):
    """Calculate Labor Sold Hours Percentage By Pay Type - FOPC_DV_0163"""
    log_info("Calculating Labor Sold Hours Percentage By Pay Type...")

    All_Sold_Hours = pd.to_numeric(combined_revenue_details['lbrsoldhours'], errors='coerce').fillna(0).sum()

    # Use the same filtered_df_with_sales as in the original code (from Average RO Open Days calculation)
    filtered_df_with_sales = filtered_df[
        ~((pd.to_numeric(filtered_df['lbrsale'], errors='coerce').fillna(0) == 0) &
            (pd.to_numeric(filtered_df['lbrsoldhours'], errors='coerce').fillna(0) == 0) &
            (pd.to_numeric(filtered_df['prtextendedsale'], errors='coerce').fillna(0) == 0) &
            (pd.to_numeric(filtered_df['prtextendedcost'], errors='coerce').fillna(0) == 0))
    ]

    all_revenue_C = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'C']
    all_revenue_W = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'W']
    all_revenue_I = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'I']
    all_revenue_E = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'E']
    all_revenue_M = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'M']
    all_revenue_F = filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'F']

    # Calculate labor sold hours for each pay type
    labor_sold_hours_C = round_off((pd.to_numeric(all_revenue_C['lbrsoldhours']).fillna(0).sum()), 2) if not all_revenue_C.empty else 0
    labor_sold_hours_W = round_off((pd.to_numeric(all_revenue_W['lbrsoldhours']).fillna(0).sum()), 2) if not all_revenue_W.empty else 0
    labor_sold_hours_I = round_off((pd.to_numeric(all_revenue_I['lbrsoldhours']).fillna(0).sum()), 2) if not all_revenue_I.empty else 0
    labor_sold_hours_E = round_off((pd.to_numeric(all_revenue_E['lbrsoldhours']).fillna(0).sum()), 2) if not all_revenue_E.empty else 0
    labor_sold_hours_M = round_off((pd.to_numeric(all_revenue_M['lbrsoldhours']).fillna(0).sum()), 2) if not all_revenue_M.empty else 0
    labor_sold_hours_F = round_off((pd.to_numeric(all_revenue_F['lbrsoldhours']).fillna(0).sum()), 2) if not all_revenue_F.empty else 0
    
    # Calculate labor sale for each pay type
    labor_sale_C = round_off((pd.to_numeric(all_revenue_C['lbrsale']).fillna(0).sum()), 2) if not all_revenue_C.empty else 0
    labor_sale_W = round_off((pd.to_numeric(all_revenue_W['lbrsale']).fillna(0).sum()), 2) if not all_revenue_W.empty else 0
    labor_sale_I = round_off((pd.to_numeric(all_revenue_I['lbrsale']).fillna(0).sum()), 2) if not all_revenue_I.empty else 0
    labor_sale_E = round_off((pd.to_numeric(all_revenue_E['lbrsale']).fillna(0).sum()), 2) if not all_revenue_E.empty else 0
    labor_sale_M = round_off((pd.to_numeric(all_revenue_M['lbrsale']).fillna(0).sum()), 2) if not all_revenue_M.empty else 0
    labor_sale_F = round_off((pd.to_numeric(all_revenue_F['lbrsale']).fillna(0).sum()), 2) if not all_revenue_F.empty else 0

    # Calculate percentages
    Labor_Sold_Hours_Percentage_C = round_off(((labor_sold_hours_C / All_Sold_Hours) * 100)) if All_Sold_Hours != 0 else 0
    Labor_Sold_Hours_Percentage_W = round_off(((labor_sold_hours_W / All_Sold_Hours) * 100)) if All_Sold_Hours != 0 else 0
    Labor_Sold_Hours_Percentage_I = round_off(((labor_sold_hours_I / All_Sold_Hours) * 100)) if All_Sold_Hours != 0 else 0
    Labor_Sold_Hours_Percentage_E = round_off(((labor_sold_hours_E / All_Sold_Hours) * 100)) if All_Sold_Hours != 0 else 0
    Labor_Sold_Hours_Percentage_M = round_off(((labor_sold_hours_M / All_Sold_Hours) * 100)) if All_Sold_Hours != 0 else 0
    Labor_Sold_Hours_Percentage_F = round_off(((labor_sold_hours_F / All_Sold_Hours) * 100)) if All_Sold_Hours != 0 else 0
    
    labor_sold_hours_percentage_drilldown_result ={
            "Customer Pay": {
                "Labor Sale - Customer Pay": labor_sale_C,
                "Labor Sold Hours - All Categories": All_Sold_Hours,
                "Labor Sold Hours - Customer Pay": labor_sold_hours_C,
                "Labor Sold Hours % - Customer Pay": Labor_Sold_Hours_Percentage_C
            },
            "Extended Service": {
                # "Labor Sale - Extended Service": labor_sale_E,
                "Labor Sold Hours - All Categories": All_Sold_Hours,
                "Labor Sold Hours - Extended Service": labor_sold_hours_E,
                "Labor Sold Hours % - Extended Service": Labor_Sold_Hours_Percentage_E
            },
            "Internal": {
                "Labor Sold Hours - All Categories": All_Sold_Hours,
                "Labor Sold Hours - Internal": labor_sold_hours_I,
                "Labor Sold Hours % - Internal": Labor_Sold_Hours_Percentage_I
            },
            "Maintenance Plan": {
                # "Labor Sale - Maintenance Plan": labor_sale_M,
                "Labor Sold Hours - All Categories": All_Sold_Hours,
                "Labor Sold Hours - Maintenance": labor_sold_hours_M,
                "Labor Sold Hours % - Maintenance": Labor_Sold_Hours_Percentage_M
            },
            "Warranty": {
                "Labor Sold Hours - All Categories": All_Sold_Hours,
                "Labor Sold Hours - Warranty": labor_sold_hours_W,
                "Labor Sold Hours % - Warranty": Labor_Sold_Hours_Percentage_W
            },
            "Factory Service Contract": {
                # "Labor Sale - Factory Service Contract": labor_sale_F,
                "Labor Sold Hours - All Categories": All_Sold_Hours,
                "Labor Sold Hours - Factory Service Contract": labor_sold_hours_F,
                "Labor Sold Hours % - Factory Service Contract": Labor_Sold_Hours_Percentage_F
            }
        }

    return {
        'Labor_Sold_Hours_Percentage_C': Labor_Sold_Hours_Percentage_C,
        'Labor_Sold_Hours_Percentage_W': Labor_Sold_Hours_Percentage_W,
        'Labor_Sold_Hours_Percentage_I': Labor_Sold_Hours_Percentage_I,
        'Labor_Sold_Hours_Percentage_E': Labor_Sold_Hours_Percentage_E,
        'Labor_Sold_Hours_Percentage_M': Labor_Sold_Hours_Percentage_M,
        'Labor_Sold_Hours_Percentage_F': Labor_Sold_Hours_Percentage_F
    },labor_sold_hours_percentage_drilldown_result

def calculate_cp_parts_to_labor_ratio_by_category(total_CP_revenue_details_df,customer_pay_types):
    """Calculate CP Parts to Labor Ratio By Category"""
    log_info("Calculating CP Parts to Labor Ratio By Category...")

    total_CP_revenue_details_comp = total_CP_revenue_details_df[total_CP_revenue_details_df['opcategory'] == 'COMPETITIVE']
    total_CP_revenue_details_maint = total_CP_revenue_details_df[total_CP_revenue_details_df['opcategory'] == 'MAINTENANCE']
    total_CP_revenue_details_rep = total_CP_revenue_details_df[total_CP_revenue_details_df['opcategory'] == 'REPAIR']

    # Competitive category
    lbr_sale_comp = pd.to_numeric(total_CP_revenue_details_comp['lbrsale'], errors='coerce').fillna(0).sum() if not total_CP_revenue_details_comp.empty else 0
    prt_ext_sale_comp = pd.to_numeric(total_CP_revenue_details_comp['prtextendedsale'], errors='coerce').fillna(0).sum() if not total_CP_revenue_details_comp.empty else 0
    CP_Parts_to_Labor_Ratio_Comp = round_off((prt_ext_sale_comp / lbr_sale_comp), 2) if lbr_sale_comp != 0 else 0

    # Maintenance category
    lbr_sale_maint = pd.to_numeric(total_CP_revenue_details_maint['lbrsale'], errors='coerce').fillna(0).sum() if not total_CP_revenue_details_maint.empty else 0
    prt_ext_sale_maint = pd.to_numeric(total_CP_revenue_details_maint['prtextendedsale'], errors='coerce').fillna(0).sum() if not total_CP_revenue_details_maint.empty else 0
    CP_Parts_to_Labor_Ratio_maint = round_off((prt_ext_sale_maint / lbr_sale_maint), 2) if lbr_sale_maint != 0 else 0

    # Repair category
    lbr_sale_rep = pd.to_numeric(total_CP_revenue_details_rep['lbrsale'], errors='coerce').fillna(0).sum() if not total_CP_revenue_details_rep.empty else 0
    prt_ext_sale_rep = pd.to_numeric(total_CP_revenue_details_rep['prtextendedsale'], errors='coerce').fillna(0).sum() if not total_CP_revenue_details_rep.empty else 0
    CP_Parts_to_Labor_Ratio_rep = round_off((prt_ext_sale_rep / lbr_sale_rep), 2) if lbr_sale_rep != 0 else 0


    #936 summary details

    competitive_df = total_CP_revenue_details_df[
    (total_CP_revenue_details_df['opcategory'] == 'COMPETITIVE') &
    (total_CP_revenue_details_df['paytypegroup'].isin(customer_pay_types))
    ]

    maintenance_df = total_CP_revenue_details_df[
    (total_CP_revenue_details_df['opcategory'] == 'MAINTENANCE') &
    (total_CP_revenue_details_df['paytypegroup'].isin(customer_pay_types))
    ]

    repair_df = total_CP_revenue_details_df[
    (total_CP_revenue_details_df['opcategory'] == 'REPAIR') &
    (total_CP_revenue_details_df['paytypegroup'].isin(customer_pay_types))
    ]
    
    labor_sale_competitive = competitive_df['lbrsale'].sum()
    parts_sale_competitive = competitive_df['prtextendedsale'].sum()
    ratio_competitive = round_off(parts_sale_competitive / labor_sale_competitive, 2) if labor_sale_competitive > 0 else 0

    labor_sale_maintenance = maintenance_df['lbrsale'].sum()
    parts_sale_maintenance = maintenance_df['prtextendedsale'].sum()
    ratio_maintenance = round_off(parts_sale_maintenance / labor_sale_maintenance, 2) if labor_sale_maintenance > 0 else 0

    labor_sale_repair = repair_df['lbrsale'].sum()
    parts_sale_repair = repair_df['prtextendedsale'].sum()
    ratio_repair = round_off(parts_sale_repair / labor_sale_repair, 2) if labor_sale_repair > 0 else 0

    parts_to_labor_ratio_category_drilldown_result = {
        "Competitive": {
            "Labor Sale - Competitive": labor_sale_competitive,
            "Parts Sale - Competitive": parts_sale_competitive,
            "Parts To Labor Ratio - Competitive": ratio_competitive
        },
        "Maintenance": {
            "Labor Sale - Maintenance": labor_sale_maintenance,
            "Parts Sale - Maintenance": parts_sale_maintenance,
            "Parts To Labor Ratio - Maintenance": ratio_maintenance
        },
        "Repair": {
            "Labor Sale - Repair": labor_sale_repair,
            "Parts Sale - Repair": parts_sale_repair,
            "Parts To Labor Ratio - Repair": ratio_repair
        }
    }
    
    return {
        'CP_Parts_to_Labor_Ratio_Comp': CP_Parts_to_Labor_Ratio_Comp,
        'CP_Parts_to_Labor_Ratio_maint': CP_Parts_to_Labor_Ratio_maint,
        'CP_Parts_to_Labor_Ratio_rep': CP_Parts_to_Labor_Ratio_rep
    },parts_to_labor_ratio_category_drilldown_result

def calculate_mpi_penetration_percentage(combined_revenue_details, MPI_setup_df, mpi_opcodes):
    """Calculate MPI Penetration Percentage - FOPC_DV_0166"""
    log_info("Calculating MPI Penetration Percentage...")

    all_revenue_details_list = combined_revenue_details.to_dict('records')

    # Identify the FRH value updated on UI from DB table
    frh_value = 0
    if not MPI_setup_df.empty:
        frh_value = MPI_setup_df.loc[MPI_setup_df['is_active'] == '1', 'frh'].iloc[0]

    # Remove jobs with 0 sales and department body shop and filter paytype group with C and W
    MPI_Opportunity_list = [
        row for row in all_revenue_details_list
        if not (
            (
                row['lbrsale'] == 0 and
                row['lbrsoldhours'] == 0 and
                row['prtextendedsale'] == 0 and
                row['prtextendedcost'] == 0) or
                row['department'] == 'Body Shop' or
                row['hide_ro'] == True or
                (pd.to_numeric(row['mileage'], errors='coerce') or 0) <= 1000
        ) and row['group'] in {'C', 'W'} and row['paytypegroup'] in {'C', 'M', 'E', 'W', 'F'}
    ]

    # Get distinct values of the 'ronumber' column
    MPI_Opportunity_list_df = pd.DataFrame(MPI_Opportunity_list)
    # Exclude the rows with customer name blank or None
    MPI_Opportunity_list_df['customer_name'] = MPI_Opportunity_list_df['customer_name'].replace('', np.nan)
    MPI_Opportunity_list_df = MPI_Opportunity_list_df[MPI_Opportunity_list_df['customer_name'].notna()]

    Opportunities = 0
    if not MPI_Opportunity_list_df.empty:
        # Same ronumber with different closeddate will be considered as two different ronumber
        MPI_Opportunity_list_df['unique_ronumber'] = MPI_Opportunity_list_df['ronumber'].astype(str) + '_' + MPI_Opportunity_list_df['closeddate'].astype(str)
        distinct_ronumbers = set(MPI_Opportunity_list_df['unique_ronumber'])
        Opportunities = len(distinct_ronumbers)

    # MPI Completed calculation
    # Identify the ronumber with MPI jobs
    ronumbers_with_mpi = {row['ronumber'] for row in all_revenue_details_list if row.get('lbropcode','').strip() in mpi_opcodes}

    # Filter all jobs of ronumber, if MPI opcodes are available in any of its job
    filtered_rows = [row for row in all_revenue_details_list if row['ronumber'] in ronumbers_with_mpi]

    # Remove jobs with zero sales and body shop jobs and jobs with paytype group 'I'
    final_filtered_rows = [
        row for row in filtered_rows
        if not (
            (row['lbrsale'] == 0 and row['lbrsoldhours'] == 0 and row['prtextendedsale'] == 0 and row['prtextendedcost'] == 0)
            or row['department'] == 'Body Shop' or row['paytypegroup'] == 'I' or row['hide_ro'] == True or row['group'] == 'I'
        )
    ]

    # Convert the list to data frame
    final_filtered_rows_df = pd.DataFrame(final_filtered_rows)
    Completed_MPI_ROs = set()
    Completed_MPI_ROs_Perc = 0

    if not final_filtered_rows_df.empty:
        # Move final list of jobs to a set to identify the count of completed MPI ROs
        # Same ronumber with different closeddate will be considered as two different ronumber
        final_filtered_rows_df['unique_onumber'] = final_filtered_rows_df['ronumber'].astype(str)+'_'+final_filtered_rows_df['closeddate'].astype(str)
        Completed_MPI_ROs = set(final_filtered_rows_df['unique_onumber'])
        Completed_MPI_ROs_Perc = round_off(((len(Completed_MPI_ROs) / Opportunities) * 100), 2) if Opportunities > 0 else 0
    
    mpi_penetration_percentage_drilldown_result = {
        "MPI Penetration Percentage": {
            "MPI Count": len(Completed_MPI_ROs),
            "Total RO Count": Opportunities,
            "MPI Penetration %": Completed_MPI_ROs_Perc
        },
        
    }
    return Completed_MPI_ROs_Perc ,mpi_penetration_percentage_drilldown_result

def calculate_menu_penetration_percentage(combined_revenue_details, menu_master_df, assigned_menu_models_df, menu_service_type_df, assigned_menu_opcodes_df):
    """Calculate Menu Penetration Percentage"""
    log_info("Calculating Menu Penetration Percentage...")

    menu_sold = 0
    menu_jobs = 0
    menu_sold_perc = 0
    menu_pportunities = 0

    if not combined_revenue_details.empty:
        # Convert the data frame to dictionary for applying conditions
        all_revenue_details_list = combined_revenue_details.to_dict('records')
        # Remove jobs with department body shop and filter paytype group with C and W
        Menu_Opportunity_list = [
            row for row in all_revenue_details_list
            if not (
                (row['department'] == 'Body Shop')
            ) and row['group'] in {'C', 'W'}
            and row['paytypegroup'] in {'C', 'M', 'E', 'W', 'F'}
        ]

        # Convert the list to data frame
        Menu_Opportunity_list_df = pd.DataFrame(Menu_Opportunity_list)

        # Exclude the rows with mileage less than 1000 and customer_name is blank or None
        Menu_Opportunity_list_df = Menu_Opportunity_list_df[pd.to_numeric(Menu_Opportunity_list_df['mileage'], errors='coerce') > 1000]
        Menu_Opportunity_list_df['customer_name'] = Menu_Opportunity_list_df['customer_name'].replace('', np.nan)
        Menu_Opportunity_list_df = Menu_Opportunity_list_df[Menu_Opportunity_list_df['customer_name'].notna()]

        # Identify the available menus from menu master table
        menu_names = set(menu_master_df['menu_name'])

        # Store the list of available default menu
        default_menu_series = menu_master_df[menu_master_df['is_default'].astype(int) == 1]['menu_name']

        # Identify the default menu name
        if not default_menu_series.empty:
            default_menu = default_menu_series.iloc[0]
        else:
            default_menu = np.nan

        # Check whether models are assigned to the available menu
        if not assigned_menu_models_df.empty:
            # Create a mapping from 'model' to 'menu_name'
            model_to_menu_map = assigned_menu_models_df.set_index('model')['menu_name'].to_dict()
            # Map the 'model' column in Menu_Opportunity_list_df to the 'mapped_menu'
            Menu_Opportunity_list_df['mapped_menu'] = Menu_Opportunity_list_df['model'].map(
                model_to_menu_map).fillna(default_menu)
        else:
            # If no model mapping available all models mapped to default menu
            Menu_Opportunity_list_df['mapped_menu'] = default_menu

        # If no menus are added, all values under menu sales will be 0
        if menu_names:
            for name in menu_names:
                Menu_Opportunity_list_df[name] = ''

            # Create a dictionary to map service_type_id to service_type using menu_service_type table
            service_type_mapping = menu_service_type_df.set_index('id')['service_type'].to_dict()

            # Create a set of menu names from the menu_master table
            menu_names = [name for name in menu_names if name in Menu_Opportunity_list_df.columns]

            # Iterate over each row in Menu_Opportunity_list_df
            for i, row in Menu_Opportunity_list_df.iterrows():
                mileage = float(row['mileage']) if row['mileage'] is not None else 0

                # Initialize menu_names columns to NaN
                Menu_Opportunity_list_df.loc[i, menu_names] = np.nan

                # Find rows in menu_master_df where mileage is within the range
                matching_menus = menu_master_df[(pd.to_numeric(menu_master_df['range_from'], errors='coerce') <= mileage) & (
                    pd.to_numeric(menu_master_df['range_to'], errors='coerce') >= mileage)]

                for _, menu_row in matching_menus.iterrows():
                    menu_name = menu_row['menu_name']
                    service_type_id = menu_row['service_type_id']

                    # Check if the menu_name column exists in Menu_Opportunity_list_df
                    if menu_name in Menu_Opportunity_list_df.columns:
                        # Map service_type_id to service_type
                        service_type = service_type_mapping.get(service_type_id, None)

                        # Update the value of the column in Menu_Opportunity_list_df with service_type
                        if service_type:
                            Menu_Opportunity_list_df.at[i, menu_name] = service_type
                            Menu_Opportunity_list_df.at[i, menu_name + '_id'] = service_type_id

            # Menu calculation is applicable to menu only if the models in current data set are assigned to the menu
            mapped_menus = set()
            for menus in Menu_Opportunity_list_df['mapped_menu']:
                mapped_menus.update(menus.split(','))

            individual_ro_counts = {
                menu: {'Basic': 0, 'Intermediate': 0, 'Major': 0} for menu in mapped_menus}

            for menu in mapped_menus:
                # Filter rows where 'mapped_menu' contains the current menu
                filtered_df_2 = Menu_Opportunity_list_df[Menu_Opportunity_list_df['mapped_menu'] == menu].copy()

                opprtunity_ros_set = set(filtered_df_2['ronumber'])
                all_revenue_with_opprtunity_ros = combined_revenue_details[combined_revenue_details['ronumber'].isin(opprtunity_ros_set)]

                ronumbers_with_menu_opcodes = {
                        row['ronumber']
                        for _, row in all_revenue_with_opprtunity_ros.iterrows()
                        if row.get('lbropcode', '').strip() in assigned_menu_opcodes_df.loc[assigned_menu_opcodes_df['menu_name'] == menu, 'menu_opcode'].values
                    }
                # Filter out rows where the current menu column is null
                filtered_df_2 = filtered_df_2.dropna(subset=[menu])

                # Filter rows with menu opcodes that assigned to the current menu
                filtered_df_with_menu_opcodes = filtered_df_2[filtered_df_2['ronumber'].isin(ronumbers_with_menu_opcodes)].copy()

                # Filter rows with menu opcodes that assigned to the current menu also filter only the rows that has paytypegroup C, E, and M
                filtered_df_with_menu_opcodes_CP = filtered_df_2[(filtered_df_2['lbropcode'].str.upper().isin(assigned_menu_opcodes_df.loc[assigned_menu_opcodes_df['menu_name'] == menu, 'menu_opcode'])) & (filtered_df_2['group'].isin({'C'})) & (filtered_df_2['paytypegroup'].isin({'C','M','E'}))].copy()

                # Get the unique ro count for each category for calculating upsell potentials and potential hours
                for category in ['Basic', 'Intermediate', 'Major']:
                    # Filter the list based on category and paytypegroup C, E, and M
                    category_filtered_df = filtered_df_2[(filtered_df_2[menu] == category) & (filtered_df_2['group'].isin({'C'})) & (filtered_df_2['paytypegroup'].isin({'C','M','E'}))].copy()
                    # Same RO with different closed date will be considered as two individual ROs
                    category_filtered_df['unique_ronumber_category'] = category_filtered_df['ronumber'].astype(str) + '_' + category_filtered_df['closeddate'].astype(str).copy()
                    unique_ro_count_for_cat = category_filtered_df['unique_ronumber_category'].nunique()
                    individual_ro_counts[menu][category] = unique_ro_count_for_cat

                # Same RO with different closed date will be considered as two individual ROs
                filtered_df_2['unique_ronumber'] = filtered_df_2['ronumber'].astype(str) + '_' + filtered_df_2['closeddate'].astype(str).copy()

                # Identify the ro count of data with menu opcodes
                filtered_df_with_menu_opcodes['unique_ronumber'] = filtered_df_with_menu_opcodes['ronumber'].astype(str) + '_' + filtered_df_with_menu_opcodes['closeddate'].astype(str).copy()

                # Labor sale, Parts sale and sold hour calculation
                labor_sale_with_menu_opcodes = pd.to_numeric(filtered_df_with_menu_opcodes_CP['lbrsale']).fillna(0).sum()
                parts_sale_with_menu_opcodes = pd.to_numeric(filtered_df_with_menu_opcodes_CP['prtextendedsale']).fillna(0).sum()
                labor_parts_sale = labor_sale_with_menu_opcodes + parts_sale_with_menu_opcodes
                sold_hours_with_menu_opcodes = pd.to_numeric(filtered_df_with_menu_opcodes_CP['lbrsoldhours']).fillna(0).sum()

                # Get the unique ro count
                unique_ro_count = filtered_df_2['unique_ronumber'].nunique()

                rocount_with_menu_opcodes = filtered_df_with_menu_opcodes['unique_ronumber'].nunique()
                jobcount_with_menu_opcodes = filtered_df_with_menu_opcodes_CP.shape[0]

                # Add the unique ro count to the total
                menu_pportunities += unique_ro_count
                menu_sold += rocount_with_menu_opcodes
                menu_jobs += jobcount_with_menu_opcodes

            if menu_pportunities != 0:
                menu_sold_perc = round_off(((menu_sold / menu_pportunities) * 100), 2)
            else:
                menu_sold_perc = 0

    menu_penetration_percentage_drilldown_result = {
        "MPI Penetration Percentage": {
            "Menu Count": menu_sold,
            "Total RO Count": menu_pportunities,
            "Menu Penetration %": menu_sold_perc
        },
        
    }
    return menu_sold_perc,menu_penetration_percentage_drilldown_result

def calculate_shopsupplies_metrics(advisor_filter=None, target_month=None):
    """
    Calculate Shop Supplies metrics for the target month only.
    """
    log_info("Calculating Shop Supplies metrics for target month...")

    try:
        if target_month is None:
            raise ValueError("target_month is required")
            
        target_date = pd.to_datetime(target_month)
        
        # Step 1: Fetch raw data from three separate tables
        log_info("Step 1: Fetching revenue details data...")
        revenue_db_connect = allRevenueDetailsForShopSuppliesTable()
        revenue_df = revenue_db_connect.getTableResult(advisor_filter)
        log_info(f"Retrieved {len(revenue_df)} revenue detail records")

        log_info("Step 2: Fetching paytype retail flag settings...")
        paytype_db_connect = paytypeRetailFlagSettingTable()
        paytype_df = paytype_db_connect.getTableResult()
        log_info(f"Retrieved {len(paytype_df)} paytype setting records")

        log_info("Step 3: Fetching shop supplies data...")
        shop_supplies_db_connect = shopSuppliesDataTable()
        shop_supplies_df = shop_supplies_db_connect.getTableResult()
        log_info(f"Retrieved {len(shop_supplies_df)} shop supplies records")

        if revenue_df.empty or paytype_df.empty or shop_supplies_df.empty:
            log_warn("Missing data - revenue_df empty: {}, paytype_df empty: {}, shop_supplies_df empty: {}".format(
                revenue_df.empty, paytype_df.empty, shop_supplies_df.empty))
            return {
                "shop_supplies": {
                    "combined": 0,
                    "customer_pay": 0,
                    "warranty": 0,
                    "internal": 0,
                    "month": target_date.strftime("%Y-%m")
                }
            }

        # Step 4: Data preparation and validation
        log_info("Step 4: Preparing data for calculations...")

        # Convert date columns to datetime
        revenue_df['closeddate'] = pd.to_datetime(revenue_df['closeddate'])
        shop_supplies_df['closeddate'] = pd.to_datetime(shop_supplies_df['closeddate'])

        # Filter for target month
        shop_supplies_df = shop_supplies_df[
            (shop_supplies_df['closeddate'].dt.year == target_date.year) & 
            (shop_supplies_df['closeddate'].dt.month == target_date.month)
        ]

        # Convert numeric columns to proper types
        numeric_columns = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        for col in numeric_columns:
            if col in revenue_df.columns:
                revenue_df[col] = pd.to_numeric(revenue_df[col], errors='coerce').fillna(0)

        shop_supplies_df['totalshopsupply'] = pd.to_numeric(shop_supplies_df['totalshopsupply'], errors='coerce').fillna(0)

        # Step 5: Join revenue details with paytype settings
        log_info("Step 5: Joining revenue details with paytype settings...")
        revenue_with_paytype = pd.merge(
            revenue_df,
            paytype_df,
            left_on=['paytypegroup', 'store_id'],
            right_on=['source_paytype', 'store_id'],
            how='left'
        )

        # Step 6: Apply business logic to determine RO type
        log_info("Step 6: Applying business logic to determine RO types...")
        def determine_ro_type(row):
            has_revenue = not (row['lbrsale'] == 0 and row['lbrsoldhours'] == 0 and
                             row['prtextendedsale'] == 0 and row['prtextendedcost'] == 0)

            if has_revenue and row['opcategory'] != 'N/A' and pd.notna(row['mapped_paytype']):
                return row['mapped_paytype']
            else:
                return 'I'  # Internal

        revenue_with_paytype['ro_type'] = revenue_with_paytype.apply(determine_ro_type, axis=1)

        # Step 7: Handle multiple entries per RO (prioritize Customer Pay > Warranty > Internal)
        log_info("Step 7: Handling multiple entries per RO with priority logic...")
        def priority_score(ro_type):
            if ro_type == 'C':
                return 1
            elif ro_type == 'W':
                return 2
            else:
                return 3

        revenue_with_paytype['priority'] = revenue_with_paytype['ro_type'].apply(priority_score)

        # Keep only the highest priority RO type for each unique RO
        ro_type_df = revenue_with_paytype.loc[
            revenue_with_paytype.groupby(['ronumber', 'closeddate', 'store_id'])['priority'].idxmin()
        ][['ronumber', 'closeddate', 'store_id', 'ro_type']]

        # Step 8: Join RO type data with shop supplies data
        log_info("Step 8: Joining RO types with shop supplies data...")
        merged_df = pd.merge(
            shop_supplies_df,
            ro_type_df,
            on=['ronumber', 'closeddate', 'store_id'],
            how='inner'
        )

        if merged_df.empty:
            log_warn("No matching data after joining RO types with shop supplies")
            return {
                "shop_supplies": {
                    "combined": 0,
                    "customer_pay": 0,
                    "warranty": 0,
                    "internal": 0,
                    "month": target_date.strftime("%Y-%m")
                }
            }

        # Step 9: Calculate totals by RO type for target month
        total_customer = merged_df[merged_df['ro_type'] == 'C']['totalshopsupply'].sum()
        total_warranty = merged_df[merged_df['ro_type'] == 'W']['totalshopsupply'].sum()
        total_internal = merged_df[merged_df['ro_type'] == 'I']['totalshopsupply'].sum()
        total_combined = total_customer + total_warranty + total_internal
        log_info(f"  - Customer Pay: ${total_customer:.2f}")
        log_info(f"  - Warranty: ${total_warranty:.2f}")
        log_info(f"  - Internal: ${total_internal:.2f}")
        log_info(f"  - Total ROs processed: {len(merged_df)}")
        
        # summary details
        distinct_ro_count_customer = merged_df[(merged_df['ro_type'] == 'C') & (merged_df['totalshopsupply'] != 0)]['ronumber'].nunique()
        distinct_ro_count_warranty = merged_df[(merged_df['ro_type'] == 'W') & (merged_df['totalshopsupply'] != 0)]['ronumber'].nunique()
        distinct_ro_count_internal = merged_df[(merged_df['ro_type'] == 'I') & (merged_df['totalshopsupply'] != 0)]['ronumber'].nunique()
        log_info(f"  - Distinct Customer Pay ROs with non-zero shop supplies: {distinct_ro_count_customer}")
        log_info(f"  - Distinct Warranty ROs with non-zero shop supplies: {distinct_ro_count_warranty}")
        log_info(f"  - Distinct Internal ROs with non-zero shop supplies: {distinct_ro_count_internal}")


        # Return results in the required format for target month only
        shopsupplies_metrics_drilldown_result = {  
            "Customer Pay": {
                "RO Count": distinct_ro_count_customer,
                "Shop Supplies - Customer Pay": round_off(total_customer, 2),
            },
            "Warranty": {
                "RO Count": distinct_ro_count_warranty,
                "Shop Supplies - Warranty": round_off(total_warranty, 2)
            },
            "Internal": {
                "RO Count": distinct_ro_count_internal,
                "Shop Supplies - Internal": round_off(total_internal, 2)
            }
        }
        return {
            "shop_supplies": {
                "combined": round_off(total_combined, 2),
                "customer_pay": round_off(total_customer, 2),
                "warranty": round_off(total_warranty, 2),
                "internal": round_off(total_internal, 2),
                "month": target_date.strftime("%Y-%m")
            }
             
        },shopsupplies_metrics_drilldown_result

    except Exception as e:
        log_error(f"Error calculating shop supplies metrics: {str(e)}")
        import traceback
        log_error(f"Full traceback: {traceback.format_exc()}")
        return {
            "shop_supplies": {
                "combined": 0,
                "customer_pay": 0,
                "warranty": 0,
                "internal": 0,
                "month": target_date.strftime("%Y-%m") if target_date else None
            }
        }



def process_target_month_data(all_revenue_details_df, month_start, month_end, advisor, tech, retail_flag, customer_pay_types, warranty_pay_types, columns_to_check, db_tables):
    """Process data for the target month and return results"""
    month_start = month_start.date()
    month_end = month_end.date()

    # Filter data for the specific target month
    month_data = all_revenue_details_df[
        (all_revenue_details_df['closeddate'] >= month_start) &
        (all_revenue_details_df['closeddate'] <= month_end)
    ]
    log_info(f"Target month data shape: {month_data.shape}")

    if month_data.empty:
        log_info("No data found for the target month")
        return None

    # Apply existing filtering logic
    filtered_df = month_data[
        (month_data['department'] == 'Service') &
        (month_data['hide_ro'] != True)
    ]

    if filtered_df.empty:
        log_info("No service department data found for the target month")
        return None

    filtered_df = filtered_df.copy()
    filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)

    # Initialize combined revenue details
    combined_revenue_details = filtered_df.copy()
    combined_revenue_details['group'] = pd.Series(dtype="string")

    # Assign RO groups
    combined_revenue_details = assign_ro_groups(combined_revenue_details, customer_pay_types, warranty_pay_types, columns_to_check)

    # Apply advisor and tech filters
    combined_revenue_details = apply_advisor_tech_filters(combined_revenue_details, advisor, tech, columns_to_check, filtered_df)

    # Calculate RO counts
    Scorecard_10_CP, Scorecard_10_Wty, Scorecard_10_Int, all_unique_ros = calculate_ro_counts(combined_revenue_details)

    # Process customer pay data
    total_revenue_details_C_df, service_type_mapping = process_customer_pay_data(
        combined_revenue_details, customer_pay_types,
        db_tables['menu_master_df'], db_tables['assigned_menu_models_df'],
        db_tables['menu_service_type_df'], db_tables['assigned_menu_opcodes_df']
    )

    # Calculate mileage-based metrics
    total_ro_count_below_60k, total_ro_count_above_60k, perc_of_business_below_60k, perc_of_business_above_60k = calculate_mileage_based_metrics(total_revenue_details_C_df)

    # Process One Line and Multi Line ROs
    One_Line_RO_Details, Multi_Line_RO_Details = process_one_line_multi_line_ros(
        total_revenue_details_C_df, service_type_mapping, db_tables['assigned_menu_opcodes_df']
    )

    # Calculate One Line and Multi Line metrics
    one_multi_line_metrics,one_line_drilldown_result,perc_of_one_line_drilldown_result,multi_line_drilldown_result,perc_of_multi_line_drilldown_result, = calculate_one_line_multi_line_metrics(
        One_Line_RO_Details, Multi_Line_RO_Details, total_ro_count_below_60k, total_ro_count_above_60k, Scorecard_10_CP
    )

    # Calculate average RO open days
    avg_open_days_metrics,avg_open_days_drilldown_result = calculate_average_ro_open_days(combined_revenue_details, filtered_df, columns_to_check)

    # Calculate CP Parts to Labor Ratio
    CP_Parts_to_Labor_Ratio, total_CP_revenue_details_df,lbr_sale_total,prt_ext_sale_total = calculate_cp_parts_to_labor_ratio(combined_revenue_details, customer_pay_types)

    # Calculate Labor Sold Hours Percentage
    labor_hours_percentage_metrics,labor_sold_hours_percentage_drilldown_result = calculate_labor_sold_hours_percentage(combined_revenue_details, filtered_df)
    # Calculate CP Parts to Labor Ratio by Category
    parts_labor_ratio_by_category,parts_to_labor_ratio_category_drilldown_result = calculate_cp_parts_to_labor_ratio_by_category(total_CP_revenue_details_df,customer_pay_types)
    # Calculate MPI Penetration Percentage
    Completed_MPI_ROs_Perc,mpi_penetration_percentage_drilldown_result = calculate_mpi_penetration_percentage(combined_revenue_details, db_tables['MPI_setup_df'], db_tables['mpi_opcodes'])

    # Calculate Menu Penetration Percentage
    menu_sold_perc ,menu_penetration_percentage_drilldown_result= calculate_menu_penetration_percentage(
        combined_revenue_details, db_tables['menu_master_df'], db_tables['assigned_menu_models_df'],
        db_tables['menu_service_type_df'], db_tables['assigned_menu_opcodes_df']
    )

    # Calculate Shop Supplies Metrics for the target month
    shopsupplies_metrics,shopsupplies_metrics_drilldown_result = calculate_shopsupplies_metrics(advisor, month_start.strftime('%Y-%m'))

    # ToDo Define drilldown structure
    drilldown_results = {
        "948": one_line_drilldown_result,
        "1357": avg_open_days_drilldown_result,
        "923": perc_of_one_line_drilldown_result,
        "1354": multi_line_drilldown_result,
        "1355": perc_of_multi_line_drilldown_result,
        "938": {
            "12 Months Return Rate": {"Value": None},
            "6 Months Return Rate": {"Value": None}
        },
        "930": {
            "Parts to Labor Ratio": {
                "Labor Sale - Customer Pay": lbr_sale_total,
                "Total Parts Sale": prt_ext_sale_total,
                "Parts To Labor Ratio": CP_Parts_to_Labor_Ratio
            }
        },
        "935": labor_sold_hours_percentage_drilldown_result,
        "936": parts_to_labor_ratio_category_drilldown_result,
        "1239": shopsupplies_metrics_drilldown_result,
        "1316": mpi_penetration_percentage_drilldown_result,
        "1317": menu_penetration_percentage_drilldown_result
    }

    # Return comprehensive result set
    return {
        "target_month": month_start.strftime('%Y-%m'),
        "target_month_name": month_start.strftime('%B %Y'),
        "total_ros": all_unique_ros,
        "ro_counts": {
            "customer_pay_ros": Scorecard_10_CP,
            "warranty_ros": Scorecard_10_Wty,
            "internal_ros": Scorecard_10_Int
        },
        "mileage_metrics": {
            "total_ro_count_below_60k": total_ro_count_below_60k,
            "total_ro_count_above_60k": total_ro_count_above_60k,
            "perc_of_business_below_60k": perc_of_business_below_60k,
            "perc_of_business_above_60k": perc_of_business_above_60k
        },
        "tooltip_results": {
            "one_multi_line_metrics": one_multi_line_metrics,
            "avg_open_days_metrics": avg_open_days_metrics,
            "cp_parts_to_labor_ratio": CP_Parts_to_Labor_Ratio,
            "labor_hours_percentage_metrics": labor_hours_percentage_metrics,
            "parts_labor_ratio_by_category": parts_labor_ratio_by_category,
            "mpi_penetration_percentage": Completed_MPI_ROs_Perc,
            "menu_penetration_percentage": menu_sold_perc,
            "shopsupplies_metrics": shopsupplies_metrics
        },
        "drilldown_results": drilldown_results
    }

def db_execution(target_date_str, advisor, tech, retail_flag, columns_to_check):
    """
    Handle database operations and execute month processing
    """
    try:
        # Get target month date range
        month_start, month_end = get_month_date_range_from_target(target_date_str)
        log_info(f"Target month range: {month_start.date()} to {month_end.date()}")

        # Fetch all data from database
        log_info("Fetching data from database...")
        
        # all_revenue_details_table_db_connect = allRevenueDetailsCPOverview()
        all_revenue_details_table_db_connect = allRevenueDetailsTable()
        all_revenue_details_df = all_revenue_details_table_db_connect.getTableResult()
        

        if all_revenue_details_df.empty:
            log_error("ERROR: No data retrieved from database!")
            return False, False, False

        # Convert date column and other preprocessing
        columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))

        # Get database tables
        db_tables = get_database_tables()

        # Define customer and warranty pay types based on retail_flag
        customer_pay_types, warranty_pay_types = define_customer_warranty_pay_types(db_tables['retail_flag'])

        target_month_result = process_target_month_data(
            all_revenue_details_df,
            month_start,
            month_end,
            advisor,
            tech,
            retail_flag,
            customer_pay_types,
            warranty_pay_types,
            columns_to_check,
            db_tables
        )
        return target_month_result, customer_pay_types, warranty_pay_types
    except Exception as e:
        log_error(f"ERROR in db_execution: {str(e)}")
        log_error("=" * 60)
        log_error("DATABASE EXECUTION FAILED")
        log_error("=" * 60)
        return None, None, None

def db_calculation():
    """
    Main execution function for db calculation
    """
    # Configuration variables
    columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']

    storeid = config.store_id
    realm = config.database_name
    advisor_set = config.advisor
    tech_set = config.technician

    # Process advisor and technician configurations
    advisor, tech = process_advisor_tech_filters(advisor_set, tech_set)

    if advisor != {'all'}:
        advisor_id = next(iter(advisor))
    else:
        advisor_id = 'all'

    # Get database tables and retail flag
    tables_result = get_database_tables()
    if not tables_result:
        log_error("Failed to fetch database tables!")
        return

    retail_flag, menu_master_df, service_type_mapping, assigned_menu_models_df, assigned_menu_opcodes_df, mpi_setup_df, mpi_opcodes_df = tables_result

    # Execute database operations and processing
    if not TARGET_MONTHS_YEARS or len(TARGET_MONTHS_YEARS) == 0:
        log_error("No target month specified!")
        return
    target_date_str = TARGET_MONTHS_YEARS[0]
    target_month_result, customer_pay_types, warranty_pay_types = db_execution(
        target_date_str, advisor, tech, retail_flag, columns_to_check
    )

    # Process results
    if target_month_result:
        log_info("\n" + "=" * 80)
        log_info("RESULTS PROCESSING")
        log_info("=" * 80)

        # Create the final result set for the target month only
        final_result_set = {
            "analysis_info": {
                "target_month": target_date_str,
                "analysis_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "store_id": storeid,
                "realm": realm,
                "advisor_filter": list(advisor),
                "technician_filter": list(tech),
                "customer_pay_types": list(customer_pay_types),
                "warranty_pay_types": list(warranty_pay_types)
            },
            "target_month_results": target_month_result
        }

        # Write results to JSON file
        # output_filename = "chart_processing_results/special_metrics_calculated_value.json"
        result_dir,output_filename = create_folder_file_path(subfolder="chart_processing_results", output_file=db_json)  
        with open(output_filename, 'w', encoding='utf-8') as json_file:
            json.dump(final_result_set, json_file, indent=4, ensure_ascii=False)

        log_info(f"\nTarget month Special Metrics data written successfully to {output_filename}")


    else:
        log_info("\n" + "=" * 80)
        log_info("NO DATA RESULTS PROCESSING")
        log_info("=" * 80)
        log_info(f"No data available for target month {target_date_str}")

    log_info("\n" + "=" * 80)
    log_info("SPECIAL METRICS ANALYSIS - MAIN EXECUTION COMPLETED")
    log_info("=" * 80)
  
class MultiChartParallelProcessor:
    """Process multiple charts in parallel with separate browsers"""
    
    # def __init__(self, max_browsers=4, auth_manager=None):
    #     self.max_browsers = max_browsers
    #     self.auth_manager = auth_manager or AuthManager()
    def __init__(self, max_browsers=MAX_CONCURRENT_BROWSERS, auth_manager=None):
        self.max_browsers = max_browsers
        self.auth_manager = auth_manager or AuthManager(config)
        self.charts_info = None

    async def discover_charts(self):
        """Discover all charts on the new component page with 12 bar charts"""
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []
        
        page = await self.auth_manager.new_page()
        try:
            await page.goto(f"{config.site_url.rstrip('/')}/SpecialMetrics", timeout=50000)
            chart_found = False

            # Wait for the page to load and charts to be rendered
            selectors_to_try = [
                'canvas.chartjs-render-monitor',
                'canvas[class*="chartjs"]',
                'canvas',
                '[id*="chart"]',
                '.react-grid-item canvas',
                '[id*="chartContainterId"]'
            ]

            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    chart_found = True
                    print(f"✓ Found charts using selector: {selector}")
                    break
                except:
                    continue

            if not chart_found:
                print("❌ No chart elements found with any selector")
                return False

            # Enhanced waiting strategy for chart loading
            print("⏳ Waiting for charts to fully load...")
            await asyncio.sleep(3)  # Initial wait

            # Wait for chart containers to be present (flexible approach)
            print("⏳ Waiting for chart containers to load...")
            await page.wait_for_function("""
                () => {
                    const containers = document.querySelectorAll('[id*="chartContainterId"]');
                    console.log(`Found ${containers.length} chart containers`);
                    return containers.length > 0;  // Wait for at least one chart
                }
            """, timeout=60000)

            # Additional wait for chart rendering
            await asyncio.sleep(5)

            # Check how many chart containers we found
            container_count = await page.evaluate("""
                () => {
                    const containers = document.querySelectorAll('[id*="chartContainterId"]');
                    return containers.length;
                }
            """)
            print(f"✓ Found {container_count} chart containers on page")

            # Wait for canvas elements to be rendered (flexible approach)
            await page.wait_for_function("""
                () => {
                    const canvases = document.querySelectorAll('canvas');
                    console.log(`Found ${canvases.length} canvas elements`);
                    return canvases.length > 0;  // Wait for at least one canvas
                }
            """, timeout=60000)

            # Check how many canvases we found
            canvas_count = await page.evaluate("""
                () => {
                    const canvases = document.querySelectorAll('canvas');
                    return canvases.length;
                }
            """)
            print(f"✓ Found {canvas_count} canvas elements - charts appear to be loaded")
            
            charts_info = await page.evaluate("""
                () => {
                    console.log('Starting enhanced chart discovery...');

                    // First, try to find all chart containers
                    const chartContainers = document.querySelectorAll('[id*="chartContainterId"]');
                    console.log('Found chart containers:', chartContainers.length);

                    const chartsInfo = [];
                    const processedContainers = new Set();

                    // Method 1: Find charts by container ID (Enhanced)
                    chartContainers.forEach((container, containerIndex) => {
                        const containerId = container.id;
                        const chartId = containerId.replace('chartContainterId-', '');

                        // Skip if already processed
                        if (processedContainers.has(containerId)) {
                            return;
                        }
                        processedContainers.add(containerId);

                        // Look for canvas in this container
                        const canvas = container.querySelector('canvas');
                        if (canvas) {
                            const rect = canvas.getBoundingClientRect();

                            // Get chart title from card header
                            let chartTitle = `Chart ${chartId}`;
                            const cardHeader = container.querySelector(`#card-header-${chartId}`);
                            if (cardHeader) {
                                const titleElement = cardHeader.querySelector('.MuiCardHeader-title');
                                if (titleElement && titleElement.textContent.trim()) {
                                    chartTitle = titleElement.textContent.trim();
                                }
                            }

                            // Check if chart has data (no "divNoData" element)
                            const hasData = !container.querySelector('.divNoData');

                            // Check if canvas is actually rendered and visible
                            const isVisible = rect.width > 0 && rect.height > 0 &&
                                            window.getComputedStyle(canvas).display !== 'none' &&
                                            window.getComputedStyle(container).display !== 'none';

                            chartsInfo.push({
                                canvasIndex: containerIndex,
                                chartTitle: chartTitle,
                                canvasId: canvas.id || `canvas-${chartId}`,
                                canvasClass: canvas.className,
                                containerId: containerId,
                                chartId: chartId,
                                position: {
                                    x: Math.round(rect.left),
                                    y: Math.round(rect.top),
                                    width: Math.round(rect.width),
                                    height: Math.round(rect.height)
                                },
                                visible: isVisible,
                                isChartJs: canvas.classList.contains('chartjs-render-monitor') ||
                                        canvas.classList.contains('chartjs-render'),
                                hasData: hasData,
                                detectionMethod: 'container-based',
                                isFullyLoaded: canvas.width > 0 && canvas.height > 0
                            });

                            console.log(`Processed chart ${chartId}: ${chartTitle}, visible: ${isVisible}, hasData: ${hasData}`);
                        } else {
                            console.log(`No canvas found in container ${containerId}`);
                        }
                    });
                    
                    console.log('Charts found by container method:', chartsInfo.length);

                    // Method 2: Find any remaining canvases that might have been missed
                    const allCanvases = document.querySelectorAll('canvas');
                    console.log('Total canvases found:', allCanvases.length);
                    
                    allCanvases.forEach((canvas, canvasIndex) => {
                        // Skip if this canvas is already in our list
                        const alreadyFound = chartsInfo.some(chart => 
                            chart.canvasId === canvas.id || 
                            (chart.canvasId.includes('canvas-') && canvas.closest(`#${chart.containerId}`))
                        );
                        
                        if (!alreadyFound) {
                            const rect = canvas.getBoundingClientRect();
                            
                            // Only include visible canvases with reasonable dimensions
                            if (rect.width > 50 && rect.height > 50) {
                                // Try to find chart title
                                let chartTitle = `Chart ${canvasIndex + 1}`;
                                let chartId = null;
                                let containerId = null;
                                
                                // Look for parent container
                                let container = canvas.closest('.react-grid-item, .MuiCard-root, [id*="chart"], [class*="chart"]');
                                if (container) {
                                    // Try to extract ID from container
                                    if (container.id && container.id.includes('chartContainterId')) {
                                        containerId = container.id;
                                        chartId = container.id.replace('chartContainterId-', '');
                                    }
                                    
                                    // Look for title
                                    const titleElement = container.querySelector('h1, h2, h3, h4, h5, h6, .title, [class*="title"], .MuiCardHeader-title');
                                    if (titleElement && titleElement.textContent.trim()) {
                                        chartTitle = titleElement.textContent.trim();
                                    }
                                }
                                
                                // Check for data
                                const hasData = !container || !container.querySelector('.divNoData');
                                
                                chartsInfo.push({
                                    canvasIndex: canvasIndex,
                                    chartTitle: chartTitle,
                                    canvasId: canvas.id || `canvas-fallback-${canvasIndex}`,
                                    canvasClass: canvas.className,
                                    containerId: containerId,
                                    chartId: chartId || `fallback-${canvasIndex}`,
                                    position: {
                                        x: Math.round(rect.left),
                                        y: Math.round(rect.top),
                                        width: Math.round(rect.width),
                                        height: Math.round(rect.height)
                                    },
                                    visible: rect.width > 0 && rect.height > 0,
                                    isChartJs: canvas.classList.contains('chartjs-render-monitor') || canvas.classList.contains('chartjs-render'),
                                    hasData: hasData,
                                    detectionMethod: 'canvas-based'
                                });
                            }
                        }
                    });
                    
                    // Method 3: Look for react-grid-items that might contain charts
                    const gridItems = document.querySelectorAll('.react-grid-item');
                    console.log('Grid items found:', gridItems.length);
                    
                    gridItems.forEach((item, itemIndex) => {
                        const canvas = item.querySelector('canvas');
                        if (canvas) {
                            // Check if we already have this chart
                            const alreadyFound = chartsInfo.some(chart => 
                                chart.canvasId === canvas.id || 
                                chart.containerId === item.id
                            );
                            
                            if (!alreadyFound) {
                                const rect = canvas.getBoundingClientRect();
                                
                                if (rect.width > 0 && rect.height > 0) {
                                    let chartTitle = `Grid Chart ${itemIndex + 1}`;
                                    let chartId = null;
                                    
                                    if (item.id && item.id.includes('chartContainterId')) {
                                        chartId = item.id.replace('chartContainterId-', '');
                                        
                                        const cardHeader = item.querySelector(`#card-header-${chartId}`);
                                        if (cardHeader) {
                                            const titleElement = cardHeader.querySelector('.MuiCardHeader-title');
                                            if (titleElement && titleElement.textContent.trim()) {
                                                chartTitle = titleElement.textContent.trim();
                                            }
                                        }
                                    }
                                    
                                    const hasData = !item.querySelector('.divNoData');
                                    
                                    chartsInfo.push({
                                        canvasIndex: itemIndex,
                                        chartTitle: chartTitle,
                                        canvasId: canvas.id || `grid-canvas-${itemIndex}`,
                                        canvasClass: canvas.className,
                                        containerId: item.id,
                                        chartId: chartId || `grid-${itemIndex}`,
                                        position: {
                                            x: Math.round(rect.left),
                                            y: Math.round(rect.top),
                                            width: Math.round(rect.width),
                                            height: Math.round(rect.height)
                                        },
                                        visible: rect.width > 0 && rect.height > 0,
                                        isChartJs: canvas.classList.contains('chartjs-render-monitor') || canvas.classList.contains('chartjs-render'),
                                        hasData: hasData,
                                        detectionMethod: 'grid-based'
                                    });
                                }
                            }
                        }
                    });
                    
                    console.log(`Total charts discovered: ${chartsInfo.length}`);

                    // Re-index canvasIndex to ensure proper ordering
                    chartsInfo.forEach((chart, index) => {
                        chart.canvasIndex = index;
                    });

                    // Sort by position (top to bottom, left to right)
                    chartsInfo.sort((a, b) => {
                        if (Math.abs(a.position.y - b.position.y) < 50) {
                            return a.position.x - b.position.x;
                        }
                        return a.position.y - b.position.y;
                    });

                    // Final re-indexing after sorting
                    chartsInfo.forEach((chart, index) => {
                        chart.canvasIndex = index;
                    });

                    console.log(`Final chart count: ${chartsInfo.length}`);
                    console.log('Chart IDs found:', chartsInfo.map(c => c.chartId));

                    return chartsInfo;
                }
            """)
            
            print(f"📊 Found {len(charts_info)} charts on the new component page")

            # If we don't have any charts, try one more time with additional wait
            if len(charts_info) == 0:
                print(f"⚠️ No charts found. Retrying with longer wait...")
                await asyncio.sleep(5)  # Additional wait

                # Try discovery again
                retry_charts_info = await page.evaluate("""
                    () => {
                        const containers = document.querySelectorAll('[id*="chartContainterId"]');
                        const retryChartsInfo = [];

                        containers.forEach((container, index) => {
                            const containerId = container.id;
                            const chartId = containerId.replace('chartContainterId-', '');
                            const canvas = container.querySelector('canvas');

                            if (canvas) {
                                const rect = canvas.getBoundingClientRect();
                                let chartTitle = `Chart ${chartId}`;
                                const cardHeader = container.querySelector(`#card-header-${chartId}`);
                                if (cardHeader) {
                                    const titleElement = cardHeader.querySelector('.MuiCardHeader-title');
                                    if (titleElement && titleElement.textContent.trim()) {
                                        chartTitle = titleElement.textContent.trim();
                                    }
                                }

                                retryChartsInfo.push({
                                    canvasIndex: index,
                                    chartTitle: chartTitle,
                                    canvasId: canvas.id || `canvas-${chartId}`,
                                    canvasClass: canvas.className,
                                    containerId: containerId,
                                    chartId: chartId,
                                    position: {
                                        x: Math.round(rect.left),
                                        y: Math.round(rect.top),
                                        width: Math.round(rect.width),
                                        height: Math.round(rect.height)
                                    },
                                    visible: rect.width > 0 && rect.height > 0,
                                    isChartJs: canvas.classList.contains('chartjs-render-monitor'),
                                    hasData: !container.querySelector('.divNoData'),
                                    detectionMethod: 'retry-attempt',
                                    isFullyLoaded: canvas.width > 0 && canvas.height > 0
                                });
                            }
                        });

                        return retryChartsInfo;
                    }
                """)

                if len(retry_charts_info) > len(charts_info):
                    print(f"✓ Retry successful! Found {len(retry_charts_info)} charts")
                    charts_info = retry_charts_info
                else:
                    print(f"⚠️ Retry didn't improve results. Continuing with {len(charts_info)} charts")

            # If still no charts found, this might be a page loading issue
            if len(charts_info) == 0:
                print("❌ No charts found after retry. This might indicate a page loading issue or changed page structure.")
                return False

            # Group charts by their data availability
            charts_with_data = [chart for chart in charts_info if chart['hasData']]
            charts_no_data = [chart for chart in charts_info if not chart['hasData']]

            print(f"✅ Charts with data: {len(charts_with_data)}")
            print(f"❌ Charts with no data: {len(charts_no_data)}")

            # Display detailed info for each chart
            for i, chart in enumerate(charts_info):
                status = "✅ HAS DATA" if chart['hasData'] else "❌ NO DATA"
                method = chart.get('detectionMethod', 'unknown')
                print(f"  {i+1}. Chart ID: {chart['chartId']} - {chart['chartTitle']} {status}")
                print(f"     Container: {chart['containerId']}")
                print(f"     Canvas: {chart['canvasId']} (Method: {method})")
                print(f"     Position: {chart['position']}")
                print(f"     Visible: {chart['visible']}, ChartJS: {chart['isChartJs']}")
                print()

            # Debug: Also log all chart container IDs found on page
            container_ids = await page.evaluate("""
                () => {
                    const containers = document.querySelectorAll('[id*="chartContainterId"]');
                    return Array.from(containers).map(c => c.id);
                }
            """)
            print(f"All chart container IDs on page: {container_ids}")

            return charts_info
            
        except Exception as e:
            print(f"❌ Error discovering charts: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            log_info("Finished discovering chart-point combinations")
            await page.close()                         
    async def find_matching_points_in_chart(self, page, chart_index, target_month_year,chart_id):
        """Find matching data points for a specific chart and target month-year"""
        try:
            log_info(f"Finding points in chart {chart_index} for target: {target_month_year}")            
            matching_points = await page.evaluate("""
                (args) => {
                    const { chartIndex, targetMonthYear } = args;
                    const matchingPoints = [];
                    
                    try {
                        // Find all canvas elements
                        const canvases = document.querySelectorAll('canvas');
                        console.log(`Found ${canvases.length} canvas elements`);
                        
                        // Check if the requested chart index exists
                        if (chartIndex >= canvases.length) {
                            console.log(`Chart index ${chartIndex} out of range (max: ${canvases.length - 1})`);
                            return [];
                        }
                        
                        const canvas = canvases[chartIndex];
                        let chart = null;
                        
                        console.log(`Processing canvas ${chartIndex} for target: ${targetMonthYear}`);
                        
                        // Multiple methods to get chart instance
                        try {
                            // Method 1: Chart.getChart (Chart.js v3+)
                            if (typeof Chart !== 'undefined' && Chart.getChart) {
                                chart = Chart.getChart(canvas);
                                console.log(`Method 1 - Chart.getChart: ${chart ? 'Found' : 'Not found'}`);
                            }
                            
                            // Method 2: Chart.instances (Chart.js v2)
                            if (!chart && typeof Chart !== 'undefined' && Chart.instances) {
                                const instances = Object.values(Chart.instances);
                                chart = instances.find(instance => instance.canvas === canvas);
                                console.log(`Method 2 - Chart.instances: ${chart ? 'Found' : 'Not found'}`);
                            }
                            
                            // Method 3: Canvas._chart property (older versions)
                            if (!chart && canvas._chart) {
                                chart = canvas._chart;
                                console.log(`Method 3 - canvas._chart: Found`);
                            }
                            
                            // Method 4: Check for chart instance in canvas properties
                            if (!chart) {
                                const keys = Object.keys(canvas);
                                for (const key of keys) {
                                    if (key.includes('chart') || key.includes('Chart')) {
                                        chart = canvas[key];
                                        if (chart && chart.data) {
                                            console.log(`Method 4 - Found chart via property: ${key}`);
                                            break;
                                        }
                                    }
                                }
                            }
                            
                        } catch (e) {
                            console.warn(`Error getting chart instance for canvas ${chartIndex}:`, e);
                        }
                        
                        if (!chart) {
                            console.log(`No chart found for canvas ${chartIndex}`);
                            return [];
                        }
                        
                        // Validate chart structure
                        if (!chart.data || !chart.data.datasets) {
                            console.log(`Invalid chart data structure for canvas ${chartIndex}`);
                            return [];
                        }
                        
                        console.log(`Processing chart with ${chart.data.datasets.length} datasets`);
                        
                        const canvasRect = canvas.getBoundingClientRect();
                        console.log(`Canvas rect:`, canvasRect);
                        
                        // Get x-axis labels
                        const xLabels = chart.data.labels || [];
                        console.log(`X-axis labels:`, xLabels);
                        
                        // Helper function to check if label matches target
                        const isLabelMatch = (label, target) => {
                            if (!label || !target) return false;
                            
                            const labelStr = String(label).toLowerCase().trim();
                            const targetStr = String(target).toLowerCase().trim();
                            
                            // Direct match
                            if (labelStr === targetStr) return true;
                            
                            // Contains match
                            if (labelStr.includes(targetStr) || targetStr.includes(labelStr)) return true;
                            
                            // Month-year pattern matching (e.g., "Jan 2024", "January 2024", "01/2024")
                            const monthYearRegex = /(\w+)[\/\-\s]+(\d{4})/;
                            const labelMatch = labelStr.match(monthYearRegex);
                            const targetMatch = targetStr.match(monthYearRegex);
                            
                            if (labelMatch && targetMatch) {
                                const labelMonth = labelMatch[1];
                                const labelYear = labelMatch[2];
                                const targetMonth = targetMatch[1];
                                const targetYear = targetMatch[2];
                                
                                // Check if years match and months match (partial match allowed)
                                if (labelYear === targetYear && 
                                    (labelMonth.includes(targetMonth) || targetMonth.includes(labelMonth))) {
                                    return true;
                                }
                            }
                            
                            return false;
                        };
                        
                        // Process each dataset
                        for (let datasetIndex = 0; datasetIndex < chart.data.datasets.length; datasetIndex++) {
                            const dataset = chart.data.datasets[datasetIndex];
                            
                            if (!dataset.data || !Array.isArray(dataset.data)) {
                                console.log(`No data in dataset ${datasetIndex}`);
                                continue;
                            }
                            
                            console.log(`Processing dataset ${datasetIndex} with ${dataset.data.length} points`);
                            
                            // Process each data point
                            for (let pointIndex = 0; pointIndex < dataset.data.length; pointIndex++) {
                                const value = dataset.data[pointIndex];
                                const xLabel = xLabels[pointIndex] || `Point ${pointIndex}`;
                                
                                // Only process points that match the target month-year
                                if (!isLabelMatch(xLabel, targetMonthYear)) {
                                    continue;
                                }
                                
                                console.log(`Found matching point: ${xLabel} matches ${targetMonthYear}`);
                                
                                try {
                                    let screenX = null;
                                    let screenY = null;
                                    let canvasX = null;
                                    let canvasY = null;
                                    
                                    // Enhanced coordinate extraction
                                    try {
                                        const meta = chart.getDatasetMeta(datasetIndex);
                                        if (meta && meta.data && meta.data[pointIndex]) {
                                            const element = meta.data[pointIndex];
                                            
                                            // Check if coordinates are valid numbers
                                            if (typeof element.x === 'number' && !isNaN(element.x) && 
                                                typeof element.y === 'number' && !isNaN(element.y)) {
                                                canvasX = element.x;
                                                canvasY = element.y;
                                                screenX = canvasRect.left + element.x;
                                                screenY = canvasRect.top + element.y;
                                                console.log(`Element coordinates: canvas(${canvasX}, ${canvasY}) -> screen(${screenX}, ${screenY})`);
                                            }
                                        }
                                    } catch (e) {
                                        console.warn(`Could not get element position for point ${pointIndex}:`, e);
                                    }
                                    
                                    // Fallback: Use chart scales to calculate coordinates
                                    if ((canvasX === null || isNaN(canvasX)) && chart.scales) {
                                        try {
                                            // Find x and y scales
                                            const xScale = chart.scales.x || chart.scales['x-axis-0'] || chart.scales.xAxes?.[0] ||
                                                        Object.values(chart.scales).find(s => s.axis === 'x' || s.type === 'category' || s.type === 'time');
                                            const yScale = chart.scales.y || chart.scales['y-axis-0'] || chart.scales.yAxes?.[0] ||
                                                        Object.values(chart.scales).find(s => s.axis === 'y' || s.position === 'left');
                                            
                                            if (xScale && yScale && xScale.getPixelForValue && yScale.getPixelForValue) {
                                                // Get the actual y value
                                                let yValue = value;
                                                if (typeof value === 'object' && value !== null) {
                                                    yValue = value.y || value.value || value.data;
                                                }
                                                if (typeof yValue === 'string') {
                                                    yValue = parseFloat(yValue);
                                                }
                                                
                                                if (!isNaN(yValue)) {
                                                    canvasX = xScale.getPixelForValue(pointIndex);
                                                    canvasY = yScale.getPixelForValue(yValue);
                                                    
                                                    if (!isNaN(canvasX) && !isNaN(canvasY)) {
                                                        screenX = canvasRect.left + canvasX;
                                                        screenY = canvasRect.top + canvasY;
                                                        console.log(`Scale-based coordinates: canvas(${canvasX}, ${canvasY}) -> screen(${screenX}, ${screenY})`);
                                                    }
                                                }
                                            }
                                        } catch (e) {
                                            console.warn(`Error in scale-based calculation:`, e);
                                        }
                                    }
                                    
                                    // Final validation of coordinates
                                    const coordsValid = screenX !== null && screenY !== null && 
                                                    !isNaN(screenX) && !isNaN(screenY) &&
                                                    isFinite(screenX) && isFinite(screenY);
                                    
                                    // Handle different value formats
                                    let displayValue = value;
                                    if (typeof value === 'object' && value !== null) {
                                        displayValue = value.y || value.value || value.data || JSON.stringify(value);
                                    }
                                    
                                    const pointData = {
                                        canvasIndex: chartIndex,
                                        datasetIndex: datasetIndex,
                                        pointIndex: pointIndex,
                                        value: displayValue,
                                        xLabel: xLabel,
                                        screenX: screenX,
                                        screenY: screenY,
                                        canvasX: canvasX,
                                        canvasY: canvasY,
                                        datasetLabel: dataset.label || `Dataset ${datasetIndex}`,
                                        chartType: chart.config ? chart.config.type : 'unknown',
                                        coordinatesValid: coordsValid,
                                        targetMonthYear: targetMonthYear
                                    };
                                    
                                    matchingPoints.push(pointData);
                                    console.log(`Added matching point:`, pointData);
                                    
                                } catch (e) {
                                    console.warn(`Error processing matching point ${pointIndex}:`, e);
                                }
                            }
                        }
                        
                    } catch (e) {
                        console.error('Error in chart point extraction:', e);
                        return [];
                    }
                    
                    console.log(`Found ${matchingPoints.length} matching points for chart ${chartIndex} and target ${targetMonthYear}`);
                    return matchingPoints;
                }
            """, {'chartIndex': chart_index, 'targetMonthYear': target_month_year})
            
            log_info(f" Found {len(matching_points)} matching points in chart {chart_index} for {target_month_year}")
            log_info(f"Matching points: {matching_points}")
            return matching_points            
        except Exception as e:
            log_error(f"Error finding matching points in chart {chart_index}: {str(e)}")
            return []      
    
    async def create_chart_point_combinations(self, target_months_years):
        """Create combinations of charts and their matching points"""
        log_info("Creating chart-point combinations...")
        
        # Use the dedicated parallel function to check and re-login the session.
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []

        # Get a NEW PAGE from the existing session.
        page = await self.auth_manager.new_page()
        try:
            # Navigate to SpecialMetrics
            await page.goto(f"{config.site_url.rstrip('/')}/SpecialMetrics", timeout=60000)
            await asyncio.sleep(5)

            # Discover all charts 
            charts_info = await self.discover_charts()
            
            if not charts_info:
                log_warn(" No charts found======================")
                # Delete auth_state.json file when no charts are found
                auth_state_path = "auth_state.json"
                try:
                    if os.path.exists(auth_state_path):
                        os.remove(auth_state_path)
                        log_warn(f"Deleted {auth_state_path} due to no charts found")
                    else:
                        log_info(f"{auth_state_path} not found to delete")
                except Exception as e:
                    log_error(f"Error deleting {auth_state_path}: {e}")
                return []

            chart_point_combinations = []
            charts_with_points = []

            # Process each chart and its points
            for chart_info in charts_info:
                chart_index = chart_info['canvasIndex']
                container_id = chart_info.get('containerId', '')
                chart_id = container_id.split('-')[-1] if container_id else None
                chart_title = chart_info.get('chartTitle', f'Chart {chart_id}')
                
                log_info(f"Processing Chart {chart_id}: {chart_title}")
                chart_total_points = 0
                chart_combinations = []

                # Process each target month-year
                for target_month_year in target_months_years:
                    log_info(f"Looking for data points matching: {target_month_year}")
                    matching_points = await self.find_matching_points_in_chart(
                        page, chart_index, target_month_year, chart_id)
                    
                    log_info(f"Found {len(matching_points) if matching_points else 0} matching points for {target_month_year}")
                    
                    if matching_points:
                        combination = {
                            'chart_index': f"chart_{chart_index}",
                            'chart_id': chart_id,
                            'chart_info': chart_info,
                            'target_month_year': target_month_year,
                            'matching_points': matching_points,
                            'processing_status': 'pending',
                            'points_count': len(matching_points)
                        }
                        chart_combinations.append(combination)
                        chart_total_points += len(matching_points)
                        log_info(f"   Added combination: Chart {chart_index} - {target_month_year} ({len(matching_points)} points)")
                    else:
                        log_info(f"   No matching points found for Chart {chart_index} - {target_month_year}")

                # Track charts with their point counts
                if chart_combinations:
                    charts_with_points.append({
                        'chart_index': chart_index,
                        'chart_id': chart_id,
                        'chart_title': chart_title,
                        'total_points': chart_total_points,
                        'combinations': chart_combinations
                    })

            # Sort and process final combinations
            charts_with_points.sort(key=lambda x: x['total_points'], reverse=True)
            
            for chart_data in charts_with_points:
                chart_point_combinations.extend(chart_data['combinations'])
                log_info(f"  Added Chart {chart_data['chart_index']}: {chart_data['chart_title']} ({chart_data['total_points']} points)")

            # Create summary
            log_info(f"\nSummary: Created {len(chart_point_combinations)} chart-point combinations from {len(charts_with_points)} charts")
            
            chart_summary = {}
            for combo in chart_point_combinations:
                chart_id = combo['chart_id']
                if chart_id not in chart_summary:
                    chart_summary[chart_id] = 0
                chart_summary[chart_id] += 1

            for chart_id, count in chart_summary.items():
                log_info(f"  {chart_id}: {count} combinations")

            return chart_point_combinations

        except Exception as e:
            log_error(f" Error creating chart-point combinations: {str(e)}")
            return []

        finally:
            log_info("Finished creating chart-point combinations")
            await page.close()                
   
    async def apply_enhanced_legend_control(self, page):
        """Apply the enhanced legend control script to the page with robust chart detection"""
        try:
            # Wait for charts to be loaded with multiple attempts
            log_info("Waiting for charts to load...")

            # Try multiple selectors to find charts
            chart_found = False
            selectors_to_try = [
                'canvas.chartjs-render-monitor',
                'canvas[class*="chartjs"]',
                'canvas',
                '[id*="chart"]'
            ]
            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    chart_found = True
                    log_info(f" Found charts using selector: {selector}")
                    break
                except Exception:
                    continue

            if not chart_found:
                log_warn("No chart elements found with any selector")
                return False
            await asyncio.sleep(3)  # Give charts time to fully initialize
            # Execute the comprehensive chart detection script
            result = await page.evaluate("""
                (function() {
                    console.log('=== COMPREHENSIVE CHART DETECTION ===');

                    // Check Chart.js availability
                    const chartJsAvailable = typeof Chart !== 'undefined';
                    console.log('Chart.js available:', chartJsAvailable);

                    if (!chartJsAvailable) {
                        // Try to find Chart.js in window object
                        const chartKeys = Object.keys(window).filter(key => key.toLowerCase().includes('chart'));
                        console.log('Potential chart objects:', chartKeys);
                        return false;
                    }

                    // Initialize chart instances map
                    window.legendControlInitialized = true;
                    window.chartInstances = new Map();

                    // Find all possible canvas elements
                    const allCanvases = document.querySelectorAll('canvas');
                    const chartCanvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                    const anyChartCanvas = document.querySelectorAll('canvas[class*="chart"]');

                    console.log(`Canvas elements found:`);
                    console.log(`  - Total canvas: ${allCanvases.length}`);
                    console.log(`  - chartjs-render-monitor: ${chartCanvases.length}`);
                    console.log(`  - chart-related: ${anyChartCanvas.length}`);

                    // Use the most specific selector that found canvases
                    let canvasesToProcess = chartCanvases.length > 0 ? chartCanvases :
                                          anyChartCanvas.length > 0 ? anyChartCanvas : allCanvases;

                    console.log(`Processing ${canvasesToProcess.length} canvas elements`);

                    let successfullyRegistered = 0;

                    canvasesToProcess.forEach((canvas, index) => {
                        console.log(`\\n--- Processing Canvas ${index} ---`);

                        let chartInstance = null;
                        let detectionMethod = 'none';

                        // Method 1: Chart.getChart (Chart.js v3+)
                        try {
                            if (Chart.getChart) {
                                chartInstance = Chart.getChart(canvas);
                                if (chartInstance) {
                                    detectionMethod = 'Chart.getChart';
                                    console.log(`✓ Found via Chart.getChart`);
                                }
                            }
                        } catch (e) {
                            console.log(`✗ Chart.getChart failed:`, e.message);
                        }

                        // Method 2: Canvas chart property
                        if (!chartInstance) {
                            try {
                                if (canvas.chart) {
                                    chartInstance = canvas.chart;
                                    detectionMethod = 'canvas.chart';
                                    console.log(`✓ Found via canvas.chart`);
                                }
                            } catch (e) {
                                console.log(`✗ canvas.chart failed:`, e.message);
                            }
                        }

                        // Method 3: Canvas _chart property
                        if (!chartInstance) {
                            try {
                                if (canvas._chart) {
                                    chartInstance = canvas._chart;
                                    detectionMethod = 'canvas._chart';
                                    console.log(`✓ Found via canvas._chart`);
                                }
                            } catch (e) {
                                console.log(`✗ canvas._chart failed:`, e.message);
                            }
                        }

                        // Method 4: Search Chart.instances
                        if (!chartInstance) {
                            try {
                                if (Chart.instances) {
                                    Object.values(Chart.instances).forEach(instance => {
                                        if (instance && instance.canvas === canvas) {
                                            chartInstance = instance;
                                            detectionMethod = 'Chart.instances';
                                            console.log(`✓ Found via Chart.instances`);
                                        }
                                    });
                                }
                            } catch (e) {
                                console.log(`✗ Chart.instances search failed:`, e.message);
                            }
                        }

                        // Method 5: Check canvas data attributes or properties
                        if (!chartInstance) {
                            try {
                                // Look for any property that might contain a chart instance
                                const props = Object.getOwnPropertyNames(canvas);
                                for (const prop of props) {
                                    if (prop.includes('chart') || prop.includes('Chart')) {
                                        const value = canvas[prop];
                                        if (value && typeof value === 'object' && value.data && value.data.datasets) {
                                            chartInstance = value;
                                            detectionMethod = `canvas.${prop}`;
                                            console.log(`✓ Found via canvas.${prop}`);
                                            break;
                                        }
                                    }
                                }
                            } catch (e) {
                                console.log(`✗ Property search failed:`, e.message);
                            }
                        }

                        // Validate chart instance
                        if (chartInstance) {
                            console.log(`Chart instance found via: ${detectionMethod}`);

                            if (chartInstance.data && chartInstance.data.datasets && chartInstance.data.datasets.length > 0) {
                                // Use canvas ID or create consistent ID mapping
                                let chartId = canvas.id || `canvas-${index}`;

                                // Also register with alternative IDs for compatibility
                                const alternativeIds = [`chart_${index}`, `chart-${index}`, `canvas_${index}`];

                                window.chartInstances.set(chartId, {
                                    instance: chartInstance,
                                    canvas: canvas,
                                    detectionMethod: detectionMethod,
                                    originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                                });

                                // Register with alternative IDs
                                alternativeIds.forEach(altId => {
                                    window.chartInstances.set(altId, {
                                        instance: chartInstance,
                                        canvas: canvas,
                                        detectionMethod: detectionMethod,
                                        originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                                    });
                                });

                                console.log(` Registered ${chartId} (and alternatives: ${alternativeIds.join(', ')}):`);
                                console.log(`   - Datasets: ${chartInstance.data.datasets.length}`);
                                console.log(`   - Detection: ${detectionMethod}`);

                                chartInstance.data.datasets.forEach((dataset, dsIndex) => {
                                    console.log(`   - Dataset ${dsIndex}: "${dataset.label || 'Unnamed'}"`);
                                });

                                successfullyRegistered++;
                            } else {
                                console.log(`✗ Chart instance invalid (no datasets)`);
                            }
                        } else {
                            console.log(`✗ No chart instance found for canvas ${index}`);
                        }
                    });

                    console.log(`\\n=== DETECTION COMPLETE ===`);
                    console.log(`Successfully registered: ${successfullyRegistered} charts`);
                    console.log(`Chart IDs: [${Array.from(window.chartInstances.keys()).join(', ')}]`);

                    return successfullyRegistered > 0;
                })()
            """)

            if result:
                log_info(" Enhanced legend control applied successfully with comprehensive detection")
                return True
            else:
                log_info(" No chart instances found even with comprehensive detection")
                return False

        except Exception as e:
            log_error(f" Failed to apply enhanced legend control: {str(e)}")
            return False
    async def disable_all_legends(self, page):
        """Disable legends for all charts"""
        try:
            await page.evaluate("""
                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        
                        // Disable legend
                        if (!chart.options.plugins) chart.options.plugins = {};
                        if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                        chart.options.plugins.legend.display = false;
                        
                        // Update chart
                        chart.update('none');
                        
                        console.log(`Legend disabled for ${chartId}`);
                    });
                }
            """)
            
            log_info(" All legends disabled")
            return True
            
        except Exception as e:
            log_error(f" Failed to disable legends: {str(e)}")
            return False
     
    async def enable_only_target_legend(self, page, chart_id, target_dataset_label):
        """Enable only the target dataset legend and disable all others"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to enable legend for chart: {chart_id}, dataset: {target_dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found, attempting to reinitialize...');
                    return false;
                }}

                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;
                let actualChartId = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        actualChartId = id;
                        console.log(`Found chart with ID: ${{id}}`);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found with any ID variation');
                    console.log('Available charts:', Array.from(window.chartInstances.keys()));
                    return false;
                }}

                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure for', actualChartId);
                    return false;
                }}

                // Find target dataset index with fuzzy matching
                let targetDatasetIndex = -1;
                chart.data.datasets.forEach((dataset, index) => {{
                    const datasetLabel = dataset.label || '';
                    const targetLabel = '{target_dataset_label}';

                    // Exact match first
                    if (datasetLabel === targetLabel) {{
                        targetDatasetIndex = index;
                        return;
                    }}

                    // Fuzzy match (contains or similar)
                    if (datasetLabel.includes(targetLabel) || targetLabel.includes(datasetLabel)) {{
                        targetDatasetIndex = index;
                        console.log(`Fuzzy match found: "${{datasetLabel}}" matches "${{targetLabel}}"`);
                    }}
                }});

                if (targetDatasetIndex === -1) {{
                    console.log('Target dataset not found: {target_dataset_label}');
                    console.log('Available datasets:', chart.data.datasets.map(d => d.label));

                    // Try to find any dataset and use the first one as fallback
                    if (chart.data.datasets.length > 0) {{
                        targetDatasetIndex = 0;
                        console.log('Using first dataset as fallback:', chart.data.datasets[0].label);
                    }} else {{
                        return false;
                    }}
                }}

                try {{
                    // Show only the target dataset
                    chart.data.datasets.forEach((dataset, index) => {{
                        const meta = chart.getDatasetMeta(index);
                        if (meta) {{
                            meta.hidden = (index !== targetDatasetIndex);
                        }}
                    }});

                    // Enable legend but filter to show only target dataset
                    if (!chart.options.plugins) chart.options.plugins = {{}};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {{}};

                    chart.options.plugins.legend.display = true;
                    chart.options.plugins.legend.labels = {{
                        filter: function(legendItem) {{
                            return legendItem.datasetIndex === targetDatasetIndex;
                        }}
                    }};

                    // Update chart
                    chart.update('none');

                    const actualDatasetLabel = chart.data.datasets[targetDatasetIndex].label;
                    console.log('Successfully enabled legend for:', actualDatasetLabel, '(index:', targetDatasetIndex, ')');
                    return true;
                }} catch (error) {{
                    console.error('Error updating chart:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                log_info(f" Enabled only legend for {target_dataset_label} in chart {chart_id}")
                return True
            else:
                log_error(f"Failed to enable legend for {target_dataset_label} in chart {chart_id}")
                return False

        except Exception as e:
            log_error(f" Error enabling legend for {target_dataset_label}: {str(e)}")
            return False

    async def debug_legend_control(self, page):
        """Debug function to check legend control setup"""
        try:
            debug_info = await page.evaluate("""
            (function() {
                const info = {
                    chartInstancesExists: !!window.chartInstances,
                    chartInstancesSize: window.chartInstances ? window.chartInstances.size : 0,
                    chartIds: window.chartInstances ? Array.from(window.chartInstances.keys()) : [],
                    chartDetails: []
                };

                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        info.chartDetails.push({
                            id: chartId,
                            hasInstance: !!chart,
                            hasData: !!(chart && chart.data),
                            datasetCount: chart && chart.data && chart.data.datasets ? chart.data.datasets.length : 0,
                            datasetLabels: chart && chart.data && chart.data.datasets ? chart.data.datasets.map(d => d.label) : []
                        });
                    });
                }

                return info;
            })()
            """)
            
            for chart_detail in debug_info.get('chartDetails', []):
                chart_id = chart_detail.get('id', 'Unknown')
                dataset_count = chart_detail.get('datasetCount', 0)
                dataset_labels = chart_detail.get('datasetLabels', [])                
            return debug_info

        except Exception as e:
            log_error(f" Error debugging legend control: {str(e)}")
            return None

    async def click_data_point_for_drilldown(self, page, chart_id, point_data):
        """Click on a specific data point to trigger drilldown navigation"""
        try:
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')

            # First try using the pre-calculated screen coordinates
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    log_info(f"Clicking at pre-calculated coordinates: ({screen_x}, {screen_y})")
                    await page.mouse.click(screen_x, screen_y)
                    await asyncio.sleep(1)
                    return {
                        'success': True,
                        'method': 'pre_calculated_coordinates',
                        'position': {'x': screen_x, 'y': screen_y},
                        'dataset_label': dataset_label,
                        'x_label': x_label
                    }
                except Exception as coord_error:
                    log_error(f"Pre-calculated coordinates failed: {coord_error}")

            # Fallback to JavaScript-based clicking
            click_result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to click data point for chart: {chart_id}, dataset: {dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found');
                    return {{ success: false, error: 'Chart instances not initialized' }};
                }}

                if (!window.chartInstances.has('{chart_id}')) {{
                    console.log('Chart {chart_id} not found');
                    console.log('Available charts:', Array.from(window.chartInstances.keys()));
                    return {{ success: false, error: 'Chart instance not found' }};
                }}

                const chartData = window.chartInstances.get('{chart_id}');
                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure');
                    return {{ success: false, error: 'Invalid chart data' }};
                }}

                try {{
                    // Find target dataset index by label
                    let targetDatasetIndex = {dataset_index};
                    chart.data.datasets.forEach((dataset, index) => {{
                        if (dataset.label === '{dataset_label}') {{
                            targetDatasetIndex = index;
                        }}
                    }});

                    // Get dataset meta
                    const meta = chart.getDatasetMeta(targetDatasetIndex);
                    if (!meta || !meta.data || !meta.data[{point_index}]) {{
                        console.log('Data point not found at index: {point_index}');
                        return {{ success: false, error: 'Data point not found' }};
                    }}

                    // Get point element and position
                    const pointElement = meta.data[{point_index}];
                    const pointPosition = pointElement.getCenterPoint();
                    const canvas = chart.canvas;

                    // Create click event
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {{
                        clientX: rect.left + pointPosition.x,
                        clientY: rect.top + pointPosition.y,
                        bubbles: true,
                        cancelable: true
                    }});

                    // Dispatch click event
                    canvas.dispatchEvent(clickEvent);

                    console.log('Successfully clicked data point: {x_label} from {dataset_label}');
                    return {{
                        success: true,
                        method: 'javascript_click',
                        position: pointPosition,
                        dataset_label: '{dataset_label}',
                        x_label: '{x_label}'
                    }};

                }} catch (error) {{
                    console.error('Error clicking data point:', error);
                    return {{ success: false, error: error.message }};
                }}
            }})()
            """)

            if click_result.get('success', False):
                log_info(f" Data point clicked for drilldown: {x_label} from {dataset_label}")
                return click_result
            else:
                log_error(f"Failed to click data point: {click_result.get('error', 'Unknown error')}")
                return click_result

        except Exception as e:
            log_error(f" Error clicking data point for drilldown: {str(e)}")
            return {'success': False, 'error': str(e)}

 
    async def process_single_point_task_with_selective_legend(self, page, task, target_month_year):
        """Process a single point task with selective legend control and drilldown"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        try:
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')         
            # Step 1: Enable only the target legend, disable all others
            legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
            if not legend_enabled:
                log_error(f"{task_id}: Legend control failed, continuing anyway")
            # Wait for legend update
            await asyncio.sleep(1)
            # Debug: Check if the chart area is visible and clickable
            await self.debug_chart_clickability(page, chart_id, point_data)
            # Step 2: Click data point and wait for drilldown navigation
            click_result = None
            navigation_result = None
            # Get current URL before clicking
            initial_url = page.url
            log_info(f"{task_id}: Current URL before click: {initial_url}")
            # Method 1: Use pre-calculated coordinates (most reliable)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    await page.mouse.click(screen_x, screen_y)                   
                    await asyncio.sleep(3)  # Give time for navigation to start
                    # Check if URL changed
                    current_url = page.url
                    if current_url != initial_url:
                        log_info(f" {task_id}: Navigation detected to: {current_url}")
                        # Wait for page to fully load
                        try:
                            # await page.wait_for_load_state("networkidle", timeout=10000)
                            await asyncio.sleep(2)
                        except Exception:
                            pass  # Continue even if load state fails

                        click_result = {
                            'success': True,
                            'method': 'pre_calculated_coordinates',
                            'coordinates': {'x': screen_x, 'y': screen_y}
                        }

                        navigation_result = {
                            'success': True,
                            'url': current_url,
                            'navigation_completed': True,
                            'initial_url': initial_url
                        }

                    else:
                        log_info(f"{task_id}: No navigation detected after coordinate click")
                        # Wait a bit more and check again
                        await asyncio.sleep(2)
                        current_url = page.url

                        if current_url != initial_url:
                            log_info(f" {task_id}: Delayed navigation detected to: {current_url}")
                            click_result = {'success': True, 'method': 'pre_calculated_coordinates'}
                            navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                        else:
                            log_info(f" {task_id}: No navigation after coordinate click")
                            click_result = {'success': False, 'error': 'No navigation detected after coordinate click'}
                            navigation_result = {'success': False, 'error': 'No URL change detected'}

                except Exception as coord_error:
                    log_error(f"{task_id}: Coordinate clicking failed: {coord_error}")
                    click_result = {'success': False, 'error': str(coord_error)}

            # Method 2: Fallback to JavaScript-based clicking
            if not click_result or not click_result.get('success', False):
                log_info(f"{task_id}: Trying JavaScript-based clicking...")

                try:
                    # Try JavaScript clicking
                    js_click_result = await self.click_data_point_for_drilldown(page, chart_id, point_data)

                    if js_click_result.get('success', False):
                        log_info(f"{task_id}: JavaScript click executed, waiting for navigation...")

                        # Wait for potential navigation
                        await asyncio.sleep(3)
                        # Check if URL changed
                        current_url = page.url
                        if current_url != initial_url:                           
                            # Wait for page to fully load
                            try:
                                # await page.wait_for_load_state("networkidle", timeout=10000)
                                await asyncio.sleep(2)
                            except Exception:
                                pass
                            click_result = js_click_result
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                        else:                           
                            # Wait a bit more and check again
                            await asyncio.sleep(2)
                            current_url = page.url
                            if current_url != initial_url:
                                log_info(f" {task_id}: Delayed JavaScript navigation detected to: {current_url}")
                                click_result = js_click_result
                                navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                            else:
                                click_result = {'success': False, 'error': 'JavaScript click no navigation'}
                                navigation_result = {'success': False, 'error': 'No URL change after JavaScript click'}
                    else:
                        click_result = js_click_result
                        navigation_result = {'success': False, 'error': 'JavaScript click failed'}

                except Exception as js_error:
                    log_error(f" {task_id}: JavaScript clicking failed: {js_error}")
                    click_result = {'success': False, 'error': str(js_error)}
                    navigation_result = {'success': False, 'error': str(js_error)}

            # Method 3: Fallback to simple click with longer wait
            if not click_result or not click_result.get('success', False):
                log_info(f"{task_id}: Trying simple click with extended wait...")
                try:
                    # Simple click without complex navigation detection
                    await page.mouse.click(screen_x, screen_y)
                    log_info(f"{task_id}: Simple click executed, waiting longer for navigation...")
                    # Wait longer for navigation
                    await asyncio.sleep(5)
                    # Check URL multiple times
                    for attempt in range(3):
                        current_url = page.url
                        if current_url != initial_url:
                            click_result = {
                                'success': True,
                                'method': 'simple_click_extended_wait',
                                'coordinates': {'x': screen_x, 'y': screen_y}
                            }
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                            break

                        if attempt < 2:  # Don't wait after last attempt
                            await asyncio.sleep(2)

                    if not click_result or not click_result.get('success', False):
                        log_info(f" {task_id}: Simple click also failed to trigger navigation")
                        click_result = {'success': False, 'error': 'Simple click no navigation'}
                        navigation_result = {'success': False, 'error': 'No URL change after simple click'}
                except Exception as simple_error:
                    log_error(f" {task_id}: Simple clicking failed: {simple_error}")
                    click_result = {'success': False, 'error': str(simple_error)}
                    navigation_result = {'success': False, 'error': str(simple_error)}

            # Check if any clicking method succeeded
            if not click_result or not click_result.get('success', False):
                error_msg = click_result.get('error', 'All clicking methods failed') if click_result else 'No click result'
                log_error(f" {task_id}: Failed to click data point: {error_msg}")
                return {
                    'task_id': task_id,
                    'chart_id': chart_id,
                    'error': f"Failed to click data point: {error_msg}",
                    'success': False,
                    'legend_controlled': legend_enabled
                }
            # Check navigation result
            if not navigation_result or not navigation_result.get('success', False):
                log_info(f"{task_id}: Navigation failed, but click was successful - attempting data extraction anyway")
                # Try to extract data from current page
                current_url = page.url
                navigation_result = {
                    'success': False,
                    'url': current_url,
                    'navigation_completed': False,
                    'error': 'Navigation failed but click succeeded'
                }

            # Step 3: Extract data from the drilldown page
            extracted_data = None
            extraction_success = False
            # Only attempt extraction if navigation was successful
            if navigation_result and navigation_result.get('success', False):
                current_url = page.url
                # Check if we're on the expected drilldown page
                if "AnalyzeData" in current_url and "chartId=drillDown" in current_url:
                    log_info(f" {task_id}: On correct drilldown page, extracting data...")
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year, chart_id, dataset_label)
                    # Check extraction success from the nested structure
                    extraction_success = False
                    if extracted_data and extracted_data.get('extraction_data'):
                        extraction_success = extracted_data['extraction_data'].get('success', False)
                    if extraction_success:
                        log_info(f" {task_id}: Data extraction successful")
                    else:
                        log_error(f"{task_id}: Data extraction failed or incomplete")
                else:
                    log_info(f"{task_id}: Not on expected drilldown page. URL: {current_url}")
                    extracted_data = {
                        'extraction_data': {
                            'success': False,
                            'error': f'Unexpected page URL: {current_url}',
                            'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
                        }
                    }
                    extraction_success = False
            else:
                log_error(f" {task_id}: Skipping data extraction due to navigation failure")
                extracted_data = {
                    'extraction_data': {
                        'success': False,
                        'error': 'Navigation failed, cannot extract data',
                        'navigation_error': navigation_result.get('error', 'Unknown navigation error') if navigation_result else 'No navigation result'
                    }
                }
                extraction_success = False
            # Determine overall success
            overall_success = (
                click_result.get('success', False) and
                navigation_result.get('success', False) and
                extraction_success
            )
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'click_result': click_result,
                'navigation_result': navigation_result,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': overall_success,
                'legend_controlled': legend_enabled,
                'drilldown_url': navigation_result.get('url', '') if navigation_result else '',
                'click_success': click_result.get('success', False) if click_result else False,
                'navigation_success': navigation_result.get('success', False) if navigation_result else False,
                'extraction_success': extraction_success
            }
            log_info(f" {task_id}: Successfully processed {point_label} from {dataset_label}")
            return result
        except Exception as e:
            log_info(f" {task_id}: Error processing point: {e}")
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': False
            }
            return error_result

    async def extract_ag_grid_data_for_938(self, page, dataset_label):
        """Extract data from AG-Grid table specifically for chart 938 (Return Rate)"""
        try:
            log_info(f"🔍 Extracting AG-Grid data for chart 938, dataset: {dataset_label}")
            log_info(f"🔍 Current page URL: {page.url}")

            # Wait for AG-Grid to load
            try:
                await page.wait_for_selector('#returnRateDrilldown', timeout=10000)
                log_info("✅ AG-Grid element found, waiting for data to load...")
                await asyncio.sleep(2)
            except Exception as wait_error:
                log_info(f"⚠️ AG-Grid element not found within timeout: {wait_error}")
                # Try alternative selectors
                alternative_selectors = ['.ag-theme-balham', '.ag-root', '[id*="returnRate"]', '[id*="drilldown"]']
                for selector in alternative_selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=5000)
                        log_info(f"✅ Found alternative AG-Grid selector: {selector}")
                        break
                    except:
                        continue
                else:
                    log_info("❌ No AG-Grid elements found with any selector")
                    return []

            ag_grid_data = []

            # Find the AG-Grid table
            ag_grid_element = await page.query_selector('#returnRateDrilldown')
            if not ag_grid_element:
                log_info("⚠️ AG-Grid element #returnRateDrilldown not found, trying alternatives...")
                # Try alternative selectors
                alternative_selectors = ['.ag-theme-balham', '.ag-root']
                for selector in alternative_selectors:
                    ag_grid_element = await page.query_selector(selector)
                    if ag_grid_element:
                        log_info(f"✅ Found AG-Grid using selector: {selector}")
                        break

                if not ag_grid_element:
                    log_info("❌ No AG-Grid element found with any selector")
                    return []

            # Get all data rows from the AG-Grid
            data_rows = await ag_grid_element.query_selector_all('.ag-row')
            log_info(f"Found {len(data_rows)} data rows in AG-Grid")

            for row_index, row in enumerate(data_rows):
                try:
                    # Extract cell values from the row
                    cells = await row.query_selector_all('.ag-cell')
                    if len(cells) >= 8:  # Ensure we have all expected columns
                        # Extract the return rate values based on dataset_label
                        six_month_cell = cells[6]  # Column index 6 for 6 Month Return Rate
                        twelve_month_cell = cells[7]  # Column index 7 for 12 Month Return Rate

                        six_month_value = await six_month_cell.text_content()
                        twelve_month_value = await twelve_month_cell.text_content()

                        # Clean the values
                        six_month_clean = six_month_value.strip() if six_month_value else ""
                        twelve_month_clean = twelve_month_value.strip() if twelve_month_value else ""

                        # Create data structure based on which dataset was clicked
                        if "6 Month" in dataset_label or "6 Months" in dataset_label:
                            # User clicked on 6 Month Return Rate
                            container_data = {
                                "container_index": row_index,
                                "selector_used": "ag-grid-6-month",
                                "items": [{
                                    "item_index": 0,
                                    "title": "6 Months Return Rate",
                                    "value": six_month_clean,
                                    "html_structure": {
                                        "source": "ag-grid-cell",
                                        "column": "sixMonthReturnrate"
                                    }
                                }]
                            }
                        elif "12 Month" in dataset_label or "12 Months" in dataset_label:
                            # User clicked on 12 Month Return Rate
                            container_data = {
                                "container_index": row_index,
                                "selector_used": "ag-grid-12-month",
                                "items": [{
                                    "item_index": 0,
                                    "title": "12 Months Return Rate",
                                    "value": twelve_month_clean,
                                    "html_structure": {
                                        "source": "ag-grid-cell",
                                        "column": "twelveMonthReturnrate"
                                    }
                                }]
                            }
                        else:
                            # Default: extract both values
                            container_data = {
                                "container_index": row_index,
                                "selector_used": "ag-grid-both",
                                "items": [
                                    {
                                        "item_index": 0,
                                        "title": "6 Months Return Rate",
                                        "value": six_month_clean,
                                        "html_structure": {
                                            "source": "ag-grid-cell",
                                            "column": "sixMonthReturnrate"
                                        }
                                    },
                                    {
                                        "item_index": 1,
                                        "title": "12 Months Return Rate",
                                        "value": twelve_month_clean,
                                        "html_structure": {
                                            "source": "ag-grid-cell",
                                            "column": "twelveMonthReturnrate"
                                        }
                                    }
                                ]
                            }

                        ag_grid_data.append(container_data)
                        log_info(f"✅ Extracted row {row_index}: 6M={six_month_clean}, 12M={twelve_month_clean}")

                except Exception as row_error:
                    log_error(f"Error extracting row {row_index}: {row_error}")
                    continue

            log_info(f"✅ Successfully extracted {len(ag_grid_data)} containers from AG-Grid")
            return ag_grid_data

        except Exception as e:
            log_error(f"❌ Error extracting AG-Grid data for chart 938: {e}")
            return []

    async def extract_data_from_drilldown_page(self, page, point_data, target_month_year, chart_id=None, dataset_label=None):
        """Extract MUI Grid data from drilldown page with enhanced chart handling"""
        log_info(f"🔍 Starting data extraction - Chart ID: {chart_id}, Dataset: {dataset_label}")
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                log_info(f"Extracting drill-down page data... (Attempt {attempt + 1}/{max_retries})")

                # Add longer wait for page load
                await asyncio.sleep(3)
                
                # Get chart ID and info
                chart_id = point_data.get('chart_id', '')
                dataset_label = point_data.get('datasetLabel', 'Unknown')
                
                extraction_data = {
                    "extraction_timestamp": datetime.now().isoformat(),
                    "page_url": page.url,
                    "mui_grid_data": [],
                    "all_text_content": [],
                    "raw_html_sections": [],
                    "monetary_data": [],
                    "success": False,
                    "error": None,
                    "attempt": attempt + 1,
                    "chart_id": chart_id,
                    "dataset_label": dataset_label
                }
                log_info(f"Chart ID-----------------1-->: {chart_id}, Dataset Label: {dataset_label} FROM DRILL DWON")
                # Special handling for problematic charts
                if chart_id in ['1357', '1354', '938', '1239', '1316']:
                    # Wait for specific elements based on chart
                    selectors = [
                        '.MuiGrid-container',
                        '[class*="MuiGrid-container"]',
                        'h5.MuiTypography-h5',
                        'h6.MuiTypography-subtitle1'
                    ]
                    
                    for selector in selectors:
                        try:
                            await page.wait_for_selector(selector, timeout=5000)
                        except:
                            continue

                # Method 1: Look for MUI Grid containers with specific structure
                mui_grid_selectors = [
                    '.MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3',
                    '.MuiGrid-root.MuiGrid-container',
                    '[class*="MuiGrid-container"]'
                ]

                for selector in mui_grid_selectors:
                    try:
                        grid_containers = await page.query_selector_all(selector)
                        log_info(f"Found {len(grid_containers)} grid containers with selector: {selector}")
                        
                        for container_index, container in enumerate(grid_containers):
                            if await container.is_visible():
                                grid_items = await container.query_selector_all('.MuiGrid-item')
                                
                                container_data = {
                                    "container_index": container_index,
                                    "selector_used": selector,
                                    "items": []
                                }

                                for item_index, item in enumerate(grid_items):
                                    h5_element = await item.query_selector('h5.MuiTypography-root.MuiTypography-h5')
                                    h6_element = await item.query_selector('h6.MuiTypography-root.MuiTypography-subtitle1')
                                    
                                    if h5_element and h6_element:
                                        title = (await h5_element.text_content()).strip()
                                        value = (await h6_element.text_content()).strip()
                                        
                                        item_data = {
                                            "item_index": item_index,
                                            "title": title,
                                            "value": value,
                                            "html_structure": {
                                                "h5_html": await h5_element.inner_html(),
                                                "h6_html": await h6_element.inner_html()
                                            }
                                        }
                                        container_data["items"].append(item_data)

                                if container_data["items"]:
                                    extraction_data["mui_grid_data"].append(container_data)

                    except Exception as selector_error:
                        log_error(f"Error with selector {selector}: {selector_error}")
                        continue
                log_info(f"Chart ID-----------------4-->: {chart_id}")
                # Method 2: Direct element extraction for specific charts
                if chart_id in ['1357', '1354', '938', '1239', '1316', 'chart_938']:
                    try:
                        # Special handling for chart 938 - AG-Grid table extraction
                        if chart_id == '938' or chart_id == 'chart_938':
                            log_info(f"🎯 Chart 938 detected! Attempting AG-Grid extraction with dataset: {dataset_label}")
                            ag_grid_data = await self.extract_ag_grid_data_for_938(page, dataset_label or '')
                            if ag_grid_data:
                                extraction_data["mui_grid_data"].extend(ag_grid_data)
                                log_info(f"✅ Extracted AG-Grid data for chart 938: {len(ag_grid_data)} containers")
                            else:
                                log_info("⚠️ No AG-Grid data extracted for chart 938")

                        # Get all h5 and h6 elements for other charts
                        h5_elements = await page.query_selector_all('h5.MuiTypography-h5')
                        h6_elements = await page.query_selector_all('h6.MuiTypography-subtitle1')

                        direct_data = {
                            "container_index": "direct",
                            "selector_used": "direct_elements",
                            "items": []
                        }

                        for i, (h5, h6) in enumerate(zip(h5_elements, h6_elements)):
                            if h5 and h6:
                                title = (await h5.text_content()).strip()
                                value = (await h6.text_content()).strip()
                                
                                item_data = {
                                    "item_index": i,
                                    "title": title,
                                    "value": value,
                                    "html_structure": {
                                        "h5_html": await h5.inner_html(),
                                        "h6_html": await h6.inner_html()
                                    }
                                }
                                direct_data["items"].append(item_data)
                        
                        if direct_data["items"]:
                            extraction_data["mui_grid_data"].append(direct_data)

                    except Exception as direct_error:
                        log_error(f"Error in direct element extraction: {direct_error}")

                # Determine success and add metadata
                extraction_data["success"] = len(extraction_data["mui_grid_data"]) > 0
                extraction_data["total_items_found"] = sum(len(container["items"]) for container in extraction_data["mui_grid_data"])
                
                log_info(f"Extraction success: {extraction_data['success']}")
                log_info(f"Total items found: {extraction_data['total_items_found']}")

                # If successful, return result immediately
                if extraction_data["success"]:
                    log_info(f"Data extraction successful on attempt {attempt + 1}")
                    result = {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': extraction_data,
                        'error': None
                    }
                    return result

                # If not successful and not the last attempt, wait and retry
                if attempt < max_retries - 1:
                    log_error(f"Attempt {attempt + 1} failed, retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                else:
                    log_error(f"All {max_retries} attempts failed")
                    result = {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': extraction_data,
                        'error': f"Data extraction failed after {max_retries} attempts"
                    }
                    return result

            except Exception as e:
                log_error(f"Error in attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    return {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': {
                            "extraction_timestamp": datetime.now().isoformat(),
                            "success": False,
                            "error": str(e),
                            "attempt": max_retries
                        },
                        'error': str(e)
                    }

        return {
            'target_month_year': target_month_year,
            'point_data': point_data,
            'click_success': True,
            'navigation_success': True,
            'extraction_data': {
                "extraction_timestamp": datetime.now().isoformat(),
                "success": False,
                "error": "Maximum retries exceeded",
                "attempt": max_retries
            },
            'error': "Maximum retries exceeded"
        }
   
    async def debug_and_setup_charts(self, page):
        """Debug and manually setup chart instances"""
        try:
            result = await page.evaluate("""
                (function() {
                    console.log('=== CHART DEBUG AND SETUP ===');

                    // Check if Chart.js is available
                    console.log('Chart.js available:', typeof Chart !== 'undefined');

                    // Find all canvas elements
                    const allCanvases = document.querySelectorAll('canvas');
                    console.log(`Total canvas elements found: ${allCanvases.length}`);

                    const chartCanvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                    console.log(`Chart.js canvas elements found: ${chartCanvases.length}`);

                    // Initialize chart instances map
                    if (!window.chartInstances) {
                        window.chartInstances = new Map();
                    }

                    let foundCharts = 0;

                    // Try multiple methods to find charts
                    chartCanvases.forEach((canvas, index) => {
                        let chartInstance = null;

                        console.log(`Processing canvas ${index}:`);

                        // Method 1: Chart.getChart
                        if (typeof Chart !== 'undefined' && Chart.getChart) {
                            try {
                                chartInstance = Chart.getChart(canvas);
                                if (chartInstance) {
                                    console.log(`  - Found via Chart.getChart`);
                                }
                            } catch (e) {
                                console.log(`  - Chart.getChart failed:`, e.message);
                            }
                        }

                        // Method 2: Canvas chart property
                        if (!chartInstance && canvas.chart) {
                            chartInstance = canvas.chart;
                            console.log(`  - Found via canvas.chart`);
                        }

                        // Method 3: Canvas _chart property
                        if (!chartInstance && canvas._chart) {
                            chartInstance = canvas._chart;
                            console.log(`  - Found via canvas._chart`);
                        }

                        // Method 4: Search Chart.instances
                        if (!chartInstance && typeof Chart !== 'undefined' && Chart.instances) {
                            Object.values(Chart.instances).forEach(instance => {
                                if (instance.canvas === canvas) {
                                    chartInstance = instance;
                                    console.log(`  - Found via Chart.instances`);
                                }
                            });
                        }

                        if (chartInstance && chartInstance.data && chartInstance.data.datasets) {
                            const chartId = `chart_${index}`;
                            window.chartInstances.set(chartId, {
                                instance: chartInstance,
                                canvas: canvas,
                                originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                            });

                            console.log(`  - Registered as ${chartId} with ${chartInstance.data.datasets.length} datasets`);
                            chartInstance.data.datasets.forEach((dataset, dsIndex) => {
                                console.log(`    Dataset ${dsIndex}: ${dataset.label || 'Unnamed'}`);
                            });

                            foundCharts++;
                        } else {
                            console.log(`  - No valid chart instance found`);
                        }
                    });

                    console.log(`=== SETUP COMPLETE: ${foundCharts} charts registered ===`);
                    return foundCharts;
                })()
            """)

            log_info(f"Debug setup completed: {result} charts found and registered")
            return result > 0

        except Exception as e:
            log_error(f" Debug setup failed: {str(e)}")
            return False
   
    async def click_chart_data_point(self, page, chart_id, point_data):
        """Click on a specific chart data point directly"""
        try:
            # Extract point information
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)            
            # Click on the specific data point
            point_clicked = await page.evaluate(f"""
            (function() {{
                if (window.chartInstances && window.chartInstances.has('{chart_id}')) {{
                    const chartData = window.chartInstances.get('{chart_id}');
                    const chart = chartData.instance;
                    
                    try {{
                        // Find the dataset by label if datasetIndex is not reliable
                        let targetDatasetIndex = {dataset_index};
                        
                        // Verify dataset index by label
                        chart.data.datasets.forEach((dataset, index) => {{
                            if (dataset.label === '{dataset_label}') {{
                                targetDatasetIndex = index;
                            }}
                        }});
                        
                        // Get the dataset meta
                        const meta = chart.getDatasetMeta(targetDatasetIndex);
                        if (!meta || !meta.data || !meta.data[{point_index}]) {{
                            console.log('Data point not found at index: ' + {point_index});
                            return false;
                        }}
                        
                        // Get the data point element
                        const pointElement = meta.data[{point_index}];
                        if (!pointElement) {{
                            console.log('Point element not found');
                            return false;
                        }}                        
                        // Get the chart canvas
                        const canvas = chart.canvas;
                        if (!canvas) {{
                            console.log('Canvas not found');
                            return false;
                        }}                        
                        // Get point position
                        const pointPosition = pointElement.getCenterPoint();                        
                        // Create and dispatch click event
                        const rect = canvas.getBoundingClientRect();
                        const clickEvent = new MouseEvent('click', {{
                            clientX: rect.left + pointPosition.x,
                            clientY: rect.top + pointPosition.y,
                            bubbles: true,
                            cancelable: true
                        }});                        
                        // Dispatch the click event
                        canvas.dispatchEvent(clickEvent);                        
                        console.log('Clicked data point: ' + '{x_label}' + ' from dataset: ' + '{dataset_label}');
                        console.log('Point position:', pointPosition);                        
                        return true;                        
                    }} catch (error) {{
                        console.error('Error clicking data point:', error);
                        return false;
                    }}
                }}
                console.log('Chart instance not found: {chart_id}');
                return false;
            }})()
            """)
            
            if point_clicked:
                log_info(f" Data point clicked for {chart_id} - {x_label} from {dataset_label}")
                return True
            else:
                log_error(f"Could not click data point for {chart_id} - {x_label} from {dataset_label}")
                return False               
        except Exception as e:
            log_error(f"Failed to click data point for {chart_id} - {x_label}: {str(e)}")
            return False
   
    async def process_all_combinations_parallel(self, combinations):
        """Process charts in parallel with multiple browsers, each handling different charts"""

        log_info(f"🚀 Starting parallel processing of {len(combinations)} charts with 3 browsers")       
        # Organize combinations by chart for easier processing
        chart_combinations = {}
        for combo_idx, combination in enumerate(combinations):
            chart_id = combination.get('chart_id', f'chart_{combo_idx}')
            target_month = combination.get('target_month_year', 'unknown')
            matching_points = combination.get('matching_points', [])

            chart_combinations[chart_id] = {
                'combination_index': combo_idx,
                'chart_info': combination['chart_info'],
                'target_month_year': target_month,
                'matching_points': matching_points,
                'current_point_index': 0
            }
            log_info(f"**Chart {chart_id}: {target_month} ({(len(matching_points))} points)")

        all_results = []
        target_month_year = TARGET_MONTHS_YEARS
        max_browsers = 3

        # Group charts for parallel processing (3 charts at a time)
        chart_items = list(chart_combinations.items())
        chart_batches = []

        # Create batches of 3 charts each
        for i in range(0, len(chart_items), max_browsers):
            batch = chart_items[i:i + max_browsers]
            chart_batches.append(batch)

        log_info(f"Processing {len(chart_batches)} batches with up to {max_browsers} browsers per batch")
        # Process each batch of charts in parallel
        for batch_index, chart_batch in enumerate(chart_batches, 1):
            log_info(f"\n🚀 Starting Batch {batch_index}/{len(chart_batches)} with {len(chart_batch)} charts")

            # Create parallel tasks for this batch
            batch_tasks = []
            for browser_index, (chart_id, chart_data) in enumerate(chart_batch):
                browser_id = f"Browser_{browser_index + 1}"
                log_info(f"   📋 {browser_id}: Will process Chart {chart_id} - {chart_data['chart_info'].get('chartTitle', 'Unknown')}")
               
                task = asyncio.create_task(
                    self.process_single_chart_parallel(chart_data, target_month_year, browser_id,chart_id)
                )
                batch_tasks.append((browser_id, chart_id, task))
            # Wait for all browsers in this batch to complete
            log_info(f"Waiting for all {len(batch_tasks)} browsers in batch {batch_index} to complete...")
            for browser_id, chart_id, task in batch_tasks:
                try:
                    result = await task
                    if isinstance(result, list):
                        all_results.extend(result)
                        log_info(f" {browser_id} completed Chart {chart_id}: {len(result)} results")
                    else:
                        log_info(f"{browser_id} returned unexpected result type for Chart {chart_id}")
                except Exception as e:
                    log_error(f" {browser_id} failed processing Chart {chart_id}: {str(e)}")
                    continue
            log_info(f" Batch {batch_index} completed")
            # Add delay between batches to avoid overwhelming the system
            if batch_index < len(chart_batches):
                log_info("Waiting before next batch...")
                await asyncio.sleep(3)

        # Process final results
        successful_results = []
        failed_results = []

        for result in all_results:
            if isinstance(result, dict) and result.get('success', False):
                successful_results.append(result)
            else:
                failed_results.append(result)

        log_info(f"\n🎉 Parallel processing with {max_browsers} browsers completed!")
        log_info("Summary:")
        log_info(f"   - Total charts processed: {len(chart_combinations)}")
        log_info(f"   - Total batches processed: {len(chart_batches)}")
        log_info(f"   - Total point tasks processed: {len(all_results)}")
        log_info(f"   - Successful: {len(successful_results)}")
        log_info(f"   - Failed: {len(failed_results)}")
        log_info(f"   - Success rate: {(len(successful_results) / len(all_results) * 100):.1f}%" if all_results else "0%")
        log_info(all_results,"all_results+++++++++++")
        return {
            'successful': successful_results,
            'failed': failed_results,
            'all_results': all_results,
            'total_processed': len(all_results),
            'total_charts': len(chart_combinations),
            'batches_processed': len(chart_batches),
            'success_rate': (len(successful_results) / len(all_results) * 100) if all_results else 0
        }

    async def process_single_chart_parallel(self, chart_data, target_month_year, browser_id, chart_id):
        """Process all points in a single chart in parallel (for use with multiple browsers)"""
        
        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
        matching_points = chart_data.get('matching_points', [])
        log_info(f"{browser_id}: Processing chart: {chart_title} :({chart_id}) with {len(matching_points)} points")
        chart_results = []

        # Use the dedicated parallel function to check and re-login the session.
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []

        # Get a NEW PAGE from the existing session.
        page = await self.auth_manager.new_page()
        
        try:
            # Navigate to SpecialMetrics
            log_info(f"{browser_id}: Navigating to SpecialMetrics for {chart_id}")
            await page.goto(f"{config.site_url.rstrip('/')}/SpecialMetrics", timeout=60000)
            await asyncio.sleep(10)

            # Apply enhanced legend control
            legend_setup_success = await self.apply_enhanced_legend_control(page)
            await asyncio.sleep(2)

            if not legend_setup_success:
                log_info(f"{browser_id}: Legend control setup failed for {chart_id}, attempting manual setup...")
                await self.debug_and_setup_charts(page)
            
            # Debug legend control setup
            await self.debug_legend_control(page)
            
            # Process each point in this chart sequentially within this browser
            for point_idx, point_data in enumerate(matching_points):
                point_label = point_data.get('xLabel', f'Point_{point_idx}')
                dataset_label = point_data.get('datasetLabel', 'Unknown Dataset') 
                
                try:
                    # Step 1: Disable ALL legends first                        
                    await self.disable_all_legends(page)
                    await asyncio.sleep(1)
                    
                    # Step 2: Enable ONLY the legend for current chart/dataset                        
                    legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
                    await asyncio.sleep(2)  # Give more time for chart to update
                    
                    if legend_enabled:
                        log_info(f" {browser_id}: Legend control successful - ONLY {chart_id} legend is active")
                    else:
                        log_error(f"{browser_id}: Legend control failed, but continuing with processing")
                    
                    # Step 2.5: Ensure chart is interactive and data points are clickable
                    log_info(f"{browser_id}: Ensuring chart {chart_id} is interactive after legend control...")
                    await self.ensure_chart_interactivity(page, chart_id)
                    await asyncio.sleep(1)
                    
                    # Step 3: Create task for this point
                    task = {
                        'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_info': chart_data['chart_info'],
                        'target_month_year': chart_data['target_month_year'],
                        'point_data': point_data,
                        'point_index': point_idx
                    }

                    # Step 4: Process this point with enhanced clicking
                    result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year)
                    
                    if isinstance(result, dict):
                        result['chart_title'] = chart_title
                        result['point_sequence'] = point_idx + 1
                        result['method'] = 'parallel_processing'
                        result['browser_id'] = browser_id
                        result['chart_id'] = chart_id

                    chart_results.append(result)

                    # Log detailed result
                    if result.get('success', False):
                        click_success = result.get('click_success', False)
                        nav_success = result.get('navigation_success', False)
                        extract_success = result.get('extraction_success', False)                           
                        
                        if nav_success:
                            drilldown_url = result.get('drilldown_url', 'Unknown')
                            log_info(f"   🔗 Drilldown URL: {drilldown_url}")
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        log_error(f" {browser_id}: Failed point {point_idx + 1}: {point_label} - {error_msg}")

                    # Step 5: Navigate back to SpecialMetrics for next point (if not last point)
                    if point_idx < len(matching_points) - 1:
                        log_info(f"{browser_id}: Navigating back to SpecialMetrics for next point")
                        try:
                            await page.goto(f"{config.site_url.rstrip('/')}/SpecialMetrics", timeout=60000)
                            await asyncio.sleep(2)

                            # Re-apply legend control
                            await self.apply_enhanced_legend_control(page)
                            await asyncio.sleep(1)
                            log_info(f" {browser_id}: Successfully navigated back to SpecialMetrics")
                        except Exception as nav_back_error:
                            log_error(f" {browser_id}: Failed to navigate back to SpecialMetrics: {nav_back_error}")
                            # Try to continue anyway
                            pass

                except Exception as e:
                    log_error(f" {browser_id}: Error processing point {point_idx + 1} - {point_label}: {str(e)}")
                    error_result = {
                        'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_title': chart_title,
                        'point_label': point_label,
                        'error': str(e),
                        'success': False,
                        'method': 'parallel_processing',
                        'browser_id': browser_id,
                        'point_sequence': point_idx + 1
                    }
                    chart_results.append(error_result)

            log_info(f" {browser_id}: Completed all points for chart: {chart_title}")            

        except Exception as e:
            log_error(f" {browser_id}: Error setting up chart {chart_id}: {str(e)}")
            error_result = {
                'chart_id': chart_id,
                'chart_title': chart_title,
                'error': f"Chart setup failed: {str(e)}",
                'success': False,
                'method': 'parallel_processing',
                'browser_id': browser_id
            }
            chart_results.append(error_result)

        finally:
            try:
                await page.close()
                log_info(f"🔒 {browser_id}: Browser closed for chart {chart_id}")
            except Exception as cleanup_error:
                log_error(f"{browser_id}: Cleanup error for {chart_id}: {cleanup_error}")

        return chart_results
    async def ensure_chart_interactivity(self, page, chart_id):
        """Ensure chart is interactive and data points are clickable after legend control"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Ensuring chart interactivity for: {chart_id}');

                if (!window.chartInstances) {{
                    console.log('No chart instances found');
                    return false;
                }}

                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;
                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found for interactivity check');
                    return false;
                }}

                const chart = chartData.instance;

                try {{
                    // Ensure chart is responsive to events
                    if (chart.options) {{
                        if (!chart.options.interaction) chart.options.interaction = {{}};
                        chart.options.interaction.intersect = false;
                        chart.options.interaction.mode = 'point';

                        if (!chart.options.onHover) {{
                            chart.options.onHover = function(event, elements) {{
                                console.log('Chart hover detected:', elements.length, 'elements');
                            }};
                        }}

                        if (!chart.options.onClick) {{
                            chart.options.onClick = function(event, elements) {{
                                console.log('Chart click detected:', elements.length, 'elements');
                                if (elements.length > 0) {{
                                    const element = elements[0];
                                    console.log('Clicked element:', element);
                                }}
                            }};
                        }}
                    }}

                    // Force chart update to apply interaction settings
                    chart.update('none');

                    console.log('Chart interactivity ensured for: {chart_id}');
                    return true;
                }} catch (error) {{
                    console.error('Error ensuring chart interactivity:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                log_info(f" Chart {chart_id} interactivity ensured")
            else:
                log_error(f"Failed to ensure chart {chart_id} interactivity")

            return result

        except Exception as e:
            log_error(f" Error ensuring chart interactivity: {str(e)}")
            return False

    async def process_single_point_task_with_enhanced_clicking(self, page, task, target_month_year):
        """Process a single point task with enhanced clicking methods for drilldown"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        log_info(f"Chart ID-----------------222>{chart_id}")
        try:
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            # Get current URL before clicking
            initial_url = page.url
            log_info(f"{task_id}: Current URL before click: {initial_url}")
            if chart_id == 'chart_1316':
                log_info(f" {task_id}: Waiting for chart to load...IFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF")
                await asyncio.sleep(20)
            # Method 1: Try Chart.js event-based clicking first
            click_result = await self.try_chartjs_event_click(page, chart_id, point_data)
            navigation_result = None

            if click_result.get('success', False):
                log_info(f" {task_id}: Chart.js event click successful, checking for navigation...")

                # Wait for navigation
                await asyncio.sleep(3)
                current_url = page.url

                if current_url != initial_url:
                    log_info(f" {task_id}: Navigation successful to: {current_url}")
                    navigation_result = {'success': True, 'url': current_url, 'method': 'chartjs_event'}
                else:
                    log_error(f"{task_id}: Chart.js event click didn't trigger navigation")
                    click_result = {'success': False, 'error': 'No navigation after Chart.js event click'}

            # Method 2: Fallback to coordinate clicking if Chart.js event didn't work
            if not click_result or not click_result.get('success', False):
                log_info(f"{task_id}: Trying coordinate-based clicking...")

                screen_x = point_data.get('screenX')
                screen_y = point_data.get('screenY')

                if screen_x and screen_y:
                    # Try multiple click methods
                    for click_method in ['single', 'double', 'with_delay']:
                        log_info(f"{task_id}: Trying {click_method} click at ({screen_x}, {screen_y})")

                        if click_method == 'single':
                            await page.mouse.click(screen_x, screen_y)
                        elif click_method == 'double':
                            await page.mouse.click(screen_x, screen_y, click_count=2)
                        elif click_method == 'with_delay':
                            await page.mouse.move(screen_x, screen_y)
                            await asyncio.sleep(0.5)
                            await page.mouse.down()
                            await asyncio.sleep(0.1)
                            await page.mouse.up()

                        # Check for navigation after each method
                        await asyncio.sleep(3)
                        current_url = page.url

                        if current_url != initial_url:
                            log_info(f" {task_id}: {click_method} click triggered navigation to: {current_url}")
                            click_result = {'success': True, 'method': f'coordinate_{click_method}'}
                            navigation_result = {'success': True, 'url': current_url, 'method': f'coordinate_{click_method}'}
                            break
                        else:
                            log_error(f"{task_id}: {click_method} click didn't trigger navigation")

                    if not navigation_result or not navigation_result.get('success', False):
                        click_result = {'success': False, 'error': 'All coordinate click methods failed'}
                        navigation_result = {'success': False, 'error': 'No navigation with any click method'}
                else:
                    click_result = {'success': False, 'error': 'No coordinates available'}
                    navigation_result = {'success': False, 'error': 'No coordinates for clicking'}

            # Continue with data extraction if navigation was successful
            # if navigation_result and navigation_result.get('success', False):
            #     current_url = page.url
            #     log_info(f"{task_id}: Attempting data extraction from: {current_url}")

            #     if "AnalyzeData" in current_url and "chartId=drillDown" in current_url:
            #         log_info(f" {task_id}: On correct drilldown page, extracting data...")
            #         extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year)
            #         extraction_success = False
            #         if extracted_data and extracted_data.get('extraction_data'):
            #             extraction_success = extracted_data['extraction_data'].get('success', False)

            #         if extraction_success:
            #             log_info(f" {task_id}: Data extraction successful")
            #         else:
            #             log_error(f"{task_id}: Data extraction failed or incomplete")
            #     else:
            #         log_error(f"{task_id}: Not on expected drilldown page. URL: {current_url}")
            #         extracted_data = {
            #             'extraction_data': {
            #                 'success': False,
            #                 'error': f'Unexpected page URL: {current_url}',
            #                 'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
            #             }
            #         }
            #         extraction_success = False
            # else:
            #     log_error(f" {task_id}: Skipping data extraction due to navigation failure")
            #     extracted_data = {
            #         'extraction_data': {
            #             'success': False,
            #             'error': 'Navigation failed, cannot extract data',
            #             'navigation_error': navigation_result.get('error', 'Unknown navigation error') if navigation_result else 'No navigation result'
            #         }
            #     }
            #     extraction_success = False

            if navigation_result and navigation_result.get('success', False):
                current_url = page.url
                # Modified URL check to handle different chart IDs
                if "AnalyzeData" in current_url:
                    # Add delay to ensure page loads completely
                    await asyncio.sleep(3)
                    
                    # For charts that need special handling
                    log_info(f"Chart ID-----------------55-->: {chart_id}")
                    special_charts = ['1357', '1354', '938', '1239', '1316']
                    if chart_id in special_charts:
                        # Force navigation to drilldown URL
                        await page.goto(f"{config.site_url.rstrip('/')}/AnalyzeData?chartId=drillDown", timeout=60000)
                        await asyncio.sleep(2)
                        
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year, chart_id, dataset_label)
                    # Check extraction success
                    extraction_success = False
                    if extracted_data and extracted_data.get('extraction_data'):
                        extraction_success = extracted_data['extraction_data'].get('success', False)
                        
                    if extraction_success:
                        log_info(f" {task_id}: Data extraction successful")
                    else:
                        log_error(f"{task_id}: Data extraction failed or incomplete")
                else:
                    log_info(f"{task_id}: Not on expected drilldown page. URL: {current_url}")
                    extracted_data = {
                        'extraction_data': {
                            'success': False,
                            'error': f'Unexpected page URL: {current_url}',
                            'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
                        }
                    }
                    extraction_success = False

            # Determine overall success
            overall_success = (
                click_result.get('success', False) and
                navigation_result.get('success', False) and
                extraction_success
            )
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'click_result': click_result,
                'navigation_result': navigation_result,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': overall_success,
                'legend_controlled': True,  # We know legend control was attempted
                'drilldown_url': navigation_result.get('url', '') if navigation_result else '',
                'click_success': click_result.get('success', False) if click_result else False,
                'navigation_success': navigation_result.get('success', False) if navigation_result else False,
                'extraction_success': extraction_success
            }
            log_info(f" {task_id}: Enhanced processing completed for {point_label} from {dataset_label}")
            return result

        except Exception as e:
            log_error(f" {task_id}: Error in enhanced processing: {e}")
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': True
            }
            return error_result

    async def try_chartjs_event_click(self, page, chart_id, point_data):
        """Try to trigger Chart.js click event programmatically"""
        try:
            print(f"Chart ID-----------------3333--> {chart_id}")
            if chart_id == 'chart_1316':
                await asyncio.sleep(10)
            result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting Chart.js event click for chart: {chart_id}');

                if (!window.chartInstances) {{
                    console.log('No chart instances found');
                    return {{ success: false, error: 'No chart instances' }};
                }}
                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        break;
                    }}
                }}
                if (!chartData) {{
                    console.log('Chart not found for event click');
                    return {{ success: false, error: 'Chart not found' }};
                }}

                const chart = chartData.instance;
                const canvas = chartData.canvas;

                try {{
                    // Get point data
                    const pointIndex = {point_data.get('pointIndex', 0)};
                    const datasetIndex = {point_data.get('datasetIndex', 0)};

                    // Get the data point element
                    const meta = chart.getDatasetMeta(datasetIndex);
                    if (!meta || !meta.data || !meta.data[pointIndex]) {{
                        console.log('Data point not found');
                        return {{ success: false, error: 'Data point not found' }};
                    }}

                    const pointElement = meta.data[pointIndex];
                    const pointPosition = pointElement.getCenterPoint();

                    // Create a synthetic click event
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {{
                        clientX: rect.left + pointPosition.x,
                        clientY: rect.top + pointPosition.y,
                        bubbles: true,
                        cancelable: true,
                        view: window
                    }});

                    // Trigger the click event
                    canvas.dispatchEvent(clickEvent);

                    // Also try Chart.js onClick if available
                    if (chart.options && chart.options.onClick) {{
                        const elements = chart.getElementsAtEventForMode(clickEvent, 'nearest', {{ intersect: true }}, false);
                        chart.options.onClick(clickEvent, elements, chart);
                    }}

                    console.log('Chart.js event click executed successfully');
                    return {{ success: true, method: 'chartjs_event', position: pointPosition }};

                }} catch (error) {{
                    console.error('Error in Chart.js event click:', error);
                    return {{ success: false, error: error.message }};
                }}
            }})()
            """)
            return result
        except Exception as e:
            log_error(f" Error in Chart.js event click: {str(e)}")
            return {'success': False, 'error': str(e)}

    async def debug_chart_clickability(self, page, chart_id, point_data):
        """Debug function to check if chart area is clickable"""
        try:
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')

            if screen_x and screen_y:
                # Check what element is at the click coordinates
                element_info = await page.evaluate(f"""
                    () => {{
                        const element = document.elementFromPoint({screen_x}, {screen_y});
                        if (element) {{
                            return {{
                                tagName: element.tagName,
                                className: element.className,
                                id: element.id,
                                isCanvas: element.tagName === 'CANVAS',
                                boundingRect: element.getBoundingClientRect(),
                                visible: element.offsetParent !== null
                            }};
                        }}
                        return null;
                    }}
                """)

                if element_info:
                    log_info(f"Debug - Element at ({screen_x}, {screen_y}):")
                    log_info(f"   Tag: {element_info.get('tagName', 'Unknown')}")
                    log_info(f"   Class: {element_info.get('className', 'None')}")
                    log_info(f"   Canvas: {element_info.get('isCanvas', False)}")
                    log_info(f"   Visible: {element_info.get('visible', False)}")
                else:
                    log_info(f"Debug - No element found at coordinates ({screen_x}, {screen_y})")

        except Exception as e:
            log_error(f"Debug function failed: {e}")
    async def process_single_chart_sequential(self, chart_data, target_month_year):
        """Process all points in a single chart sequentially"""
        chart_id = chart_data.get('chart_info', {}).get('canvasId', 'unknown_chart')
        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
        matching_points = chart_data.get('matching_points', [])

        print(f"🎯 Processing chart: {chart_title} ({chart_id}) with {len(matching_points)} points")

        chart_results = []
        if not await self.auth_manager.check_session_and_relogin_parallel():
            log_error("Initial session check failed. Cannot proceed.")
            return []
    
        page = await self.auth_manager.new_page()

        try:
            # Navigate to SpecialMetrics
            print(f"Navigating to SpecialMetrics for {chart_id}")
            await page.goto(f"{config.site_url.rstrip('/')}/SpecialMetrics", timeout=60000)
            # await page.wait_for_load_state("networkidle", timeout=15000)
            await asyncio.sleep(2)

            # Apply enhanced legend control
            legend_setup_success = await self.apply_enhanced_legend_control(page)
            await asyncio.sleep(2)

            if not legend_setup_success:
                print(f"Legend control setup failed for {chart_id}, attempting manual setup...")
                await self.debug_and_setup_charts(page)

            # Debug legend control setup
            await self.debug_legend_control(page)

            print(f" Page setup completed for {chart_id}")

            # Process each point in this chart sequentially
            for point_idx, point_data in enumerate(matching_points):
                point_label = point_data.get('xLabel', f'Point_{point_idx}')
                dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')

                print(f"\nProcessing point {point_idx + 1}/{len(matching_points)}: {point_label} ({dataset_label})")

                try:
                    # Step 1: Disable ALL legends first
                    print(f"🔒 Disabling all legends before processing {chart_id}")
                    await self.disable_all_legends(page)
                    await asyncio.sleep(1)

                    # Step 2: Enable ONLY the legend for current chart/dataset
                    print(f"🔓 Enabling ONLY legend for {chart_id} - {dataset_label}")
                    legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
                    await asyncio.sleep(2)  # Give more time for chart to update

                    if legend_enabled:
                        print(f" Legend control successful - ONLY {chart_id} legend is active")
                    else:
                        print(f"Legend control failed, but continuing with processing")

                    # Step 2.5: Ensure chart is interactive and data points are clickable
                    print(f"Ensuring chart {chart_id} is interactive after legend control...")
                    await self.ensure_chart_interactivity(page, chart_id)
                    await asyncio.sleep(1)

                    # Step 3: Create task for this point
                    task = {
                        'task_id': f"{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_info': chart_data['chart_info'],
                        'target_month_year': chart_data['target_month_year'],
                        'point_data': point_data,
                        'point_index': point_idx
                    }

                    # Step 4: Process this point with enhanced clicking
                    result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year)

                    if isinstance(result, dict):
                        result['chart_title'] = chart_title
                        result['point_sequence'] = point_idx + 1
                        result['method'] = 'sequential_processing'

                    chart_results.append(result)

                    # Log detailed result
                    if result.get('success', False):
                        click_success = result.get('click_success', False)
                        nav_success = result.get('navigation_success', False)
                        extract_success = result.get('extraction_success', False)
                        print(f" Completed point {point_idx + 1}: {point_label}")
                        print(f"   Click: {'' if click_success else ''} | Navigation: {'' if nav_success else ''} | Extraction: {'' if extract_success else ''}")
                        if nav_success:
                            drilldown_url = result.get('drilldown_url', 'Unknown')
                            print(f"   🔗 Drilldown URL: {drilldown_url}")
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        print(f" Failed point {point_idx + 1}: {point_label} - {error_msg}")

                    # Step 5: Navigate back to SpecialMetrics for next point (if not last point)
                    if point_idx < len(matching_points) - 1:
                        print(f"Navigating back to SpecialMetrics for next point")
                        try:
                            await page.goto(f"{config.site_url.rstrip('/')}/SpecialMetrics", timeout=60000)
                            # await page.wait_for_load_state("networkidle", timeout=15000)
                            await asyncio.sleep(10)

                            # Re-apply legend control
                            await self.apply_enhanced_legend_control(page)
                            await asyncio.sleep(1)
                            print(f" Successfully navigated back to SpecialMetrics")
                        except Exception as nav_back_error:
                            print(f" Failed to navigate back to SpecialMetrics: {nav_back_error}")
                            # Try to continue anyway
                            pass

                except Exception as e:
                    print(f" Error processing point {point_idx + 1} - {point_label}: {str(e)}")
                    error_result = {
                        'task_id': f"{chart_id}_point_{point_idx}",
                        'chart_id': chart_id,
                        'chart_title': chart_title,
                        'point_label': point_label,
                        'error': str(e),
                        'success': False,
                        'method': 'sequential_processing',
                        'point_sequence': point_idx + 1
                    }
                    chart_results.append(error_result)

            print(f" Completed all points for chart: {chart_title}")

        except Exception as e:
            print(f" Error setting up chart {chart_id}: {str(e)}")
            error_result = {
                'chart_id': chart_id,
                'chart_title': chart_title,
                'error': f"Chart setup failed: {str(e)}",
                'success': False,
                'method': 'sequential_processing'
            }
            chart_results.append(error_result)

        finally:
            try:
                # await context.close()
                # await browser.close()
                await page.close()
            except Exception as cleanup_error:
                print(f"Cleanup error for {chart_id}: {cleanup_error}")

        return chart_results               
       
    async def run_complete_process(self):
        # """Run the complete chart processing workflow with sequential processing"""
        """"
        Run the complete chart processing workflow.
        This function now orchestrates the entire session lifecycle.
        """
        log_info("🚀 Starting complete chart processing workflow with enhanced legend control...")

        # Step 1: Start the AuthManager ONCE at the beginning of the entire process
        success = await self.auth_manager.start(headless=False)
        if not success:
            log_error("❌ Authentication failed. Exiting.")
            return None

        try:
            # Step 1: Create chart-point combinations            
            combinations = await self.create_chart_point_combinations(TARGET_MONTHS_YEARS)            
            if not combinations:
                print(" No chart-point combinations found")
                return None
            print(f" Created {len(combinations)} chart-point combinations")

            # Step 2: Process combinations sequentially with single browser
            print("Step 2: Processing combinations sequentially with single browser...")
            all_results = []
            for combination in combinations:
                chart_results = await self.process_single_chart_sequential(combination, TARGET_MONTHS_YEARS)
                if chart_results:
                    all_results.extend(chart_results)

            results = {
                'successful': [r for r in all_results if r.get('success', False)],
                'failed': [r for r in all_results if not r.get('success', False)],
                'all_results': all_results,
                'total_processed': len(all_results),
                'total_charts': len(combinations),
                'batches_processed': 1,
                'success_rate': (sum(1 for r in all_results if r.get('success', False)) / len(all_results) * 100) if all_results else 0
            }

            # Step 3: Save results
            print("Step 3: Saving results...")
            await self.save_results(results)
            print(" Complete chart processing workflow finished successfully")
            print(f"Final Summary:")
            print(f"   - Total combinations processed: {len(combinations)}")
            print(f"   - Total tasks completed: {results.get('total_processed', 0)}")
            print(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
            return results

        except Exception as e:
            print(f" Error in complete process: {e}")            
            traceback.print_exc()
            return None
        finally:
            log_info("Closing AuthManager browser/session...")
            await self.auth_manager.stop()  # <-- ensures browser closes

    async def save_results(self, results):
        """Save processing results to files with enhanced formatting"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # Create results directory if it doesn't exist
            results_dir= create_folder_file_path(
                subfolder="chart_processing_results",                               
            )  

            # Save all results (combined)
            if results.get('all_results'):
                all_results_file = os.path.join(results_dir, chart_process_json)
                with open(all_results_file, 'w', encoding='utf-8') as f:
                    json.dump(results['all_results'], f, indent=2, default=str, ensure_ascii=False)
                print(f"All results saved to {all_results_file}")

            # Save enhanced summary
            summary = {
                'timestamp': timestamp,
                'processing_method': 'enhanced_legend_control_parallel',
                'target_months_years': TARGET_MONTHS_YEARS,
                'max_concurrent_browsers': MAX_CONCURRENT_BROWSERS,
                'total_processed': results.get('total_processed', 0),
                'total_charts': results.get('total_charts', 0),
                'rounds_processed': results.get('rounds_processed', 0),
                'successful_count': len(results.get('successful', [])),
                'failed_count': len(results.get('failed', [])),
                'success_rate': results.get('success_rate', 0),
                'processing_statistics': {
                    'charts_with_data': results.get('total_charts', 0),
                    'average_points_per_chart': results.get('total_processed', 0) / results.get('total_charts', 1) if results.get('total_charts', 0) > 0 else 0,
                    'total_rounds': results.get('rounds_processed', 0)
                }
            }

            # Perform comparison with Special Metrics results
            # print("\nStep 4: Performing UI vs DB comparison...")
            # await self.compare_with_cp_overview_results(results, timestamp)

        except Exception as e:
            print(f" Error saving results: {e}")            
            traceback.print_exc()
            
async def ui_capture():
    """Handles chart UI capture and runs the workflow"""
    
    try:
        
        processor = MultiChartParallelProcessor(
            max_browsers=MAX_CONCURRENT_BROWSERS            
        )

        log_info(f"   - Processing mode: Parallel ({MAX_CONCURRENT_BROWSERS}browsers, different charts)")
        log_info(f"   - Max concurrent browsers: {MAX_CONCURRENT_BROWSERS}")
        log_info(f"   - Target months/years: {TARGET_MONTHS_YEARS}")
        log_info(f"   - Browser timeout: {BROWSER_TIMEOUT}ms")
        log_info("=" * 80)

        # Run the parallel chart processing workflow
        results = await processor.run_complete_process()    
        if results:
            log_info("\n" + "=" * 80)
            log_info("Parallel processing with 3 browsers completed successfully!")
            log_info("Final Results:")
            log_info(f"   - Total tasks processed: {results.get('total_processed', 0)}")
            log_info(f"   - Charts processed: {results.get('total_charts', 0)}")
            log_info(f"   - Batches processed: {results.get('batches_processed', 0)}")
            log_info(f"   - Successful tasks: {len(results.get('successful', []))}")
            log_info(f"   - Failed tasks: {len(results.get('failed', []))}")
            log_info(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
            log_info("=" * 80)
            # Additional statistics
            if results.get('successful'):
                log_info(f" Parallel processing completed with {len(results['successful'])} successful extractions")
            if results.get('failed'):
                log_error(f" {len(results['failed'])} tasks failed - check failed results file for details")

            return True

    except Exception as e:
        log_error(f"❌ UI capture failed: {e}")
        traceback.print_exc()
        return False
    


# Main execution
async def main():
    """Main function: orchestrates UI capture, DB calculation, and comparison. Also run the enhanced chart processing with legend control"""

    start_time = time.time()
    log_info(f"Start Time: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    # Initialize components
    ui_results = await ui_capture()

    if not ui_results:
        log_error("UI capture did not return results. Exiting.")
        return False
    
    if ui_results:   
        # Generate final comparison report
        log_info("\n" + "=" * 80)
        log_info("GENERATING FINAL UI vs DB COMPARISON REPORT")
        log_info("=" * 80)
        try:
            #step4:
            db_calculation()
            # step5: 

            log_info("DB calculation completed successfully")   
            result_dir,db_json_path = create_folder_file_path(subfolder="chart_processing_results", output_file=db_json) 
            ui_json_path = os.path.join(result_dir, chart_process_json)
            log_info(f"UI JSON Path: {ui_json_path}")
            log_info(f"DB JSON Path: {db_json_path}")
            compare_special_metrics_results(ui_json_path, db_json_path)
            end_time = time.time()-start_time
            log_info(f"End Time: {end_time}")
            return True
        except Exception as comparison_error:
            log_error(f" Error generating comparison report: {comparison_error}")            
            traceback.print_exc()
            return False
    else:
        log_error(" Parallel processing failed - check logs for details")
        return False
def run_validation():
    """Run the all process"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        log_error(" Processing interrupted by user")
    except Exception as e:
        log_error(f"\n Unexpected error: {e}")        
        traceback.print_exc()

if __name__ == "__main__":
    run_validation()

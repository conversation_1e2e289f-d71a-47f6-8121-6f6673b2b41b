import asyncio
import json
import os
import time
import glob
import traceback
import pandas as pd
import numpy as np
from datetime import datetime
from math import isfinite, isnan
from playwright.async_api import async_playwright
from concurrent.futures import ThreadPoolExecutor
import threading
from datetime import datetime
from collections import defaultdict   
import re
from decimal import Decimal, ROUND_HALF_UP
import openpyxl
import openpyxl.cell
from openpyxl.styles import <PERSON><PERSON><PERSON><PERSON>, Alignment, Font
from openpyxl.utils import get_column_letter
import csv
from lib.pattern.config import config
from lib.pattern.qa_auto_core.compare_advisor_metrics import compare_advisor_metrics_results

from lib.pattern.qa_auto_core.db_handler.db_connector import allRevenueDetailsCPOverview
from dateutil.relativedelta import relativedelta
from datetime import timedelta
#  Target months-years for drilling down (modify as needed)
TARGET_MONTHS_YEARS = config.target_month_year
# Configuration constants
MAX_CONCURRENT_BROWSERS = 3
BROWSER_TIMEOUT = 30000
AUTH_STATE_FILE = "auth_state.json"
namespace = {
    'ns': 'http://www.dmotorworks.com/service-repair-order-history'
}

def round_off(n, decimals=0):
    """Round off numbers with proper decimal handling"""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def zero_sales_check(df, columns):
    total_sum = df[columns].sum().sum()
    return total_sum == 0

def get_month_date_range_from_target(target_date_str):
    """Get the start and end date for the target month"""
    # Parse the target date string
    target_date = datetime.strptime(target_date_str, "%Y-%m-%d")    
    # Get first day of the target month
    month_start = target_date.replace(day=1)    
    # Get last day of the target month
    month_end = month_start + relativedelta(months=1) - timedelta(days=1)    
    return month_start, month_end

def process_target_month_data(all_revenue_details_df, month_start, month_end, advisor, tech, retail_flag, customer_pay_types, warranty_pay_types, columns_to_check):
    """Process data for the target month and return results"""
    month_start = month_start.date()
    month_end = month_end.date()   
    # Filter data for the specific target month
    month_data = all_revenue_details_df[
        (all_revenue_details_df['closeddate'] >= month_start) &
        (all_revenue_details_df['closeddate'] <= month_end)
    ]    
    print(f"Target month data shape: {month_data.shape}")    
    if month_data.empty:
        print("No data found for the target month")
        return None    
    # Apply existing filtering logic
    filtered_df = month_data[
        (month_data['department'] == 'Service') & 
        (month_data['hide_ro'] != True)
    ]    
    if filtered_df.empty:
        print("No service department data found for the target month")
        return None    
    filtered_df = filtered_df.copy()
    filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)
    # Initialize variables
    labor_revenue = 0
    parts_revenue = 0
    combined_revenue = 0
    labor_gross_profit = 0
    parts_gross_profit = 0
    combined_gross_profit = 0
    labor_gross_profit_percentage = 0
    parts_gross_profit_percentage = 0
    combined_gross_profit_percentage = 0
    labor_sold_hours = 0
    labor_sold_hours_combined = 0    
    # NEW VARIABLES - Adding the missing calculations
    total_labor_cost = 0
    total_parts_cost = 0
    labor_sale_customer_pay = 0    
    # ... [existing group assignment logic remains the same] ...    
    combined_revenue_details = filtered_df.copy()
    combined_revenue_details['group'] = pd.Series(dtype="string")    
    # Create a temporary version for zero check without modifying the original data
    temp_revenue_details = combined_revenue_details.copy()
    temp_revenue_details.loc[temp_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0

    # Iterate through each unique RO number for group assignment
    for ro_number in combined_revenue_details['unique_ro_number'].unique():
        ro_specific_rows = temp_revenue_details[temp_revenue_details['unique_ro_number'] == ro_number]        
        ro_specific_rows_C = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(customer_pay_types)]
        ro_specific_rows_W = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(warranty_pay_types)]       
        zero_sales_C = zero_sales_check(ro_specific_rows_C, columns_to_check)
        zero_sales_W = zero_sales_check(ro_specific_rows_W, columns_to_check)        
        if not ro_specific_rows_C.empty and not zero_sales_C:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'C'
        elif not ro_specific_rows_W.empty and not zero_sales_W:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'W'
        else:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'I'
    # Apply filters based on advisor and tech conditions
    if advisor == {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details['unique_ro_number'].unique()
    elif advisor != {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['serviceadvisor'].astype(str).isin(advisor), 'unique_ro_number'].unique()
    elif advisor == {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['lbrtechno'].astype(str).isin(tech), 'unique_ro_number'].unique()
    elif advisor != {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[(combined_revenue_details['serviceadvisor'].astype(str).isin(advisor)) & 
            (combined_revenue_details['lbrtechno'].astype(str).isin(tech)), 'unique_ro_number'].unique()
    # Applying the Advisor and tech filter conditions
    combined_revenue_details = combined_revenue_details[combined_revenue_details['unique_ro_number'].isin(matching_ro_numbers)]
    combined_revenue_details = combined_revenue_details.reset_index(drop=True)
    combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    # RO Counts
    Scorecard_10_CP = combined_revenue_details.loc[combined_revenue_details['group'] == 'C', 'unique_ro_number'].nunique()
    Scorecard_10_Wty = combined_revenue_details.loc[combined_revenue_details['group'] == 'W', 'unique_ro_number'].nunique()
    Scorecard_10_Int = combined_revenue_details.loc[combined_revenue_details['group'] == 'I', 'unique_ro_number'].nunique()
    all_unique_ros = Scorecard_10_CP + Scorecard_10_Wty + Scorecard_10_Int      
    # ENHANCED CP CALCULATIONS - Following the second code pattern
    # Pre-compute zero-value mask for efficient filtering
    zero_values_mask = (
        (combined_revenue_details['lbrsale'].fillna(0) == 0) &
        (combined_revenue_details['lbrsoldhours'].fillna(0) == 0) &
        (combined_revenue_details['prtextendedsale'].fillna(0) == 0) &
        (combined_revenue_details['prtextendedcost'].fillna(0) == 0)
    )    
    # Create masks for customer pay and group C
    customer_pay_mask = combined_revenue_details['paytypegroup'].isin(customer_pay_types)
    group_C_mask = combined_revenue_details['group'] == 'C'
    cp_and_customer_mask = customer_pay_mask & group_C_mask    
    # Filter CP job details
    list_of_paytypegroup_C = combined_revenue_details[cp_and_customer_mask]
    total_CP_revenue_details_df = pd.DataFrame(list_of_paytypegroup_C)
    total_CP_revenue_details_df = total_CP_revenue_details_df[~zero_values_mask[cp_and_customer_mask]]    
    # Apply tech filter if needed
    if tech != {'all'}:
        tech_mask = total_CP_revenue_details_df['lbrtechno'].astype(str).isin(tech)
        total_CP_revenue_details_df = total_CP_revenue_details_df[tech_mask]    
    # CALCULATE THE REQUIRED METRICS
    effective_labor_rate_CP=0
    cp_parts_markup_CP=0
    if not total_CP_revenue_details_df.empty:
        # Convert columns to numeric once and store for reuse
        numeric_columns = {}
        columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']        
        for col in columns_to_convert:
            numeric_columns[col] = pd.to_numeric(total_CP_revenue_details_df[col], errors='coerce').fillna(0)        
        # 1. Labor Sale - Customer Pay
        labor_sale_customer_pay = numeric_columns['lbrsale'].sum()        
        # 2. Total Labor Cost
        total_labor_cost = numeric_columns['lbrcost'].sum()        
        # 3. Total Parts Sale
        total_parts_sale = numeric_columns['prtextendedsale'].sum()        
        # 4. Total Parts Cost
        total_parts_cost = numeric_columns['prtextendedcost'].sum()        
        # Additional calculations (existing logic)
        labor_sold_hours = numeric_columns['lbrsoldhours'].sum()        
        # Calculate gross profits
        labor_gross_profit = labor_sale_customer_pay - total_labor_cost
        parts_gross_profit = total_parts_sale - total_parts_cost
        combined_gross_profit = labor_gross_profit + parts_gross_profit        
        # Calculate percentages
        labor_gross_profit_percentage = round_off((labor_gross_profit / labor_sale_customer_pay) * 100, 1) if labor_sale_customer_pay != 0 else 0
        parts_gross_profit_percentage = round_off((parts_gross_profit / total_parts_sale) * 100, 1) if total_parts_sale != 0 else 0
        combined_revenue = labor_sale_customer_pay + total_parts_sale
        combined_gross_profit_percentage = round_off((combined_gross_profit / combined_revenue) * 100, 1) if combined_revenue != 0 else 0        
        # Effective labor rate
        
        effective_labor_rate_CP = round_off(labor_sale_customer_pay / labor_sold_hours, 2) if labor_sold_hours != 0 else 0        
        # Parts markup
        
        cp_parts_markup_CP = round_off(total_parts_sale / total_parts_cost, 4) if total_parts_cost != 0 else 0        
        # Round the main values
        labor_revenue = round_off(labor_sale_customer_pay, 2)
        parts_revenue = round_off(total_parts_sale, 2)
        labor_sold_hours = round_off(labor_sold_hours, 2)    
    else:
        print("No Customer Pay data available for calculations")    
    # Continue with combined calculations for all pay types
    if not combined_revenue_details.empty:
        labor_sold_hours_combined_value = pd.to_numeric(combined_revenue_details['lbrsoldhours']).fillna(0).sum()
        labor_sold_hours_combined = round_off(labor_sold_hours_combined_value, 2)        
        all_labor_sale = pd.to_numeric(combined_revenue_details['lbrsale']).fillna(0).sum()
        all_sold_hours = pd.to_numeric(combined_revenue_details['lbrsoldhours']).fillna(0).sum()
        effective_labor_rate_combined = round_off(all_labor_sale / all_sold_hours, 2) if all_sold_hours != 0 else 0        
        part_extended_sale_combined = pd.to_numeric(combined_revenue_details['prtextendedsale']).fillna(0).sum()
        part_extended_cost_combined = pd.to_numeric(combined_revenue_details['prtextendedcost']).fillna(0).sum()
        cp_parts_markup_combined = round_off(part_extended_sale_combined / part_extended_cost_combined, 4) if part_extended_cost_combined != 0 else 0    
    # Return enhanced result set
    return {
        "target_month": month_start.strftime('%Y-%m'),
        "target_month_name": month_start.strftime('%B %Y'),       
        "total_ros": all_unique_ros,
        "ro_counts": {
            "customer_pay_ros": Scorecard_10_CP,
            "warranty_ros": Scorecard_10_Wty,
            "internal_ros": Scorecard_10_Int
        },
        # ENHANCED CUSTOMER PAY METRICS
        "customer_pay_metrics": {
            "labor_sale_customer_pay": round_off(labor_sale_customer_pay, 2),
            "total_labor_cost": round_off(total_labor_cost, 2),
            "total_parts_sale": round_off(total_parts_sale, 2),
            "total_parts_cost": round_off(total_parts_cost, 2),
            "labor_gross_profit": round_off(labor_gross_profit, 2),
            "parts_gross_profit": round_off(parts_gross_profit, 2),
            "combined_gross_profit": round_off(combined_gross_profit, 2),
            "labor_gross_profit_percentage": labor_gross_profit_percentage,
            "parts_gross_profit_percentage": parts_gross_profit_percentage,
            "combined_gross_profit_percentage": combined_gross_profit_percentage,
            "effective_labor_rate": effective_labor_rate_CP,
            "elr": effective_labor_rate_CP,
            "parts_markup": cp_parts_markup_CP,
            "labor_sold_hours": round_off(labor_sold_hours, 2),
            "parts_to_labor_ratio": round_off(total_parts_sale / labor_sale_customer_pay, 2) if labor_sale_customer_pay != 0 else 0
        },
        # Existing metrics (keeping for compatibility)
        "labor_revenue": labor_revenue,
        "parts_revenue": parts_revenue,
        "combined_revenue": round_off(combined_revenue, 2),
        "labor_gross_profit": round_off(labor_gross_profit, 2),
        "parts_gross_profit": round_off(parts_gross_profit, 2),
        "combined_gross_profit": round_off(combined_gross_profit, 2),
        "labor_gross_profit_percentage": labor_gross_profit_percentage,
        "parts_gross_profit_percentage": parts_gross_profit_percentage,
        "combined_gross_profit_percentage": combined_gross_profit_percentage,
        "labor_sold_hours": labor_sold_hours,
        "labor_sold_hours_combined": labor_sold_hours_combined
    }

def db_execution(target_date_str, advisor, tech, retail_flag, columns_to_check):
    """
    Handle database operations and execute month processing
    """      
    try:
        customer_pay_types={},
        warranty_pay_types={}
        # Get target month date range
        month_start, month_end = get_month_date_range_from_target(target_date_str)        
        print(f"Target month range: {month_start.date()} to {month_end.date()}")       
        # Fetch all data from database
        print("Fetching data from database...")
        all_revenue_details_table_db_connect = allRevenueDetailsCPOverview()
        all_revenue_details_df = all_revenue_details_table_db_connect.getTableResult()        
        if all_revenue_details_df.empty:
            print("ERROR: No data retrieved from database!")
            return None       
        # Convert date column and other preprocessing
        columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))        
        # Define customer and warranty pay types based on retail_flag
        if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C'}
            warranty_pay_types = {'W', 'F', 'M', 'E'}
        elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'M'}
            warranty_pay_types = {'W', 'F', 'E'}
        elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C', 'E'}
            warranty_pay_types = {'W', 'F', 'M'}
        elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'E', 'M'}
            warranty_pay_types = {'W', 'F'}       
        target_month_result = process_target_month_data(
            all_revenue_details_df, 
            month_start, 
            month_end,            
            advisor, 
            tech, 
            retail_flag, 
            customer_pay_types, 
            warranty_pay_types, 
            columns_to_check
        ) 
        return target_month_result, customer_pay_types, warranty_pay_types        
    except Exception as e:
        print(f"ERROR in db_execution: {str(e)}")
        print("=" * 60)
        print("DATABASE EXECUTION FAILED")
        print("=" * 60)
        return None, None, None

def db_calculation():
    """
    Main execution function for db calculation
    """    
    # Configuration variables
    columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
    retail_flag = {'C'}    
    storeid = config.store_id
    realm = config.database_name   
    advisor_set = config.advisor
    tech_set = config.technician    
    # Process advisor configuration
    if isinstance(advisor_set, str):
        if advisor_set.lower() == 'all':
            advisor = {'all'}
        elif ',' in advisor_set:
            advisor = {x.strip() for x in advisor_set.split(',')}
        else:
            advisor = {advisor_set.strip()}
    else:
        advisor = {'all'}
    
    if advisor != {'all'}:
        advisor_id = next(iter(advisor))   
    else:
        advisor_id = 'all'    
    # Process technician configuration
    if isinstance(tech_set, str):
        if tech_set.lower() == 'all':
            tech = {'all'}
        elif ',' in tech_set:
            tech = {x.strip() for x in tech_set.split(',')}
        else:
            tech = {tech_set.strip()}
    else:
        tech = {'all'}   
    
    # Execute database operations and processing
    target_date_str = TARGET_MONTHS_YEARS[0]
    target_month_result, customer_pay_types, warranty_pay_types = db_execution(
        target_date_str, advisor, tech, retail_flag, columns_to_check
    )    
    # Process results
    if target_month_result:
        print("\n" + "=" * 80)
        print("RESULTS PROCESSING")
        print("=" * 80)        
        # Create the final result set for the target month only
        final_result_set = {
            "analysis_info": {
                "target_month": target_date_str,
                "analysis_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "store_id": storeid,
                "realm": realm,
                "advisor_filter": list(advisor),
                "technician_filter": list(tech),
                "customer_pay_types": list(customer_pay_types),
                "warranty_pay_types": list(warranty_pay_types)
            },
            "target_month_results": target_month_result
        }     
        # Write results to JSON file
        output_filename = "chart_processing_results/db_calculated_value.json"
        with open(output_filename, 'w', encoding='utf-8') as json_file:
            json.dump(final_result_set, json_file, indent=4, ensure_ascii=False)        
        print(f"\nTarget month CP overview data written successfully to {output_filename}")        
        # Display summary
        print(f"\nTarget Month Summary for {target_month_result['target_month_name']}:")
        print(f"  Total Revenue: ${target_month_result['combined_revenue']:,.2f}")
        print(f"  Total Gross Profit: ${target_month_result['combined_gross_profit']:,.2f}")
        print(f"  GP Percentage: {target_month_result['combined_gross_profit_percentage']}%")
        print(f"  Total ROs: {target_month_result['total_ros']}")
        print(f"    - Customer Pay ROs: {target_month_result['ro_counts']['customer_pay_ros']}")
        print(f"    - Warranty ROs: {target_month_result['ro_counts']['warranty_ros']}")
        print(f"    - Internal ROs: {target_month_result['ro_counts']['internal_ros']}")
        # print(f"  Average ROs per day: {target_month_result['average_ros_per_day']}")
        # print(f"  Working days: {target_month_result['working_days']}")
        print(f"  Labor sold hours: {target_month_result['labor_sold_hours']}")        
    else:
        print("\n" + "=" * 80)
        print("NO DATA RESULTS PROCESSING")
        print("=" * 80)        
        print(f"No data available for target month {target_date_str}")   
    
    print("\n" + "=" * 80)
    print("VALIDATE ADVISOR METRICS ANALYSIS - MAIN EXECUTION COMPLETED")
    print("=" * 80)

class AuthManager:
    """Handles authentication for the application"""
    
    def __init__(self):
        self.auth_state = None
        self.auth_file = AUTH_STATE_FILE
    
    def get_auth_state(self):
        """Get current auth state"""
        if not self.auth_state:
            self.load_auth_state()
        return self.auth_state
    
    def load_auth_state(self):
        """Load auth state from file"""
        try:
            if os.path.exists(self.auth_file):
                with open(self.auth_file, 'r',encoding='utf-8') as f:
                    self.auth_state = json.load(f)
                print("Auth state loaded from file")
                return True
        except Exception as e:
            print(f"Could not load auth state: {e}")
        return False
    
    def save_auth_state(self, auth_state):
        """Save auth state to file"""
        try:
            with open(self.auth_file, 'w',encoding='utf-8') as f:
                json.dump(auth_state, f, indent=2)
            self.auth_state = auth_state
            print("Auth state saved to file")
            return True
        except Exception as e:
            print(f"Could not save auth state: {e}")
            return False
    
    async def setup_authentication(self, playwright):
        """Setup authentication with login process"""
        print("Setting up authentication...")        
        browser = await playwright.chromium.launch(
            headless=False,  # Set to True for headless mode
            args=['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu']
        )        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )        
        page = await context.new_page()
        page.set_default_timeout(BROWSER_TIMEOUT)        
        try:
            login_success = await self.perform_login(page)            
            if login_success:
                # Save authentication state
                auth_state = await context.storage_state()
                self.save_auth_state(auth_state)
                print(" Authentication setup completed successfully")
                return True
            else:
                print(" Authentication setup failed")
                return False                
        finally:
            await context.close()
            await browser.close()
    
    async def perform_login(self, page, max_retries=3):
        """Perform login with retry mechanism"""
        
        for attempt in range(max_retries):
            try:
                print(f"Login attempt {attempt + 1}/{max_retries}")                
                # Navigate to login page
                print("Navigating to login page...")
                await page.goto("https://carriageag-simt.fixedopspc.com/auth/login?provenance=fopc", timeout=30000)
                # await page.wait_for_load_state("networkidle")
                # Click login button
                print("Clicking login button...")
                await page.wait_for_selector("button#login", timeout=10000)
                await page.click("button#login", force=True)

                # Fill credentials
                print("Filling username and password...")
                await page.wait_for_selector("input[name='username']", timeout=10000)
                await page.fill("input[name='username']", "<EMAIL>")
                await page.fill("input[name='password']", "123")
                await page.click("input#kc-login")
                # await page.wait_for_load_state("networkidle")

                # Select Store
                print("Selecting store...")
                await page.wait_for_selector("#store-select", timeout=30000)
                await page.click("#store-select")
                await page.wait_for_selector("role=option[name='Carriage Kia of Woodstock']", timeout=30000)
                await page.get_by_role("option", name='Carriage Kia of Woodstock').click()
                # await page.wait_for_load_state("networkidle")

                # Click "View Dashboard"
                print("Accessing dashboard...")
                await page.wait_for_selector("button#login", timeout=30000)
                await page.click("button#login")
                # await page.wait_for_load_state("networkidle")
                
                # Verify by navigating to target page
                await page.goto("https://carriageag-simt.fixedopspc.com/ServiceAdvisorPerformance", timeout=30000)
                # await page.wait_for_load_state("networkidle")
                
                print(" Login completed successfully")
                return True
                
            except Exception as e:
                print(f" Login attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    print("Retrying in 5 seconds...")
                    await asyncio.sleep(5)
        
        return False
    
class MultiChartParallelProcessor:
    """Process multiple charts in parallel with separate browsers"""
    
    def __init__(self, max_browsers=4, auth_manager=None):
        self.max_browsers = max_browsers
        self.auth_manager = auth_manager or AuthManager()
    
    async def create_authenticated_browser_context(self, playwright, headless=False):
        """Create an authenticated browser context"""
        browser = await playwright.chromium.launch(
            headless=headless,
            args=['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu']
        )        
        # Load auth state
        auth_state = self.auth_manager.get_auth_state()        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            storage_state=auth_state)                
        page = await context.new_page()
        page.set_default_timeout(BROWSER_TIMEOUT)        
        return browser, context, page

    async def discover_charts(self):
        """Discover all charts on the ServiceAdvisorPerformance page"""
        print("Discovering charts on ServiceAdvisorPerformance page...")
        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            try:
                await page.goto("https://carriageag-simt.fixedopspc.com/ServiceAdvisorPerformance", timeout=50000)
                chart_found = False
                
                selectors_to_try = [
                    'div.apexcharts-canvas',
                    'svg.apexcharts-svg',
                    'div[class*="apexcharts"]',
                    '[id*="apexcharts"]',
                    'div.diagram-section'
                ]
                
                for selector in selectors_to_try:
                    try:
                        await page.wait_for_selector(selector, timeout=5000)
                        chart_found = True
                        print(f" Found charts using selector: {selector}")
                        break
                    except Exception:
                        continue
                
                if not chart_found:
                    print("No chart elements found with any selector")
                    return False
                
                await asyncio.sleep(2)
                
                charts_info = await page.evaluate("""
                    () => {
                        const svgElements = document.querySelectorAll('svg.apexcharts-svg');
                        const chartsInfo = [];
                        
                        for (let i = 0; i < svgElements.length; i++) {
                            const svg = svgElements[i];
                            const rect = svg.getBoundingClientRect();
                            
                            // Try to find chart title - look in the card header
                            let chartTitle = `Chart ${i + 1}`;
                            let chartId = '';
                            let containerId = '';
                            
                            // Look for parent ApexCharts container
                            let apexContainer = svg.closest('div[class*="apexcharts"]');
                            if (apexContainer && apexContainer.id) {
                                chartId = apexContainer.id;
                            }
                            
                            // Look for parent diagram-section container
                            let diagramSection = svg.closest('div.diagram-section');
                            if (diagramSection && diagramSection.id) {
                                containerId = diagramSection.id;
                            }
                            
                            // Look for the MuiCard container and extract title
                            let cardContainer = svg.closest('.MuiCard-root');
                            if (cardContainer) {
                                const cardHeader = cardContainer.querySelector('.MuiCardHeader-root');
                                if (cardHeader) {
                                    const titleElement = cardHeader.querySelector('.MuiCardHeader-title');
                                    if (titleElement && titleElement.textContent.trim()) {
                                        chartTitle = titleElement.textContent.trim();
                                    }
                                }
                            }
                            
                            // Check if SVG is a valid ApexCharts element
                            let hasChart = false;
                            if (svg.classList.contains('apexcharts-svg') || 
                                svg.getAttribute('xmlns:data') === 'ApexChartsNS' || 
                                svg.closest('[class*="apexcharts"]')) {
                                hasChart = true;
                            }
                            
                            // Only include SVGs that appear to be charts and are visible
                            if (hasChart && rect.width > 0 && rect.height > 0) {
                                chartsInfo.push({
                                    svgIndex: i,
                                    chartTitle: chartTitle,
                                    svgId: svg.id || `svg-${i}`,
                                    svgClass: svg.className.baseVal || svg.className,
                                    apexChartsId: chartId,
                                    containerId: containerId,
                                    position: {
                                        x: Math.round(rect.left),
                                        y: Math.round(rect.top),
                                        width: Math.round(rect.width),
                                        height: Math.round(rect.height)
                                    },
                                    visible: rect.width > 0 && rect.height > 0,
                                    isApexCharts: true,
                                    chartType: 'svg'
                                });
                            }
                        }
                        
                        console.log(`Found ${chartsInfo.length} ApexCharts SVG elements`);
                        return chartsInfo;
                    }
                """)
                
                print(f"Found {len(charts_info)} charts on ServiceAdvisorPerformance page")
                for chart in charts_info:
                    print(f" - Chart {chart['svgIndex']}: {chart['chartTitle']} (Container: {chart['containerId']}, ApexCharts ID: {chart.get('apexChartsId', 'N/A')})")
                
                return charts_info
                
            finally:
                await context.close()
                await browser.close()
    async def find_matching_points_in_chart(self, page, chart_index, target_month_year,chart_id):
        """Find matching data points for a specific chart and target month-year"""
        try:
            print(f"Finding points in chart {chart_index} for target: {target_month_year}")  
            page.on("console", lambda msg: print(f"🔔 [Browser Console] {msg.type}: {msg.text}"))
          
            matching_points = await page.evaluate("""
    (args) => {
        const { chartIndex, targetMonthYear } = args;
        const matchingPoints = [];
        
        try {
            // Find all ApexCharts SVG elements
            const svgCharts = document.querySelectorAll('svg.apexcharts-svg');
            console.log(`Found ${svgCharts.length} ApexCharts SVG elements`);
            
            // Check if the requested chart index exists
            if (chartIndex >= svgCharts.length) {
                console.log(`Chart index ${chartIndex} out of range (max: ${svgCharts.length - 1})`);
                return [];
            }
            
            const svg = svgCharts[chartIndex];
            console.log(`Processing SVG chart ${chartIndex} for target: ${targetMonthYear}`);
            
            const svgRect = svg.getBoundingClientRect();
            console.log(`SVG rect:`, svgRect);
            
            // Extract x-axis labels from the SVG
            const xAxisLabels = [];
            const xAxisTexts = svg.querySelectorAll('.apexcharts-xaxis-label text, .apexcharts-xaxis-texts-g text');
            xAxisTexts.forEach(textEl => {
                // First try to get text from tspan (more reliable for ApexCharts)
                const tspanEl = textEl.querySelector('tspan');
                const labelText = tspanEl ? tspanEl.textContent : textEl.textContent;
                
                if (labelText && labelText.trim()) {
                    xAxisLabels.push(labelText.trim());
                }
            });
            console.log(`X-axis labels found:`, xAxisLabels);
            
            // Helper function to check if label matches target
            const isLabelMatch = (label, target) => {
                if (!label || !target) return false;
                
                const labelStr = String(label).toLowerCase().trim();
                const targetStr = String(target).toLowerCase().trim();
                
                // Direct match
                if (labelStr === targetStr) return true;
                
                // Contains match
                if (labelStr.includes(targetStr) || targetStr.includes(labelStr)) return true;
                
                // Month-year pattern matching (e.g., "Jan '24", "Nov '24", "January 2024")
                const monthYearRegex = /(\\w+)[\\s']*['\\/\\-\\s]+(\\d{2,4})/;
                const labelMatch = labelStr.match(monthYearRegex);
                const targetMatch = targetStr.match(monthYearRegex);
                
                if (labelMatch && targetMatch) {
                    const labelMonth = labelMatch[1];
                    let labelYear = labelMatch[2];
                    const targetMonth = targetMatch[1];
                    let targetYear = targetMatch[2];
                    
                    // Handle 2-digit years
                    if (labelYear.length === 2) labelYear = '20' + labelYear;
                    if (targetYear.length === 2) targetYear = '20' + targetYear;
                    
                    // Check if years match and months match (partial match allowed)
                    if (labelYear === targetYear && 
                        (labelMonth.includes(targetMonth) || targetMonth.includes(labelMonth))) {
                        return true;
                    }
                }
                
                return false;
            };
            
            // Find matching x-axis labels
            const matchingLabelIndices = [];
            xAxisLabels.forEach((label, index) => {
                if (isLabelMatch(label, targetMonthYear)) {
                    matchingLabelIndices.push({ index, label });
                    console.log(`Found matching label at index ${index}: "${label}" matches "${targetMonthYear}"`);
                }
            });
            
            if (matchingLabelIndices.length === 0) {
                console.log(`No matching labels found for target: ${targetMonthYear}`);
                return [];
            }
            
            // Find all data series and their points
            const seriesGroups = svg.querySelectorAll('.apexcharts-series');
            console.log(`Found ${seriesGroups.length} data series`);
            
            seriesGroups.forEach((seriesGroup, seriesIndex) => {
                const seriesName = seriesGroup.getAttribute('seriesName') || 
                                 seriesGroup.getAttribute('data:realIndex') || 
                                 `Series ${seriesIndex}`;
                
                // Find data points (circles for line charts, paths for other types)
                const dataPoints = seriesGroup.querySelectorAll('circle, .apexcharts-marker');
                console.log(`Series ${seriesIndex} (${seriesName}) has ${dataPoints.length} data points`);
                // Process matching points based on label indices
                matchingLabelIndices.forEach(({index: labelIndex, label}) => {
                    if (labelIndex < dataPoints.length) {
                        const point = dataPoints[labelIndex];
                        
                        try {
                            // Extract coordinates from the SVG element
                            let canvasX = null;
                            let canvasY = null;
                            let screenX = null;
                            let screenY = null;
                            
                            if (point.tagName.toLowerCase() === 'circle') {
                                canvasX = parseFloat(point.getAttribute('cx'));
                                canvasY = parseFloat(point.getAttribute('cy'));
                            } else {
                                // For other elements, try to get transform or position
                                const transform = point.getAttribute('transform');
                                if (transform) {
                                    const translateMatch = transform.match(/translate\\(([^,]+),([^)]+)\\)/);
                                    if (translateMatch) {
                                        canvasX = parseFloat(translateMatch[1]);
                                        canvasY = parseFloat(translateMatch[2]);
                                    }
                                }
                            }
                            
                            // Calculate screen coordinates
                            if (canvasX !== null && canvasY !== null && !isNaN(canvasX) && !isNaN(canvasY)) {
                                screenX = svgRect.left + canvasX;
                                screenY = svgRect.top + canvasY;
                            }
                            
                            // Try to extract the data value from tooltip or data attributes
                            let value = null;
                            
                            // Look for data value in various places
                            const rel = point.getAttribute('rel');
                            const j = point.getAttribute('j');
                            const index = point.getAttribute('index');
                            // Try to find the value from the point's position on y-axis
                            // This is approximate but better than nothing
                            if (canvasY !== null) {
                                // Find y-axis labels to estimate value
                                // Find y-axis labels using the correct selector based on the SVG structure
                                const yAxisLabels = svg.querySelectorAll('.apexcharts-yaxis-texts-g .apexcharts-yaxis-label tspan');
                               // console.log('Y-axis labels found:', yAxisLabels.length);

                                // Alternative selectors if the above doesn't work
                                if (yAxisLabels.length === 0) {
                                    // Try finding by the text elements directly
                                    const alternativeLabels = svg.querySelectorAll('.apexcharts-yaxis-texts-g text tspan');
                                    console.log('Alternative y-axis labels found:', alternativeLabels.length);
                                    
                                    // Or try this more specific path based on your SVG structure
                                    const specificLabels = svg.querySelectorAll('g[class*="apexcharts-yaxis-texts-g"] text tspan');
                                    console.log('Specific y-axis labels found:', specificLabels.length);
                                }

                                if (yAxisLabels.length > 0) {
                                    // Simple approximation based on y position
                                    const chartHeight = svgRect.height;
                                    const relativeY = canvasY / chartHeight;
                                    
                                    // Extract numeric values from y-axis labels
                                    const yValues = Array.from(yAxisLabels).map(label => {
                                        const text = label.textContent.replace(/[$,\s]/g, ''); // Remove $, commas, and spaces
                                       // console.log('Label text:', label.textContent, 'Parsed:', text);
                                        return parseFloat(text);
                                    }).filter(val => !isNaN(val)).sort((a, b) => b - a); // Sort descending
                                                                        
                                    if (yValues.length >= 2) {
                                        const maxValue = yValues[0];
                                        const minValue = yValues[yValues.length - 1];
                                        value = Math.round(maxValue - (relativeY * (maxValue - minValue)));
                                        console.log(`Calculated value: ${value} (max: ${maxValue}, min: ${minValue}, relativeY: ${relativeY})`);
                                    }
                                }
                            }
                            
                            const coordsValid = screenX !== null && screenY !== null && 
                                            !isNaN(screenX) && !isNaN(screenY) &&
                                            isFinite(screenX) && isFinite(screenY);
                            
                            const pointData = {
                                chartIndex: chartIndex,
                                seriesIndex: seriesIndex,
                                pointIndex: labelIndex,
                                value: value,
                                xLabel: label,
                                screenX: screenX,
                                screenY: screenY,
                                canvasX: canvasX,
                                canvasY: canvasY,
                                seriesName: seriesName,
                                chartType: 'apexcharts',
                                coordinatesValid: coordsValid,
                                targetMonthYear: targetMonthYear,
                                // ApexCharts specific data
                                elementType: point.tagName.toLowerCase(),
                                elementId: point.id,
                                elementClass: point.className.baseVal || point.className
                            };
                            
                            matchingPoints.push(pointData);
                            console.log(`Added matching point:`, pointData);
                            
                        } catch (e) {
                            console.warn(`Error processing point at index ${labelIndex}:`, e);
                        }
                    }
                });
            });
            
        } catch (e) {
            console.error('Error in ApexCharts point extraction:', e);
            return [];
        }
        
        console.log(`Found ${matchingPoints.length} matching points for chart ${chartIndex} and target ${targetMonthYear}`);
        return matchingPoints;
    }
""", {'chartIndex': chart_index, 'targetMonthYear': target_month_year});
            print(f" Found {len(matching_points)} matching points in chart {chart_index} for {target_month_year}")
            print(f"Matching points: {matching_points}")
            return matching_points            
        except Exception as e:
            print(f"Error finding matching points in chart {chart_index}: {str(e)}")
            return []      
    
    async def create_chart_point_combinations(self, target_months_years):
        """Create combinations of charts and their matching points"""
        print("Creating chart-point combinations...")
        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            try:
                # Navigate to ServiceAdvisorPerformance
                await page.goto("https://carriageag-simt.fixedopspc.com/ServiceAdvisorPerformance", timeout=30000)
                # await page.wait_for_load_state("networkidle", timeout=15000)
                await asyncio.sleep(2)                
                # Discover all charts
                charts_info = await self.discover_charts()
                if not charts_info:
                    print(" No charts found")
                    # Delete auth_state.json file when no charts are found
                    auth_state_path = "auth_state.json"
                    try:
                        if os.path.exists(auth_state_path):
                            os.remove(auth_state_path)
                            print(f"Deleted {auth_state_path} due to no charts found")
                        else:
                            print(f"{auth_state_path} not found to delete")
                    except Exception as e:
                        print(f"Error deleting {auth_state_path}: {e}")
                    return []
                
                chart_point_combinations = []
                charts_with_points = []
                print("  For each chart, find matching points for each target month-year")
                # For each chart, find matching points for each target month-year
                for chart_info in charts_info:
                    chart_index = chart_info['svgIndex']
                    container_id = chart_info.get('containerId', '')
                    chart_id = container_id.split('-')[-1] if container_id else None
                    chart_title = chart_info.get('chartTitle', f'Chart {chart_id}')
                    print(f"Processing Chart {chart_id}: {chart_title}")                    
                    chart_total_points = 0
                    chart_combinations = []                    
                    # Process each target month-year for this chart
                    for target_month_year in target_months_years:
                        print(f"Looking for data points matching: {target_month_year}")
                        # Find matching points for this chart and target month-year
                        matching_points = await self.find_matching_points_in_chart(
                            page, chart_index, target_month_year,chart_id)                        
                        print(f"Found {len(matching_points) if matching_points else 0} matching points for {target_month_year}")
                        if matching_points:
                            # Create combination for this chart and target month-year
                            combination = {
                                'chart_index': f"chart_{chart_index}",
                                'chart_id': chart_id,
                                'chart_info': chart_info,
                                'target_month_year': target_month_year,
                                'matching_points': matching_points,
                                'processing_status': 'pending',
                                'points_count': len(matching_points)
                            }
                            chart_combinations.append(combination)
                            chart_total_points += len(matching_points)                            
                            print(f"   Added combination: Chart {chart_index} - {target_month_year} ({len(matching_points)} points)")
                        else:
                            print(f"   No matching points found for Chart {chart_index} - {target_month_year}")                    
                    # Track charts with their point counts
                    if chart_combinations:
                        charts_with_points.append({
                            'chart_index': chart_index,
                            'chart_id': chart_id,
                            'chart_title': chart_title,
                            'total_points': chart_total_points,
                            'combinations': chart_combinations
                        })                
                # Sort charts by total points (descending) to get charts with most points first
                charts_with_points.sort(key=lambda x: x['total_points'], reverse=True)             # Take all charts and their combinations 
                for chart_data in charts_with_points:
                    chart_point_combinations.extend(chart_data['combinations'])
                    print(f"  Added Chart {chart_data['chart_index']}: {chart_data['chart_title']} ({chart_data['total_points']} points)")
                print(f"\nSummary: Created {len(chart_point_combinations)} chart-point combinations from {len(charts_with_points)} charts")              
                # Print summary by chart
                chart_summary = {}
                for combo in chart_point_combinations:
                    chart_id = combo['chart_id']
                    if chart_id not in chart_summary:
                        chart_summary[chart_id] = 0
                    chart_summary[chart_id] += 1                
                for chart_id, count in chart_summary.items():
                    print(f"  {chart_id}: {count} combinations")                
                return chart_point_combinations                
            except Exception as e:
                print(f" Error creating chart-point combinations: {str(e)}")
                return []            
            finally:
                await context.close()
                await browser.close()    
   
    async def apply_enhanced_legend_control(self, page):
        """Apply the enhanced legend control script to the page with robust chart detection"""
        try:
            # Wait for charts to be loaded with multiple attempts
            print("Waiting for charts to load...")

            # Try multiple selectors to find charts
            chart_found = False
            selectors_to_try = [
                 'div.apexcharts-canvas',
                    'svg.apexcharts-svg',
                    'div[class*="apexcharts"]',
                    '[id*="apexcharts"]',
                    'div.diagram-section'
            ]
            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    chart_found = True
                    print(f" Found charts using selector: {selector}")
                    break
                except Exception:
                    continue

            if not chart_found:
                print("No chart elements found with any selector222")
                return False
            await asyncio.sleep(3)  # Give charts time to fully initialize
            page.on("console", lambda msg: print(f"🔔 [Browser Console] {msg.type}: {msg.text}"))
            # Execute the comprehensive ApexCharts detection script
            result = await page.evaluate("""
                (function() {
                    console.log('🔍 ApexCharts Detection Started');

                    const apexChartsAvailable = typeof ApexCharts !== 'undefined';
                    console.log(`📊 ApexCharts: ${apexChartsAvailable ? '✅' : '❌'}`);

                    window.legendControlInitialized = true;
                    window.chartInstances = new Map();

                    const apexContainers = document.querySelectorAll('div[id^="apexcharts"]');
                    const allDiagramSections = document.querySelectorAll('.diagram-section');

                    console.log(`📦 Found: ${apexContainers.length} containers, ${allDiagramSections.length} sections`);

                    let successfullyRegistered = 0;
console.log('apexContainers', apexContainers)
                    // Process ApexCharts containers
                    apexContainers.forEach((container, index) => {
                        let chartInstance = container._chart || container.chart;
                        const hasSVG = container.querySelector('svg') !== null;

                        console.log(`🔧 Debug | Container ID: ${container.id}, chartInstance:`, chartInstance, ', hasSVG:', hasSVG);

                        if (chartInstance?.w?.config) {
                            const chartId = container.id || `apexchart-${index}`;
                            window.chartInstances.set(chartId, {
                                instance: chartInstance,
                                container: container,
                                detectionMethod: 'direct',
                                chartType: 'apexcharts',
                                config: chartInstance.w.config,
                                originalLegendDisplay: chartInstance.w.config.legend?.show ?? true
                            });

                            console.log(`✅ "${chartId}" registered with direct instance`);
                            successfullyRegistered++;

                        } else if (hasSVG) {
                            const chartId = container.id || `apexchart-heuristic-${index}`;
                            let chartInstance;
                            // Method 4: Find by ApexCharts canvas element
                            const apexChartsCanvas = document.querySelector('[id^="apexcharts"]');
                                         console.log("apexChartsCanvas----", apexChartsCanvas)
                            if (apexChartsCanvas) {
                                const canvasId = apexChartsCanvas.id;
                                // Try to get parent container and check for __apexcharts__
                                const parentContainer = apexChartsCanvas.closest('[id^="chart-"]');
                                if (parentContainer && parentContainer.__apexcharts__) {
                                    chartInstance = parentContainer.__apexcharts__;
                                    console.log('Chart instance from canvas parent:', chartInstance);
                                }
                            }

                            window.chartInstances.set(chartId, {
                                instance: chartInstance || null,  // No direct JS handle available
                                container: container,
                                detectionMethod: 'heuristic',
                                chartType: 'apexcharts',
                                config: null,  // Config not available
                                originalLegendDisplay: true  // Assume true
                            });

                            console.log(`⚡ "${chartId}" registered by heuristic (SVG detected)`);
                            successfullyRegistered++;

                        } else {
                            console.log(`❌ Skipping container "${container.id}" — no chart instance or SVG found`);
                        }
                    });

                    // Process diagram sections
                    allDiagramSections.forEach((section, index) => {
                        const apexContainer = section.querySelector('[id^="apexcharts"]');
                        if (apexContainer && !window.chartInstances.has(apexContainer.id)) {
                            let chartInstance = apexContainer._chart || apexContainer.chart;
                            
                            if (chartInstance?.w?.config) {
                                const chartId = section.id || apexContainer.id || `section-chart-${index}`;
                                window.chartInstances.set(chartId, {
                                    instance: chartInstance,
                                    container: apexContainer,
                                    section: section,
                                    detectionMethod: 'section',
                                    chartType: 'apexcharts',
                                    config: chartInstance.w.config,
                                    originalLegendDisplay: chartInstance.w.config.legend?.show ?? true
                                });

                                console.log(`✅ "${chartId}" from section (${chartInstance.w.config.chart?.type || 'unknown'})`);
                                successfullyRegistered++;
                            }
                        }
                    });

                    // Install helper functions
                    window.toggleApexLegend = function(chartId, show) {
                        const chartData = window.chartInstances.get(chartId);
                        if (chartData?.instance?.updateOptions) {
                            try {
                                chartData.instance.updateOptions({ legend: { show: show } });
                                console.log(`🎛️ Legend ${show ? 'shown' : 'hidden'}: ${chartId}`);
                                return true;
                            } catch (e) {
                                console.error(`❌ Legend toggle failed for ${chartId}`);
                                return false;
                            }
                        }
                        return false;
                    };

                    window.hideApexLegend = chartId => window.toggleApexLegend(chartId, false);
                    window.showApexLegend = chartId => window.toggleApexLegend(chartId, true);

                    const chartIds = Array.from(window.chartInstances.keys());
                    console.log(`🎯 Result: ${successfullyRegistered} charts registered`);
                    if (successfullyRegistered > 0) {
                        console.log(`🏷️ IDs: [${chartIds.join(', ')}]`);
                    }

                    return successfullyRegistered > 0;
                })()
            """)
            if result:
                print(" Enhanced legend control applied successfully with comprehensive detection")
                return True
            else:
                print(" No chart instances found even with comprehensive detection")
                return False

        except Exception as e:
            print(f" Failed to apply enhanced legend control: {str(e)}")
            return False
    async def disable_all_legends(self, page):
        """Disable legends for all charts"""
        try:
            await page.evaluate("""
                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        
                        // Disable legend
                        if (!chart.options.plugins) chart.options.plugins = {};
                        if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                        chart.options.plugins.legend.display = false;
                        
                        // Update chart
                        chart.update('none');
                        
                        console.log(`Legend disabled for ${chartId}`);
                    });
                }
            """)
            
            print(" All legends disabled")
            return True
            
        except Exception as e:
            print(f" Failed to disable legends: {str(e)}")
            return False
     
    async def enable_only_target_legend(self, page, chart_id, target_dataset_label):
        """Enable only the target dataset legend and disable all others"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to enable legend for chart: {chart_id}, dataset: {target_dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found, attempting to reinitialize...');
                    return false;
                }}

                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;
                let actualChartId = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        actualChartId = id;
                        console.log(`Found chart with ID: ${{id}}`);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found with any ID variation');
                    console.log('Available charts:', Array.from(window.chartInstances.keys()));
                    return false;
                }}

                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure for', actualChartId);
                    return false;
                }}

                // Find target dataset index with fuzzy matching
                let targetDatasetIndex = -1;
                chart.data.datasets.forEach((dataset, index) => {{
                    const datasetLabel = dataset.label || '';
                    const targetLabel = '{target_dataset_label}';

                    // Exact match first
                    if (datasetLabel === targetLabel) {{
                        targetDatasetIndex = index;
                        return;
                    }}

                    // Fuzzy match (contains or similar)
                    if (datasetLabel.includes(targetLabel) || targetLabel.includes(datasetLabel)) {{
                        targetDatasetIndex = index;
                        console.log(`Fuzzy match found: "${{datasetLabel}}" matches "${{targetLabel}}"`);
                    }}
                }});

                if (targetDatasetIndex === -1) {{
                    console.log('Target dataset not found: {target_dataset_label}');
                    console.log('Available datasets:', chart.data.datasets.map(d => d.label));

                    // Try to find any dataset and use the first one as fallback
                    if (chart.data.datasets.length > 0) {{
                        targetDatasetIndex = 0;
                        console.log('Using first dataset as fallback:', chart.data.datasets[0].label);
                    }} else {{
                        return false;
                    }}
                }}

                try {{
                    // Show only the target dataset
                    chart.data.datasets.forEach((dataset, index) => {{
                        const meta = chart.getDatasetMeta(index);
                        if (meta) {{
                            meta.hidden = (index !== targetDatasetIndex);
                        }}
                    }});

                    // Enable legend but filter to show only target dataset
                    if (!chart.options.plugins) chart.options.plugins = {{}};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {{}};

                    chart.options.plugins.legend.display = true;
                    chart.options.plugins.legend.labels = {{
                        filter: function(legendItem) {{
                            return legendItem.datasetIndex === targetDatasetIndex;
                        }}
                    }};

                    // Update chart
                    chart.update('none');

                    const actualDatasetLabel = chart.data.datasets[targetDatasetIndex].label;
                    console.log('Successfully enabled legend for:', actualDatasetLabel, '(index:', targetDatasetIndex, ')');
                    return true;
                }} catch (error) {{
                    console.error('Error updating chart:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                print(f" Enabled only legend for {target_dataset_label} in chart {chart_id}")
                return True
            else:
                print(f"Failed to enable legend for {target_dataset_label} in chart {chart_id}")
                return False

        except Exception as e:
            print(f" Error enabling legend for {target_dataset_label}: {str(e)}")
            return False

    async def debug_legend_control(self, page):
        """Debug function to check legend control setup"""
        try:
            debug_info = await page.evaluate("""
            (function() {
                const info = {
                    chartInstancesExists: !!window.chartInstances,
                    chartInstancesSize: window.chartInstances ? window.chartInstances.size : 0,
                    chartIds: window.chartInstances ? Array.from(window.chartInstances.keys()) : [],
                    chartDetails: []
                };

                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        info.chartDetails.push({
                            id: chartId,
                            hasInstance: !!chart,
                            hasData: !!(chart && chart.data),
                            datasetCount: chart && chart.data && chart.data.datasets ? chart.data.datasets.length : 0,
                            datasetLabels: chart && chart.data && chart.data.datasets ? chart.data.datasets.map(d => d.label) : []
                        });
                    });
                }

                return info;
            })()
            """)
            
            for chart_detail in debug_info.get('chartDetails', []):
                chart_id = chart_detail.get('id', 'Unknown')
                dataset_count = chart_detail.get('datasetCount', 0)
                dataset_labels = chart_detail.get('datasetLabels', [])                
            return debug_info

        except Exception as e:
            print(f" Error debugging legend control: {str(e)}")
            return None

    async def click_data_point_for_drilldown(self, page, chart_id, point_data):
        """Click on a specific data point to trigger drilldown navigation"""
        try:
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')

            # First try using the pre-calculated screen coordinates
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    print(f"Clicking at pre-calculated coordinates: ({screen_x}, {screen_y})")
                    await page.mouse.click(screen_x, screen_y)
                    await asyncio.sleep(1)
                    return {
                        'success': True,
                        'method': 'pre_calculated_coordinates',
                        'position': {'x': screen_x, 'y': screen_y},
                        'dataset_label': dataset_label,
                        'x_label': x_label
                    }
                except Exception as coord_error:
                    print(f"Pre-calculated coordinates failed: {coord_error}")

            # Fallback to JavaScript-based clicking
            click_result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to click data point for chart: {chart_id}, dataset: {dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found');
                    return {{ success: false, error: 'Chart instances not initialized' }};
                }}

                if (!window.chartInstances.has('{chart_id}')) {{
                    console.log('Chart {chart_id} not found');
                    console.log('Available charts:', Array.from(window.chartInstances.keys()));
                    return {{ success: false, error: 'Chart instance not found' }};
                }}

                const chartData = window.chartInstances.get('{chart_id}');
                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure');
                    return {{ success: false, error: 'Invalid chart data' }};
                }}

                try {{
                    // Find target dataset index by label
                    let targetDatasetIndex = {dataset_index};
                    chart.data.datasets.forEach((dataset, index) => {{
                        if (dataset.label === '{dataset_label}') {{
                            targetDatasetIndex = index;
                        }}
                    }});

                    // Get dataset meta
                    const meta = chart.getDatasetMeta(targetDatasetIndex);
                    if (!meta || !meta.data || !meta.data[{point_index}]) {{
                        console.log('Data point not found at index: {point_index}');
                        return {{ success: false, error: 'Data point not found' }};
                    }}

                    // Get point element and position
                    const pointElement = meta.data[{point_index}];
                    const pointPosition = pointElement.getCenterPoint();
                    const canvas = chart.canvas;

                    // Create click event
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {{
                        clientX: rect.left + pointPosition.x,
                        clientY: rect.top + pointPosition.y,
                        bubbles: true,
                        cancelable: true
                    }});

                    // Dispatch click event
                    canvas.dispatchEvent(clickEvent);

                    console.log('Successfully clicked data point: {x_label} from {dataset_label}');
                    return {{
                        success: true,
                        method: 'javascript_click',
                        position: pointPosition,
                        dataset_label: '{dataset_label}',
                        x_label: '{x_label}'
                    }};

                }} catch (error) {{
                    console.error('Error clicking data point:', error);
                    return {{ success: false, error: error.message }};
                }}
            }})()
            """)

            if click_result.get('success', False):
                print(f" Data point clicked for drilldown: {x_label} from {dataset_label}")
                return click_result
            else:
                print(f"Failed to click data point: {click_result.get('error', 'Unknown error')}")
                return click_result

        except Exception as e:
            print(f" Error clicking data point for drilldown: {str(e)}")
            return {'success': False, 'error': str(e)}

 
    async def process_single_point_task_with_selective_legend(self, page, task, target_month_year):
        """Process a single point task with selective legend control and drilldown"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        try:
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')         
            # Step 1: Enable only the target legend, disable all others
            legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
            if not legend_enabled:
                print(f"{task_id}: Legend control failed, continuing anyway")
            # Wait for legend update
            await asyncio.sleep(1)
            # Debug: Check if the chart area is visible and clickable
            await self.debug_chart_clickability(page, chart_id, point_data)
            # Step 2: Click data point and wait for drilldown navigation
            click_result = None
            navigation_result = None
            # Get current URL before clicking
            initial_url = page.url
            print(f"{task_id}: Current URL before click: {initial_url}")
            # Method 1: Use pre-calculated coordinates (most reliable)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    await page.mouse.click(screen_x, screen_y)                   
                    await asyncio.sleep(3)  # Give time for navigation to start
                    # Check if URL changed
                    current_url = page.url
                    if current_url != initial_url:
                        print(f" {task_id}: Navigation detected to: {current_url}")
                        # Wait for page to fully load
                        try:
                            # await page.wait_for_load_state("networkidle", timeout=10000)
                            await asyncio.sleep(2)
                        except Exception:
                            pass  # Continue even if load state fails

                        click_result = {
                            'success': True,
                            'method': 'pre_calculated_coordinates',
                            'coordinates': {'x': screen_x, 'y': screen_y}
                        }

                        navigation_result = {
                            'success': True,
                            'url': current_url,
                            'navigation_completed': True,
                            'initial_url': initial_url
                        }

                    else:
                        print(f"{task_id}: No navigation detected after coordinate click")
                        # Wait a bit more and check again
                        await asyncio.sleep(2)
                        current_url = page.url

                        if current_url != initial_url:
                            print(f" {task_id}: Delayed navigation detected to: {current_url}")
                            click_result = {'success': True, 'method': 'pre_calculated_coordinates'}
                            navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                        else:
                            print(f" {task_id}: No navigation after coordinate click")
                            click_result = {'success': False, 'error': 'No navigation detected after coordinate click'}
                            navigation_result = {'success': False, 'error': 'No URL change detected'}

                except Exception as coord_error:
                    print(f"{task_id}: Coordinate clicking failed: {coord_error}")
                    click_result = {'success': False, 'error': str(coord_error)}

            # Method 2: Fallback to JavaScript-based clicking
            if not click_result or not click_result.get('success', False):
                print(f"{task_id}: Trying JavaScript-based clicking...")

                try:
                    # Try JavaScript clicking
                    js_click_result = await self.click_data_point_for_drilldown(page, chart_id, point_data)

                    if js_click_result.get('success', False):
                        print(f"{task_id}: JavaScript click executed, waiting for navigation...")

                        # Wait for potential navigation
                        await asyncio.sleep(3)
                        # Check if URL changed
                        current_url = page.url
                        if current_url != initial_url:                           
                            # Wait for page to fully load
                            try:
                                # await page.wait_for_load_state("networkidle", timeout=10000)
                                await asyncio.sleep(2)
                            except Exception:
                                pass
                            click_result = js_click_result
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                        else:                           
                            # Wait a bit more and check again
                            await asyncio.sleep(2)
                            current_url = page.url
                            if current_url != initial_url:
                                print(f" {task_id}: Delayed JavaScript navigation detected to: {current_url}")
                                click_result = js_click_result
                                navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                            else:
                                click_result = {'success': False, 'error': 'JavaScript click no navigation'}
                                navigation_result = {'success': False, 'error': 'No URL change after JavaScript click'}
                    else:
                        click_result = js_click_result
                        navigation_result = {'success': False, 'error': 'JavaScript click failed'}

                except Exception as js_error:
                    print(f" {task_id}: JavaScript clicking failed: {js_error}")
                    click_result = {'success': False, 'error': str(js_error)}
                    navigation_result = {'success': False, 'error': str(js_error)}

            # Method 3: Fallback to simple click with longer wait
            if not click_result or not click_result.get('success', False):
                print(f"{task_id}: Trying simple click with extended wait...")
                try:
                    # Simple click without complex navigation detection
                    await page.mouse.click(screen_x, screen_y)
                    print(f"{task_id}: Simple click executed, waiting longer for navigation...")
                    # Wait longer for navigation
                    await asyncio.sleep(5)
                    # Check URL multiple times
                    for attempt in range(3):
                        current_url = page.url
                        if current_url != initial_url:
                            click_result = {
                                'success': True,
                                'method': 'simple_click_extended_wait',
                                'coordinates': {'x': screen_x, 'y': screen_y}
                            }
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                            break

                        if attempt < 2:  # Don't wait after last attempt
                            await asyncio.sleep(2)

                    if not click_result or not click_result.get('success', False):
                        print(f" {task_id}: Simple click also failed to trigger navigation")
                        click_result = {'success': False, 'error': 'Simple click no navigation'}
                        navigation_result = {'success': False, 'error': 'No URL change after simple click'}
                except Exception as simple_error:
                    print(f" {task_id}: Simple clicking failed: {simple_error}")
                    click_result = {'success': False, 'error': str(simple_error)}
                    navigation_result = {'success': False, 'error': str(simple_error)}

            # Check if any clicking method succeeded
            if not click_result or not click_result.get('success', False):
                error_msg = click_result.get('error', 'All clicking methods failed') if click_result else 'No click result'
                print(f" {task_id}: Failed to click data point: {error_msg}")
                return {
                    'task_id': task_id,
                    'chart_id': chart_id,
                    'error': f"Failed to click data point: {error_msg}",
                    'success': False,
                    'legend_controlled': legend_enabled
                }
            # Check navigation result
            if not navigation_result or not navigation_result.get('success', False):
                print(f"{task_id}: Navigation failed, but click was successful - attempting data extraction anyway")
                # Try to extract data from current page
                current_url = page.url
                navigation_result = {
                    'success': False,
                    'url': current_url,
                    'navigation_completed': False,
                    'error': 'Navigation failed but click succeeded'
                }

            # Step 3: Extract data from the drilldown page
            extracted_data = None
            extraction_success = False
            # Only attempt extraction if navigation was successful
            if navigation_result and navigation_result.get('success', False):
                current_url = page.url
                # Check if we're on the expected drilldown page
                if "AnalyzeData" in current_url and "chartId=drillDown" in current_url:
                    print(f" {task_id}: On correct drilldown page, extracting data...")
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year)
                    # Check extraction success from the nested structure
                    extraction_success = False
                    if extracted_data and extracted_data.get('extraction_data'):
                        extraction_success = extracted_data['extraction_data'].get('success', False)
                    if extraction_success:
                        print(f" {task_id}: Data extraction successful")
                    else:
                        print(f"{task_id}: Data extraction failed or incomplete")
                else:
                    print(f"{task_id}: Not on expected drilldown page. URL: {current_url}")
                    extracted_data = {
                        'extraction_data': {
                            'success': False,
                            'error': f'Unexpected page URL: {current_url}',
                            'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
                        }
                    }
                    extraction_success = False
            else:
                print(f" {task_id}: Skipping data extraction due to navigation failure")
                extracted_data = {
                    'extraction_data': {
                        'success': False,
                        'error': 'Navigation failed, cannot extract data',
                        'navigation_error': navigation_result.get('error', 'Unknown navigation error') if navigation_result else 'No navigation result'
                    }
                }
                extraction_success = False
            # Determine overall success
            overall_success = (
                click_result.get('success', False) and
                navigation_result.get('success', False) and
                extraction_success
            )
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'click_result': click_result,
                'navigation_result': navigation_result,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': overall_success,
                'legend_controlled': legend_enabled,
                'drilldown_url': navigation_result.get('url', '') if navigation_result else '',
                'click_success': click_result.get('success', False) if click_result else False,
                'navigation_success': navigation_result.get('success', False) if navigation_result else False,
                'extraction_success': extraction_success
            }
            print(f" {task_id}: Successfully processed {point_label} from {dataset_label}")
            return result
        except Exception as e:
            print(f" {task_id}: Error processing point: {e}")
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': False
            }
            return error_result

    async def extract_data_from_drilldown_page(self, page, point_data, target_month_year):
        """Extract MUI Grid data from drilldown page focusing on h5 and h6 tags only"""
        max_retries = 3
        retry_delay = 2  # seconds between retries
        
        for attempt in range(max_retries):
            try:
                print(f"Extracting drill-down page data... (Attempt {attempt + 1}/{max_retries})")

                # Wait for page to load completely
                # await page.wait_for_load_state("networkidle", timeout=10000)
                await asyncio.sleep(3)
                extraction_data = {
                    "extraction_timestamp": datetime.now().isoformat(),
                    "page_url": page.url,
                    "mui_grid_data": [],
                    "all_text_content": [],
                    "raw_html_sections": [],
                    "monetary_data": [],
                    "success": False,
                    "error": None,
                    "attempt": attempt + 1
                }
                # Method 1: Look for MUI Grid containers with the specific structure
                mui_grid_selectors = [
                    '.MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3',
                    '.MuiGrid-root.MuiGrid-container',
                    '[class*="MuiGrid-container"]'
                ]
                for selector in mui_grid_selectors:
                    try:
                        grid_containers = await page.query_selector_all(selector)
                        print(f"Found {len(grid_containers)} grid containers with selector: {selector}")
                        for container_index, container in enumerate(grid_containers):
                            if await container.is_visible():
                                # Extract the specific structure we're looking for
                                grid_items = await container.query_selector_all('.MuiGrid-item')
                                print(f"Container {container_index} has {len(grid_items)} grid items")
                                container_data = {
                                    "container_index": container_index,
                                    "selector_used": selector,
                                    "items": []
                                }
                                for item_index, item in enumerate(grid_items):
                                    # Look for h5 (title) and h6 (value) elements
                                    h5_element = await item.query_selector('h5.MuiTypography-root.MuiTypography-h5')
                                    h6_element = await item.query_selector('h6.MuiTypography-root.MuiTypography-subtitle1')
                                    if h5_element and h6_element:
                                        title = (await h5_element.text_content()).strip()
                                        value = (await h6_element.text_content()).strip()
                                        item_data = {
                                            "item_index": item_index,
                                            "title": title,
                                            "value": value,
                                            "html_structure": {
                                                "h5_html": await h5_element.inner_html(),
                                                "h6_html": await h6_element.inner_html()
                                            }
                                        }
                                        container_data["items"].append(item_data)
                                        # print(f"Extracted: {title} - {value}")
                                if container_data["items"]:
                                    extraction_data["mui_grid_data"].append(container_data)
                                    # print(f"Added container {container_index} with {len(container_data['items'])} items")

                    except Exception as selector_error:
                        print(f"Error with selector {selector}: {selector_error}")
                        continue
                # Determine success
                extraction_data["success"] = len(extraction_data["mui_grid_data"]) > 0
                print(f"Extraction success: {extraction_data['success']}")
                print(f"MUI Grid data items: {len(extraction_data['mui_grid_data'])}")
                # If successful, return the result immediately
                if extraction_data["success"]:
                    print(f" Data extraction successful on attempt {attempt + 1}")
                    result = {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': extraction_data,
                        'error': None,
                        'processing_time': 0,
                        'screenshot_path': None
                    }
                    return result

                # If not successful and not the last attempt, wait and retry
                if attempt < max_retries - 1:
                    print(f"Attempt {attempt + 1} failed, retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)                   
                else:
                    print(f" All {max_retries} attempts failed")
                    # Return failure result after all attempts exhausted
                    result = {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': extraction_data,
                        'error': f"Data extraction failed after {max_retries} attempts",
                        'processing_time': 0,
                        'screenshot_path': None
                    }
                    return result

            except Exception as e:
                print(f" Error extracting drill-down page data on attempt {attempt + 1}: {e}")
              
                # If not the last attempt, wait and retry
                if attempt < max_retries - 1:
                    print(f"Attempt {attempt + 1} failed with error, retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                else:
                    print(f" All {max_retries} attempts failed with errors")
                    # Return error result after all attempts exhausted
                    return {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': {
                            "extraction_timestamp": datetime.now().isoformat(),
                            "success": False,
                            "error": str(e),
                            "attempt": attempt + 1
                        },
                        'error': str(e),
                        'processing_time': 0,
                        'screenshot_path': None
                    }
        return {
            'target_month_year': target_month_year,
            'point_data': point_data,
            'click_success': True,
            'navigation_success': True,
            'extraction_data': {
                "extraction_timestamp": datetime.now().isoformat(),
                "success": False,
                "error": "Maximum retries exceeded",
                "attempt": max_retries
            },
            'error': "Maximum retries exceeded",
            'processing_time': 0,
            'screenshot_path': None
        }
   
    async def debug_and_setup_charts(self, page):
        """Debug and manually setup ApexCharts instances"""
        page.on("console", lambda msg: print(f"🔔 [Browser Console] {msg.type}: {msg.text}"))
        try:
            result = await page.evaluate("""
                (function() {
                    console.log('=== APEXCHARTS DEBUG AND SETUP ===');

                    // Check if ApexCharts is available
                    console.log('ApexCharts available:', typeof ApexCharts !== 'undefined');

                    // Find all ApexCharts containers
                    const apexContainers = document.querySelectorAll('.apexcharts-canvas');
                    console.log(`ApexCharts canvas elements found: ${apexContainers.length}`);

                    // Also look for containers with apexcharts div IDs
                    const apexDivs = document.querySelectorAll('div[id^="apexcharts"]');
                    console.log(`ApexCharts div containers found: ${apexDivs.length}`);

                    // Initialize chart instances map
                    if (!window.chartInstances) {
                        window.chartInstances = new Map();
                    }

                    let foundCharts = 0;

                    // Process ApexCharts canvas elements
                    apexContainers.forEach((canvas, index) => {
                        let chartInstance = null;

                        console.log(`Processing ApexCharts canvas ${index}:`);

                        // Method 1: Check if canvas has ApexCharts instance via parent element
                        const parentDiv = canvas.closest('div[id^="apexcharts"]');
                        if (parentDiv && parentDiv.id) {
                            // Try to find the chart instance in ApexCharts global registry
                            if (typeof ApexCharts !== 'undefined' && ApexCharts.getChartByID) {
                                try {
                                    chartInstance = ApexCharts.getChartByID(parentDiv.id);
                                    if (chartInstance) {
                                        console.log(`  - Found via ApexCharts.getChartByID for ${parentDiv.id}`);
                                    }
                                } catch (e) {
                                    console.log(`  - ApexCharts.getChartByID failed:`, e.message);
                                }
                            }

                            // Method 2: Check if the div has __apexcharts__ property
                            if (!chartInstance && parentDiv.__apexcharts__) {
                                chartInstance = parentDiv.__apexcharts__;
                                console.log(`  - Found via parentDiv.__apexcharts__`);
                            }
                        }

                        // Method 3: Check canvas for ApexCharts-specific properties
                        if (!chartInstance && canvas.parentElement) {
                            const container = canvas.parentElement;
                            if (container.__apexcharts__) {
                                chartInstance = container.__apexcharts__;
                                console.log(`  - Found via container.__apexcharts__`);
                            }
                        }

                        // Method 4: Search through global ApexCharts instances if available
                        if (!chartInstance && typeof ApexCharts !== 'undefined' && ApexCharts._chartInstances) {
                            ApexCharts._chartInstances.forEach((instance, key) => {
                                if (instance.el && (instance.el === canvas || instance.el.contains(canvas))) {
                                    chartInstance = instance;
                                    console.log(`  - Found via ApexCharts._chartInstances`);
                                }
                            });
                        }

                        if (chartInstance && chartInstance.w && chartInstance.w.config) {
                            const chartId = `apexchart_${index}`;
                            const config = chartInstance.w.config;
                            
                            window.chartInstances.set(chartId, {
                                instance: chartInstance,
                                canvas: canvas,
                                container: parentDiv || canvas.parentElement,
                                originalLegendDisplay: config.legend?.show ?? true,
                                config: config
                            });

                            console.log(`  - Registered as ${chartId}`);
                            
                            // Log series information
                            if (config.series && Array.isArray(config.series)) {
                                console.log(`    Series count: ${config.series.length}`);
                                config.series.forEach((series, seriesIndex) => {
                                    console.log(`    Series ${seriesIndex}: ${series.name || 'Unnamed'} (${series.data?.length || 0} data points)`);
                                });
                            }

                            foundCharts++;
                        } else {
                            console.log(`  - No valid ApexCharts instance found`);
                        }
                    });

                    // Also check for charts that might be in divs without canvas (like some ApexCharts configurations)
                    apexDivs.forEach((div, index) => {
                        if (div.querySelector('.apexcharts-canvas')) {
                            return; // Already processed above
                        }

                        console.log(`Processing standalone ApexCharts div ${index}: ${div.id}`);
                        
                        let chartInstance = null;
                        
                        if (div.__apexcharts__) {
                            chartInstance = div.__apexcharts__;
                            console.log(`  - Found via div.__apexcharts__`);
                        } else if (typeof ApexCharts !== 'undefined' && ApexCharts.getChartByID) {
                            try {
                                chartInstance = ApexCharts.getChartByID(div.id);
                                if (chartInstance) {
                                    console.log(`  - Found via ApexCharts.getChartByID`);
                                }
                            } catch (e) {
                                console.log(`  - ApexCharts.getChartByID failed:`, e.message);
                            }
                        }

                        if (chartInstance && chartInstance.w && chartInstance.w.config) {
                            const chartId = `apexchart_div_${index}`;
                            const config = chartInstance.w.config;
                            
                            window.chartInstances.set(chartId, {
                                instance: chartInstance,
                                canvas: div.querySelector('svg') || div,
                                container: div,
                                originalLegendDisplay: config.legend?.show ?? true,
                                config: config
                            });

                            console.log(`  - Registered as ${chartId}`);
                            foundCharts++;
                        }
                    });

                    console.log(`=== SETUP COMPLETE: ${foundCharts} ApexCharts registered ===`);
                    
                    // Log all registered charts
                    console.log('Registered chart instances:', Array.from(window.chartInstances.keys()));
                    
                    return foundCharts;
                })()
            """)

            print(f"Debug setup completed: {result} ApexCharts found and registered")
            return result > 0

        except Exception as e:
            print(f"Debug setup failed: {str(e)}")
            return False
    async def click_chart_data_point(self, page, chart_id, point_data):
        """Click on a specific chart data point directly"""
        try:
            # Extract point information
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)            
            # Click on the specific data point
            point_clicked = await page.evaluate(f"""
            (function() {{
                if (window.chartInstances && window.chartInstances.has('{chart_id}')) {{
                    const chartData = window.chartInstances.get('{chart_id}');
                    const chart = chartData.instance;
                    
                    try {{
                        // Find the dataset by label if datasetIndex is not reliable
                        let targetDatasetIndex = {dataset_index};
                        
                        // Verify dataset index by label
                        chart.data.datasets.forEach((dataset, index) => {{
                            if (dataset.label === '{dataset_label}') {{
                                targetDatasetIndex = index;
                            }}
                        }});
                        
                        // Get the dataset meta
                        const meta = chart.getDatasetMeta(targetDatasetIndex);
                        if (!meta || !meta.data || !meta.data[{point_index}]) {{
                            console.log('Data point not found at index: ' + {point_index});
                            return false;
                        }}
                        
                        // Get the data point element
                        const pointElement = meta.data[{point_index}];
                        if (!pointElement) {{
                            console.log('Point element not found');
                            return false;
                        }}                        
                        // Get the chart canvas
                        const canvas = chart.canvas;
                        if (!canvas) {{
                            console.log('Canvas not found');
                            return false;
                        }}                        
                        // Get point position
                        const pointPosition = pointElement.getCenterPoint();                        
                        // Create and dispatch click event
                        const rect = canvas.getBoundingClientRect();
                        const clickEvent = new MouseEvent('click', {{
                            clientX: rect.left + pointPosition.x,
                            clientY: rect.top + pointPosition.y,
                            bubbles: true,
                            cancelable: true
                        }});                        
                        // Dispatch the click event
                        canvas.dispatchEvent(clickEvent);                        
                        console.log('Clicked data point: ' + '{x_label}' + ' from dataset: ' + '{dataset_label}');
                        console.log('Point position:', pointPosition);                        
                        return true;                        
                    }} catch (error) {{
                        console.error('Error clicking data point:', error);
                        return false;
                    }}
                }}
                console.log('Chart instance not found: {chart_id}');
                return false;
            }})()
            """)
            
            if point_clicked:
                print(f" Data point clicked for {chart_id} - {x_label} from {dataset_label}")
                return True
            else:
                print(f"Could not click data point for {chart_id} - {x_label} from {dataset_label}")
                return False               
        except Exception as e:
            print(f"Failed to click data point for {chart_id} - {x_label}: {str(e)}")
            return False
   
    async def process_all_combinations_parallel(self, combinations):
        """Process charts in parallel with multiple browsers, each handling different charts"""

        print(f"🚀 Starting parallel processing of {len(combinations)} charts with 3 browsers")       
        # Organize combinations by chart for easier processing
        chart_combinations = {}
        for combo_idx, combination in enumerate(combinations):
            chart_id = combination.get('chart_id', f'chart_{combo_idx}')
            target_month = combination.get('target_month_year', 'unknown')
            matching_points = combination.get('matching_points', [])

            chart_combinations[chart_id] = {
                'combination_index': combo_idx,
                'chart_info': combination['chart_info'],
                'target_month_year': target_month,
                'matching_points': matching_points,
                'current_point_index': 0
            }
            print(f"**Chart {chart_id}: {target_month} ({(len(matching_points))} points)")

        all_results = []
        target_month_year = TARGET_MONTHS_YEARS
        max_browsers = 3

        # Group charts for parallel processing (3 charts at a time)
        chart_items = list(chart_combinations.items())
        chart_batches = []

        # Create batches of 3 charts each
        for i in range(0, len(chart_items), max_browsers):
            batch = chart_items[i:i + max_browsers]
            chart_batches.append(batch)

        print(f"Processing {len(chart_batches)} batches with up to {max_browsers} browsers per batch")
        # Process each batch of charts in parallel
        for batch_index, chart_batch in enumerate(chart_batches, 1):
            print(f"\n🚀 Starting Batch {batch_index}/{len(chart_batches)} with {len(chart_batch)} charts")

            # Create parallel tasks for this batch
            batch_tasks = []
            for browser_index, (chart_id, chart_data) in enumerate(chart_batch):
                browser_id = f"Browser_{browser_index + 1}"
                print(f"   📋 {browser_id}: Will process Chart {chart_id} - {chart_data['chart_info'].get('chartTitle', 'Unknown')}")
               
                task = asyncio.create_task(
                    self.process_single_chart_parallel(chart_data, target_month_year, browser_id,chart_id)
                )
                batch_tasks.append((browser_id, chart_id, task))
            # Wait for all browsers in this batch to complete
            print(f"Waiting for all {len(batch_tasks)} browsers in batch {batch_index} to complete...")
            for browser_id, chart_id, task in batch_tasks:
                try:
                    result = await task
                    if isinstance(result, list):
                        all_results.extend(result)
                        print(f" {browser_id} completed Chart {chart_id}: {len(result)} results")
                    else:
                        print(f"{browser_id} returned unexpected result type for Chart {chart_id}")
                except Exception as e:
                    print(f" {browser_id} failed processing Chart {chart_id}: {str(e)}")
                    continue
            print(f" Batch {batch_index} completed")
            # Add delay between batches to avoid overwhelming the system
            if batch_index < len(chart_batches):
                print("Waiting before next batch...")
                await asyncio.sleep(3)

        # Process final results
        successful_results = []
        failed_results = []

        for result in all_results:
            if isinstance(result, dict) and result.get('success', False):
                successful_results.append(result)
            else:
                failed_results.append(result)

        print(f"\n🎉 Parallel processing with {max_browsers} browsers completed!")
        print("Summary:")
        print(f"   - Total charts processed: {len(chart_combinations)}")
        print(f"   - Total batches processed: {len(chart_batches)}")
        print(f"   - Total point tasks processed: {len(all_results)}")
        print(f"   - Successful: {len(successful_results)}")
        print(f"   - Failed: {len(failed_results)}")
        print(f"   - Success rate: {(len(successful_results) / len(all_results) * 100):.1f}%" if all_results else "0%")
        print(all_results,"all_results+++++++++++")
        return {
            'successful': successful_results,
            'failed': failed_results,
            'all_results': all_results,
            'total_processed': len(all_results),
            'total_charts': len(chart_combinations),
            'batches_processed': len(chart_batches),
            'success_rate': (len(successful_results) / len(all_results) * 100) if all_results else 0
        }

    async def process_single_chart_parallel(self, chart_data, target_month_year, browser_id,chart_id):
        """Process all points in a single chart in parallel (for use with multiple browsers)"""
        
        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
        matching_points = chart_data.get('matching_points', [])
        print(f"{browser_id}: Processing chart: {chart_title} :(chart-{chart_id}) with {len(matching_points)} points")
        chart_results = []

        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            try:
                # Navigate to ServiceAdvisorPerformance
                print(f"{browser_id}: Navigating to ServiceAdvisorPerformance for chart-{chart_id}")
                await page.goto("https://carriageag-simt.fixedopspc.com/ServiceAdvisorPerformance", timeout=30000)
                # await page.wait_for_load_state("networkidle", timeout=15000)
                await asyncio.sleep(2)

                # Apply enhanced legend control
                legend_setup_success = await self.apply_enhanced_legend_control(page)
                await asyncio.sleep(2)

                if not legend_setup_success:
                    print(f"{browser_id}: Legend control setup failed for chart-{chart_id}, attempting manual setup...")
                    await self.debug_and_setup_charts(page)
                # Debug legend control setup
                await self.debug_legend_control(page)
                # Process each point in this chart sequentially within this browser
                for point_idx, point_data in enumerate(matching_points):
                    point_label = point_data.get('xLabel', f'Point_{point_idx}')
                    dataset_label = point_data.get('datasetLabel', 'Unknown Dataset') 
                    try:
                        # Step 1: Disable ALL legends first                        
                        # await self.disable_all_legends(page)
                        # await asyncio.sleep(1)
                        # Step 2: Enable ONLY the legend for current chart/dataset                        
                        # legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
                        # await asyncio.sleep(2)  # Give more time for chart to update
                        # if legend_enabled:
                        #     print(f" {browser_id}: Legend control successful - ONLY {chart_id} legend is active")
                        # else:
                        #     print(f"{browser_id}: Legend control failed, but continuing with processing")
                        # Step 2.5: Ensure chart is interactive and data points are clickable
                        print(f"{browser_id}: Ensuring chart chart-{chart_id} is interactive after legend control...")
                        
                        await self.ensure_chart_interactivity(page, chart_id)
                        await asyncio.sleep(1)
                        # Step 3: Create task for this point
                        task = {
                            'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                            'chart_id': chart_id,
                            'chart_info': chart_data['chart_info'],
                            'target_month_year': chart_data['target_month_year'],
                            'point_data': point_data,
                            'point_index': point_idx
                        }

                        # Step 4: Process this point with enhanced clicking
                        result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year)
                        if isinstance(result, dict):
                            result['chart_title'] = chart_title
                            result['point_sequence'] = point_idx + 1
                            result['method'] = 'parallel_processing'
                            result['browser_id'] = browser_id
                            result['chart_id'] = chart_id

                        chart_results.append(result)

                        # Log detailed result
                        if result.get('success', False):
                            click_success = result.get('click_success', False)
                            nav_success = result.get('navigation_success', False)
                            extract_success = result.get('extraction_success', False)                           
                            if nav_success:
                                drilldown_url = result.get('drilldown_url', 'Unknown')
                                print(f"   🔗 Drilldown URL: {drilldown_url}")
                        else:
                            error_msg = result.get('error', 'Unknown error')
                            print(f" {browser_id}: Failed point {point_idx + 1}: {point_label} - {error_msg}")

                        # Step 5: Navigate back to ServiceAdvisorPerformance for next point (if not last point)
                        if point_idx < len(matching_points) - 1:
                            print(f"{browser_id}: Navigating back to ServiceAdvisorPerformance for next point")
                            try:
                                await page.goto("https://carriageag-simt.fixedopspc.com/ServiceAdvisorPerformance", timeout=30000)
                                # await page.wait_for_load_state("networkidle", timeout=15000)
                                await asyncio.sleep(2)

                                # Re-apply legend control
                                await self.apply_enhanced_legend_control(page)
                                await asyncio.sleep(1)
                                print(f" {browser_id}: Successfully navigated back to ServiceAdvisorPerformance")
                            except Exception as nav_back_error:
                                print(f" {browser_id}: Failed to navigate back to ServiceAdvisorPerformance: {nav_back_error}")
                                # Try to continue anyway
                                pass

                    except Exception as e:
                        print(f" {browser_id}: Error processing point {point_idx + 1} - {point_label}: {str(e)}")
                        error_result = {
                            'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                            'chart_id': chart_id,
                            'chart_title': chart_title,
                            'point_label': point_label,
                            'error': str(e),
                            'success': False,
                            'method': 'parallel_processing',
                            'browser_id': browser_id,
                            'point_sequence': point_idx + 1
                        }
                        chart_results.append(error_result)

                print(f" {browser_id}: Completed all points for chart: {chart_title}")

            except Exception as e:
                print(f" {browser_id}: Error setting up chart {chart_id}: {str(e)}")
                error_result = {
                    'chart_id': chart_id,
                    'chart_title': chart_title,
                    'error': f"Chart setup failed: {str(e)}",
                    'success': False,
                    'method': 'parallel_processing',
                    'browser_id': browser_id
                }
                chart_results.append(error_result)

            finally:
                try:
                    await context.close()
                    await browser.close()
                    print(f"🔒 {browser_id}: Browser closed for chart {chart_id}")
                except Exception as cleanup_error:
                    print(f"{browser_id}: Cleanup error for {chart_id}: {cleanup_error}")

        return chart_results
  
    async def ensure_chart_interactivity_old(self, page, chart_id):
        """Ensure chart is interactive and data points are clickable after legend control"""
        page.on("console", lambda msg: print(f"🔔 [Browser Console] {msg.type}: {msg.text}"))
        
        try:
            # First ensure we wait for chart to be present in DOM
            try:
                await page.wait_for_selector(f'#chart-{chart_id}, [id*="chart-{chart_id}"], .apexcharts-canvas', timeout=5000)
            except Exception as e:
                print(f"Warning: Timeout waiting for chart selector: {e}")
            
            # Get chart instance
            chart_instance = await self.get_chart_instance_from_dom(page, f"chart-{chart_id}")
            if not chart_instance:
                # Try without chart- prefix as fallback
                chart_instance = await self.get_chart_instance_from_dom(page, chart_id)
                if not chart_instance:
                    print(f"❌ Chart instance not found for chart ID: {chart_id}")
                    return False
                
            result = await page.evaluate(f"""
            (function() {{
                console.log('Ensuring chart interactivity for: {chart_id}');
                
                // Try multiple methods to get chart instance
                let chart = null;
                
                // Method 1: Direct from window.ApexCharts
                if (window.ApexCharts?.charts?.['{chart_id}']) {{
                    chart = window.ApexCharts.charts['{chart_id}'];
                }}
                
                // Method 2: Try with chart- prefix
                if (!chart && window.ApexCharts?.charts?.['chart-{chart_id}']) {{
                    chart = window.ApexCharts.charts['chart-{chart_id}'];
                }}
                let chartElement = null;
                
                // Try to find the chart element first
                const chartContainer = document.getElementById('chart-{chart_id}') || 
                                    document.querySelector('[id*="chart-{chart_id}"]') ||
                                    document.querySelector('.apexcharts-canvas');
                
                if (chartContainer) {{
                    // Look for ApexCharts canvas within container
                    const apexCanvas = chartContainer.querySelector('.apexcharts-canvas') || chartContainer;
                    console.log(apexCanvas)
                    console.log(apexCanvas.id)
                    if (apexCanvas && apexCanvas.id) {{
                        console.log('Found ApexCharts canvas with ID--:', apexCanvas.id);
                        
                        // Extract chart ID from canvas ID (ApexCharts uses format like 'apexcharts' + random)
                        const canvasId = apexCanvas.id;
                        console.log("canvasIdcanvasIdcanvasId",canvasId)
                        // Method 1a: Try ApexCharts.getChartByID with canvas ID
                        try {{ console.log("canvasId",canvasId)
                            chart = ApexCharts.getChartByID(canvasId);
                            console.log(chart)
                            if (chart) {{
                                console.log('Found chart using ApexCharts.getChartByID');
                            }}
                        }} catch (e) {{
                            console.log('getChartByID failed:', e.message);
                        }}
                        
                        // Method 1b: Try getting chart from global ApexCharts exec
                        if (!chart) {{
                            try {{
                                const allCharts = ApexCharts.exec(canvasId, 'getChartByID');
                                if (allCharts) {{
                                    chart = allCharts;
                                    console.log('Found chart using ApexCharts.exec');
                                }}
                            }} catch (e) {{
                                console.log('ApexCharts.exec failed:', e.message);
                            }}
                        }}
                        
                        chartElement = apexCanvas;
                    }}
                }}
                
                // Method 2: Fallback to window.chartInstances (your custom storage)
                if (!chart && window.chartInstances) {{
                    console.log('Trying custom chartInstances storage...');
                    
                    // Try exact match first
                    if (window.chartInstances.has('chart-{chart_id}')) {{
                        const chartData = window.chartInstances.get('chart-{chart_id}');
                        chart = chartData.instance;
                        console.log('Found chart in custom storage with exact ID');
                    }} else {{
                        // Try variations
                        const variations = [
                            'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5',
                            'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'
                        ];
                        
                        for (const id of variations) {{
                            if (window.chartInstances.has(id)) {{
                                const chartData = window.chartInstances.get(id);
                                chart = chartData.instance;
                                console.log('Found chart in custom storage with ID:', id);
                                break;
                            }}
                        }}
                        
                        // Last resort: get first available chart
                        if (!chart && window.chartInstances.size > 0) {{
                            const firstEntry = window.chartInstances.entries().next().value;
                            chart = firstEntry[1].instance;
                            console.log('Using first available chart from custom storage');
                        }}
                    }}
                }}
                
                // Method 3: Search DOM for any ApexCharts instance
                if (!chart) {{
                    console.log('Trying DOM-based chart discovery...');
                    const allCanvases = document.querySelectorAll('.apexcharts-canvas');
                    
                    for (const canvas of allCanvases) {{
                        if (canvas.id) {{
                            try {{
                                const foundChart = ApexCharts.getChartByID(canvas.id);
                                if (foundChart) {{
                                    chart = foundChart;
                                    chartElement = canvas;
                                    console.log('Found chart via DOM search with ID:', canvas.id);
                                    break;
                                }}
                            }} catch (e) {{
                                // Continue searching
                            }}
                        }}
                    }}
                }}
                
                if (!chart) {{
                    console.error('No chart found for interactivity setup');
                    return false;
                }}
                
                console.log('Chart found, setting up interactivity...');
                
                try {{
                    // For ApexCharts, we need to update options and re-render
                    const currentOptions = chart.opts || chart.options || {{}};
                    
                    // Ensure chart options exist
                    if (!currentOptions.chart) currentOptions.chart = {{}};
                    if (!currentOptions.plotOptions) currentOptions.plotOptions = {{}};
                    
                    // Set up interactivity options
                    const interactivityOptions = {{
                        chart: {{
                            ...currentOptions.chart,
                            events: {{
                                ...currentOptions.chart.events,
                                dataPointSelection: function(event, chartContext, config) {{
                                    console.log('Data point clicked:', config);
                                    console.log('Series:', config.seriesIndex, 'Point:', config.dataPointIndex);
                                    // Dispatch custom event for external handling
                                    const customEvent = new CustomEvent('chartDataPointClick', {{
                                        detail: {{
                                            seriesIndex: config.seriesIndex,
                                            dataPointIndex: config.dataPointIndex,
                                            chartId: '{chart_id}',
                                            data: config
                                        }}
                                    }});
                                    document.dispatchEvent(customEvent);
                                }},
                                dataPointMouseEnter: function(event, chartContext, config) {{
                                    console.log('Data point hover:', config);
                                }},
                                markerClick: function(event, chartContext, config) {{
                                    console.log('Marker clicked:', config);
                                }}
                            }},
                            selection: {{
                                enabled: true
                            }},
                            zoom: {{
                                enabled: true
                            }}
                        }},
                        markers: {{
                            ...currentOptions.markers,
                            size: currentOptions.markers?.size || 6,
                            hover: {{
                                size: (currentOptions.markers?.size || 6) + 2
                            }}
                        }},
                        tooltip: {{
                            ...currentOptions.tooltip,
                            enabled: true,
                            shared: false,
                            intersect: false
                        }}
                    }};
                    
                    // Update chart with new options
                    chart.updateOptions(interactivityOptions, false, true);
                    
                    // Also ensure the chart container has proper event handling
                    if (chartElement) {{
                        chartElement.style.cursor = 'pointer';
                        
                        // Add click event listener to the SVG elements
                        const markers = chartElement.querySelectorAll('.apexcharts-marker');
                        markers.forEach((marker, index) => {{
                            marker.style.cursor = 'pointer';
                            marker.addEventListener('click', (e) => {{
                                console.log('Direct marker click:', index);
                                e.stopPropagation();
                            }});
                        }});
                    }}
                    
                    console.log('Chart interactivity setup completed for: {chart_id}');
                    return true;
                    
                }} catch (error) {{
                    console.error('Error setting up chart interactivity:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                print(f"✅ Chart {chart_id} interactivity ensured")
            else:
                print(f"❌ Failed to ensure chart {chart_id} interactivity")

            return result

        except Exception as e:
            print(f"💥 Error ensuring chart interactivity: {str(e)}")
            return False


    async def get_chart_instance_from_dom(self, page, chart_container_id):
        """Extract ApexCharts instance with Playwright-specific enhancements"""
        
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('🔍 Playwright ApexCharts Detection Started...');
                
                // Method 1: ApexCharts Global Registry with Playwright optimizations
                function findInApexChartsRegistry() {{
                    console.log('🔍 Method 1: ApexCharts Global Registry...');
                    
                    if (typeof window.ApexCharts !== 'undefined') {{
                        // Check instances array
                        if (window.ApexCharts.instances?.length > 0) {{
                            console.log('📊 Found', window.ApexCharts.instances.length, 'ApexCharts instances');
                            
                            for (const instance of window.ApexCharts.instances) {{
                                if (instance?.el) {{
                                    const elId = instance.el.id;
                                    const parentId = instance.el.parentElement?.id;
                                    
                                    // Create possible ID variations to check
                                    const targetIds = [
                                        '{chart_container_id}',
                                        'chart-{chart_container_id}',
                                        'apexcharts' + elId,
                                        elId
                                    ];
                                    
                                    const hasTargetId = [elId, parentId].some(id => 
                                        targetIds.some(target => id === target || id?.includes(target))
                                    );
                                    
                                    if (hasTargetId) {{
                                        console.log('✅ Found matching ApexCharts instance!');
                                        // Add Playwright-specific metadata
                                        instance._playwrightMeta = {{
                                            containerId: 'chart-{chart_container_id}',
                                            detectionMethod: 'global_registry',
                                            isRealInstance: true
                                        }};
                                        return instance;
                                    }}
                                }}
                            }}
                        }}
                        console.log(window.ApexCharts)
                        // Check charts object
                        if (window.ApexCharts.charts) {{
                            for (const [chartId, chart] of Object.entries(window.ApexCharts.charts)) {{
                                if (chart?.el) {{
                                    const elId = chart.el.id;
                                    const parentId = chart.el.parentElement?.id;
                                    const hasTargetId = [elId, parentId].some(id => 
                                        id === 'chart-{chart_container_id}' || id?.includes('chart-{chart_container_id}')
                                    );
                                    
                                    if (hasTargetId) {{
                                        console.log('✅ Found chart in ApexCharts.charts!');
                                        chart._playwrightMeta = {{
                                            containerId: 'chart-{chart_container_id}',
                                            detectionMethod: 'charts_object',
                                            chartId: chartId,
                                            isRealInstance: true
                                        }};
                                        return chart;
                                    }}
                                }}
                            }}
                        }}
                    }}
                    
                    return null;
                }}
                
                // Method 2: DOM-based detection with Playwright selectors
                function findViaPlaywrightSelectors() {{
                    console.log('🔍 Method 2: Playwright selector-based detection...');
                    
                    // Multiple selector strategies
                    const selectors = [
                        '#chart-{chart_container_id}',
                        '[id*="chart-{chart_container_id}"]',
                        '.apexcharts-canvas',
                        '[class*="apexcharts-canvas"]',
                        'div:has(> .apexcharts-canvas)',
                        'div:has(> svg[class*="apexcharts"])'
                    ];
                    
                    let container = null;
                    for (const selector of selectors) {{
                        try {{
                            container = document.querySelector(selector);
                            if (container) {{
                                console.log('✅ Found container with selector:', selector);
                                break;
                            }}
                        }} catch (e) {{
                            // Some selectors might not work in all browsers
                            continue;
                        }}
                    }}
                    
                    if (!container) return null;
                    
                    // Check container and all child elements for ApexCharts instance
                    const elementsToCheck = [
                        container,
                        container.parentElement,
                        ...container.querySelectorAll('*')
                    ].filter(Boolean);
                    
                    for (const element of elementsToCheck) {{
                        // Check for ApexCharts instance properties
                        const instanceProps = [
                            '__apexcharts__',
                            '_apexcharts',
                            'apexcharts',
                            'chart',
                            '_chart',
                            '__chart__'
                        ];
                        
                        for (const prop of instanceProps) {{
                            const instance = element[prop];
                            if (instance && typeof instance === 'object' && 
                                instance.render && instance.updateOptions && instance.destroy) {{
                                console.log('✅ Found ApexCharts instance via DOM property:', prop);
                                instance._playwrightMeta = {{
                                    containerId: 'chart-{chart_container_id}',
                                    detectionMethod: 'dom_property',
                                    property: prop,
                                    isRealInstance: true
                                }};
                                return instance;
                            }}
                        }}
                        
                        // Check React/Vue component instances
                        const reactKey = Object.keys(element).find(key => 
                            key.startsWith('__reactInternalInstance') || 
                            key.startsWith('__reactFiber')
                        );
                        
                        if (reactKey) {{
                            const chart = searchComponentTree(element[reactKey]);
                            if (chart) {{
                                chart._playwrightMeta = {{
                                    containerId: 'chart-{chart_container_id}',
                                    detectionMethod: 'react_component',
                                    isRealInstance: true
                                }};
                                return chart;
                            }}
                        }}
                    }}
                    
                    return null;
                }}
                
                // Method 3: Component tree traversal
                function searchComponentTree(node, depth = 0, maxDepth = 8) {{
                    if (!node || depth > maxDepth) return null;
                    
                    try {{
                        // Check various component properties
                        const chartCandidates = [
                            node.memoizedProps?.chart,
                            node.memoizedProps?.chartInstance,
                            node.memoizedState?.chart,
                            node.stateNode?.chart,
                            node.stateNode?.chartInstance,
                            node.pendingProps?.chart
                        ].filter(Boolean);
                        
                        for (const candidate of chartCandidates) {{
                            if (candidate.render && candidate.updateOptions && candidate.destroy) {{
                                console.log('✅ Found chart in component tree at depth', depth);
                                return candidate;
                            }}
                        }}
                        
                        // Recursively search
                        const childResult = searchComponentTree(node.child, depth + 1, maxDepth);
                        if (childResult) return childResult;
                        
                        const siblingResult = searchComponentTree(node.sibling, depth + 1, maxDepth);
                        if (siblingResult) return siblingResult;
                        
                    }} catch (e) {{
                        // Ignore traversal errors
                    }}
                    
                    return null;
                }}
                
                // Method 4: Window object exhaustive search
                function exhaustiveWindowSearch() {{
                    console.log('🔍 Method 4: Exhaustive window search...');
                    
                    const charts = [];
                    
                    // Search window properties
                    const searchObject = (obj, path = 'window', depth = 0, maxDepth = 3) => {{
                        if (!obj || depth > maxDepth || typeof obj !== 'object') return;
                        
                        try {{
                            for (const [key, value] of Object.entries(obj)) {{
                                if (value && typeof value === 'object') {{
                                    // Check if it's an ApexCharts instance
                                    if (value.render && value.updateOptions && value.destroy && value.el) {{
                                        charts.push({{
                                            path: `${{path}}.${{key}}`,
                                            instance: value,
                                            confidence: 'high'
                                        }});
                                    }}
                                    // Check if it looks like ApexCharts
                                    else if (value.constructor?.name === 'ApexCharts') {{
                                        charts.push({{
                                            path: `${{path}}.${{key}}`,
                                            instance: value,
                                            confidence: 'medium'
                                        }});
                                    }}
                                    // Recurse into objects/arrays
                                    else if (depth < maxDepth) {{
                                        searchObject(value, `${{path}}.${{key}}`, depth + 1, maxDepth);
                                    }}
                                }}
                            }}
                        }} catch (e) {{
                            // Ignore access errors
                        }}
                    }};
                    
                    searchObject(window);
                    
                    console.log('📊 Found', charts.length, 'potential charts via window search');
                    
                    // Try to match with our container
                    const container = document.querySelector('#chart-{chart_container_id}') || 
                                    document.querySelector('[id*="chart-{chart_container_id}"]');
                    
                    if (container && charts.length > 0) {{
                        for (const {{ instance, path, confidence }} of charts) {{
                            if (instance.el) {{
                                const elId = instance.el.id;
                                const parentId = instance.el.parentElement?.id;
                                
                                if ([elId, parentId].some(id => 
                                    id === 'chart-{chart_container_id}' || id?.includes('chart-{chart_container_id}') ||
                                    container.contains(instance.el))) {{
                                    console.log('✅ Found matching chart via window search:', path);
                                    instance._playwrightMeta = {{
                                        containerId: 'chart-{chart_container_id}',
                                        detectionMethod: 'window_search',
                                        windowPath: path,
                                        confidence: confidence,
                                        isRealInstance: true
                                    }};
                                    return instance;
                                }}
                            }}
                        }}
                        
                        // If no exact match, return highest confidence chart
                        const highConfidenceChart = charts.find(c => c.confidence === 'high');
                        if (highConfidenceChart) {{
                            console.log('⚠️ Using high-confidence chart as fallback');
                            highConfidenceChart.instance._playwrightMeta = {{
                                containerId: 'chart-{chart_container_id}',
                                detectionMethod: 'window_search_fallback',
                                windowPath: highConfidenceChart.path,
                                isRealInstance: true
                            }};
                            return highConfidenceChart.instance;
                        }}
                    }}
                    
                    return null;
                }}
                
                // Method 5: Create Playwright-optimized wrapper
                function createPlaywrightWrapper() {{
                    console.log('🔧 Creating Playwright-optimized wrapper...');
                    
                    const container = document.querySelector('#chart-{chart_container_id}') || 
                                    document.querySelector('[id*="chart-{chart_container_id}"]') ||
                                    document.querySelector('.apexcharts-canvas');
                                    
                    if (!container) {{
                        console.error('❌ No container found for wrapper creation');
                        return null;
                    }}
                    
                    const svg = container.querySelector('svg');
                    if (!svg) {{
                        console.error('❌ No SVG found in container');
                        return null;
                    }}
                    
                    const wrapper = {{
                        // Standard ApexCharts interface
                        el: container,
                        element: container,
                        svg: svg,
                        
                        // Playwright-specific metadata
                        _playwrightMeta: {{
                            containerId: 'chart-{chart_container_id}',
                            detectionMethod: 'playwright_wrapper',
                            isRealInstance: false,
                            wrapperVersion: 'v2.0'
                        }},
                        
                        // Enhanced element detection
                        getElements: function() {{
                            return {{
                                container: this.el,
                                svg: this.svg,
                                markers: [...this.svg.querySelectorAll('.apexcharts-marker, circle[class*="apexcharts"]')],
                                series: [...this.svg.querySelectorAll('.apexcharts-series')],
                                bars: [...this.svg.querySelectorAll('.apexcharts-bar-area, rect[class*="apexcharts"]')],
                                lines: [...this.svg.querySelectorAll('.apexcharts-line, path[class*="apexcharts-line"]')],
                                areas: [...this.svg.querySelectorAll('.apexcharts-area, path[class*="apexcharts-area"]')],
                                points: [...this.svg.querySelectorAll('circle, rect')],
                                labels: [...this.svg.querySelectorAll('text')],
                                legend: [...this.el.querySelectorAll('.apexcharts-legend-series')],
                                tooltip: this.el.querySelector('.apexcharts-tooltip')
                            }};
                        }},
                        
                        // Playwright-optimized updateOptions
                        updateOptions: function(options, redrawPaths = true, animate = true) {{
                            console.log('🔧 Playwright wrapper updateOptions:', options);
                            
                            const promise = new Promise((resolve) => {{
                                if (options.chart?.events) {{
                                    this.setupPlaywrightEvents(options.chart.events);
                                }}
                                
                                if (options.plotOptions) {{
                                    this.handlePlotOptions(options.plotOptions);
                                }}
                                
                                if (options.markers) {{
                                    this.updateMarkers(options.markers);
                                }}
                                
                                if (options.colors) {{
                                    this.updateColors(options.colors);
                                }}
                                
                                // Simulate async behavior like real ApexCharts
                                setTimeout(() => resolve(this), 10);
                            }});
                            
                            return promise;
                        }},
                        
                        // Enhanced event setup for Playwright
                        setupPlaywrightEvents: function(events) {{
                            const elements = this.getElements();
                            
                            if (events.dataPointSelection) {{
                                const interactive = [
                                    ...elements.markers,
                                    ...elements.bars,
                                    ...elements.points
                                ].filter(el => el && el.getBoundingClientRect().width > 0);
                                
                                interactive.forEach((el, index) => {{
                                    // Make elements Playwright-clickable
                                    el.style.pointerEvents = 'auto';
                                    el.style.cursor = 'pointer';
                                    el.setAttribute('data-playwright-clickable', 'true');
                                    el.setAttribute('data-chart-point-index', index);
                                    
                                    // Remove existing handlers
                                    el.onclick = null;
                                    
                                    // Add new handler
                                    el.addEventListener('click', (e) => {{
                                        e.preventDefault();
                                        e.stopPropagation();
                                        
                                        const seriesIndex = parseInt(el.getAttribute('rel') || '0');
                                        const dataPointIndex = parseInt(el.getAttribute('j') || index.toString());
                                        
                                        console.log('📊 Playwright click:', {{ seriesIndex, dataPointIndex }});
                                        
                                        events.dataPointSelection(e, this, {{
                                            seriesIndex,
                                            dataPointIndex,
                                            selectedDataPoints: [[dataPointIndex]]
                                        }});
                                    }}, {{ passive: false }});
                                }});
                                
                                console.log('✅ Setup Playwright events on', interactive.length, 'elements');
                            }}
                        }},
                        
                        // Standard ApexCharts methods
                        render: function() {{ return Promise.resolve(this); }},
                        destroy: function() {{ return true; }},
                        toggleSeries: function() {{ return this; }},
                        showSeries: function() {{ return this; }},
                        hideSeries: function() {{ return this; }},
                        
                        // Playwright automation helpers
                        playwrightClick: function(seriesIndex, dataPointIndex) {{
                            const elements = this.getElements();
                            const clickable = [...elements.markers, ...elements.bars, ...elements.points];
                            
                            const target = clickable.find(el => {{
                                const elSeriesIndex = parseInt(el.getAttribute('rel') || '0');
                                const elDataIndex = parseInt(el.getAttribute('j') || '0');
                                return elSeriesIndex === seriesIndex && elDataIndex === dataPointIndex;
                            }}) || clickable[dataPointIndex];
                            
                            if (target) {{
                                target.dispatchEvent(new MouseEvent('click', {{
                                    bubbles: true,
                                    cancelable: true,
                                    clientX: target.getBoundingClientRect().left + target.getBoundingClientRect().width / 2,
                                    clientY: target.getBoundingClientRect().top + target.getBoundingClientRect().height / 2
                                }}));
                                return true;
                            }}
                            return false;
                        }},
                        
                        getPlaywrightSelectors: function() {{
                            const elements = this.getElements();
                            return {{
                                container: '#chart-{chart_container_id}',
                                markers: elements.markers.map((el, i) => `#chart-{chart_container_id} .apexcharts-marker:nth-child(${{i + 1}})`),
                                bars: elements.bars.map((el, i) => `#chart-{chart_container_id} .apexcharts-bar-area:nth-child(${{i + 1}})`),
                                clickableElements: '[data-playwright-clickable="true"]'
                            }};
                        }},
                        
                        getDataPointInfo: function() {{
                            const elements = this.getElements();
                            return [...elements.markers, ...elements.bars, ...elements.points].map((el, index) => ({{
                                element: el,
                                seriesIndex: parseInt(el.getAttribute('rel') || '0'),
                                dataPointIndex: parseInt(el.getAttribute('j') || index.toString()),
                                selector: `[data-chart-point-index="${{index}}"]`,
                                boundingBox: el.getBoundingClientRect(),
                                isVisible: el.getBoundingClientRect().width > 0 && el.getBoundingClientRect().height > 0
                            }}));
                        }}
                    }};
                    
                    return wrapper;
                }}
                
                // Execute detection methods in order of preference
                console.log('🚀 Starting Playwright ApexCharts detection...');
                
                let chartInstance = null;
                
                // Try to find real ApexCharts instance first
                chartInstance = findInApexChartsRegistry() || 
                              findViaPlaywrightSelectors() || 
                              exhaustiveWindowSearch();
                console.log(chartInstance._playwrightMeta)
                if (chartInstance) {{
                    console.log('✅ Found real ApexCharts instance via:', chartInstance._playwrightMeta?.detectionMethod);
                    return chartInstance;
                }}
                
                // Create Playwright-optimized wrapper as fallback
                chartInstance = createPlaywrightWrapper();
                
                if (chartInstance) {{
                    console.log('🔧 Created Playwright wrapper for automation');
                }} else {{
                    console.error('❌ Failed to create chart interface');
                }}
                
                return chartInstance;
            }})()
            """)

            return result

        except Exception as e:
            print(f"💥 Error in Playwright chart extraction: {str(e)}")
            return None
    
    async def ensure_chart_interactivity(self, page, chart_id):
        """Ensure chart is interactive and data points are clickable after legend control"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Ensuring chart interactivity for: {chart_id}');

                // Check if this is an ApexCharts chart with chartContainerId class
                const chartContainer = document.querySelector('#chartContainerId-{chart_id}');
                if (chartContainer) {{
                    console.log('Found ApexCharts container for chartContainerId-{chart_id}');

                    try {{
                        // For ApexCharts, ensure tooltips and hover events are enabled
                        const apexCanvas = chartContainer.querySelector('.apexcharts-canvas');
                        if (apexCanvas) {{
                            // Enable pointer events on all chart elements
                            const chartPoints = chartContainer.querySelectorAll('.apexcharts-marker');
                            chartPoints.forEach(point => {{
                                point.style.pointerEvents = 'all';
                                point.style.cursor = 'pointer';
                            }});

                            // Ensure series are interactive
                            const series = chartContainer.querySelectorAll('.apexcharts-series');
                            series.forEach(serie => {{
                                serie.style.pointerEvents = 'all';
                            }});

                            // Ensure the entire canvas is interactive
                            apexCanvas.style.pointerEvents = 'all';

                            // Enable interaction on the SVG root
                            const svgRoot = apexCanvas.querySelector('svg');
                            if (svgRoot) {{
                                svgRoot.style.pointerEvents = 'all';
                            }}

                            console.log('ApexCharts interactivity ensured for: {chart_id}');
                            return true;
                        }}
                    }} catch (error) {{
                        console.error('Error ensuring ApexCharts interactivity:', error);
                        return false;
                    }}
                }}

                // Fallback to original Chart.js logic if not ApexCharts
                if (!window.chartInstances) {{
                    console.log('No chart instances found');
                    return false;
                }}

                // Try multiple chart ID variations for Chart.js
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found for interactivity check');
                    return false;
                }}

                const chart = chartData.instance;

                try {{
                    // Ensure chart is responsive to events
                    if (chart.options) {{
                        if (!chart.options.interaction) chart.options.interaction = {{}};
                        chart.options.interaction.intersect = false;
                        chart.options.interaction.mode = 'point';

                        if (!chart.options.onHover) {{
                            chart.options.onHover = function(event, elements) {{
                                console.log('Chart hover detected:', elements.length, 'elements');
                            }};
                        }}

                        if (!chart.options.onClick) {{
                            chart.options.onClick = function(event, elements) {{
                                console.log('Chart click detected:', elements.length, 'elements');
                                if (elements.length > 0) {{
                                    const element = elements[0];
                                    console.log('Clicked element:', element);
                                }}
                            }};
                        }}
                    }}

                    // Force chart update to apply interaction settings
                    chart.update('none');

                    console.log('Chart interactivity ensured for: {chart_id}');
                    return true;
                }} catch (error) {{
                    console.error('Error ensuring chart interactivity:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                print(f"Chart {chart_id} interactivity ensured")
            else:
                print(f"Failed to ensure chart {chart_id} interactivity")

            return result

        except Exception as e:
            print(f"Error ensuring chart interactivity: {str(e)}")
            return False
    async def setup_automation_event_listener(self, page, chart_id):
        """Set up event listener for automation to detect chart clicks"""
        
        await page.evaluate(f"""
            // Remove any existing listener for this chart
            document.removeEventListener('chartAutomationClick', window._chartListener_{chart_id});
            
            // Set up new listener
            window._chartListener_{chart_id} = function(event) {{
                if (event.detail.chartId === '{chart_id}') {{
                    console.log('🤖 Automation detected chart click:', event.detail);
                    
                    // Store click data for automation to retrieve
                    if (!window.automationChartClicks) window.automationChartClicks = [];
                    window.automationChartClicks.push(event.detail);
                    
                    // Set flag for automation polling
                    window.lastChartClick = event.detail;
                }}
            }};
            
            document.addEventListener('chartAutomationClick', window._chartListener_{chart_id});
            console.log('✅ Automation event listener set up for {chart_id}');
        """)


    async def click_chart_data_point(self, page, chart_container_id, series_index=0, data_point_index=0):
        """Programmatically click a specific data point for automation"""
        
        try:
            result = await page.evaluate(f"""
            (function() {{
                const container = document.getElementById('{chart_container_id}') || 
                                document.querySelector('[id*="{chart_container_id}"]');
                
                if (!container) {{
                    console.error('Container not found');
                    return false;
                }}
                
                // Find the specific marker to click
                const markers = container.querySelectorAll('.apexcharts-marker');
                
                // Find marker with matching series and data point indices
                let targetMarker = null;
                for (const marker of markers) {{
                    const markerSeriesIndex = parseInt(marker.getAttribute('rel') || '0');
                    const markerDataIndex = parseInt(marker.getAttribute('j') || '0');
                    
                    if (markerSeriesIndex === {series_index} && markerDataIndex === {data_point_index}) {{
                        targetMarker = marker;
                        break;
                    }}
                }}
                
                if (!targetMarker && markers.length > {data_point_index}) {{
                    // Fallback: use index-based selection
                    targetMarker = markers[{data_point_index}];
                }}
                
                if (targetMarker) {{
                    console.log('🎯 Clicking marker:', targetMarker);
                    
                    // Simulate click
                    const clickEvent = new MouseEvent('click', {{
                        bubbles: true,
                        cancelable: true,
                        view: window
                    }});
                    
                    targetMarker.dispatchEvent(clickEvent);
                    return true;
                }} else {{
                    console.error('Target marker not found');
                    return false;
                }}
            }})()
            """)
            
            return result
            
        except Exception as e:
            print(f"💥 Error clicking chart data point: {str(e)}")
            return False


    async def get_last_chart_click(self, page):
        """Get the last chart click data for automation verification"""
        
        try:
            result = await page.evaluate("""
                return window.lastChartClick || null;
            """)
            return result
        except Exception as e:
            print(f"💥 Error getting last chart click: {str(e)}")
            return None
    # Helper method to listen for chart events
    async def setup_chart_event_listeners(self, page):
        """Set up global event listeners for chart interactions"""
        await page.evaluate("""
            // Set up global chart event listeners
            document.addEventListener('chartDataPointClick', function(event) {
                console.log('Global chart event received:', event.detail);
                // You can handle chart clicks here - send to backend, update UI, etc.
                
                // Example: Store click data
                if (!window.chartClickHistory) window.chartClickHistory = [];
                window.chartClickHistory.push({
                    timestamp: new Date(),
                    chartId: event.detail.chartId,
                    seriesIndex: event.detail.seriesIndex,
                    dataPointIndex: event.detail.dataPointIndex
                });
            });
        
        // Optional: Set up mutation observer to handle dynamically added charts
        const chartObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && node.querySelector && node.querySelector('.apexcharts-canvas')) {
                        console.log('New chart detected in DOM');
                        // Could trigger interactivity setup for new charts
                    }
                });
            });
        });
        
        chartObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    """)


    # Alternative method using direct ApexCharts API
    async def ensure_apex_chart_clicks_simple(self, page, chart_id):
        """Simplified method focusing on ApexCharts built-in methods"""
        try:
            result = await page.evaluate(f"""
                (function() {{
                    // Find all ApexCharts instances on page
                    const allChartElements = document.querySelectorAll('.apexcharts-canvas');
                    console.log('Found', allChartElements.length, 'chart canvases');
                    
                    let targetChart = null;
                    
                    // Try to find our specific chart
                    for (const element of allChartElements) {{
                        const chartId = element.id;
                        if (chartId) {{
                            try {{
                                const chart = ApexCharts.getChartByID(chartId);
                                if (chart) {{
                                    // Check if this is our target chart (you might need to adjust this logic)
                                    const container = element.closest('[id*="{chart_id}"]');
                                    if (container || element.closest('.diagram-section')) {{
                                        targetChart = chart;
                                        console.log('Target chart found with ID:', chartId);
                                        break;
                                    }}
                                }}
                            }} catch (e) {{
                                console.log('Could not get chart for ID:', chartId);
                            }}
                        }}
                    }}
                    
                    if (!targetChart && allChartElements.length > 0) {{
                        // Fallback: use first chart found
                        const firstElement = allChartElements[0];
                        try {{
                            targetChart = ApexCharts.getChartByID(firstElement.id);
                            console.log('Using first available chart');
                        }} catch (e) {{
                            console.log('Could not get first chart');
                        }}
                    }}
                
                    if (!targetChart) {{
                        console.error('No ApexCharts instance found');
                        return false;
                    }}
                    
                    // Simple update to enable clicks
                    targetChart.updateOptions({{
                        chart: {{
                            events: {{
                                dataPointSelection: function(event, chartContext, config) {{
                                    console.log('✅ Chart click successful!', config);
                                    alert(`Clicked: Series ${{config.seriesIndex}}, Point ${{config.dataPointIndex}}`);
                                }}
                            }}
                        }},
                        markers: {{
                            size: 6,
                            hover: {{ size: 8 }}
                        }}
                    }});
                    
                    return true;
                }})()
            """)
            
            return result
            
        except Exception as e:
            print(f"Error in simple chart click setup: {str(e)}")
            return False

    async def process_single_point_task_with_enhanced_clicking(self, page, task, target_month_year):
        """Process a single point task with enhanced clicking methods for drilldown"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']

        try:
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            # Get current URL before clicking
            initial_url = page.url
            print(f"{task_id}: Current URL before click: {initial_url}")

            # Method 1: Try Chart.js event-based clicking first
            click_result = await self.try_chartjs_event_click(page, chart_id, point_data)
            navigation_result = None

            if click_result.get('success', False):
                print(f" {task_id}: Chart.js event click successful, checking for navigation...")

                # Wait for navigation
                await asyncio.sleep(3)
                current_url = page.url

                if current_url != initial_url:
                    print(f" {task_id}: Navigation successful to: {current_url}")
                    navigation_result = {'success': True, 'url': current_url, 'method': 'chartjs_event'}
                else:
                    print(f"{task_id}: Chart.js event click didn't trigger navigation")
                    click_result = {'success': False, 'error': 'No navigation after Chart.js event click'}

            # Method 2: Fallback to coordinate clicking if Chart.js event didn't work
            if not click_result or not click_result.get('success', False):
                print(f"{task_id}: Trying coordinate-based clicking...")

                screen_x = point_data.get('screenX')
                screen_y = point_data.get('screenY')

                if screen_x and screen_y:
                    # Try multiple click methods
                    for click_method in ['single', 'double', 'with_delay']:
                        print(f"{task_id}: Trying {click_method} click at ({screen_x}, {screen_y})")

                        if click_method == 'single':
                            await page.mouse.click(screen_x, screen_y)
                        elif click_method == 'double':
                            await page.mouse.click(screen_x, screen_y, click_count=2)
                        elif click_method == 'with_delay':
                            await page.mouse.move(screen_x, screen_y)
                            await asyncio.sleep(0.5)
                            await page.mouse.down()
                            await asyncio.sleep(0.1)
                            await page.mouse.up()

                        # Check for navigation after each method
                        await asyncio.sleep(3)
                        current_url = page.url

                        if current_url != initial_url:
                            print(f" {task_id}: {click_method} click triggered navigation to: {current_url}")
                            click_result = {'success': True, 'method': f'coordinate_{click_method}'}
                            navigation_result = {'success': True, 'url': current_url, 'method': f'coordinate_{click_method}'}
                            break
                        else:
                            print(f"{task_id}: {click_method} click didn't trigger navigation")

                    if not navigation_result or not navigation_result.get('success', False):
                        click_result = {'success': False, 'error': 'All coordinate click methods failed'}
                        navigation_result = {'success': False, 'error': 'No navigation with any click method'}
                else:
                    click_result = {'success': False, 'error': 'No coordinates available'}
                    navigation_result = {'success': False, 'error': 'No coordinates for clicking'}

            # Continue with data extraction if navigation was successful
            if navigation_result and navigation_result.get('success', False):
                current_url = page.url
                print(f"{task_id}: Attempting data extraction from: {current_url}")

                if "AnalyzeData" in current_url and "chartId=drillDown" in current_url:
                    print(f" {task_id}: On correct drilldown page, extracting data...")
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year)
                    extraction_success = False
                    if extracted_data and extracted_data.get('extraction_data'):
                        extraction_success = extracted_data['extraction_data'].get('success', False)

                    if extraction_success:
                        print(f" {task_id}: Data extraction successful")
                    else:
                        print(f"{task_id}: Data extraction failed or incomplete")
                else:
                    print(f"{task_id}: Not on expected drilldown page. URL: {current_url}")
                    extracted_data = {
                        'extraction_data': {
                            'success': False,
                            'error': f'Unexpected page URL: {current_url}',
                            'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
                        }
                    }
                    extraction_success = False
            else:
                print(f" {task_id}: Skipping data extraction due to navigation failure")
                extracted_data = {
                    'extraction_data': {
                        'success': False,
                        'error': 'Navigation failed, cannot extract data',
                        'navigation_error': navigation_result.get('error', 'Unknown navigation error') if navigation_result else 'No navigation result'
                    }
                }
                extraction_success = False
            # Determine overall success
            overall_success = (
                click_result.get('success', False) and
                navigation_result.get('success', False) and
                extraction_success
            )
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'click_result': click_result,
                'navigation_result': navigation_result,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': overall_success,
                'legend_controlled': True,  # We know legend control was attempted
                'drilldown_url': navigation_result.get('url', '') if navigation_result else '',
                'click_success': click_result.get('success', False) if click_result else False,
                'navigation_success': navigation_result.get('success', False) if navigation_result else False,
                'extraction_success': extraction_success
            }
            print(f" {task_id}: Enhanced processing completed for {point_label} from {dataset_label}")
            return result

        except Exception as e:
            print(f" {task_id}: Error in enhanced processing: {e}")
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': True
            }
            return error_result

    async def try_chartjs_event_click(self, page, chart_id, point_data):
        """Try to trigger ApexCharts click event programmatically"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                try {{
                    console.log('Attempting ApexCharts click for chart: {chart_id}');
                    
                    // First find the chart container
                    const containerSelectors = [
                        `#chartContainerId-{chart_id} .apexcharts-canvas svg`
                    ];
                    
                    let container = null;
                    for (const selector of containerSelectors) {{
                        const elements = document.querySelectorAll(selector);
                        for (const el of elements) {{
                            if (el.querySelector('.apexcharts-series')) {{
                                container = el;
                                console.log('Found chart container:', selector);
                                break;
                            }}
                        }}
                        if (container) break;
                    }}
                    
                    if (!container) {{
                        console.log('Chart container not found');
                        return {{ success: false, error: 'Chart container not found' }};
                    }}

                    // Get chart instance
                    let chartInstance = null;
                    const chartId = container.id;
                    
                    if (window.ApexCharts) {{
                        chartInstance = window.ApexCharts.getChartByID(chartId);
                        console.log('Found ApexCharts instance:', chartId);
                    }}

                    if (!chartInstance) {{
                        console.log('No ApexCharts instance found');
                        return {{ success: false, error: 'Chart instance not found' }};
                    }}

                    // Get point data
                    const pointIndex = {point_data.get('pointIndex', 0)};
                    const seriesIndex = {point_data.get('seriesIndex', 0)};
                    
                    // Find the specific point marker
                    const markerSelector = `.apexcharts-series[rel="${{seriesIndex}}"] .apexcharts-marker[j="${{pointIndex}}"]`;
                    const targetMarker = container.querySelector(markerSelector);

                    if (!targetMarker) {{
                        console.log('Target marker not found:', markerSelector);
                        return {{ success: false, error: 'Target marker not found' }};
                    }}

                    // Get marker position and trigger click
                    const rect = targetMarker.getBoundingClientRect();
                    const clickX = rect.left + (rect.width / 2);
                    const clickY = rect.top + (rect.height / 2);

                    // Trigger DOM click event
                    const clickEvent = new MouseEvent('click', {{
                        bubbles: true,
                        cancelable: true,
                        view: window,
                        clientX: clickX,
                        clientY: clickY
                    }});
                    targetMarker.dispatchEvent(clickEvent);

                    // Trigger ApexCharts internal event
                    chartInstance.eventList.dataPointSelection({{
                        type: 'dataPointSelection',
                        target: targetMarker,
                        chartContext: chartInstance,
                        dataPointIndex: pointIndex,
                        seriesIndex: seriesIndex,
                        x: clickX,
                        y: clickY
                    }});
                    
                    console.log('ApexCharts click executed successfully');
                    return {{ 
                        success: true, 
                        method: 'apexcharts_event',
                        position: {{ x: clickX, y: clickY }},
                        chartId: chartId,
                        markerFound: true
                    }};
                }} catch (error) {{
                    console.error('Error in ApexCharts click:', error);
                    return {{ success: false, error: error.message }};
                }}
            }})()
            """)
            return result
        except Exception as e:
            print(f"Error in ApexCharts click: {str(e)}")
            return {'success': False, 'error': str(e)}

    async def debug_chart_clickability(self, page, chart_id, point_data):
        """Debug function to check if chart area is clickable"""
        try:
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')

            if screen_x and screen_y:
                # Check what element is at the click coordinates
                element_info = await page.evaluate(f"""
                    () => {{
                        const element = document.elementFromPoint({screen_x}, {screen_y});
                        if (element) {{
                            return {{
                                tagName: element.tagName,
                                className: element.className,
                                id: element.id,
                                isCanvas: element.tagName === 'CANVAS',
                                boundingRect: element.getBoundingClientRect(),
                                visible: element.offsetParent !== null
                            }};
                        }}
                        return null;
                    }}
                """)

                if element_info:
                    print(f"Debug - Element at ({screen_x}, {screen_y}):")
                    print(f"   Tag: {element_info.get('tagName', 'Unknown')}")
                    print(f"   Class: {element_info.get('className', 'None')}")
                    print(f"   Canvas: {element_info.get('isCanvas', False)}")
                    print(f"   Visible: {element_info.get('visible', False)}")
                else:
                    print(f"Debug - No element found at coordinates ({screen_x}, {screen_y})")

        except Exception as e:
            print(f"Debug function failed: {e}")
                
        
    async def run_complete_process(self):
        """Run the complete chart processing workflow with enhanced legend control"""
        print("🚀 Starting complete chart processing workflow with enhanced legend control...")

        try:
            # Step 1: Create chart-point combinations            
            combinations = await self.create_chart_point_combinations(TARGET_MONTHS_YEARS)            
            if not combinations:
                print(" No chart-point combinations found")
                return None
            print(f" Created {len(combinations)} chart-point combinations")
            # Step 2: Process all combinations in parallel with 3 browsers
            print("Step 2: Processing combinations in parallel with 3 browsers...")
            results = await self.process_all_combinations_parallel(combinations)
            if not results:
                print(" No results from processing")
                return None
            # Step 3: Save results
            print("Step 3: Saving results...")
            await self.save_results(results)
            print(" Complete chart processing workflow finished successfully")
            print("Final Summary:")
            print(f"   - Total combinations processed: {len(combinations)}")
            print(f"- Total tasks completed: {results.get('total_processed', 0)}")
            print(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
            return results
        except Exception as e:
            print(f" Error in complete process: {e}")            
            traceback.print_exc()
            return None
    
    async def save_results(self, results):
        """Save processing results to files with enhanced formatting"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # Create results directory if it doesn't exist
            results_dir = "chart_processing_results"
            os.makedirs(results_dir, exist_ok=True)

            # Save all results (combined)
            if results.get('all_results'):
                all_results_file = os.path.join(results_dir, "chart_processing_all.json")
                with open(all_results_file, 'w', encoding='utf-8') as f:
                    json.dump(results['all_results'], f, indent=2, default=str, ensure_ascii=False)
                print(f"All results saved to {all_results_file}")

            # Save enhanced summary
            summary = {
                'timestamp': timestamp,
                'processing_method': 'enhanced_legend_control_parallel',
                'target_months_years': TARGET_MONTHS_YEARS,
                'max_concurrent_browsers': MAX_CONCURRENT_BROWSERS,
                'total_processed': results.get('total_processed', 0),
                'total_charts': results.get('total_charts', 0),
                'rounds_processed': results.get('rounds_processed', 0),
                'successful_count': len(results.get('successful', [])),
                'failed_count': len(results.get('failed', [])),
                'success_rate': results.get('success_rate', 0),
                'processing_statistics': {
                    'charts_with_data': results.get('total_charts', 0),
                    'average_points_per_chart': results.get('total_processed', 0) / results.get('total_charts', 1) if results.get('total_charts', 0) > 0 else 0,
                    'total_rounds': results.get('rounds_processed', 0)
                }
            }       
        except Exception as e:
            print(f" Error saving results: {e}")            
            traceback.print_exc()

# Main execution
async def main():
    """Main function to run the enhanced chart processing with legend control"""

    start_time = time.time()
    print(f"Start Time: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    # Initialize components
    auth_manager = AuthManager()
    processor = MultiChartParallelProcessor(
        max_browsers=MAX_CONCURRENT_BROWSERS,  # Parallel processing uses 3 browsers
        auth_manager=auth_manager
    )
    print("   - Processing mode: Parallel (3 browsers, different charts)")
    print("   - Max concurrent browsers: 3")
    print(f"   - Target months/years: {TARGET_MONTHS_YEARS}")
    print(f"   - Browser timeout: {BROWSER_TIMEOUT}ms")
    print("=" * 80)
    # step1:
    # Check if we have valid authentication
    if not auth_manager.load_auth_state():
        print("No valid authentication found. Setting up authentication...")
        async with async_playwright() as playwright:
            success = await auth_manager.setup_authentication(playwright)
            if not success:
                print(" Authentication setup failed. Exiting.")
                return
    else:
        print(" Valid authentication found, proceeding with processing...")
    # Run the complete processing workflow
    print("\n Starting parallel chart processing workflow ...")
    #step2:
    results = await processor.run_complete_process()    
    if results:
        print("\n" + "=" * 80)
        print("Parallel processing with 3 browsers completed successfully!")
        print("Final Results:")
        print(f"   - Total tasks processed: {results.get('total_processed', 0)}")
        print(f"   - Charts processed: {results.get('total_charts', 0)}")
        print(f"   - Batches processed: {results.get('batches_processed', 0)}")
        print(f"   - Successful tasks: {len(results.get('successful', []))}")
        print(f"   - Failed tasks: {len(results.get('failed', []))}")
        print(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
        print("=" * 80)
        # Additional statistics
        if results.get('successful'):
            print(f" Parallel processing completed with {len(results['successful'])} successful extractions")
        if results.get('failed'):
            print(f" {len(results['failed'])} tasks failed - check failed results file for details")
     
        # Generate final comparison report
        print("\n" + "=" * 80)
        print("GENERATING FINAL UI vs DB COMPARISON REPORT")
        print("=" * 80)
        try:
            #step4:
            db_calculation()
            # step5:           
            db_json_path='chart_processing_results/db_calculated_value.json'
            ui_json_path='chart_processing_results/chart_processing_all.json'
            compare_advisor_metrics_results(ui_json_path, db_json_path)
            end_time = time.time()-start_time
            print(f"End Time: {end_time}")
        except Exception as comparison_error:
            print(f" Error generating comparison report: {comparison_error}")            
            traceback.print_exc()
    else:
        print(" Parallel processing failed - check logs for details")

def run_validation():
    """Run the all process"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(" Processing interrupted by user")
    except Exception as e:
        print(f"\n Unexpected error: {e}")        
        traceback.print_exc()

if __name__ == "__main__":
    run_validation()

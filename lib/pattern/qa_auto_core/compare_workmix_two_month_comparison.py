"""
Simplified three-way comparison: UI, Extracted, and DB values with single match column
Includes others_summary comparison with OTHER opcode
"""

import json
import csv
import os
import traceback
from dotenv import load_dotenv
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import log_info
from lib.std.universal.chart_dict import VALIDATION_CHARTS


chart_key = "work_mix_two_months_comparison"
dict_xlsx_file = VALIDATION_CHARTS[chart_key]["xlsx"]
db_json = VALIDATION_CHARTS[chart_key]["db_json"]
dict_json = VALIDATION_CHARTS[chart_key]["json"]
dict_html = VALIDATION_CHARTS[chart_key]["html"]
dict_csv = VALIDATION_CHARTS[chart_key]["csv"]
sub_folder = "Individual_Reports"
transformed_json = VALIDATION_CHARTS[chart_key]["transformed_json"]
load_dotenv()

# Constants
Tenant = config.database_name
store = config.store_name
role = config.role


def compare_with_labour_comparison_results(ui_json_path, db_json_path, timestamp):
    """Compare chart processing results with WorkMix DB calculated values opcode-wise"""
    try:
        log_info("Starting simplified three-way comparison...")
        
        comparison_results = compare_three_way_values(ui_json_path, db_json_path)
        save_simplified_comparison_results(comparison_results, timestamp)
        
        log_info("Three-way comparison completed successfully")

    except Exception as e:
        log_info(f"Error in three-way comparison: {e}")
        traceback.print_exc()


def compare_three_way_values(transformed_json_path, db_json_path):
    """Compare UI, Extracted, and DB values with simplified structure"""
    try:
        log_info(f"Loading transformed data (UI) from: {transformed_json_path}")
        log_info(f"Loading DB data from: {db_json_path}")

        with open(transformed_json_path, 'r') as f:
            ui_data = json.load(f)

        with open(db_json_path, 'r') as f:
            db_data = json.load(f)

    except Exception as e:
        log_info(f"Error loading JSON files: {e}")
        traceback.print_exc()
        return {}

    # Extract work mix data from both sources
    ui_work_mix = ui_data.get('target_month_results', {}).get('work_mix_analysis', {})
    db_work_mix = db_data.get('target_month_results', {}).get('work_mix_analysis', {})
    
    # Get month information dynamically from DB data
    db_target_results = db_data.get('target_month_results', {})
    month1_period = db_target_results.get('month1_period', '')
    month2_period = db_target_results.get('month2_period', '')
    month1_name = db_target_results.get('month1_name', '')
    month2_name = db_target_results.get('month2_name', '')
    
    log_info(f"DB Month1: {month1_period} ({month1_name})")
    log_info(f"DB Month2: {month2_period} ({month2_name})")
    
    # Validate we have month info
    if not month1_period or not month2_period:
        log_info("ERROR: Could not extract month periods from DB data")
        return {}

    # Create comparison structure
    opcode_comparison_results = {}
    
    # Process DB data to create lookup by opcode
    db_opcode_data = {}
    db_category_breakdown = db_work_mix.get('category_breakdown', {})
    
    log_info(f"Processing DB categories: {list(db_category_breakdown.keys())}")
    
    for category_name, category_data in db_category_breakdown.items():
        individual_opcodes = category_data.get('individual_opcodes', [])
        log_info(f"  Category {category_name}: {len(individual_opcodes)} individual opcodes")
        
        for opcode_data in individual_opcodes:
            opcode = opcode_data.get('opcode', '')
            if opcode == 'OTHER':
                continue  # Skip 'OTHER' opcode in individual_opcodes

            if opcode:
                db_opcode_data[opcode] = {
                    'category': category_name,
                    'data': opcode_data
                }
        
        # Store others_summary for comparison with OTHER opcode
        others_summary = category_data.get('others_summary', {})
        if others_summary and any(others_summary.get(f'workmix_mon{i}', 0) != 0 for i in [1, 2]):
            # Create a synthetic opcode entry for others_summary
            others_opcode_key = f"OTHER_{category_name}"
            db_opcode_data[others_opcode_key] = {
                'category': category_name,
                'data': others_summary,
                'is_others_summary': True
            }
            log_info(f"  Added others_summary as {others_opcode_key}")
        
    log_info(f"Found {len(db_opcode_data)} opcodes in DB data: {list(db_opcode_data.keys())}")

    # Process UI data to create lookup by opcode
    ui_opcode_data = {}
    ui_category_breakdown = ui_work_mix.get('category_breakdown', {})
    
    log_info(f"Processing UI categories: {list(ui_category_breakdown.keys())}")
    
    for category_name, category_data in ui_category_breakdown.items():
        individual_opcodes = category_data.get('individual_opcodes', [])
        log_info(f"  Category {category_name}: {len(individual_opcodes)} individual opcodes")
        
        for opcode_data in individual_opcodes:
            opcode = opcode_data.get('opcode', '')
            if opcode:
                if opcode == 'OTHER':
                    # Map OTHER to category-specific key
                    others_opcode_key = f"OTHER_{category_name}"
                    ui_opcode_data[others_opcode_key] = {
                        'category': category_name,
                        'data': opcode_data,
                        'is_other': True
                    }
                    log_info(f"  Found OTHER opcode, mapped to {others_opcode_key}")
                else:
                    ui_opcode_data[opcode] = {
                        'category': category_name,
                        'data': opcode_data
                    }
    
    log_info(f"Found {len(ui_opcode_data)} opcodes in UI data: {list(ui_opcode_data.keys())}")

    # Get all unique opcodes from both sources
    all_opcodes = set(list(db_opcode_data.keys()) + list(ui_opcode_data.keys()))
    log_info(f"Total unique opcodes across both sources: {len(all_opcodes)}")
    
    if not all_opcodes:
        log_info("ERROR: No opcodes found in either data source!")
        return {}

    # Compare each opcode
    for opcode in sorted(all_opcodes):
        log_info(f"Processing opcode: {opcode}")
        
        # Get data from both sources
        ui_opcode_info = ui_opcode_data.get(opcode, {})
        db_opcode_info = db_opcode_data.get(opcode, {})
        
        ui_opcode_values = ui_opcode_info.get('data', {})
        db_opcode_values = db_opcode_info.get('data', {})
        
        # Determine category (prefer DB, fallback to UI)
        category = db_opcode_info.get('category') or ui_opcode_info.get('category', 'UNKNOWN')
        
        # Check if this is an OTHER/others_summary comparison
        is_others_comparison = (db_opcode_info.get('is_others_summary', False) or 
                               ui_opcode_info.get('is_other', False))
        
        # Skip if no data in either source
        if not ui_opcode_values and not db_opcode_values:
            log_info(f"  Skipping {opcode} - no data in either source")
            continue
        
        # Determine display name
        display_opcode = opcode.replace('OTHER_', 'OTHER (') + ')' if opcode.startswith('OTHER_') else opcode
        
        # Initialize opcode result
        opcode_comparison_results[opcode] = {
            'opcode': display_opcode,
            'category': category,
            'is_others_comparison': is_others_comparison,
            'months': {},
            'summary': {
                'total_comparisons': 0,
                'all_match': 0,
                'match_rate': 0
            }
        }
        
        # Compare both months
        for month_suffix, month_period, month_name in [
            ('_mon1', month1_period, month1_name),
            ('_mon2', month2_period, month2_name)
        ]:
            month_key = f"month{month_suffix[-1]}"
            
            opcode_comparison_results[opcode]['months'][month_key] = {
                'period': month_period,
                'name': month_name,
                'metrics': [],
                'matches': 0,
                'total': 0
            }
            
            # Define metrics to compare
            metrics_to_compare = [
                {'name': 'Work Mix %', 'key': f'workmix{month_suffix}'},
                {'name': 'Job Count', 'key': f'job_count{month_suffix}'},
                {'name': 'GP %', 'key': f'gp_percentage{month_suffix}'},
                {'name': 'ELR', 'key': f'elr{month_suffix}'},
                {'name': 'Labor Hours', 'key': f'labor_hours{month_suffix}'}
            ]
            
            for metric in metrics_to_compare:
                metric_key = metric['key']
                
                # Get values from both sources
                ui_value = ui_opcode_values.get(metric_key, 0)
                db_value = db_opcode_values.get(metric_key, 0)
                
                # For OTHER comparisons, extracted value is always N/A (not compared)
                if is_others_comparison:
                    extracted_value = None
                else:
                    # Check for extracted value (if exists in UI data)
                    extracted_key = f"extracted_{metric_key}"
                    extracted_value = ui_opcode_values.get(extracted_key, None)
                
                # Round values for comparison
                ui_rounded = round_value(ui_value)
                db_rounded = round_value(db_value)
                extracted_rounded = round_value(extracted_value) if extracted_value is not None else None
                
                # Check if values match (different logic for OTHER comparisons)
                if is_others_comparison:
                    # For OTHER, only compare UI and DB
                    all_match = ui_rounded == db_rounded
                else:
                    # For regular opcodes, compare all three
                    all_match = check_all_values_match(ui_rounded, extracted_rounded, db_rounded)
                
                # Create metric result
                metric_result = {
                    'metric_name': metric['name'],
                    'ui_value': format_display_value(ui_value),
                    'extracted_value': format_display_value(extracted_value) if extracted_value is not None else "N/A",
                    'db_value': format_display_value(db_value),
                    'all_match': all_match,
                    'is_others_comparison': is_others_comparison
                }
                
                opcode_comparison_results[opcode]['months'][month_key]['metrics'].append(metric_result)
                opcode_comparison_results[opcode]['months'][month_key]['total'] += 1
                opcode_comparison_results[opcode]['summary']['total_comparisons'] += 1
                
                if all_match:
                    opcode_comparison_results[opcode]['months'][month_key]['matches'] += 1
                    opcode_comparison_results[opcode]['summary']['all_match'] += 1
        
        # Calculate match rate for opcode
        total = opcode_comparison_results[opcode]['summary']['total_comparisons']
        if total > 0:
            opcode_comparison_results[opcode]['summary']['match_rate'] = (
                opcode_comparison_results[opcode]['summary']['all_match'] / total * 100
            )

    log_info(f"Generated comparison for {len(opcode_comparison_results)} opcodes")
    return opcode_comparison_results


def round_value(value, decimals=2):
    """Round value for comparison"""
    if value is None:
        return None
    
    try:
        str_val = str(value).replace("%", "").strip()
        if not str_val or str_val.lower() == 'none':
            return 0.0
        float_val = float(str_val)
        return round(float_val, decimals)
    except (ValueError, TypeError):
        return 0.0


def check_all_values_match(ui_val, extracted_val, db_val):
    """Check if all three values match (considering extracted can be None)"""
    if extracted_val is None:
        # If no extracted value, only compare UI and DB
        return ui_val == db_val
    
    # All three values must match
    return ui_val == extracted_val == db_val


def format_display_value(value):
    """Format value for display"""
    if value is None:
        return "N/A"
    
    try:
        if isinstance(value, (int, float)):
            if isinstance(value, float):
                return round(value, 2)
            return value
        return str(value)
    except:
        return str(value)


def save_simplified_comparison_results(opcode_results, timestamp):
    """Save simplified comparison results to CSV and HTML"""
    try:
        if not opcode_results:
            log_info("No comparison results to save")
            return
        
        log_info("Saving simplified comparison results")
        
        # Flatten results for CSV
        flattened_results = []
        overall_summary = {
            'total_opcodes': len(opcode_results),
            'total_comparisons': 0,
            'all_match': 0,
            'opcode_summaries': []
        }
        
        for opcode, opcode_data in opcode_results.items():
            overall_summary['total_comparisons'] += opcode_data['summary']['total_comparisons']
            overall_summary['all_match'] += opcode_data['summary']['all_match']
            
            overall_summary['opcode_summaries'].append({
                'opcode': opcode,
                'category': opcode_data['category'],
                'match_rate': opcode_data['summary']['match_rate'],
                'total_comparisons': opcode_data['summary']['total_comparisons'],
                'is_others_comparison': opcode_data.get('is_others_comparison', False)
            })
            
            for month_key, month_data in opcode_data['months'].items():
                for metric in month_data['metrics']:
                    comparison_type = "UI = DB" if metric.get('is_others_comparison') else "UI = Extracted = DB"
                    
                    flattened_results.append({
                        'Opcode': opcode_data['opcode'],
                        'Category': opcode_data['category'],
                        'Comparison_Type': comparison_type,
                        'Month_Period': month_data['period'],
                        'Month_Name': month_data['name'],
                        'Metric': metric['metric_name'],
                        'UI_Value': metric['ui_value'],
                        'Extracted_Value': metric['extracted_value'],
                        'DB_Value': metric['db_value'],
                        'All_Match': 'YES' if metric['all_match'] else 'NO'
                    })
        
        # Calculate overall match rate
        if overall_summary['total_comparisons'] > 0:
            overall_summary['match_rate'] = (
                overall_summary['all_match'] / overall_summary['total_comparisons'] * 100
            )
        else:
            overall_summary['match_rate'] = 0
        
        # Save CSV
        _output_folder, output_csv = create_folder_file_path(
            subfolder=sub_folder,
            output_file=dict_csv,
        )
        
        with open(output_csv, "w", newline="", encoding="utf-8") as csvfile:
            fieldnames = ["Opcode", "Category", "Comparison_Type", "Month_Period", "Month_Name", "Metric", 
                         "UI_Value", "Extracted_Value", "DB_Value", "All_Match"]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(flattened_results)
        
        log_info(f"CSV saved to: {output_csv}")
        
        # Save JSON
        json_path = os.path.join(_output_folder, dict_json)
        with open(json_path, "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": timestamp,
                "overall_summary": overall_summary,
                "opcode_results": opcode_results
            }, f, indent=2, ensure_ascii=False)
        
        log_info(f"JSON saved to: {json_path}")
        
        # Generate HTML report
        html_path = os.path.join(_output_folder, dict_html)
        generate_simplified_html_report(html_path, opcode_results, overall_summary, timestamp)
        
        log_info(f"HTML report saved to: {html_path}")
        log_info(f"\nOverall Summary:")
        log_info(f"   Total opcodes: {overall_summary['total_opcodes']}")
        log_info(f"   Total comparisons: {overall_summary['total_comparisons']}")
        log_info(f"   All values match: {overall_summary['all_match']} ({overall_summary.get('match_rate', 0):.1f}%)")
        
    except Exception as e:
        log_info(f"Error saving results: {e}")
        traceback.print_exc()


def generate_simplified_html_report(html_path, opcode_results, overall_summary, timestamp):
    """Generate HTML report with Bootstrap accordion"""
    from datetime import datetime
    
    # Calculate statistics
    total = overall_summary.get('total_comparisons', 0)
    passed = overall_summary.get('all_match', 0)
    failed = total - passed
    match_rate = (passed / total * 100) if total > 0 else 0
    
    # Get metadata
    tenant = overall_summary.get('tenant', Tenant)
    store_name = overall_summary.get('store', store)
    role_name = overall_summary.get('role', role)
    
    html_template = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Three-Way Comparison Report - Work Mix Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {{ padding: 20px; font-family: Arial, sans-serif; }}
        .badge-pass {{ background-color: #28a745; color: white; }}
        .badge-fail {{ background-color: #dc3545; color: white; }}
        .badge-others {{ background-color: #6f42c1; color: white; }}
        .card-header {{ cursor: pointer; background-color: #cfe2f3; }}
        .comparison-section {{ display: flex; justify-content: space-between; margin-bottom: 15px; }}
        .chart-info {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin-right: 10px; }}
        .match-status {{ background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 0 0 200px; }}
        .match-indicator {{ font-weight: bold; padding: 8px 15px; border-radius: 5px; display: inline-block; }}
        .match-true {{ background-color: #d4edda; color: #155724; }}
        .match-false {{ background-color: #f8d7da; color: #721c24; }}
        .section-title {{ font-weight: bold; margin-bottom: 8px; color: #333; }}
        .field-value {{ margin-bottom: 5px; }}
        .badge-all-passed {{ background-color: #28a745; color: white; }}
        .badge-has-failures {{ background-color: #dc3545; color: white; }}
        .three-way-comparison {{ display: flex; justify-content: space-between; margin-top: 15px; }}
        .two-way-comparison {{ display: flex; justify-content: space-around; margin-top: 15px; }}
        .value-box {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin: 0 5px; }}
        .value-box.extracted {{ background-color: #fff3cd; }}
        .value-box.others-ui {{ background-color: #e7f3ff; }}
        .value-box.others-db {{ background-color: #f0e7ff; }}
        .month-badge {{ background-color: #0dcaf0; color: white; padding: 5px 10px; border-radius: 3px; font-size: 12px; margin-left: 10px; }}
        .comparison-type-badge {{ background-color: #6f42c1; color: white; padding: 3px 8px; border-radius: 3px; font-size: 11px; margin-left: 5px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Three-Way Comparison Report - Work Mix Analysis</h1>
        <h5 class="text-muted mb-4">UI = Extracted = DB Values (Regular) | UI = DB Values (OTHER)</h5>
        <div class="mb-4">
            <strong>Tenant:</strong> {tenant}<br>
            <strong>Store:</strong> {store_name}<br>
            <strong>Role:</strong> {role_name}<br>
            <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
            <strong>Report Timestamp:</strong> {timestamp}<br>
        </div>

        <div class="d-flex gap-3 mb-4">
            <span class="badge bg-success">Passed: {passed}</span>
            <span class="badge bg-danger">Failed: {failed}</span>
            <span class="badge bg-secondary">Total: {total}</span>
            <span class="badge bg-info">Match Rate: {match_rate:.1f}%</span>
        </div>

        <div class="accordion" id="reportAccordion">
"""
    
    for opcode_idx, (opcode, opcode_data) in enumerate(sorted(opcode_results.items())):
        # Check if all entries for this opcode pass
        opcode_pass = opcode_data['summary']['all_match'] == opcode_data['summary']['total_comparisons']
        badge_class = "badge-all-passed" if opcode_pass else "badge-has-failures"
        badge_text = "All Passed" if opcode_pass else "Has Failures"
        opcode_id = f"opcode{opcode_idx}"
        
        match_rate_opcode = opcode_data['summary'].get('match_rate', 0)
        is_others = opcode_data.get('is_others_comparison', False)
        comparison_type = "UI = DB" if is_others else "UI = Extracted = DB"

        html_template += f"""
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-{opcode_id}">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#{opcode_id}" aria-expanded="false" aria-controls="{opcode_id}">
                    Opcode: {opcode_data['opcode']} ({opcode_data['category']}) 
                    <span class="ms-3 badge {badge_class}">{badge_text}</span>
                    <span class="comparison-type-badge">{comparison_type}</span>
                    <small class="ms-2 text-muted">({opcode_data['summary']['total_comparisons']} comparisons - {match_rate_opcode:.1f}% match)</small>
                </button>
            </h2>
            <div id="{opcode_id}" class="accordion-collapse collapse" aria-labelledby="heading-{opcode_id}" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
"""
        
        # Process each month
        for month_key, month_data in sorted(opcode_data['months'].items()):
            month_match_rate = (month_data['matches'] / month_data['total'] * 100) if month_data['total'] > 0 else 0
            
            html_template += f"""
                <div class="mb-4">
                    <h5>{month_data['name']} ({month_data['period']}) 
                        <span class="month-badge">Match: {month_data['matches']}/{month_data['total']} ({month_match_rate:.1f}%)</span>
                    </h5>
"""
            
            # Process each metric for this month
            for metric in month_data['metrics']:
                match = metric['all_match']
                sub_badge = "badge-pass" if match else "badge-fail"
                sub_text = "Passed" if match else "Failed"
                
                metric_name = metric['metric_name']
                ui_value = metric['ui_value']
                extracted_value = metric['extracted_value']
                db_value = metric['db_value']
                is_others_metric = metric.get('is_others_comparison', False)

                html_template += f"""
                    <div class="card mb-3">
                        <div class="card-header">
                            <strong>{metric_name}</strong> <span class="ms-2 badge {sub_badge}">{sub_text}</span>
                            {"<span class='badge badge-others ms-2'>OTHER (UI = DB only)</span>" if is_others_metric else ""}
                        </div>
                        <div class="card-body">
                            <div class="comparison-section">
                                <div class="chart-info">
                                    <div class="section-title">Metric Information:</div>
                                    <div class="field-value"><strong>Opcode:</strong> {opcode_data['opcode']}</div>
                                    <div class="field-value"><strong>Category:</strong> {opcode_data['category']}</div>
                                    <div class="field-value"><strong>Month:</strong> {month_data['name']} ({month_data['period']})</div>
                                    <div class="field-value"><strong>Metric:</strong> {metric_name}</div>
                                    <div class="field-value"><strong>Comparison Type:</strong> {"UI = DB" if is_others_metric else "UI = Extracted = DB"}</div>
                                </div>
                                <div class="match-status">
                                    <div class="section-title">Match Status:</div>
                                    <span class="match-indicator {"match-true" if match else "match-false"}">
                                        {"✓ MATCH" if match else "✗ MISMATCH"}
                                    </span>
                                </div>
                            </div>
"""
                
                # Different layout for OTHER comparisons
                if is_others_metric:
                    html_template += f"""
                            <div class="two-way-comparison">
                                <div class="value-box others-ui">
                                    <div class="section-title">UI Value (OTHER):</div>
                                    <div class="field-value"><strong>Field:</strong> {metric_name}</div>
                                    <div class="field-value"><strong>Value:</strong> {ui_value}</div>
                                </div>
                                <div class="value-box others-db">
                                    <div class="section-title">DB Value (others_summary):</div>
                                    <div class="field-value"><strong>Field:</strong> {metric_name}</div>
                                    <div class="field-value"><strong>Value:</strong> {db_value}</div>
                                </div>
                            </div>
"""
                else:
                    html_template += f"""
                            <div class="three-way-comparison">
                                <div class="value-box">
                                    <div class="section-title">Tooltip Value:</div>
                                    <div class="field-value"><strong>Field:</strong> {metric_name}</div>
                                    <div class="field-value"><strong>Value:</strong> {ui_value}</div>
                                </div>
                                <div class="value-box extracted">
                                    <div class="section-title">Drilldown Value:</div>
                                    <div class="field-value"><strong>Field:</strong> {metric_name}</div>
                                    <div class="field-value"><strong>Value:</strong> {extracted_value}</div>
                                </div>
                                <div class="value-box">
                                    <div class="section-title">DB Value:</div>
                                    <div class="field-value"><strong>Field:</strong> {metric_name}</div>
                                    <div class="field-value"><strong>Value:</strong> {db_value}</div>
                                </div>
                            </div>
"""
                
                html_template += """
                        </div>
                    </div>
"""
            
            html_template += """
                </div>
"""
        
        html_template += """
                </div>
            </div>
        </div>
"""
    
    html_template += """
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""
    
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)
    
    log_info(f"HTML report generated: {html_path}")
"""
Validation script for Client Report Card (1 month).
Automates UI, extracts data, compares with DB, and generates validation reports.
"""

import os
import json
import logging
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime
from typing import Any, Dict,Tuple
import asyncio
import time
import inspect

import pandas as pd
from dotenv import load_dotenv
from playwright.sync_api import sync_playwright

# Assuming these imports are available in your environment
from lib.std.universal.extract_image_data import extract_image_data
from lib.pattern.qa_auto_core.compare_client_report_card import compare_client_report_card
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.std.universal.authmanager import AuthManager
from lib.std.universal.chart_dict import VALIDATION_CHARTS
from lib.std.universal.constants import client_report_card_prompt,client_report_before_fopc_vars,client_report_after_fopc_vars,client_report_one_month_kpi_constants,client_report_one_month_opcategory_kpi_constants

# Configure logging
# logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")

chart_key="client_report_card_one_month"
dict_jpg = VALIDATION_CHARTS[chart_key]["jpg"]
dict_json= VALIDATION_CHARTS[chart_key]["json"]
dict_md= VALIDATION_CHARTS[chart_key]["md"]

omni_folder="Omni_Results"

load_dotenv()

def round_off(n: float, decimals: int = 0) -> float:
    """Round a number to a given number of decimal places using ROUND_HALF_UP."""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def convert_month_format(date_str: str) -> str:
    """Convert YYYY-MM to Mon-YY format (e.g., '2024-10' -> 'Oct-24')."""
    dt = datetime.strptime(date_str, "%Y-%m")
    role_format = dt.strftime("%b-%y")  # e.g., Oct-24
    #fill_format = dt.strftime("%m/%y")  # e.g., 10/24
    return role_format

def automate_site(config):
    """
    Synchronous wrapper to run Client Report Card automation safely in Python 3.12+
    """
    async def runner():
        auth = AuthManager(config)
        success = await auth.start()
        if not success:
            log_error("❌ Authentication failed. Exiting KPI capture.")
            return False

        page = auth.page
        
        try:
            base_url = config.site_url.rstrip("/")
            path = "OneMonthReport"
            site_url = f"{base_url}/{path}"

            # Navigate to report page
            await page.goto(site_url)

            # Select measured and prior months
            fopc_month = convert_month_format(config.fopc_month)
            pre_fopc_month = convert_month_format(config.pre_fopc_month)
            
            # --- LOGGING UI SELECTION VALUES ---
            log_info(f"UI Selection: Measured Month (FOPC) -> {fopc_month}")
            log_info(f"UI Selection: Prior Month (Pre-FOPC) -> {pre_fopc_month}")

            await page.click("#mui-component-select-measured-mth")
            await page.get_by_role("option", name=fopc_month).click()

            await page.click("#mui-component-select-prior-mth")
            await page.get_by_role("option", name=pre_fopc_month).click()

            # Select categories
            await page.get_by_label("Competitive").click()
            await page.get_by_label("Maintenance").click()
            await page.get_by_label("Repair").click()
            log_info("UI Selection: Selected all categories (Competitive, Maintenance, Repair).")

            # Wait for page to load
            await page.wait_for_timeout(5000)

            # Take screenshot
            folder, filename = create_folder_file_path(
                subfolder=omni_folder,
                output_file=dict_jpg 
            )
            await page.screenshot(path=filename, full_page=True)
            log_info(f"✔ Screenshot saved to: {filename}")
            return True # Return True on success

        except Exception as e:
            log_error(f"❌ Failed to capture Client Report Card: {e}")
            return False 
        finally:
            await auth.stop()

    
    # Run the async part inside sync function
    # FIX: Ensure Playwright is run correctly inside an asyncio context if necessary, 
    # but for simple sync calls, `asyncio.run` is the standard Python 3.7+ way.
    try:
        return asyncio.run(runner())
    except Exception as e:
         log_error(f"❌ Async runner failed: {e}")
         return False

def _calculate_month_metrics(df_month: pd.DataFrame, month_label: str) -> Tuple[Dict[str, float], pd.DataFrame, float, float]:
    """
    Calculates all main KPIs and OpCategory metrics for a single month's DataFrame.
    Returns: (main_kpis_dict, opcategory_df, total_labor_hours, total_labor_sale)
    """
    log_info(f"\n--- Calculating Metrics for {month_label} ---")
    if df_month.empty:
        log_warn(f"DataFrame for {month_label} is empty. Returning zero metrics.")
        # Return default zero values and an empty DataFrame
        zero_kpis = {k: 0.0 for k in client_report_one_month_kpi_constants.values()}
        return zero_kpis, pd.DataFrame(), 0.0, 0.0
    
    # 1. Base Values
    ro_count = df_month["unique_ro_number"].nunique()
    log_info(f"{month_label} Base Value: RO Count (ro_count) -> {ro_count}")
    
    df_labor_only = df_month[
        (df_month["opcategory"] != "N/A")
        & (df_month["opcategory"] != "SHOP SUPPLIES")
    ]
    total_labor_hours_value = df_labor_only["lbrsoldhours"].sum()
    total_labor_sale_value = df_month["lbrsale"].sum()
    labor_cost_value = df_month["lbrcost"].sum()
    parts_sale_value = df_month["prtextendedsale"].sum()
    parts_cost_value = df_month["prtextendedcost"].sum()
    
    log_info(f"{month_label} Base Value: Total Labor Hours (lbrsoldhours.sum()) -> {total_labor_hours_value}")
    log_info(f"{month_label} Base Value: Total Labor Sale (lbrsale.sum()) -> {total_labor_sale_value}")
    log_info(f"{month_label} Base Value: Total Labor Cost (lbrcost.sum()) -> {labor_cost_value}")
    log_info(f"{month_label} Base Value: Total Parts Sale (prtextendedsale.sum()) -> {parts_sale_value}")
    log_info(f"{month_label} Base Value: Total Parts Cost (prtextendedcost.sum()) -> {parts_cost_value}")


    # 2. Main KPI Calculations (Unrounded)
    sold_hours_value = total_labor_hours_value
    hours_per_RO_value = sold_hours_value / ro_count if ro_count != 0 else 0
    ELR_value = total_labor_sale_value / sold_hours_value if sold_hours_value != 0 else 0
    
    labor_GP_value = total_labor_sale_value - labor_cost_value
    labor_GP_perc_value = (labor_GP_value / total_labor_sale_value) * 100 if total_labor_sale_value != 0 else 0
    
    parts_GP_value = parts_sale_value - parts_cost_value
    parts_GP_perc_value = (parts_GP_value / parts_sale_value) * 100 if parts_sale_value != 0 else 0
    
    parts_labor_sale_value = total_labor_sale_value + parts_sale_value
    parts_labor_GP_value = labor_GP_value + parts_GP_value

    log_info(f"{month_label} Unrounded KPI: Hours Per RO -> {hours_per_RO_value}")
    log_info(f"{month_label} Unrounded KPI: ELR -> {ELR_value}")
    log_info(f"{month_label} Unrounded KPI: Labor GP -> {labor_GP_value}")
    log_info(f"{month_label} Unrounded KPI: Labor GP % -> {labor_GP_perc_value}")
    log_info(f"{month_label} Unrounded KPI: Parts GP -> {parts_GP_value}")
    log_info(f"{month_label} Unrounded KPI: Parts GP % -> {parts_GP_perc_value}")
    log_info(f"{month_label} Unrounded KPI: Parts & Labor Sale -> {parts_labor_sale_value}")
    log_info(f"{month_label} Unrounded KPI: Parts & Labor GP -> {parts_labor_GP_value}")

    # 3. Apply Rounding and Map to simple keys
    main_kpis = {
        "ro_count": round_off(ro_count),
        "sold_hours": round_off(sold_hours_value),
        "hours_per_RO": round_off(hours_per_RO_value),
        "ELR": round_off(ELR_value, 2), # ELR is rounded to 2 decimals
        "labor_GP_perc": round_off(labor_GP_perc_value),
        "labor_sale": round_off(total_labor_sale_value),
        "labor_GP": round_off(labor_GP_value),
        "parts_sale": round_off(parts_sale_value),
        "parts_GP": round_off(parts_GP_value),
        "parts_GP_perc": round_off(parts_GP_perc_value),
        "parts_labor_sale": round_off(parts_labor_sale_value),
        "parts_labor_GP": round_off(parts_labor_GP_value),
    }

    log_info(f"{month_label} Rounded Main KPIs: {main_kpis}")

    # 4. OpCategory Calculations
    opcategory_df = df_month.groupby(['opcategory']).agg(
        hours_sold=('lbrsoldhours', 'sum'),
        lbrsale=('lbrsale', 'sum'),
    ).reset_index()

    opcategory_df = opcategory_df[
        opcategory_df['opcategory'].isin(['REPAIR', 'COMPETITIVE', 'MAINTENANCE'])
    ].copy()
    
    # Calculate unrounded ELR and Percentage
    opcategory_df['elr_unrounded'] = opcategory_df.apply(
        lambda row: row['lbrsale'] / row['hours_sold'] if row['hours_sold'] != 0 else 0.0, axis=1
    )
    opcategory_df['perc_of_total_hours_unrounded'] = opcategory_df.apply(
        lambda row: (row['hours_sold'] / total_labor_hours_value) * 100 if total_labor_hours_value != 0 else 0.0, axis=1
    )


    opcategory_df['elr'] = opcategory_df['elr_unrounded'].apply(lambda x: round_off(x, 2))
    opcategory_df['perc_of_total_hours'] = opcategory_df['perc_of_total_hours_unrounded'].apply(round_off)

    
    # Apply final rounding for OpCategory display columns
    opcategory_df['hours_sold'] = opcategory_df['hours_sold'].apply(round_off)
    opcategory_df['total_labor_sale'] = opcategory_df['lbrsale'].apply(round_off)
    
    # Drop unrounded columns for the final result
    opcategory_df = opcategory_df.drop(columns=['lbrsale', 'elr_unrounded', 'perc_of_total_hours_unrounded'])

    log_info(f"{month_label} Rounded OpCategory Metrics:")
    log_info(f"\n{opcategory_df.to_string()}")

    return main_kpis, opcategory_df, total_labor_hours_value, total_labor_sale_value

# NEW GENERAL HELPER FUNCTION
def _format_metric_value(
    value: float, 
    display_name: str, 
    is_variance: bool, 
    is_opcategory: bool = False
) -> str:
    """
    Formats a metric value (monthly value or variance) based on its type and context.
    
    :param value: The rounded numerical value.
    :param display_name: The display name (e.g., "Total Shop ELR", "Labor Sale", "Hours Per RO").
    :param is_variance: True if this is a variance column (affects negative formatting and ELR decimals).
    :param is_opcategory: True if the metric is from the OpCategory block (affects ELR decimals).
    :return: The formatted string (e.g., "$123,456", "($1.23)", "5.5%").
    """
    
    # 1. Determine Unit Type and Decimal Places for the Display Value
    prefix = ""
    suffix = ""
    decimal_places = 0
    
    # A. Percentage Metrics
    if "%" in display_name:
        suffix = "%"
        decimal_places = 0
    
    # B. ELR Metrics (Currency but with 2 decimals for absolute values)
    elif "ELR" in display_name:
        prefix = "$"
        # Variance rules: Total Shop ELR variance is 1 decimal, OpCategory ELR variance is 2.
        # Absolute monthly values (not variance) are always 2 decimals.
        if is_variance:
            # Total Shop ELR variance (simple_key="ELR") is in the main KPI loop (not opcategory)
            if not is_opcategory:
                decimal_places = 1
            else: # OpCategory ELR variance
                decimal_places = 2
        else: # Absolute monthly value
            decimal_places = 2

    # C. Other Currency Metrics (Sales, GPs, Fees)
    elif "Sale" in display_name or "GP" in display_name or display_name in ["Monthly FOPC", "Monthly DMS", "Total"]:
        prefix = "$"
        decimal_places = 0 # Sales/GP are always rounded to $0 decimals

    # D. Count/Hours Metrics
    else: # RO Count, Sold Hours, Hours Per RO
        decimal_places = 0

    # 2. Apply rounding (if needed, though input is usually pre-rounded)
    # Re-apply rounding *specifically* for the target display decimals if it's a variance or ELR
    if is_variance or "ELR" in display_name or "%" in display_name:
         value_to_format = round_off(value, decimal_places)
    else:
         value_to_format = round_off(value, decimal_places) # Use the base 0 rounding for counts/sales/GP

    # 3. Apply Variance/Negative Formatting (Only for currency variances)
    if is_variance and prefix == "$":
        if value_to_format < 0:
            # Format as ($X,XXX.XX)
            abs_value = abs(value_to_format)
            return f"(${abs_value:,.{decimal_places}f})"
        
    # 4. Apply Final Formatting (Positive values or non-currency variances)
    
    # Handle the UI's special "$0" for 0-value fees
    if prefix == "$" and value_to_format == 0 and not is_variance:
        # Check if it should be "$0" (for fees, sale, GP) or "$0.00" (for ELR)
        if decimal_places == 2:
            return "$0.00"
        return "$0"
    
    # Handle the UI's special "$O" for the Total fees
    if display_name == "Total" and value_to_format == 0:
         return "$0" # Matches the observed OCR error in the UI MD
         
    # Format the number part with commas and correct decimals
    number_format_string = f"{value_to_format:,.{decimal_places}f}"

    return f"{prefix}{number_format_string}{suffix}"

def _get_top_level_value(key: str, value: float) -> str | float:
    """
    Determines the final output format for top-level Client_Report_Card metrics.
    
    Returns:
    - Raw float (0.0, 9.08, etc.) for keys that historically fail comparison 
      when formatted as strings (Total, ROI, Repair ELR Change).
    - Formatted string for all other top-level keys (Fees, GP Change).
    """
    # Keys that require the raw float value for the comparison to pass
    RAW_FLOAT_KEYS = ["Total", "ROI", "Repair ELR Change"]
    
    if key in RAW_FLOAT_KEYS:
        # Return the raw float to satisfy the comparison tool's type expectation (e.g., 0.0, 9.08)
        return value
    else:
        # Return the formatted string for all others (Fees, GP Change)
        return _format_metric_value(value, key, "Change" in key or "Variance" in key)



def run_validation():
    start_time = time.time()
    log_info(f"Start Time: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Initialize components and capture UI
    ui_capture_success = automate_site(config)

    # Check if UI capture failed before proceeding
    if not ui_capture_success:
        log_warn("UI extraction failed. Skipping database validation and comparison.")
        return
    
    # Get the required environment variables
    storeid = config.store_id
    realm = config.database_name
    # Ensure fees are floats for round_off
    dms_fees = float(Decimal(os.environ.get("dms_fees", "0")))
    fopc_fees = float(Decimal(os.environ.get("fopc_fees", "0")))
    fopc_month = config.fopc_month
    pre_fopc_month = config.pre_fopc_month
    
    log_info(f"Configuration: Store ID -> {storeid}, Realm -> {realm}")
    log_info(f"Configuration: FOPC Month -> {fopc_month}, Pre-FOPC Month -> {pre_fopc_month}")
    log_info(f"Configuration: DMS Fees -> ${dms_fees}, FOPC Fees -> ${fopc_fees}")

    # Fetching data from DB
    retail_flag_all = config.retail_flag_all
    retail_flag = set(retail_flag_all['source_paytype'])

    # Fetching data from DB
    all_revenue_details_df = config.all_revenue_details_for_client_report_card_3_month

    # Initializing new data frame to filter only required advisor and technician
    filtered_df = all_revenue_details_df[
        (all_revenue_details_df['department'] == 'Service') &
        (all_revenue_details_df['opcategory'] != 'N/A') &
        (all_revenue_details_df['opcategory'].isin(['REPAIR', 'COMPETITIVE', 'MAINTENANCE'])) &
        (all_revenue_details_df['hide_ro'] != True)
        & (all_revenue_details_df['store_id'].astype(str).str.strip() == storeid)
    ]
    log_info(f"Filtered DB Data Rows (Service, Valid OpCategory, Visible RO, Store ID {storeid}): {len(filtered_df)}")
    
    filtered_df["store_id"] = filtered_df["store_id"].astype(str)

    merged_df = filtered_df.merge(
        retail_flag_all,
        left_on=['paytypegroup', 'store_id'],
        right_on=['source_paytype', 'store_id'],
        how='left'
    )

    merged_df = merged_df.copy() # Create a deep copy of filtered_df to avoid the warning
    # RO number with different closeddate will be considered as different RO, joining RO number and closeddate to find out the unique RO number
    merged_df["unique_ro_number"] = merged_df["ronumber"].astype(str) + "_" + merged_df["closeddate"].astype(str)
    
    # Define customer and warranty pay types dynamically
    if "C" in retail_flag and not "E" in retail_flag and not "M" in retail_flag:
        customer_pay_types = {"C"}
        warranty_pay_types = {"W", "F", "M", "E"}
    elif "C" in retail_flag and not "E" in retail_flag and "M" in retail_flag:
        customer_pay_types = {"C", "M"}
        warranty_pay_types = {"W", "F", "E"}
    elif "C" in retail_flag and "E" in retail_flag and not "M" in retail_flag:
        customer_pay_types = {"C", "E"}
        warranty_pay_types = {"W", "F", "M"}
    elif "C" in retail_flag and "E" in retail_flag and "M" in retail_flag:
        customer_pay_types = {"C", "E", "M"}
        warranty_pay_types = {"W", "F"}
    else:
        log_warn(f"Paytype mapping fell through. Retail Flags: {retail_flag_all.to_dict('records')}")
        customer_pay_types = {"C"}
        warranty_pay_types = {"W", "F", "M", "E"}
        
    log_info(f"Customer Pay Types determined as: {customer_pay_types}")

    list_of_paytypegroup_C = merged_df[merged_df["paytypegroup"].isin(customer_pay_types)].to_dict("records")
    # Coverting it to data frame
    total_CP_revenue_details_df = pd.DataFrame(list_of_paytypegroup_C)

    total_CP_revenue_details_df = total_CP_revenue_details_df[
        ~(
            (total_CP_revenue_details_df["lbrsale"].fillna(0) == 0)
            & (total_CP_revenue_details_df["lbrsoldhours"].fillna(0) == 0)
            & (total_CP_revenue_details_df["prtextendedsale"].fillna(0) == 0)
            & (total_CP_revenue_details_df["prtextendedcost"].fillna(0) == 0)
        )
    ]
    log_info(f"Total Customer Pay (CP) Revenue Details Rows (after filtering zero revenue/hours): {len(total_CP_revenue_details_df)}")


    after_FOPC_total_revenue_CP = total_CP_revenue_details_df[total_CP_revenue_details_df["month_year"] == fopc_month]
    before_FOPC_total_revenue_CP = total_CP_revenue_details_df[total_CP_revenue_details_df["month_year"] == pre_fopc_month ]
    
    log_info(f"CP Data Rows for After FOPC Month ({fopc_month}): {len(after_FOPC_total_revenue_CP)}")
    log_info(f"CP Data Rows for Before FOPC Month ({pre_fopc_month}): {len(before_FOPC_total_revenue_CP)}")
    
    # Initialize main KPI variables for after FOPC month
    after_FOPC_kpis = {} 
    after_FOPC_total_labor_sale = 0
    after_FOPC_total_labor_hours = 0
    after_FOPC_groupby_category = pd.DataFrame()

    if not after_FOPC_total_revenue_CP.empty:
        # Calculate FOPC month metrics
        after_FOPC_kpis, after_FOPC_groupby_category, after_FOPC_total_labor_hours, after_FOPC_total_labor_sale = \
            _calculate_month_metrics(after_FOPC_total_revenue_CP, f"After FOPC ({fopc_month})")
        
        after_FOPC_parts_labor_GP = after_FOPC_kpis.get("parts_labor_GP", 0.0)
    else:
        after_FOPC_parts_labor_GP = 0.0
    
    log_info(f"Final 'After FOPC' (Second Month) Parts & Labor GP: ${after_FOPC_parts_labor_GP}")


    # Initialize variables for the prior month
    before_FOPC_kpis = {} 
    before_FOPC_total_labor_sale = 0
    before_FOPC_total_labor_hours = 0
    before_FOPC_groupby_category = pd.DataFrame()

    if not before_FOPC_total_revenue_CP.empty:
        # Calculate Pre-FOPC month metrics
        before_FOPC_kpis, before_FOPC_groupby_category, before_FOPC_total_labor_hours, before_FOPC_total_labor_sale = \
            _calculate_month_metrics(before_FOPC_total_revenue_CP, f"Before FOPC ({pre_fopc_month})")

        before_FOPC_parts_labor_GP = before_FOPC_kpis.get("parts_labor_GP", 0.0)
    else:
        before_FOPC_parts_labor_GP = 0.0

    log_info(f"Final 'Before FOPC' (First Month) Parts & Labor GP: ${before_FOPC_parts_labor_GP}")

    # Calculate overall metrics
    monthly_fopc = round_off(fopc_fees)
    monthly_dms = round_off(dms_fees)
    
    total = round_off(monthly_fopc + monthly_dms)
    
    roi_denominator = total if total != 0 else 1
    roi_value_unrounded = (after_FOPC_parts_labor_GP - total) / float(roi_denominator) * 100
    roi_value = round_off(roi_value_unrounded)

    total_pts_lbr_gp_change = round_off(after_FOPC_parts_labor_GP - before_FOPC_parts_labor_GP)
    
    log_info("\n--- Overall Metrics Calculation ---")
    log_info(f"Total Monthly Fees (FOPC + DMS): ${total}")
    log_info(f"After FOPC GP - Total Fees: ${after_FOPC_parts_labor_GP - total}")
    log_info(f"ROI (Unrounded): {roi_value_unrounded}%")
    log_info(f"ROI (Rounded): {roi_value}%")
    log_info(f"Total Parts & Labor GP Change: ${total_pts_lbr_gp_change}")

    # Repair ELR Change
    repair_opcategory_after = after_FOPC_groupby_category[after_FOPC_groupby_category['opcategory'] == 'REPAIR']
    repair_opcategory_before = before_FOPC_groupby_category[before_FOPC_groupby_category['opcategory'] == 'REPAIR']
    
    after_repair_elr = repair_opcategory_after['elr'].iloc[0] if not repair_opcategory_after.empty else 0
    before_repair_elr = repair_opcategory_before['elr'].iloc[0] if not repair_opcategory_before.empty else 0

    repair_elr_change = round_off(after_repair_elr - before_repair_elr, 2)
    
    log_info(f"After FOPC Repair ELR: ${after_repair_elr}")
    log_info(f"Before FOPC Repair ELR: ${before_repair_elr}")
    log_info(f"Repair ELR Change: ${repair_elr_change}")

    # Start building the final JSON structure (Client_Report_Card)
    Client_Report_Card = {}

    # 1. Top-level KPIs - NOW USING GENERAL FORMATTER
    # Map of metric keys to their calculated float values
    top_level_metrics = {
        "Monthly FOPC": monthly_fopc,
        "Monthly DMS": monthly_dms,
        "Total": total,
        "ROI": roi_value,
        "Total Pts & Lbr GP Change": total_pts_lbr_gp_change,
        "Repair ELR Change": repair_elr_change,
    }

    # Use the helper function to assign either the raw float or the formatted string
    for key, value in top_level_metrics.items():
        # This one line handles ALL top-level metrics without repetition or explicit if/else 
        # for each key in the main run_validation function.
        Client_Report_Card[key] = _get_top_level_value(key, value)
        
    log_info(f"\n--- Final Top-Level Client Report Card Values (UI MD Format) ---")
    log_info(json.dumps(Client_Report_Card, indent=4))
    log_info("-------------------------------------------------")

    # 2. Main KPIs Block - NOW USING GENERAL FORMATTER
    kpis_block = {}
    
    log_info(f"\n--- Main KPIs Block Values ---")
    for display_name, simple_key in client_report_one_month_kpi_constants.items():
        
        after_val = after_FOPC_kpis.get(simple_key, 0.0)
        before_val = before_FOPC_kpis.get(simple_key, 0.0)
        
        if after_val is None or before_val is None:
            log_warn(f"Missing calculated KPI values for key: {simple_key}. Defaulting to 0.")
            after_val = 0.0
            before_val = 0.0

        # Variance calculation uses the *calculation* decimals (2 for ELR, 0 otherwise)
        variance_decimals = 2 if simple_key == "ELR" else 0
        variance_dec = Decimal(str(after_val)) - Decimal(str(before_val))
        variance = round_off(float(variance_dec), variance_decimals)
        
        # Determine output formatting using the general function
        after_val_formatted = _format_metric_value(after_val, display_name, False)
        before_val_formatted = _format_metric_value(before_val, display_name, False)
        # Pass variance value and specify it is a variance
        variance_formatted = _format_metric_value(variance, display_name, True)

        # ***CRITICAL FIX: SWAP ASSIGNMENT TO MATCH UI MD COLUMN ORDER***
        kpis_block[display_name] = {
            # UI's "First Month" = Your AFTER FOPC data
            "First Month": after_val_formatted, 
            # UI's "Second Month" = Your BEFORE FOPC data
            "Second Month": before_val_formatted,
            "Variance": variance_formatted, # Variance must remain 'After - Before'
        }
        log_info(f"KPI: {display_name} -> UI First (After): {after_val_formatted}, UI Second (Before): {before_val_formatted}, Variance: {variance_formatted}")
    
    Client_Report_Card["KPIs"] = kpis_block
    log_info("-------------------------------------------------")


    # 3. OpCategory Blocks - NOW USING GENERAL FORMATTER
    log_info(f"\n--- OpCategory Block Values ---")
    for opcategory in ['COMPETITIVE', 'MAINTENANCE', 'REPAIR']:
        category_block = {}
        
        filtered_after = after_FOPC_groupby_category[after_FOPC_groupby_category['opcategory'] == opcategory.upper()]
        filtered_before = before_FOPC_groupby_category[before_FOPC_groupby_category['opcategory'] == opcategory.upper()]

        log_info(f"Category: {opcategory.title()}")
        
        if not filtered_after.empty and not filtered_before.empty:
            
            after_row = filtered_after.iloc[0]
            before_row = filtered_before.iloc[0]

            for display_name, column_key in client_report_one_month_opcategory_kpi_constants.items():
                
                after_val = after_row.get(column_key, 0)
                before_val = before_row.get(column_key, 0)
                
                after_val_dec = Decimal(str(after_val))
                before_val_dec = Decimal(str(before_val))
                
                # Variance is rounded to 2 decimals for ELR, 0 for others (calculation rounding).
                decimals = 2 if "ELR" in display_name else 0
                variance = round_off(float(after_val_dec - before_val_dec), decimals)

                # Determine output formatting using the general function
                # Note: Pass is_opcategory=True here to ensure ELR variance gets 2 decimals.
                after_val_formatted = _format_metric_value(after_val, display_name, False, is_opcategory=True)
                before_val_formatted = _format_metric_value(before_val, display_name, False, is_opcategory=True)
                variance_formatted = _format_metric_value(variance, display_name, True, is_opcategory=True)

                # ***CRITICAL FIX: SWAP ASSIGNMENT TO MATCH UI MD COLUMN ORDER***
                category_block[display_name] = {
                    "First Month": after_val_formatted,
                    "Second Month": before_val_formatted,
                    "Variance": variance_formatted,
                }
                log_info(f" {display_name} -> UI First (After): {after_val_formatted}, UI Second (Before): {before_val_formatted}, Variance: {variance_formatted}")
            
            Client_Report_Card[opcategory.title()] = category_block
        else:
            log_info(f" No data found for category '{opcategory.title()}' - block will be empty in JSON.")
    log_info("-------------------------------------------------")


    # Build the output path
    result_folder, json_output_path = create_folder_file_path(subfolder=omni_folder,output_file=dict_json)
    
    output_md_file_path = os.path.join(result_folder, dict_md)
    with open(json_output_path, "w") as json_file:
        json.dump(Client_Report_Card, json_file, indent=4)
        log_info(f"✔ Final JSON output saved to: {json_output_path}")

    # extract data from UI screenshot
    image_path = os.path.join(result_folder, dict_jpg)
    extract_image_data(client_report_card_prompt, image_path)
    # compare calculated values and the UI values
    compare_client_report_card(output_md_file_path, json_output_path)
    log_info(f"✔ Comparison report generated at: {output_md_file_path}")

    end_time = time.time()
    log_info(f"End Time: {datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S')}")
    log_info(f"Total time taken: {round_off(end_time - start_time, 2)} seconds")

    return True

def main(): 
    run_validation()

if __name__ == "__main__":
    main()
import os
from datetime import datetime
from dateutil.relativedelta import relativedelta
from dotenv import load_dotenv
from lib.pattern.config import config

load_dotenv()

def get_store_id():
    store_id_set = config.store_id

    if store_id_set is None:
        raise ValueError("store_id is not set in config")

    if store_id_set == 'all':
        return 'all'
    elif ',' in store_id_set:
        return f"({', '.join(repr(x.strip()) for x in store_id_set.split(','))})"
    else:
        return f"('{store_id_set.strip()}')"

def get_start_date():
    return config.start_date

def get_end_date():
    return config.end_date

def get_fopc_month():
    return config.fopc_month

def get_pre_fopc_month():
    return config.pre_fopc_month

def get_last_month():
    return config.last_month

def get_realm():
    realm_val = config.realm
    if not realm_val or realm_val.lower() == 'none':
        raise ValueError("realm is not set or invalid in config")
    return realm_val

def get_date_ranges_sql():
    last_month = get_last_month()
    if last_month is not None:
        last_month_date = datetime.strptime(last_month, "%Y-%m")
        date_ranges = [(last_month_date - relativedelta(months=i)).strftime("%Y-%m") for i in range(3)]
        date_ranges_sql = "({})".format(", ".join([f"'{date}'" for date in date_ranges]))
        return date_ranges_sql

# -------- Query classes --------
class OpcodeQuerygenerator:
    def generate_query(self):
       return f"SELECT department, opcode, opcategory, store_id, grid_excluded FROM stateful_cc_physical_rw.ro_opcodes WHERE store_id IN {get_store_id()} AND realm = '{get_realm()}';"


class payTypeFixedRateUpdateStatus:
    def generate_query(self):
        return f"SELECT id, opcode, pay_type, start_date, end_date, store_id FROM stateful_cc_physical_rw.fixed_rate_enable_disable_master_log WHERE id IS NOT NULL AND store_id in {get_store_id()} AND realm = '{get_realm()}';"

class payTypeFixedRateTable:
    def generate_query(self):
        return f"SELECT id, paytype, labor_fixedratevalue, parts_fixedratevalue, store_id, fixedratedate FROM stateful_cc_physical_rw.fixed_rate_master_paytype WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class opcodeFixedRateTable:
    def generate_query(self):
        return f"SELECT id, opcode, paytype, fixed_rate_value, store_id, fixed_rate_date FROM stateful_cc_physical_rw.fixed_rate_master WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class gridDataTable:
    def generate_query(self):
        return f"SELECT hours, col_0, col_1, col_2, col_3, col_4, col_5, col_6, col_7, col_8, col_9, store_id, created_date, door_rate, grid_type FROM stateful_cc_physical_rw.griddata_dtl WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class OpcodeTableQuery:
    def generate_query(self):
        return f"SELECT department,opcode,opcategory,grid_excluded,store_id, mpi_item FROM stateful_cc_physical_rw.ro_opcodes WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class partInventoryDetailsQuery:
    def generate_query(self):
        return f"SELECT id,partno,part_source,list_price,store_id FROM stateful_atm_source_raw.parts_inventory_details WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class MenuOpcodes:
    def get_menu_opcodes(self):
        return f"SELECT opcode FROM stateful_cc_physical_rw.menu_opcodes WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class payTypeMasterTableQuery:
    def generate_query(self):
        return f"SELECT pay_type,department,store_id FROM stateful_cc_physical_rw.pay_type_master WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class menuMasterTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.menu_master WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class menuServiceTypeTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.menu_service_type WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class assignedMenuModelsTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.assigned_menu_models WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class assignedMenuOpcodesTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.assigned_menu_opcodes WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class MPISetupTableQuery:
    def generate_query(self):
        return f"SELECT frh,is_active FROM stateful_cc_physical_rw.mpi_setup WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class MPIOpcodesTableQuery:
    def generate_query(self):
        return f"SELECT opcode FROM stateful_cc_physical_rw.mpi_opcodes WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class fleetCustomerFixedRateTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.labor_grid_fleet_account WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class allRevenueDetailsCPOverviewQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"
        return sql_query
    
class labourWorkmixComparisonQuery:
     def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id in {get_store_id()} and realm = '{get_realm()}';"
        return sql_query


class fleetPayTypeFixedRateTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.labor_grid_fleet_paytype WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class fleetOpcodeFixedRateTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.labor_grid_fleet_opcode WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class gridDataDetailsQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.griddata_dtl WHERE store_id in {get_store_id()} AND realm = '{get_realm()}';"

class allRevenueDetailsTableQuery:
    def generate_query(self):
        store_id_sql = get_store_id()
        stored_based_realm = get_realm()
        start_date = get_start_date()
        end_date = get_end_date()
        
        # If store_id is 'all', modify the SQL accordingly
        if store_id_sql == 'all':
            return (f"SELECT * FROM stateful_cc_aggregate.all_revenue_details "
                    f"WHERE closeddate >= '{start_date}' AND closeddate <= '{end_date}';")
        else:
            return (f"SELECT * FROM stateful_cc_aggregate.all_revenue_details "
                    f"WHERE store_id IN {store_id_sql} "
                    f"AND closeddate >= '{start_date}' AND closeddate <= '{end_date}' AND realm = '{stored_based_realm}';")
        
class allRevenueDetailsForClientReportCardQuery:
    def generate_query(self):
        sid = get_store_id()
        stored_based_realm = get_realm()
        fopc_month = get_fopc_month()
        pre_fopc_month = get_pre_fopc_month()
        if sid == 'all':
            return f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE month_year IN ('{fopc_month}' ,'{pre_fopc_month}');"
        return f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id in {sid} and month_year IN ('{fopc_month}' ,'{pre_fopc_month}') AND realm = '{stored_based_realm}';"

class allRevenueDetailsForClientReportCard3MonthQuery:
    def generate_query(self):
        sid = get_store_id()
        stored_based_realm = get_realm()
        s_date_obj = datetime.strptime(get_start_date(), "%Y-%m-%d")
        e_date_obj = datetime.strptime(get_end_date(), "%Y-%m-%d")
        s_date_env = s_date_obj.strftime("%Y-%m")
        e_date_env = e_date_obj.strftime("%Y-%m")
        date_ranges_sql = get_date_ranges_sql()
        if sid == 'all':
            return f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE ((month_year >= '{s_date_env}' AND month_year <= '{e_date_env}') OR month_year IN {date_ranges_sql});"
        return f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id in {sid} AND ((month_year >= '{s_date_env}' AND month_year <= '{e_date_env}') OR month_year IN {date_ranges_sql}) AND realm = '{stored_based_realm}';"

class allRevenueDetailsForShopSuppliesQuery:
    def generate_query(self, advisor_filter=None):
        """Generate query to get revenue details data for shop supplies calculation"""
        store_id_sql = get_store_id()
        start_date = get_start_date()
        end_date = get_end_date()
        stored_based_realm = get_realm()

        # Build advisor filter condition
        advisor_condition = ""
        if advisor_filter and advisor_filter != {'all'} and 'all' not in advisor_filter:
            advisor_list = list(advisor_filter)
            advisor_values = "', '".join(advisor_list)
            advisor_condition = f"AND serviceadvisor IN ('{advisor_values}')"

        base_query = f"""
        SELECT
            ronumber,
            closeddate,
            store_id,
            paytypegroup,
            lbrsale,
            lbrsoldhours,
            prtextendedsale,
            prtextendedcost,
            opcategory
        FROM stateful_cc_aggregate.all_revenue_details
        WHERE closeddate >= '{start_date}'
        AND closeddate <= '{end_date}'
        AND hide_ro = '0'
        AND department = 'Service'
        {advisor_condition}
        """

        if store_id_sql == 'all':
            return base_query
        else:
            return base_query + f" AND store_id IN {store_id_sql} AND realm = '{stored_based_realm}'"

class DiscountLaborDetailsQuery:
    def generate_query(self, advisor_filter=None):
        """Generate query to get discount labor details data"""
        store_id_sql = get_store_id()
        start_date = get_start_date()
        end_date = get_end_date()
        stored_based_realm = get_realm()

        # Build the base query WITHOUT semicolon
        sql_query = (
            f"SELECT * FROM stateful_cc_aggregate.discount_summary_labor "
            f"WHERE store_id IN {store_id_sql} "
            f"AND closeddate >= '{start_date}' "
            f"AND closeddate <= '{end_date}' "
            f"AND opcategory != 'N/A' "
            f"AND realm = '{stored_based_realm}'"
        )

        # Add advisor filter if provided and not 'all'
        if advisor_filter and advisor_filter != {'all'} and 'all' not in advisor_filter:
            advisor_list = list(advisor_filter)
            advisor_values = "', '".join(advisor_list)
            sql_query += f" AND serviceadvisor IN ('{advisor_values}')"

        # Add semicolon at the end
        sql_query += ";"
        
        return sql_query


class DiscountPartsDetailsQuery:
    def generate_query(self, advisor_filter=None):
        """Generate query to get discount parts details data"""
        store_id_sql = get_store_id()
        start_date = get_start_date()
        end_date = get_end_date()
        stored_based_realm = get_realm()

        # Build the base query WITHOUT semicolon
        sql_query = (
            f"SELECT * FROM stateful_cc_aggregate.discount_summary_parts "
            f"WHERE store_id IN {store_id_sql} "
            f"AND closeddate >= '{start_date}' "
            f"AND closeddate <= '{end_date}' "
            f"AND opcategory != 'N/A' "
            f"AND realm = '{stored_based_realm}'"
        )

        # Add advisor filter if provided and not 'all'
        if advisor_filter and advisor_filter != {'all'} and 'all' not in advisor_filter:
            advisor_list = list(advisor_filter)
            advisor_values = "', '".join(advisor_list)
            sql_query += f" AND serviceadvisor IN ('{advisor_values}')"

        # Add semicolon at the end
        sql_query += ";"
        
        return sql_query

class paytypeRetailFlagSettingQuery:
    def generate_query(self):
        """Generate query to get paytype retail flag settings"""
        store_id_sql = get_store_id()
        stored_based_realm = get_realm()

        base_query = """
        SELECT
            store_id,
            source_paytype,
            mapped_paytype
        FROM stateful_cc_physical_rw.paytype_retail_flag_setting
        """

        if store_id_sql == 'all':
            return base_query
        else:
            return base_query + f" WHERE store_id IN {store_id_sql} AND realm = '{stored_based_realm}'"

class shopSuppliesDataQuery:
    def generate_query(self):
        """Generate query to get shop supplies data from total_details table"""
        store_id_sql = get_store_id()
        start_date = get_start_date()
        end_date = get_end_date()
        stored_based_realm = get_realm()
       

        if store_id_sql == 'all':
            return (f"SELECT ronumber, closeddate, store_id, totalshopsupply "
                    f"FROM stateful_cc_ingest.total_details "
                    f"WHERE closeddate >= '{start_date}' AND closeddate <= '{end_date}';")
        else:
            return (f"SELECT ronumber, closeddate, store_id, totalshopsupply "
                    f"FROM stateful_cc_ingest.total_details "
                    f"WHERE store_id IN {store_id_sql} AND realm = '{stored_based_realm}' "
                    f"AND closeddate >= '{start_date}' AND closeddate <= '{end_date}';")


class getCustomerPayTypeGroups:
    def generate_query(self):
        return f"SELECT source_paytype, store_id FROM stateful_cc_physical_rw.paytype_retail_flag_setting prfs WHERE store_id in {get_store_id()} AND mapped_paytype = 'C' AND realm = '{get_realm()}';"

class allRevenueDetailsPartsWorkMixQuery:
    def generate_query(self):
        # sql_query = f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id in {get_store_id()} and realm = '{get_realm()}' ;"
        if get_store_id() != 'all':
            sql_query = f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id in {get_store_id()} and closeddate >= '{get_start_date()}' and closeddate <= '{get_end_date()}' and realm = '{get_realm()}';"
        else:
            sql_query = f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE closeddate >= '{get_start_date()}' and closeddate <= '{get_end_date()}' and realm = '{get_realm()}';"
        return sql_query
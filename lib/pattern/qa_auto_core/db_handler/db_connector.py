import sys
sys.path.append('../')
import psycopg2
import pandas as pd
import os
from dotenv import load_dotenv
from lib.pattern.config import config
from lib.std.universal.logger import log_info,log_error,log_warn
from .db_query_handler import OpcodeQuerygenerator, payTypeFixedRateUpdateStatus, payTypeFixedRateTable, opcodeFixedRateTable, gridDataTable, OpcodeTableQuery, partInventoryDetailsQuery, MenuOpcodes, payTypeMasterTableQuery, menuMasterTableQuery, menuServiceTypeTableQuery, assignedMenuModelsTableQuery, assignedMenuOpcodesTableQuery, MPISetupTableQuery,MPIOpcodesTableQuery, fleetCustomerFixedRateTableQuery, fleetPayTypeFixedRateTableQuery, fleetOpcodeFixedRateTableQuery, gridDataDetailsQuery,allRevenueDetailsTableQuery, allRevenueDetailsForClientReportCardQuery, allRevenueDetailsForClientReportCard3MonthQuery, getCustomerPayTypeGroups, allRevenueDetailsCPOverviewQuery, allRevenueDetailsPartsWorkMixQuery,allRevenueDetailsForShopSuppliesQuery, paytypeRetailFlagSettingQuery, shopSuppliesDataQuery,labourWorkmixComparisonQuery,DiscountLaborDetailsQuery,DiscountPartsDetailsQuery
load_dotenv()


def get_db_params_from_config() -> dict:
    return {
        'dbname': config.database_name,
        'user': os.getenv("db_user_name"),
        'password': os.getenv("db_password"),
        'host': os.getenv("host"),
        'port': os.getenv("port"),
    }

class DbConnector:

    @staticmethod    
    def run_query(query: str, caller: str, return_type: str = "df"):
        """
        Execute query safely.
        return_type = "df"  → Pandas DataFrame
        return_type = "set" → Python set()
        return_type = "one" → Single row/record
        """
        connection, cursor = None, None
        try:
            log_info(f"🔹 [{caller}] Attempting to connect to DB...")
            db_params = get_db_params_from_config()
            connection = psycopg2.connect(**db_params)
            log_info(f"✅ [{caller}] DB connection established.")

            cursor = connection.cursor()
            # log_info(f"🔹 [{caller}] Executing query: {query}")
            cursor.execute(query)
            log_info(f"✅ [{caller}] Query executed successfully.")

            if cursor.description:
                column_names = [desc[0] for desc in cursor.description]
            else:
                column_names = []

            if return_type == "one":
                return cursor.fetchone()

            rows = cursor.fetchall()
            if return_type == "set":
                return {row[0] for row in rows}
            if return_type == "df":
                return pd.DataFrame(rows, columns=column_names)

        except (Exception, psycopg2.Error) as error:
            log_error(f"❌ [{caller}] Error: {error}")
            if return_type == "set":
                return set()
            if return_type == "one":
                return None
            return pd.DataFrame()

        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
                log_info(f"🔹 [{caller}] DB connection closed.")



    def DbHandler():
        query = OpcodeQuerygenerator().generate_query()

        # Run query via DbConnector
        df = DbConnector.run_query(
            query,
            caller="DbHandler",
            return_type="df"
        )

        department_arr = []
        if not df.empty:
            for _, row in df.iterrows():
                data = (
                    f"Department: {row['department']}, "
                    f"Opcode: {row['opcode']}, "
                    f"Opcategory: {row['opcategory']}, "
                    f"Store_id: {row['store_id']}, "
                    f"Grid_excluded: {row['grid_excluded']}"
                )
                department_arr.append(data)

        return department_arr


class opcodePayTypeFixedRateStatus:
    def getTableResult(self):
        query = payTypeFixedRateUpdateStatus().generate_query()
        df = DbConnector.run_query(
            query,
            caller="opcodePayTypeFixedRateStatus.getTableResult",
            return_type="df"
        )
        return df


class payTypeFixedRates:
    def getTableResult(self):
        query = payTypeFixedRateTable().generate_query()
        df = DbConnector.run_query(
            query,
            caller="payTypeFixedRates.getTableResult",
            return_type="df"
        )
        return df


class opcodeFixedRates:
    def getTableResult(self):
        query = opcodeFixedRateTable().generate_query()
        df = DbConnector.run_query(
            query,
            caller="opcodeFixedRates.getTableResult",
            return_type="df"
        )
        return df


class gridData:
    def getTableResult(self):
        query = gridDataTable().generate_query()
        df = DbConnector.run_query(
            query,
            caller="gridData.getTableResult",
            return_type="df"
        )
        return df


class opcodeTable:
    def getTableResult(self):
        query = OpcodeTableQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="opcodeTable.getTableResult",
            return_type="df"
        )
        return df


class partInventoryTable:
    def getTableResult(self):
        query = partInventoryDetailsQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="partInventoryTable.getTableResult",
            return_type="df"
        )
        return df


class MenuOpcodesList:
    def menu_opcode_list(self):
        query = MenuOpcodes().get_menu_opcodes()
        result = DbConnector.run_query(
            query,
            caller="MenuOpcodesList.menu_opcode_list",
            return_type="one"
        )
        return result


class payTypeMasterTableResult:
    def getTableResult(self):
        query = payTypeMasterTableQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="payTypeMasterTableResult.getTableResult",
            return_type="df"
        )
        return df


class menuMasterTableResult:
    def getTableResult(self):
        query = menuMasterTableQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="menuMasterTableResult.getTableResult",
            return_type="df"
        )
        return df


class menuServiceTypeTableResult:
    def getTableResult(self):
        query = menuServiceTypeTableQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="menuServiceTypeTableResult.getTableResult",
            return_type="df"
        )
        return df


class assignedMenuModelsTableResult:
    def getTableResult(self):
        query = assignedMenuModelsTableQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="assignedMenuModelsTableResult.getTableResult",
            return_type="df"
        )
        return df


class assignedMenuOpcodesTableResult:
    def getTableResult(self):
        query = assignedMenuOpcodesTableQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="assignedMenuOpcodesTableResult.getTableResult",
            return_type="df"
        )
        return df


class MPISetupTableResult:
    def getTableResult(self):
        query = MPISetupTableQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="MPISetupTableResult.getTableResult",
            return_type="df"
        )
        return df


class MPIOpcodesTableResult:
    def getTableResult(self):
        query = MPIOpcodesTableQuery().generate_query()
        result = DbConnector.run_query(
            query,
            caller="MPIOpcodesTableResult.getTableResult",
            return_type="set"
        )
        return result


class FleetCustomerFixedRateTableResult:
    def getTableResult(self):
        query = fleetCustomerFixedRateTableQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="FleetCustomerFixedRateTableResult.getTableResult",
            return_type="df"
        )
        return df


class FleetPayTypeFixedRateTableResult:
    def getTableResult(self):
        query = fleetPayTypeFixedRateTableQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="FleetPayTypeFixedRateTableResult.getTableResult",
            return_type="df"
        )
        return df


class FleetOpcodeFixedRateTableResult:
    def getTableResult(self):
        query = fleetOpcodeFixedRateTableQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="FleetOpcodeFixedRateTableResult.getTableResult",
            return_type="df"
        )
        return df


class gridDataDetailsTableResult:
    def getTableResult(self):
        query = gridDataDetailsQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="gridDataDetailsTableResult.getTableResult",
            return_type="df"
        )
        return df


class allRevenueDetailsTable:
    def getTableResult(self):
        query = allRevenueDetailsTableQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="allRevenueDetailsTable.getTableResult",
            return_type="df"
        )
        return df


class allRevenueDetailsForClientReportCard:
    def getTableResult(self):
        query = allRevenueDetailsForClientReportCardQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="allRevenueDetailsForClientReportCard.getTableResult",
            return_type="df"
        )
        return df


class allRevenueDetailsForClientReportCard3Month:
    def getTableResult(self):
        query = allRevenueDetailsForClientReportCard3MonthQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="allRevenueDetailsForClientReportCard3Month.getTableResult",
            return_type="df"
        )
        return df


class getCustomerPayTypeGroupsList:
    def getCustomerPayTypeList(self):
        query = getCustomerPayTypeGroups().generate_query()
        df = DbConnector.run_query(
            query,
            caller="getCustomerPayTypeGroupsList.getCustomerPayTypeList",
            return_type="df"
        )
        return df


class allRevenueDetailsCPOverview:
    def getTableResult(self):
        query = allRevenueDetailsCPOverviewQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="allRevenueDetailsCPOverview.getTableResult",
            return_type="df"
        )
        return df

class workMixComparisonTableResult:
    def getTableResult(self):
        query = labourWorkmixComparisonQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="workMixComparisonTableResult.getTableResult",
            return_type="df"
        )
        return df
    
class allRevenueDetailsForShopSuppliesTable:
    def getTableResult(self, advisor_filter=None):
        """Get revenue details data for shop supplies calculation"""
        query = allRevenueDetailsForShopSuppliesQuery().generate_query(advisor_filter)
        df = DbConnector.run_query(
            query,
            caller="allRevenueDetailsForShopSuppliesTable.getTableResult",
            return_type="df"
        )
        return df

class DiscountLaborDetails:
    def getTableResult(self, advisor_filter=None):
        """Get discount labor details data"""
        query = DiscountLaborDetailsQuery().generate_query(advisor_filter)
        df = DbConnector.run_query(
            query,
            caller="DiscountLaborDetails.getTableResult",
            return_type="df"
        )
        return df


class DiscountPartsDetails:
    def getTableResult(self, advisor_filter=None):
        """Get discount parts details data"""
        query = DiscountPartsDetailsQuery().generate_query(advisor_filter)
        df = DbConnector.run_query(
            query,
            caller="DiscountPartsDetails.getTableResult",
            return_type="df"
        )
        return df



class paytypeRetailFlagSettingTable:
    def getTableResult(self):
        """Get paytype retail flag settings"""
        query = paytypeRetailFlagSettingQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="paytypeRetailFlagSettingTable.getTableResult",
            return_type="df"
        )
        return df

class shopSuppliesDataTable:
    def getTableResult(self):
        """Get shop supplies data from total_details table"""
        query = shopSuppliesDataQuery().generate_query()
        df = DbConnector.run_query(
            query,
            caller="shopSuppliesDataTable.getTableResult",
            return_type="df"
        )
        return df
 
class allRevenueDetailsPartsWorkMixAnalysis:
    def getTableResult(self):
       
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            db_params = get_db_params_from_config()
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()
 
            query = allRevenueDetailsPartsWorkMixQuery()
            allRevenueDetailsQuery = query.generate_query()
 
            cursor.execute(allRevenueDetailsQuery)
 
            for desc in cursor.description:
                column_names.append(desc[0])
 
            rows = cursor.fetchall()
            result.extend(rows)
            # print("  ---   rows --- ", rows)
 
        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
       
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        allRevenueDetails_Table = pd.DataFrame(result, columns=column_names)
        return allRevenueDetails_Table 
  

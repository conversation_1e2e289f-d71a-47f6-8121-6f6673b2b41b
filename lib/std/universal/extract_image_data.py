import os
import json
import asyncio
import logging
from pyzerox import zerox
from dotenv import load_dotenv
from .image_to_pdf import convert_image_to_pdf
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path

load_dotenv()

# Load environment variables
MODEL = os.getenv("GEMINI_MODEL", "gemini/gemini-2.0-flash")
API_KEY = os.getenv("GEMINI_API_KEY")
OUTPUT_DIR = create_folder_file_path(subfolder="Omni_Results" )


# Ensure API key is set
if not API_KEY:
    raise ValueError("GEMINI_API_KEY is not set in the environment variables.")

os.environ["GEMINI_API_KEY"] = API_KEY

# Placeholder for additional model kwargs which might be required for some models
kwargs = {}


def extract_image_data(custom_system_prompt, image_path, select_pages=None, **kwargs):
    """Processes a single image by converting to PDF and running zerox gracefully."""
    async def run_zerox():
        try:
            if not os.path.exists(image_path):
                msg = f"❌ Image not found: {image_path}"
                logging.error(msg)
                return {"error": msg, "file": image_path}

            pdf_path = os.path.splitext(image_path)[0] + ".pdf"
            file_path = convert_image_to_pdf(image_path, pdf_path)

            return await zerox(
                file_path=file_path,
                model=MODEL,
                output_dir=OUTPUT_DIR,
                custom_system_prompt=custom_system_prompt,
                select_pages=select_pages,
                **kwargs
            )
        except Exception as e:
            msg = f"❌ Failed to extract data from {image_path}: {e}"
            logging.error(msg)
            return {"error": str(e), "file": image_path}

    return asyncio.run(run_zerox())

def extract_image_data_split(custom_system_prompt, image_paths, select_pages=None, **kwargs):
    """Processes multiple images and extracts JSON using zerox gracefully."""

    async def run_all():
        tasks = []
        for image_path in image_paths:
            try:
                if not os.path.exists(image_path):
                    msg = f"❌ Image not found: {image_path}"
                    logging.error(msg)
                    tasks.append(asyncio.sleep(0, result={"error": msg, "file": image_path}))
                    continue

                pdf_path = os.path.splitext(image_path)[0] + ".pdf"
                file_path = convert_image_to_pdf(image_path, pdf_path)

                task = zerox(
                    file_path=file_path,
                    model=MODEL,
                    output_dir=OUTPUT_DIR,
                    custom_system_prompt=custom_system_prompt,
                    select_pages=select_pages,
                    **kwargs
                )
                tasks.append(task)
            except Exception as e:
                msg = f"❌ Failed preparing {image_path}: {e}"
                logging.error(msg)
                tasks.append(asyncio.sleep(0, result={"error": str(e), "file": image_path}))

        # gather with return_exceptions=True so one failure doesn’t kill the batch
        return await asyncio.gather(*tasks, return_exceptions=True)

    return asyncio.run(run_all())

if __name__ == "__main__":
    file_path = os.getenv("FILE_PATH", "").strip()
    system_prompt = os.getenv("SYSTEM_PROMPT", "Extract structured KPI data in JSON format.")

    if not file_path:
        print("⚠️ No FILE_PATH environment variable set.")
    elif not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
    else:
        result = extract_image_data(system_prompt, file_path)
        print(result)

import os
import json
from typing import Any, Dict, Tuple
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import log_info,log_error,log_warn

omni_folder="Omni_Results"
prompt_folder="Custom_Prompts"

# --- CONSTANTS DEFINITION CLIENT REPORT CARD THREE MONTHS---
import json
from typing import Dict, Any

class ClientReportCard3Month:
    """
    Encapsulates all constants, metrics, and the dynamic prompt generation
    logic for the Client Report Card - Three Months, using snake_case attributes.
    """
    
    # --- 1. Constants for Main Metrics (Top Level) ---
    monthly_fopc = "Monthly FOPC"
    monthly_dms = "Monthly DMS"
    total = "Total"
    roi = "ROI"
    time_period = "Time Period"
    total_gp_change = "Total Pts & Lbr GP Change"
    repair_elr_change = "Repair ELR Change"
    roi_annualized = "ROI_Annualized"
    time_period_annualized = "Time Period_Annualized"
    total_gp_change_annualized = "Total Pts & Lbr GP Change_Annualized"
    repair_elr_change_annualized = "Repair ELR Change_Annualized"

    # --- 2. Constants for Sub-Metrics (Repeated columns like 3 MTH Avg) ---
    baseline = "3 MTH Avg (Baseline)"
    last_month = "Last Month"
    variance = "Variance"
    prior_annual_pace = "Prior Annual Pace"
    annual_pace = "Annual Pace"
    variance_annualized = "Variance Annualized"

    # A list of the common sub-metric keys (must use the snake_case variables above)
    common_sub_metric_keys = [
        baseline,
        last_month,
        variance,
        prior_annual_pace,
        annual_pace,
        variance_annualized,
    ]

    # --- 3. Constants for Grouped Metrics (KPIs, COMPETITIVE, etc.) ---
    # Metric Keys
    ro_count = "RO Count"
    hours_sold = "Hours Sold"
    cust_pay_hrs_per_ro = "Cust. Pay Hrs Per RO"
    total_shop_elr = "Total Shop ELR"
    total_labor_gp_percent = "Total Labor GP%"
    total_parts_gp_percent = "Total Parts GP%"
    total_labor_sold = "Total Labor Sold"
    total_labor_gp = "Total Labor GP"
    total_parts_sale = "Total Parts Sale"
    total_parts_gp = "Total Parts GP"
    total_lbr_pts_sales = "Total Lbr & Pts Sales"
    total_lbr_pts_gp_kpi = "Total Lbr & Pts GP" # Used in KPIs
    elr = "ELR"
    percent_of_total_shop_hours = "% of Total Shop Hours"
    lbr_pts_gp_group = "Lbr & Pts GP" # Used in COMPETITIVE, MAINTENANCE, REPAIR

    # --- 4. Constants for Top-Level Sections ---
    kpi_section = "KPIs"
    competitive_section = "COMPETITIVE"
    maintenance_section = "MAINTENANCE"
    repair_section = "REPAIR"

    @classmethod
    def _create_sub_metric_structure(cls, is_percent: bool = False) -> Dict[str, str]:
        """Creates the standard '3 MTH Avg' structure with 'value' placeholders."""
        sub_metrics = {}
        suffix = "%" if is_percent else ""
        # Accessing the list using the lowercase attribute name
        for key in cls.common_sub_metric_keys:
            sub_metrics[key] = f"value{suffix}"
        return sub_metrics

    @classmethod
    def generate_prompt(cls) -> str:
        """Dynamically generates the system prompt for the three-month report."""

        # 1. KPIs Section
        kpi_metrics = {
            # All constants are accessed via the lowercase class attribute name
            cls.ro_count: cls._create_sub_metric_structure(),
            cls.hours_sold: cls._create_sub_metric_structure(),
            cls.cust_pay_hrs_per_ro: cls._create_sub_metric_structure(),
            cls.total_shop_elr: cls._create_sub_metric_structure(),
            cls.total_labor_gp_percent: cls._create_sub_metric_structure(is_percent=True),
            cls.total_parts_gp_percent: cls._create_sub_metric_structure(is_percent=True),
            cls.total_labor_sold: cls._create_sub_metric_structure(),
            cls.total_labor_gp: cls._create_sub_metric_structure(),
            cls.total_parts_sale: cls._create_sub_metric_structure(),
            cls.total_parts_gp: cls._create_sub_metric_structure(),
            cls.total_lbr_pts_sales: cls._create_sub_metric_structure(),
            cls.total_lbr_pts_gp_kpi: cls._create_sub_metric_structure(),
        }

        # Metrics common to COMPETITIVE, MAINTENANCE, and REPAIR
        comp_maint_repair_metrics_list = [
            cls.hours_sold, cls.elr, cls.percent_of_total_shop_hours, 
            cls.total_labor_sold, cls.total_labor_gp, cls.total_labor_gp_percent, 
            cls.total_parts_sale, cls.total_parts_gp, cls.total_parts_gp_percent,
            cls.total_lbr_pts_sales, cls.lbr_pts_gp_group,
        ]

        # 2. Competitive, Maintenance, and Repair Sections
        comp_maint_repair_groups = {}
        for group in [cls.competitive_section, cls.maintenance_section, cls.repair_section]:
            group_metrics = {}
            for metric in comp_maint_repair_metrics_list:
                is_percent = metric.endswith('%')
                if metric == cls.lbr_pts_gp_group:
                    is_percent = False
                
                group_metrics[metric] = cls._create_sub_metric_structure(is_percent)
            comp_maint_repair_groups[group] = group_metrics

        # 3. Final Overall Structure
        json_structure = {
            cls.monthly_fopc: "value", cls.monthly_dms: "value", cls.total: "value",
            cls.roi: "value%", cls.time_period: "value", cls.total_gp_change: "value",
            cls.repair_elr_change: "value", cls.roi_annualized: "value%",
            cls.time_period_annualized: "value", cls.total_gp_change_annualized: "value",
            cls.repair_elr_change_annualized: "value",
            cls.kpi_section: kpi_metrics, # Accessing the section key
        }
        json_structure.update(comp_maint_repair_groups)

        JSON_FORMAT = json.dumps(json_structure, indent=4)

        custom_system_prompt = (
            "Analyze this document and provide the contents in JSON format. The labels should be correctly mapped as in the image. "
            "\n\n"
            "The output should be in the following format\n"
            f"{JSON_FORMAT}"
        )
        
        return custom_system_prompt

# A class property to hold the final prompt for one-time generation
client_report_three_month_prompt = ClientReportCard3Month.generate_prompt()


# --- CONSTANTS DEFINITION CLIENT REPORT CARD---

# 1. Main KPI Constants: Maps JSON display name to a simple programmatic key.
client_report_one_month_kpi_constants = {
    "RO Count": "ro_count",
    "Hours Sold": "sold_hours",
    "Hours Per RO": "hours_per_RO",
    "Total Shop ELR": "ELR",
    "Total Labor GP%": "labor_GP_perc",
    "Total Labor Sales": "labor_sale",
    "Total Labor GP": "labor_GP",
    "Total Parts Sales": "parts_sale",
    "Total Parts GP": "parts_GP",
    "Total Parts GP%": "parts_GP_perc",
    "Total Parts & Labor Sales": "parts_labor_sale",
    "Total Parts & Labor GP": "parts_labor_GP",
}

# 2. OpCategory KPI Constants: Maps JSON display name to its programmatic column/key,
# used for both JSON prompt generation and output formatting.
client_report_one_month_opcategory_kpi_constants = {
    "Hours Sold": "hours_sold",
    "ELR": "elr",
    "% of Total Shop Hours": "perc_of_total_hours",
    "Total Labor Sale": "total_labor_sale"
}

# Helper mapping for local variables to the simple keys in client_report_one_month_opcategory_kpi_constants
# Used to dynamically access calculated local variables in run_validation.
client_report_after_fopc_vars = {
    "ro_count": "after_FOPC_ro_count",
    "sold_hours": "after_FOPC_sold_hours",
    "hours_per_RO": "after_FOPC_hours_per_RO",
    "ELR": "after_FOPC_ELR",
    "labor_GP_perc": "after_FOPC_labor_GP_perc",
    "labor_sale": "after_FOPC_labor_sale",
    "labor_GP": "after_FOPC_labor_GP",
    "parts_sale": "after_FOPC_parts_sale",
    "parts_GP": "after_FOPC_parts_GP",
    "parts_GP_perc": "after_FOPC_parts_GP_perc",
    "parts_labor_sale": "after_FOPC_parts_labor_sale",
    "parts_labor_GP": "after_FOPC_parts_labor_GP",
}

client_report_before_fopc_vars = {
    "ro_count": "before_FOPC_ro_count",
    "sold_hours": "before_FOPC_sold_hours",
    "hours_per_RO": "before_FOPC_hours_per_RO",
    "ELR": "before_FOPC_ELR",
    "labor_GP_perc": "before_FOPC_labor_GP_perc",
    "labor_sale": "before_FOPC_labor_sale",
    "labor_GP": "before_FOPC_labor_GP",
    "parts_sale": "before_FOPC_parts_sale",
    "parts_GP": "before_FOPC_parts_GP",
    "parts_GP_perc": "before_FOPC_parts_GP_perc",
    "parts_labor_sale": "before_FOPC_parts_labor_sale",
    "parts_labor_GP": "before_FOPC_parts_labor_GP",
}

# Currency formatting for dynamic generation
client_report_currency_keys = ["Monthly", "Total", "ELR", "Sale", "GP", "Change"]
client_report_percent_keys = ["%", "ROI"]

# --- DYNAMIC PROMPT GENERATION ---

def _get_format_placeholder(kpi_name: str) -> str:
    """Determines the correct JSON placeholder string based on the KPI name."""
    if any(key in kpi_name for key in client_report_percent_keys):
        return '"value%"'
    if any(key in kpi_name for key in client_report_currency_keys):
        return '"$value"'
    return '"value"' # For counts, hours, or simple numbers

def _generate_nested_json_block(kpi_constants: Dict[str, Any]) -> str:
    """Generates the JSON string for nested blocks (KPIs or OpCategory)."""
    lines = []
    for kpi_display_name in kpi_constants.keys():
        placeholder = _get_format_placeholder(kpi_display_name)
        # Use single quotes for the f-string template and double quotes inside the JSON string
        block = f"""
    "{kpi_display_name}": {{
      "Second Month": {placeholder},
      "First Month": {placeholder},
      "Variance": {placeholder}
    }}"""
        lines.append(block)
    return ',\n'.join(lines).strip()

def _generate_custom_system_prompt() -> str:
    """Dynamically generates the full CUSTOM_SYSTEM_PROMPT string using constants."""
    
    # 1. Main header items (Must match the expected output structure)
    main_items = {
        "Monthly FOPC": "$value",
        "Monthly DMS": "$value",
        "Total": "$value",
        "ROI": "value%",
        "Total Pts & Lbr GP Change": "$value",
        "Repair ELR Change": "$value",
    }
    
    main_kpi_block_lines = []
    for kpi_name, value_type in main_items.items():
        main_kpi_block_lines.append(f'  "{kpi_name}": "{value_type}"')

    # 2. KPIs block
    kpis_block_content = _generate_nested_json_block(client_report_one_month_kpi_constants)

    # 3. OpCategory blocks
    opcategory_block_content = _generate_nested_json_block(client_report_one_month_opcategory_kpi_constants)
    
    json_template = f"""
    {{
    {',\n'.join(main_kpi_block_lines)},
    "KPIs": {{
    {kpis_block_content}
    }},
    "Competitive": 
    {{
    {opcategory_block_content}
    }},
    "Maintenance": 
    {{
    {opcategory_block_content}
    }},
    "Repair": 
    {{
    {opcategory_block_content}
    }}
    }}
    """
    
    prompt_header = "Analyze this document and provide the contents in JSON format. The labels should be correctly mapped as in the image "
    
    return (
        prompt_header +
        f'\n    """\n    The output should be in the following format\n    {json_template}\n    """'
    )


client_report_card_prompt = _generate_custom_system_prompt()

# --- END DYNAMIC PROMPT GENERATION ---

#-------------------KPI SCORE CARD--------------------------------------------------

kpi_section_headers = {
    "A": "A) Financial - Customer Pay",
    "B": "B) Pricing - Customer Pay",
    "C": "C) Volume",
    "D": "D) Opportunities - CP Vehicles Under 60K Miles",
    "E": "E) Opportunities - MPI (CP and Wty)",
    "F": "F) Opportunities - Menu Sales (CP and Wty)",
    "G": "G) Opportunities - CP Vehicles Over 60K Miles",
}

# Constants for KPI Item Keys (Section A)
kpi_a_keys = [
    'Labor Sales',
    'Labor Gross Profit',
    'Labor Sales Per RO',
    'Labor GP Per RO',
    'Parts Sales',
    'Parts Gross Profit',
    'Parts Sales Per RO',
    'Parts GP Per RO',
    'Labor + Parts Sales',
    'Labor + Parts Gross Profit',
    'Parts to Labor Ratio'
]

# Constants for KPI Item Keys (Section B: Pricing - Customer Pay)
kpi_b_keys = [
    "Repair Price Targets / Misses / Non-Compliance %",
    "Parts Price Targets / Misses / Non-Compliance %",
    "Competitive Hours / Sales / ELR",
    "Maintenance Hours / Sales / ELR",
    "Repair Hours / Sales / ELR",
    "Total Hours / Total Sales / Total ELR",
    "What-If” Repair ELR if Non-Compliance at 0%",
    "What-If” Total ELR if Repair Non-Compliance at 0%",
    "Maintenance / Repair Work Mix"
]

# Constants for KPI Item Keys (Section C: Volume)
kpi_c_keys = [
    "Customer Pay Vehicles",
    "Warranty Vehicles",
    "Internal Vehicles ",
    "All Vehicles",
    "Average Vehicles Per Day",
    "% of Vehicles Serviced",
    "Average Days ROs are Open",
    "Average Vehicle Age",
    "Average Miles Per Vehicle",
    "All Sold Hours",
    "Average Hours Sold Per Day",
    "Average CP Hours Per Vehicle"
]

# Constants for KPI Item Keys (Section D: Opportunities - CP Vehicles Under 60K Miles)
kpi_d_keys = [
    "Total Count / % of Business",
    "1 Line Count / % Under 60K",
    "Labor Sold Per 1 Line RO",
    "Parts Sold Per 1 Line RO",
    "Total Sold Per 1 Line RO",
    "Labor Sold Per Multi-Line RO",
    "Parts Sold Per Multi-Line RO",
    "Total Sold Per Multi-Line RO",
    "Average Jobs Per Multi-Line RO"
]
# Constants for KPI Item Keys (Section G:)
kpi_g_keys = [
    "Total Count / % of Business",
    "1 Line Count / % Over 60K",
    "Labor Sold Per 1-Line RO",
    "Parts Sold Per 1-Line RO",
    "Total Sold Per 1-Line RO",
    "Labor Sold Per Multi-Line RO",
    "Parts Sold Per Multi-Line RO",
    "Total Sold Per Multi-Line RO",
    "Average Jobs Per Multi-Line RO"
]



# Constants for KPI Item Keys (Sections E and F)

# Keys for MPI (E)
kpi_e_keys = [
    "Opportunities / Completed / %",
    "Upsell Potential $",
    "Sold $ / % Collected",
    "Potential Hours / Sold Hours / %",
    "Hours Sold Per Completed",
]

# Keys for Menu Sales (F)
kpi_f_keys = [
    "Opportunities / Sold / %",
    "Upsell Potential $",
    "Sold $ / % Collected",
    "Potential Hours / Sold Hours / %",
    "Hours Sold Per Menu", # Key name is different from E
]

# Consolidate all keys into a single map for dynamic prompt generation
kpi_key_map = {
    kpi_section_headers["A"]: kpi_a_keys,
    kpi_section_headers["B"]: kpi_b_keys,
    kpi_section_headers["C"]: kpi_c_keys,
    kpi_section_headers["D"]: kpi_d_keys,
    kpi_section_headers["E"]: kpi_e_keys,
    kpi_section_headers["F"]: kpi_f_keys,
    kpi_section_headers["G"]: kpi_g_keys,
}

# --- 1. Dynamically calculate ignored headers (Used globally) ---
kpi_scorecard_ignored_headers = set(kpi_section_headers.values())


# --- 2. Dynamic Prompt Generation Function ---

def get_kpi_placeholder(key: str) -> str:
    """Determines the correct value placeholder string for the prompt JSON."""
    
    # Simple value placeholder that will not be quoted in the final JSON string
    simple_value = 'value' 
    
    # Complex ratio placeholders that must be quoted in the JSON string
    if key in ['Labor Gross Profit', 'Parts Gross Profit']:
        # 'value/ %value' or 'value /%value' from original prompt. Using the format from A/F/E.
        return '"value/value%"' # Standardize to the common % ratio format

    if key in [
        "Repair Price Targets / Misses / Non-Compliance %",
        "Parts Price Targets / Misses / Non-Compliance %",
        "Maintenance / Repair Work Mix",
        "Total Count / % of Business ",
        "1 Line Count / % Under 60K",
        "Total Count / % of Business",
        "1 Line Count / % Over 60K",
        "Total Count / % of Business"
    ]:
        return '"value/value"' # Standard value/value ratio

    if key in [
        "Competitive Hours / Sales / ELR",
        "Maintenance Hours / Sales / ELR",
        "Repair Hours / Sales / ELR",
        "Total Hours / Total Sales / Total ELR",
    ]:
        return '"value / value / value"' # Standard triple ratio format

    if key in [
        "Opportunities / Completed / %",
        "Sold $ / % Collected",
        "Potential Hours/Sold Hours/%",
        "Opportunities / Sold / %",
        "Sold $ / % Collected-Menu",
        "Potential Hours / Sold Hours / %"
    ]:
        return '"value/value/value%"' # Standard triple/percent ratio format
    
    # Default: simple value
    return simple_value


def generate_kpi_scorecard_prompt() -> str:
    """Generates the full system prompt with the dynamically created JSON template."""
    
    kpis_template = {}
    
    for header, keys in kpi_key_map.items():
        # Add the section header with a null value
        kpis_template[header] = 'null'
        
        # Add all KPI keys for that section with their respective placeholders
        for key in keys:
            kpis_template[key] = get_kpi_placeholder(key)
    
    sample_output = {
        "KPI Scorecard": kpis_template
    }

    # Dump to JSON, then manually replace the 'value' strings that should not be quoted
    json_string = json.dumps(sample_output, indent=4)
    # This replacement ensures simple 'value' placeholders are not quoted as strings
    for header in kpi_key_map.keys():
        json_string = json_string.replace(f'"{header}": "null"', f'"{header}": null')
    json_string = json_string.replace('\"value\"', 'value')
    
    prompt = (
        "Analyze this document and provide the contents in json format. The labels should be correctly mapped as in the image. "
        "Sample Output format is given below"
        f"""{json_string}"""
    )
    
    return os.getenv("CUSTOM_SYSTEM_PROMPT", prompt)

# --- Dynamic Assignment ---

# This variable now holds the dynamically generated prompt, ensuring all keys are present.
kpi_scorecard_prompt = generate_kpi_scorecard_prompt() 


#-------------------KPI SCORE CARD---------------------------------------------------------------------------------------------------------------------------------

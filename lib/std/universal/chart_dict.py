"""
Defines all charts and their corresponding validation scripts and HTML files.
This serves as the single source of truth for orchestration and report generation.
"""

VALIDATION_CHARTS = {    
    "kpi_scorecard": {
        "script": "validate_kpi_scorecard.py",
        "jpg": "kpi_dashboard.jpg",
        "html": "kpi_dashboard.html",
        "md": "kpi_dashboard.md",
        "json": "kpi_dashboard.json",
        "csv": "comparison_results_kpi_dashboard.csv",
        "xlsx": "comparison_results_kpi_dashboard_highlighted.xlsx"
    },
    "client_report_card_one_month": {
        "script": "validate_client_report_card.py",
        "html": "client_report_card_one_month.html",
        "jpg": "client_report_card_one_month.jpg",
        "json": "client_report_card_one_month.json",
        "xlsx": "comparison_client_report_card_one_month_highlighted.xlsx",
        "csv": "comparison_results_client_report_card_one_month.csv",
        "md": "client_report_card_one_month.md"
    },
    "client_report_card_3_months": {
        "script": "validate_client_report_card_3_months.py",
        "html": "client_report_card_three_months.html",
        "jpg": "client_report_card_three_months.jpg",
        "json": "client_report_card_three_months.json",
        "xlsx": "comparison_client_report_three_months_highlighted.xlsx",
        "csv": "comparison_result_three_months_client_report.csv",        
        "md": "client_report_card_three_months.md"
    },
    "cp_summary_overview": {
        "script": "validate_cp_summary_overview.py",
        "html": "cp_overview_comparison.html",
        "json": "cp_overview_comparison.json",
        "csv": "cp_overview_comparison_results.csv",
        "xlsx": "cp_overview_comparison_highlighted.xlsx",
        "chart_process_json": "chart_processing_all_cp_summary.json",
        "db_json": "db_calculated_value_cp_summary_overview.json"
    },
    "work_mix_two_months_comparison": {
        "script": "validate_work_mix_two_months_comparison.py",
        "html": "workmix_two_month_comparison.html",
        "json": "work_mix_two_months_comparison.json",
        "csv": "work_mix_two_months_comparison.csv",
        "xlsx": "work_mix_two_months_comparison_highlighted.xlsx",
        "chart_process_json": "chart_processing_all_work_mix_two_months_comparison.json",
        "db_json": "db_calculated_value_work_mix_two_months_comparison.json",
        "transformed_json":"transformed_work_mix_two_months_comparison.json"
    },
    "special_metrics": {
        "script": "validate_metrics.py",
        "html": "special_metrics_comparison.html",
        "json": "special_metrics_comparison.json",
        "csv": "special_metrics_comparison_results.csv",
        "xlsx": "special_metrics_comparison_highlighted.xlsx",
        "chart_process_json": "chart_processing_all_special_metrics.json",
        "db_json": "db_calculated_value_special_metrics.json"
    },
    "discount_metrics_13_month_trend": {
        "script": "validate_discount_metrics_13_month_trend.py",
        "html": "discount_metrics_13_month_trend_comparison.html",
        "json": "discount_metrics_13_month_trend_comparison.json",
        "csv": "discount_metrics_13_month_trend_comparison_results.csv",
        "xlsx": "discount_metrics_13_month_trend_comparison_highlighted.xlsx",
        "chart_process_json": "chart_processing_all_discount_metrics_13_month_trend.json",
        "db_json": "db_calculated_value_discount_metrics_13_month_trend.json"
    },
    "cp_parts_overview": {
        "script": "validate_cp_parts_overview.py",
        "html": "cp_parts_overview_comparison.html",
        "json": "cp_parts_overview_comparison.json",
        "csv": "cp_parts_overview_comparison_results.csv",
        "xlsx": "cp_parts_overview_comparison_highlighted.xlsx",
        "chart_process_json": "chart_processing_all_cp_parts.json",
        "db_json": "db_calculated_value_cp_parts_overview.json"
    },
    "parts_work_mix_comparison": {
        "script": "validate_parts_workmix_2_months_comparison.py",
        "html": "validate_parts_workmix_2_months_comparison_html.html",
        "json": "validate_parts_workmix_2_months_comparison_json.json",
        "csv": "validate_parts_workmix_2_months_comparison_csv_.csv",
        "xlsx": "validate_parts_workmix_2_months_comparison_xlsx.xlsx",
        "chart_process_json": "validate_parts_workmix_2_months_comparison_chart.json",
        "db_json": "validate_parts_workmix_2_months_comparison_db.json",
        "transformed_json":"validate_parts_workmix_2_months_comparison_tr.json"
    },
    "advisor_metrics": {
        "script": "validate_advisor_metrics.py",
        "html": "advisor_metrics_comparison.html",
        "json": "advisor_metrics_comparison.json",
        "csv": "advisor_metrics_comparison_results.csv",
        "xlsx": "advisor_metrics_comparison_highlighted.xlsx",
        "chart_process_json": "chart_processing_all_advisor_metrics.json",
        "db_json": "db_calculated_value_advisor_metrics.json"
    },
    "parts_workmix": {
        "script": "validate_parts_workmix.py",
        "html": "validate_parts_workmix_html.html",
        "json": "validate_parts_workmix__json.json",
        "csv": "validate_parts_workmix_csv.csv",
        "xlsx": "validate_parts_workmix_xlsx.xlsx",
        "chart_process_json": "validate_parts_workmix_chart.json",
        "db_json": "validate_parts_workmix_db.json",
        "transformed_json":"validate_parts_workmix_tr.json"
    }
    
    
    # Add more charts here as needed

}
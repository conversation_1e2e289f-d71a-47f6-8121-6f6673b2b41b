import os
import re
from typing import Optional, Union
from lib.pattern.config import config

def create_folder_file_path(subfolder: str, output_file: Optional[str] = None) -> Union[str, tuple[str, str]]:
    """
    Generate a folder path. For subfolders other than 'logs', creates a realm-database-store specific folder:
        realm_name-database_name/store_name/subfolder
    For 'logs', creates the folder directly as a subfolder in the current working directory.

    Args:
        subfolder (str): Subfolder name to create.
        output_file (str, optional): File name to generate full path. If None, only the folder path is returned.

    Returns:
        Union[str, tuple[str, str]]:
            - If output_file is None: returns output_folder (str)
            - If output_file is provided: returns (output_folder, output_file_path) (tuple)
    """
    # Sanitize names: replace spaces and invalid characters with underscore
    def sanitize_filename(name: str) -> str:
        name = name.strip()
        # Replace any non-alphanumeric character with underscore
        return re.sub(r'[^\w]', '_', name)

    safe_subfolder = sanitize_filename(subfolder)

    if safe_subfolder.lower() == "logs":
        # Logs stay in normal/common place
        output_folder = safe_subfolder
    else:
        # For other folders, get tenant/store from config
        try:
            db_name = config.database_name
            store_name = config.store_name  # updated to store_name
            realm_name = config.realm
        except AttributeError:
            raise ValueError("Config must contain 'database_name', 'realm and 'store_name'.")

        safe_tenant_name = sanitize_filename(db_name)
        safe_store_name = sanitize_filename(store_name)
        safe_realm_name = sanitize_filename(realm_name)
        main_folder_name= f"{safe_realm_name}-{safe_tenant_name}"

        # Full path: tenant-dbname/store_name/subfolder
        output_folder = os.path.join(main_folder_name, safe_store_name, safe_subfolder)

    # Create folder if it doesn't exist
    os.makedirs(output_folder, exist_ok=True)

    if output_file:
        output_file_path = os.path.join(output_folder, output_file)
        return output_folder, output_file_path
    else:
        return output_folder



from pythonjsonlogger import jsonlogger
import logging
import os
import sys
import inspect
from datetime import datetime

# =========================
# Setup log directories
# =========================
BASE_LOG_DIR = "logs"
INFO_LOG_DIR = os.path.join(BASE_LOG_DIR, "application")
WARNING_LOG_DIR = os.path.join(BASE_LOG_DIR, "warning")
ERROR_LOG_DIR = os.path.join(BASE_LOG_DIR, "error")
OMNI_LOG_DIR = os.path.join(BASE_LOG_DIR, "omni")
LITELLM_LOG_DIR = os.path.join(BASE_LOG_DIR, "litellm")

for d in [INFO_LOG_DIR, WARNING_LOG_DIR, ERROR_LOG_DIR, OMNI_LOG_DIR, LITELLM_LOG_DIR]:
    os.makedirs(d, exist_ok=True)

# Timestamp in ISO 8601 format with milliseconds
timestamp = datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"

# Log filenames
info_log_file = os.path.join(INFO_LOG_DIR, f"qa-val-auto-{timestamp}.log")
warning_log_file = os.path.join(WARNING_LOG_DIR, f"qa-val-auto-warning-{timestamp}.log")
error_log_file = os.path.join(ERROR_LOG_DIR, f"qa-val-auto-error-{timestamp}.log")
omni_log_file = os.path.join(OMNI_LOG_DIR, f"qa-val-auto-omni-{timestamp}.log")
litellm_log_file = os.path.join(LITELLM_LOG_DIR, f"qa-val-auto-litellm-{timestamp}.log")

# =========================
# Formatters
# =========================
json_formatter = jsonlogger.JsonFormatter(
    "%(asctime)s %(levelname)s %(name)s %(filename)s %(lineno)d %(message)s"
)
text_formatter = logging.Formatter(
    "%(asctime)s [%(levelname)s] [%(name)s] (%(filename)s:%(lineno)d) %(message)s"
)

# =========================
# Custom Lazy FileHandler
# =========================
class LazyFileHandler(logging.FileHandler):
    """FileHandler that only creates the file when the first log is emitted."""
    def __init__(self, filename, mode='a', encoding=None, delay=True):
        super().__init__(filename, mode, encoding, delay)

# =========================
# Application Logger
# =========================
logger = logging.getLogger("FOPC QA AUTOMATION")
logger.setLevel(logging.DEBUG)  # capture everything
logger.propagate = False  # prevent duplication

# INFO handler
info_handler = logging.FileHandler(info_log_file, encoding='utf-8')
info_handler.setLevel(logging.INFO)
info_handler.addFilter(lambda record: record.levelno == logging.INFO)
info_handler.setFormatter(text_formatter)
logger.addHandler(info_handler)

# WARNING handler (lazy)
warning_handler = LazyFileHandler(warning_log_file, encoding='utf-8')
warning_handler.setLevel(logging.WARNING)
warning_handler.addFilter(lambda record: record.levelno == logging.WARNING)
warning_handler.setFormatter(text_formatter)
logger.addHandler(warning_handler)

# ERROR handler (lazy)
error_handler = LazyFileHandler(error_log_file, encoding='utf-8')
error_handler.setLevel(logging.ERROR)
error_handler.addFilter(lambda record: record.levelno >= logging.ERROR)
error_handler.setFormatter(text_formatter)
logger.addHandler(error_handler)

# Console handler
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(text_formatter)
logger.addHandler(console_handler)

# =========================
# Omni Logger (stdout/stderr)
# =========================
omni_handler = LazyFileHandler(omni_log_file, encoding='utf-8')
omni_handler.setLevel(logging.INFO)
omni_handler.setFormatter(text_formatter)

omni_logger = logging.getLogger("FOPC QA AUTOMATION.OMNI")
omni_logger.setLevel(logging.INFO)
omni_logger.addHandler(omni_handler)
omni_logger.propagate = False

class StreamToLogger:
    def __init__(self, log_fn):
        self.log_fn = log_fn

    def write(self, message):
        message = message.strip()
        if message:
            self.log_fn(message)

    def flush(self):
        pass

# Redirect stdout/stderr → Omni
sys.stdout = StreamToLogger(omni_logger.info)
sys.stderr = StreamToLogger(omni_logger.error)

# =========================
# LiteLLM Logger
# =========================
litellm_handler = LazyFileHandler(litellm_log_file, encoding='utf-8')
litellm_handler.setLevel(logging.DEBUG)
litellm_handler.setFormatter(text_formatter)

litellm_logger = logging.getLogger("LiteLLM")
litellm_logger.setLevel(logging.DEBUG)
litellm_logger.addHandler(litellm_handler)
litellm_logger.propagate = False

# =========================
# Capture warnings / 3rd party root logs
# =========================
logging.captureWarnings(True)
root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG)
root_logger.handlers = []  # clear defaults
root_logger.addHandler(console_handler)
root_logger.addHandler(info_handler)
root_logger.addHandler(warning_handler)
root_logger.addHandler(error_handler)

# =========================
# Helper: detect real caller
# =========================
def _get_stacklevel():
    frame = inspect.currentframe()
    stacklevel = 1
    while frame:
        filename = frame.f_code.co_filename
        if "logger.py" not in filename:  # stop when outside logger.py
            break
        frame = frame.f_back
        stacklevel += 1
    return stacklevel

# =========================
# Helper functions
# =========================
def log_info(*args, **kwargs):
    logger.info(" ".join(map(str, args)), stacklevel=_get_stacklevel(), **kwargs)

def log_warn(*args, **kwargs):
    logger.warning(" ".join(map(str, args)), stacklevel=_get_stacklevel(), **kwargs)

def log_error(*args, **kwargs):
    logger.error(" ".join(map(str, args)), stacklevel=_get_stacklevel(), **kwargs)

def log_exception(*args, **kwargs):
    logger.exception(" ".join(map(str, args)), stacklevel=_get_stacklevel(), **kwargs)

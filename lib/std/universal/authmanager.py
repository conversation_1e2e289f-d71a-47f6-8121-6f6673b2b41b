import os
import json
import asyncio
import atexit
import shutil
from playwright.async_api import async_playwright
from lib.std.universal.logger import log_info, log_error,log_warn

AUTH_STATE_FILE = "auth_state.json"
BROWSER_TIMEOUT = 30000


class AuthManager:
    """Handles authentication, session reuse, and cleanup."""

    def __init__(self, config):
        self.config = config
        self.browser = None
        self.context = None
        self.page = None
        self._playwright = None
        self.user_data_dir = getattr(config, "user_data_dir", None)
        atexit.register(self._on_exit)

    # ========================
    # SESSION MANAGEMENT
    # ========================
    async def start(self, headless: bool = False):
        """Start browser and restore session or login fresh."""
        self._playwright = await async_playwright().start()
        self.browser = await self._playwright.chromium.launch(
            headless=headless,
            args=["--no-sandbox", "--disable-dev-shm-usage", "--disable-gpu"],
        )

        if os.path.exists(AUTH_STATE_FILE):
            log_info("✅ Found saved session, trying to reuse...")
            try:
                self.context = await self.browser.new_context(storage_state=AUTH_STATE_FILE)
                self.page = await self.context.new_page()
                if await self.refresh_session():
                    log_info("✅ Reusing existing session")
                    return True
                else:
                    log_info("⚠️ Session expired → logging in again")
                    return await self.perform_login()
            except Exception as e:
                log_error(f"⚠️ Failed to reuse session: {e}")
                return await self.perform_login()

        # No saved session → login fresh
        return await self.perform_login()

    # async def refresh_session(self, page=None):
    #     """Check if session is still valid (detects login redirect)."""
    #     try:
    #         await self.page.reload(timeout=10000)

    #         if "login" in self.page.url.lower():
    #             log_info("⚠️ Redirected to login → session expired")
    #             return False

    #         if await self.page.query_selector("input[name='username']"):
    #             log_info("⚠️ Login form detected → session expired")
    #             return False

    #         return True
    #     except Exception as e:
    #         log_error(f"⚠️ Could not validate session: {e}")
    #         return False
        

    async def refresh_session(self, page=None):
        """Check if session is still valid (detects login redirect)."""
        # If no page is provided, use the main page instance.
        page_to_check = page if page is not None else self.page
        if page_to_check is None:
            log_error("⚠️ No page instance to check session on.")
            return False

        try:
            await page_to_check.reload(timeout=10000)

            if "login" in page_to_check.url.lower():
                log_info("⚠️ Redirected to login → session expired")
                return False

            if await page_to_check.query_selector("input[name='username']"):
                log_info("⚠️ Login form detected → session expired")
                return False

            return True
        except Exception as e:
            log_error(f"⚠️ Could not validate session: {e}")
            return False


    # ========================
    # LOGIN FLOW
    # ========================
    async def perform_login(self, max_retries: int = 3):
        """Perform login with retry mechanism."""
        login_url = f"{self.config.site_url.rstrip('/')}/auth/login?provenance=fopc"
        username = os.getenv("fopc_username")
        password = os.getenv("password")

        for attempt in range(max_retries):
            try:
                # new context each retry
                self.context = await self.browser.new_context()
                self.page = await self.context.new_page()
                self.page.set_default_timeout(BROWSER_TIMEOUT)

                log_info(f"🔑 Login attempt {attempt+1}/{max_retries}")
                await self.page.goto(login_url, timeout=30000)

                # login button
                await self.page.wait_for_selector("button#login", timeout=10000)
                await self.page.click("button#login", force=True)

                # credentials
                await self.page.fill("input[name='username']", username)
                await self.page.fill("input[name='password']", password)
                await self.page.click("input#kc-login")

                # store selection
                if self.config.store_name:
                    await self.page.wait_for_selector("#store-select", timeout=20000)
                    await self.page.click("#store-select")
                    await self.page.get_by_role("option", name=self.config.store_name).click()
                    await self.page.click("button:has-text('View Dashboard')")

                # verify dashboard loaded
                await self.page.wait_for_selector("input#picker", timeout=30000)

                # save session
                await self.context.storage_state(path=AUTH_STATE_FILE)
                log_info("💾 Auth state saved")
                log_info("✅ Login completed successfully")
                return True

            except Exception as e:
                log_error(f"❌ Login attempt {attempt+1} failed: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)

        return False

    # -----------------------
    # Stop / Cleanup
    # -----------------------
    async def stop(self):
        """Close browser, stop Playwright, and delete session files."""
        # Close context
        if self.context:
            try:
                await self.context.close()
                log_info("🛑 Browser context closed")
            except Exception as e:
                log_error(f"⚠️ Error closing context: {e}")

        # Close browser
        if self.browser:
            try:
                await self.browser.close()
                log_info("🛑 Browser closed")
            except Exception as e:
                log_error(f"⚠️ Error closing browser: {e}")

        # Stop Playwright
        if self._playwright:
            try:
                await self._playwright.stop()
                log_info("🛑 Playwright stopped")
            except Exception as e:
                log_error(f"⚠️ Error stopping Playwright: {e}")

        # Delete user data folder
        if self.user_data_dir:
            try:
                if os.path.exists(self.user_data_dir):
                    shutil.rmtree(self.user_data_dir)
                    log_info(f"🗑️ Session folder deleted: {self.user_data_dir}")
            except Exception as e:
                log_error(f"⚠️ Error deleting session folder: {e}")
        
        # Delete auth_state.json if it exists
        try:
            if os.path.exists(AUTH_STATE_FILE):
                os.remove(AUTH_STATE_FILE)
                log_info(f"🗑️ Deleted session file: {AUTH_STATE_FILE}")
        except Exception as e:
            log_error(f"⚠️ Error deleting session file: {e}")

        # Reset attributes
        self.browser = self.context = self.page = self._playwright = self.user_data_dir = None

    # -----------------------
    # Cleanup on process exit
    # -----------------------
    def _on_exit(self):
        """Called automatically when Python process exits."""
        try:
            if self.browser or self.context or self._playwright:
                try:
                    # Try to get the current loop
                    loop = None
                    try:
                        loop = asyncio.get_event_loop()
                    except RuntimeError:
                        # No current loop
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                    if loop.is_running():
                        # Schedule stop safely if loop is running
                        asyncio.create_task(self.stop())
                    else:
                        # Otherwise, run stop synchronously
                        loop.run_until_complete(self.stop())

                    # Only close loop if we created a new one
                    if not asyncio.get_event_loop().is_running() and loop.is_closed() is False:
                        loop.close()

                except RuntimeError:
                    # Event loop closed, just try to stop without awaiting
                    import warnings
                    warnings.warn("⚠️ Event loop closed, skipping async cleanup")
                    try:
                        self.stop()  # fire-and-forget
                    except Exception:
                        pass
        except Exception as e:
            log_error(f"⚠️ Cleanup failed on exit: {e}")

    # -----------------------
    # Parallel Page Support
    # -----------------------
    async def new_page(self):
        """Create a new page using the main context for parallel tasks."""
        if not self.context:
            raise RuntimeError("AuthManager not started or context missing")

        page = await self.context.new_page()               
        return page  # just the page    

    
    # ========================
    # NEW: PARALLEL SESSION MANAGEMENT
    # ========================
    
    async def check_session_and_relogin_parallel(self):
        """
        Robustly checks if the current browser context is authenticated.
        If not, it performs a full re-login. This is for parallel cases.
        """
        log_info("⏳ Checking for active session for parallel task...")
        check_page = None
        try:
            # We open a new, temporary page to perform the check.
            check_page = await self.context.new_page()

            # Check if we were redirected to the login page.
            if not await self.refresh_session(page=check_page):
                log_warn("⚠️ Session expired for parallel task.")
                is_valid = False
                await self.perform_login()
                # Re-check the session in the new page after re-login
                await check_page.goto(self.config.site_url, timeout=20000)
                is_valid = "login" not in check_page.url.lower()

            else:
                log_info("✅ Session is still valid for parallel task.")
                is_valid = True

        except Exception as e:
            # If navigation or any other error occurs, we assume the session is invalid.
            log_error(f"❌ Session validation failed for parallel task: {e}. Attempting re-login.")
            try:
                await self.perform_login()
                is_valid = True
            except Exception as login_e:
                log_error(f"❌ Re-login attempt also failed: {login_e}")
                is_valid = False

        finally:
            # Ensure the temporary check page is closed.
            if check_page and not check_page.is_closed():
                await check_page.close()

        return is_valid

[{"task_id": "chart_1234_point_0", "chart_id": "chart_1234", "chart_title": "CP Discounts - Labor & Parts", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 0, "datasetIndex": 0, "pointIndex": 2, "value": "23367.07", "xLabel": "2025-07-01", "screenX": 412.7649879807692, "screenY": 424.00869035552523, "canvasX": 137.76498798076923, "canvasY": 104.00869035552523, "datasetLabel": "Total Labor Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Total Labor Discount", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 137.76498798076923, "y": 106.74693250562436}}, "navigation_result": {"success": true, "url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1234", "method": "chartjs_event"}, "extracted_data": {"success": true, "chart_id": "1234", "point_data": {"canvasIndex": 0, "datasetIndex": 0, "pointIndex": 2, "value": "23367.07", "xLabel": "2025-07-01", "screenX": 412.7649879807692, "screenY": 424.00869035552523, "canvasX": 137.76498798076923, "canvasY": 104.00869035552523, "datasetLabel": "Total Labor Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-10-27T09:39:32.568576", "page_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1234", "success": true, "chart_id": "1234", "target_month_year": "2025-07-01", "dataset_labels": ["Total Labor Discount"], "extracted_values": {"totallabordiscount": "$23,367.07"}, "extracted_fields": ["totallabordiscount"], "full_row_data": {"Total Labor Discount": "$23,367.07", "Total Parts Discount": "$17,635.73", "Total Discount": "$41,002.80"}, "mui_grid_data": [{"container_index": 0, "selector_used": "ag-grid-table", "items": [{"item_index": 0, "title": "Total Labor Discount", "value": "$23,367.07", "html_structure": {"h5_html": "Total Labor Discount", "h6_html": "$23,367.07"}}]}], "total_items_found": 1}, "error": null}, "timestamp": "2025-10-27T09:39:32.568719", "success": true, "legend_controlled": true, "drilldown_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1234", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1234_point_1", "chart_id": "chart_1234", "chart_title": "CP Discounts - Labor & Parts", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 0, "datasetIndex": 1, "pointIndex": 2, "value": "17635.73", "xLabel": "2025-07-01", "screenX": 412.7649879807692, "screenY": 434.7546139237255, "canvasX": 137.76498798076923, "canvasY": 114.75461392372553, "datasetLabel": "Total Parts Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Total Parts Discount", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 137.76498798076923, "y": 116.82123585081213}}, "navigation_result": {"success": true, "url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1234", "method": "chartjs_event"}, "extracted_data": {"success": true, "chart_id": "1234", "point_data": {"canvasIndex": 0, "datasetIndex": 1, "pointIndex": 2, "value": "17635.73", "xLabel": "2025-07-01", "screenX": 412.7649879807692, "screenY": 434.7546139237255, "canvasX": 137.76498798076923, "canvasY": 114.75461392372553, "datasetLabel": "Total Parts Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-10-27T09:40:03.316612", "page_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1234", "success": true, "chart_id": "1234", "target_month_year": "2025-07-01", "dataset_labels": ["Total Parts Discount"], "extracted_values": {"totalpartsdiscount": "$17,635.73"}, "extracted_fields": ["totalpartsdiscount"], "full_row_data": {"Total Labor Discount": "$23,367.07", "Total Parts Discount": "$17,635.73", "Total Discount": "$41,002.80"}, "mui_grid_data": [{"container_index": 0, "selector_used": "ag-grid-table", "items": [{"item_index": 0, "title": "Total Parts Discount", "value": "$17,635.73", "html_structure": {"h5_html": "Total Parts Discount", "h6_html": "$17,635.73"}}]}], "total_items_found": 1}, "error": null}, "timestamp": "2025-10-27T09:40:03.316753", "success": true, "legend_controlled": true, "drilldown_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1234", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_1234_point_2", "chart_id": "chart_1234", "chart_title": "CP Discounts - Labor & Parts", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 0, "datasetIndex": 2, "pointIndex": 2, "value": "41002.80", "xLabel": "2025-07-01", "screenX": 412.7649879807692, "screenY": 390.9427395221395, "canvasX": 137.76498798076923, "canvasY": 70.94273952213949, "datasetLabel": "Total Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Total Discount", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 137.76498798076923, "y": 109.38165213962537}}, "navigation_result": {"success": true, "url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1234", "method": "chartjs_event"}, "extracted_data": {"success": true, "chart_id": "1234", "point_data": {"canvasIndex": 0, "datasetIndex": 2, "pointIndex": 2, "value": "41002.80", "xLabel": "2025-07-01", "screenX": 412.7649879807692, "screenY": 390.9427395221395, "canvasX": 137.76498798076923, "canvasY": 70.94273952213949, "datasetLabel": "Total Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-10-27T09:40:34.140615", "page_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1234", "success": true, "chart_id": "1234", "target_month_year": "2025-07-01", "dataset_labels": ["Total Discount"], "extracted_values": {"totaldiscount": "$41,002.80"}, "extracted_fields": ["totaldiscount"], "full_row_data": {"Total Labor Discount": "$23,367.07", "Total Parts Discount": "$17,635.73", "Total Discount": "$41,002.80"}, "mui_grid_data": [{"container_index": 0, "selector_used": "ag-grid-table", "items": [{"item_index": 0, "title": "Total Discount", "value": "$41,002.80", "html_structure": {"h5_html": "Total Discount", "h6_html": "$41,002.80"}}]}], "total_items_found": 1}, "error": null}, "timestamp": "2025-10-27T09:40:34.140763", "success": true, "legend_controlled": true, "drilldown_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1234", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_1113_point_0", "chart_id": "chart_1113", "chart_title": "CP RO Count for Disc by Disc Level", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 0, "pointIndex": 2, "value": "19", "xLabel": "2025-07-01", "screenX": 880.2081219951923, "screenY": 463.36758020646937, "canvasX": 116.2081219951923, "canvasY": 143.3675802064694, "datasetLabel": "RO", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "RO", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 110.81772235576923, "y": 114.42318062729733}}, "navigation_result": {"success": true, "url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1113", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 0, "pointIndex": 2, "value": "19", "xLabel": "2025-07-01", "screenX": 880.2081219951923, "screenY": 463.36758020646937, "canvasX": 116.2081219951923, "canvasY": 143.3675802064694, "datasetLabel": "RO", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-10-27T09:40:57.649524", "page_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1113", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "RO Count", "value": "18", "html_structure": {"h5_html": "RO Count", "h6_html": " 18"}}, {"item_index": 1, "title": "Labor Sale - Customer Pay", "value": "$6,645.28", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $6,645.28"}}, {"item_index": 2, "title": "RO - Discounts", "value": "15", "html_structure": {"h5_html": "RO - Discounts", "h6_html": " 15"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "RO Count", "value": "18", "html_structure": {"h5_html": "RO Count", "h6_html": " 18"}}, {"item_index": 1, "title": "Labor Sale - Customer Pay", "value": "$6,645.28", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $6,645.28"}}, {"item_index": 2, "title": "RO - Discounts", "value": "15", "html_structure": {"h5_html": "RO - Discounts", "h6_html": " 15"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "RO Count", "value": "18", "html_structure": {"h5_html": "RO Count", "h6_html": " 18"}}, {"item_index": 1, "title": "Labor Sale - Customer Pay", "value": "$6,645.28", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $6,645.28"}}, {"item_index": 2, "title": "RO - Discounts", "value": "15", "html_structure": {"h5_html": "RO - Discounts", "h6_html": " 15"}}]}, {"container_index": "direct", "selector_used": "direct_elements", "items": [{"item_index": 0, "title": "Fixed Ops Performance Center", "value": "Sheehy Auto Stores", "html_structure": {"h5_html": "Fixed Ops Performance Center", "h6_html": "Sheehy Auto Stores &nbsp;"}}, {"item_index": 1, "title": "Version 5.1.0", "value": "10/20/25", "html_structure": {"h5_html": "Version&nbsp;5.1.0", "h6_html": "<span>10/20/25</span>"}}, {"item_index": 2, "title": "See What's New", "value": "18", "html_structure": {"h5_html": "See&nbsp;What's&nbsp;New", "h6_html": " 18"}}, {"item_index": 3, "title": "RO Count", "value": "$6,645.28", "html_structure": {"h5_html": "RO Count", "h6_html": " $6,645.28"}}, {"item_index": 4, "title": "Labor Sale - Customer Pay", "value": "15", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " 15"}}]}], "success": true, "error": null, "attempt": 1, "chart_id": "1113", "dataset_label": "RO", "target_month_year": "2025-07-01", "extracted_value": null, "total_items_found": 14}, "error": null}, "timestamp": "2025-10-27T09:40:58.015752", "success": true, "legend_controlled": true, "drilldown_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1113", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1113_point_1", "chart_id": "chart_1113", "chart_title": "CP RO Count for Disc by Disc Level", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 1, "pointIndex": 2, "value": "281", "xLabel": "2025-07-01", "screenX": 880.2081219951923, "screenY": 401.9632669291975, "canvasX": 116.2081219951923, "canvasY": 81.9632669291975, "datasetLabel": " Line", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": " Line", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 116.2081219951923, "y": 114.89191584315438}}, "navigation_result": {"success": true, "url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1113", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 1, "pointIndex": 2, "value": "281", "xLabel": "2025-07-01", "screenX": 880.2081219951923, "screenY": 401.9632669291975, "canvasX": 116.2081219951923, "canvasY": 81.9632669291975, "datasetLabel": " Line", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-10-27T09:41:26.141753", "page_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1113", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "RO Count", "value": "280", "html_structure": {"h5_html": "RO Count", "h6_html": " 280"}}, {"item_index": 1, "title": "Line - Discounts", "value": "279", "html_structure": {"h5_html": "Line - Discounts", "h6_html": " 279"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "RO Count", "value": "280", "html_structure": {"h5_html": "RO Count", "h6_html": " 280"}}, {"item_index": 1, "title": "Line - Discounts", "value": "279", "html_structure": {"h5_html": "Line - Discounts", "h6_html": " 279"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "RO Count", "value": "280", "html_structure": {"h5_html": "RO Count", "h6_html": " 280"}}, {"item_index": 1, "title": "Line - Discounts", "value": "279", "html_structure": {"h5_html": "Line - Discounts", "h6_html": " 279"}}]}, {"container_index": "direct", "selector_used": "direct_elements", "items": [{"item_index": 0, "title": "Fixed Ops Performance Center", "value": "Sheehy Auto Stores", "html_structure": {"h5_html": "Fixed Ops Performance Center", "h6_html": "Sheehy Auto Stores &nbsp;"}}, {"item_index": 1, "title": "Version 5.1.0", "value": "10/20/25", "html_structure": {"h5_html": "Version&nbsp;5.1.0", "h6_html": "<span>10/20/25</span>"}}, {"item_index": 2, "title": "See What's New", "value": "280", "html_structure": {"h5_html": "See&nbsp;What's&nbsp;New", "h6_html": " 280"}}, {"item_index": 3, "title": "RO Count", "value": "279", "html_structure": {"h5_html": "RO Count", "h6_html": " 279"}}]}], "success": true, "error": null, "attempt": 1, "chart_id": "1113", "dataset_label": " Line", "target_month_year": "2025-07-01", "extracted_value": null, "total_items_found": 10}, "error": null}, "timestamp": "2025-10-27T09:41:26.397143", "success": true, "legend_controlled": true, "drilldown_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1113", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_1113_point_2", "chart_id": "chart_1113", "chart_title": "CP RO Count for Disc by Disc Level", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 2, "pointIndex": 2, "value": "0", "xLabel": "2025-07-01", "screenX": 880.2081219951923, "screenY": 467.82056475711124, "canvasX": 116.2081219951923, "canvasY": 147.82056475711124, "datasetLabel": " LOP", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "error": "cannot access local variable 'extracted_data' where it is not associated with a value", "timestamp": "2025-10-27T09:41:58.126532", "success": false, "legend_controlled": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_1236_point_0", "chart_id": "chart_1236", "chart_title": "Discounts Per Total CP ROs", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 5, "datasetIndex": 0, "pointIndex": 2, "value": "18.74", "xLabel": "2025-07-01", "screenX": 888.2913551682692, "screenY": 1071.4682709216286, "canvasX": 124.29135516826923, "canvasY": 121.4682709216286, "datasetLabel": "Labor Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Labor Discount", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 118.90095552884617, "y": 121.4682709216286}}, "navigation_result": {"success": true, "url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1236", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 5, "datasetIndex": 0, "pointIndex": 2, "value": "18.74", "xLabel": "2025-07-01", "screenX": 888.2913551682692, "screenY": 1071.4682709216286, "canvasX": 124.29135516826923, "canvasY": 121.4682709216286, "datasetLabel": "Labor Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-10-27T09:42:21.625773", "page_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1236", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$91,949.37", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $91,949.37"}}, {"item_index": 1, "title": "Overall RO Count", "value": "1,244", "html_structure": {"h5_html": "Overall RO Count", "h6_html": " 1,244"}}, {"item_index": 2, "title": "Total Labor Discount", "value": "$24,752.96", "html_structure": {"h5_html": "Total Labor Discount", "h6_html": " $24,752.96"}}, {"item_index": 3, "title": "$Discounted per Total CP ROs", "value": "$18.78", "html_structure": {"h5_html": "$Discounted per Total CP ROs", "h6_html": " $18.78"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$91,949.37", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $91,949.37"}}, {"item_index": 1, "title": "Overall RO Count", "value": "1,244", "html_structure": {"h5_html": "Overall RO Count", "h6_html": " 1,244"}}, {"item_index": 2, "title": "Total Labor Discount", "value": "$24,752.96", "html_structure": {"h5_html": "Total Labor Discount", "h6_html": " $24,752.96"}}, {"item_index": 3, "title": "$Discounted per Total CP ROs", "value": "$18.78", "html_structure": {"h5_html": "$Discounted per Total CP ROs", "h6_html": " $18.78"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$91,949.37", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $91,949.37"}}, {"item_index": 1, "title": "Overall RO Count", "value": "1,244", "html_structure": {"h5_html": "Overall RO Count", "h6_html": " 1,244"}}, {"item_index": 2, "title": "Total Labor Discount", "value": "$24,752.96", "html_structure": {"h5_html": "Total Labor Discount", "h6_html": " $24,752.96"}}, {"item_index": 3, "title": "$Discounted per Total CP ROs", "value": "$18.78", "html_structure": {"h5_html": "$Discounted per Total CP ROs", "h6_html": " $18.78"}}]}, {"container_index": "direct", "selector_used": "direct_elements", "items": [{"item_index": 0, "title": "Fixed Ops Performance Center", "value": "Sheehy Auto Stores", "html_structure": {"h5_html": "Fixed Ops Performance Center", "h6_html": "Sheehy Auto Stores &nbsp;"}}, {"item_index": 1, "title": "Version 5.1.0", "value": "10/20/25", "html_structure": {"h5_html": "Version&nbsp;5.1.0", "h6_html": "<span>10/20/25</span>"}}, {"item_index": 2, "title": "See What's New", "value": "$91,949.37", "html_structure": {"h5_html": "See&nbsp;What's&nbsp;New", "h6_html": " $91,949.37"}}, {"item_index": 3, "title": "Labor Sale - Customer Pay", "value": "1,244", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " 1,244"}}, {"item_index": 4, "title": "Overall RO Count", "value": "$24,752.96", "html_structure": {"h5_html": "Overall RO Count", "h6_html": " $24,752.96"}}, {"item_index": 5, "title": "Total Labor Discount", "value": "$18.78", "html_structure": {"h5_html": "Total Labor Discount", "h6_html": " $18.78"}}]}], "success": true, "error": null, "attempt": 1, "chart_id": "1236", "dataset_label": "Labor Discount", "target_month_year": "2025-07-01", "extracted_value": null, "total_items_found": 18}, "error": null}, "timestamp": "2025-10-27T09:42:22.018836", "success": true, "legend_controlled": true, "drilldown_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1236", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1236_point_1", "chart_id": "chart_1236", "chart_title": "Discounts Per Total CP ROs", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 5, "datasetIndex": 1, "pointIndex": 2, "value": "14.14", "xLabel": "2025-07-01", "screenX": 888.2913551682692, "screenY": 1077.9368169004558, "canvasX": 124.29135516826923, "canvasY": 127.93681690045572, "datasetLabel": "Parts Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Parts Discount", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 118.90095552884617, "y": 127.93681690045572}}, "navigation_result": {"success": true, "url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1236", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 5, "datasetIndex": 1, "pointIndex": 2, "value": "14.14", "xLabel": "2025-07-01", "screenX": 888.2913551682692, "screenY": 1077.9368169004558, "canvasX": 124.29135516826923, "canvasY": 127.93681690045572, "datasetLabel": "Parts Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-10-27T09:42:50.357782", "page_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1236", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Overall RO Count", "value": "946", "html_structure": {"h5_html": "Overall RO Count", "h6_html": " 946"}}, {"item_index": 1, "title": "Total Parts Discount", "value": "$17,635.73", "html_structure": {"h5_html": "Total Parts Discount", "h6_html": " $17,635.73"}}, {"item_index": 2, "title": "$Discounted per Total CP ROs", "value": "$18.64", "html_structure": {"h5_html": "$Discounted per Total CP ROs", "h6_html": " $18.64"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Overall RO Count", "value": "946", "html_structure": {"h5_html": "Overall RO Count", "h6_html": " 946"}}, {"item_index": 1, "title": "Total Parts Discount", "value": "$17,635.73", "html_structure": {"h5_html": "Total Parts Discount", "h6_html": " $17,635.73"}}, {"item_index": 2, "title": "$Discounted per Total CP ROs", "value": "$18.64", "html_structure": {"h5_html": "$Discounted per Total CP ROs", "h6_html": " $18.64"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Overall RO Count", "value": "946", "html_structure": {"h5_html": "Overall RO Count", "h6_html": " 946"}}, {"item_index": 1, "title": "Total Parts Discount", "value": "$17,635.73", "html_structure": {"h5_html": "Total Parts Discount", "h6_html": " $17,635.73"}}, {"item_index": 2, "title": "$Discounted per Total CP ROs", "value": "$18.64", "html_structure": {"h5_html": "$Discounted per Total CP ROs", "h6_html": " $18.64"}}]}, {"container_index": "direct", "selector_used": "direct_elements", "items": [{"item_index": 0, "title": "Fixed Ops Performance Center", "value": "Sheehy Auto Stores", "html_structure": {"h5_html": "Fixed Ops Performance Center", "h6_html": "Sheehy Auto Stores &nbsp;"}}, {"item_index": 1, "title": "Version 5.1.0", "value": "10/20/25", "html_structure": {"h5_html": "Version&nbsp;5.1.0", "h6_html": "<span>10/20/25</span>"}}, {"item_index": 2, "title": "See What's New", "value": "946", "html_structure": {"h5_html": "See&nbsp;What's&nbsp;New", "h6_html": " 946"}}, {"item_index": 3, "title": "Overall RO Count", "value": "$17,635.73", "html_structure": {"h5_html": "Overall RO Count", "h6_html": " $17,635.73"}}, {"item_index": 4, "title": "Total Parts Discount", "value": "$18.64", "html_structure": {"h5_html": "Total Parts Discount", "h6_html": " $18.64"}}]}], "success": true, "error": null, "attempt": 1, "chart_id": "1236", "dataset_label": "Parts Discount", "target_month_year": "2025-07-01", "extracted_value": null, "total_items_found": 14}, "error": null}, "timestamp": "2025-10-27T09:42:50.671602", "success": true, "legend_controlled": true, "drilldown_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1236", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_1236_point_2", "chart_id": "chart_1236", "chart_title": "Discounts Per Total CP ROs", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 5, "datasetIndex": 2, "pointIndex": 2, "value": "32.88", "xLabel": "2025-07-01", "screenX": 888.2913551682692, "screenY": 1051.5845230649732, "canvasX": 124.29135516826923, "canvasY": 101.58452306497308, "datasetLabel": "Total Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Total Discount", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 124.29135516826923, "y": 124.70254391104217}}, "navigation_result": {"success": true, "url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1236", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 5, "datasetIndex": 2, "pointIndex": 2, "value": "32.88", "xLabel": "2025-07-01", "screenX": 888.2913551682692, "screenY": 1051.5845230649732, "canvasX": 124.29135516826923, "canvasY": 101.58452306497308, "datasetLabel": "Total Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-10-27T09:43:21.721742", "page_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1236", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "$Discounted per Total CP ROs", "value": "$32.88", "html_structure": {"h5_html": "$Discounted per Total CP ROs", "h6_html": " $32.88"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "$Discounted per Total CP ROs", "value": "$32.88", "html_structure": {"h5_html": "$Discounted per Total CP ROs", "h6_html": " $32.88"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "$Discounted per Total CP ROs", "value": "$32.88", "html_structure": {"h5_html": "$Discounted per Total CP ROs", "h6_html": " $32.88"}}]}, {"container_index": "direct", "selector_used": "direct_elements", "items": [{"item_index": 0, "title": "Fixed Ops Performance Center", "value": "Sheehy Auto Stores", "html_structure": {"h5_html": "Fixed Ops Performance Center", "h6_html": "Sheehy Auto Stores &nbsp;"}}, {"item_index": 1, "title": "Version 5.1.0", "value": "10/20/25", "html_structure": {"h5_html": "Version&nbsp;5.1.0", "h6_html": "<span>10/20/25</span>"}}, {"item_index": 2, "title": "See What's New", "value": "$32.88", "html_structure": {"h5_html": "See&nbsp;What's&nbsp;New", "h6_html": " $32.88"}}]}], "success": true, "error": null, "attempt": 1, "chart_id": "1236", "dataset_label": "Total Discount", "target_month_year": "2025-07-01", "extracted_value": null, "total_items_found": 6}, "error": null}, "timestamp": "2025-10-27T09:43:21.906632", "success": true, "legend_controlled": true, "drilldown_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1236", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_1123_point_0", "chart_id": "chart_1123", "chart_title": "CP Discounted RO %", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 2, "datasetIndex": 0, "pointIndex": 2, "value": "22.6100", "xLabel": "2025-07-01", "screenX": 397.1285697115385, "screenY": 712.1665432202605, "canvasX": 122.12856971153846, "canvasY": 77.16654322026046, "datasetLabel": "Discounted RO %", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Discounted RO %", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 122.12856971153846, "y": 112.49355398868585}}, "navigation_result": {"success": true, "url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1123", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 2, "datasetIndex": 0, "pointIndex": 2, "value": "22.6100", "xLabel": "2025-07-01", "screenX": 397.1285697115385, "screenY": 712.1665432202605, "canvasX": 122.12856971153846, "canvasY": 77.16654322026046, "datasetLabel": "Discounted RO %", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-10-27T09:43:45.489909", "page_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1123", "mui_grid_data": [{"container_index": "direct", "selector_used": "direct_elements", "items": [{"item_index": 0, "title": "Fixed Ops Performance Center", "value": "Sheehy Auto Stores", "html_structure": {"h5_html": "Fixed Ops Performance Center", "h6_html": "Sheehy Auto Stores &nbsp;"}}, {"item_index": 1, "title": "Version 5.1.0", "value": "10/20/25", "html_structure": {"h5_html": "Version&nbsp;5.1.0", "h6_html": "<span>10/20/25</span>"}}]}], "success": true, "error": null, "attempt": 1, "chart_id": "1123", "dataset_label": "Discounted RO %", "target_month_year": "2025-07-01", "extracted_value": null, "total_items_found": 2}, "error": null}, "timestamp": "2025-10-27T09:43:50.667080", "success": true, "legend_controlled": true, "drilldown_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1123", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1115_point_0", "chart_id": "chart_1115", "chart_title": "CP % Disc of Total $ Sold", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 3, "datasetIndex": 0, "pointIndex": 2, "value": "10.75", "xLabel": "2025-07-01", "screenX": 886.1285697115385, "screenY": 707.2370112001639, "canvasX": 122.12856971153846, "canvasY": 72.23701120016396, "datasetLabel": "Total CP Sale %", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Total CP Sale %", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 122.12856971153846, "y": 110.02878797863761}}, "navigation_result": {"success": true, "url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1115", "method": "chartjs_event"}, "extracted_data": {"success": true, "chart_id": "1115", "point_data": {"canvasIndex": 3, "datasetIndex": 0, "pointIndex": 2, "value": "10.75", "xLabel": "2025-07-01", "screenX": 886.1285697115385, "screenY": 707.2370112001639, "canvasX": 122.12856971153846, "canvasY": 72.23701120016396, "datasetLabel": "Total CP Sale %", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-10-27T09:44:16.682437", "page_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1115", "success": true, "chart_id": "1115", "target_month_year": "2025-07-01", "dataset_label": "Overall Discount Sale %", "extracted_value": "10.75%", "extracted_field": "percentagepertotalcpsale", "full_row_data": {"Overall Discount Sale %": "10.75%"}, "mui_grid_data": [{"container_index": 0, "selector_used": "ag-grid-table", "items": [{"item_index": 0, "title": "Overall Discount Sale %", "value": "10.75%", "html_structure": {"h5_html": "Overall Discount Sale %", "h6_html": "10.75%"}}]}], "total_items_found": 1}, "error": null}, "timestamp": "2025-10-27T09:44:16.682934", "success": true, "legend_controlled": true, "drilldown_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1115", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1232_point_0", "chart_id": "chart_1232", "chart_title": "CP % Disc Per Discounted CP ROs test", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 4, "datasetIndex": 0, "pointIndex": 2, "value": "24.37", "xLabel": "2025-07-01", "screenX": 397.1285697115385, "screenY": 1025.1474856788411, "canvasX": 122.12856971153846, "canvasY": 62.14748567884121, "datasetLabel": "% Discounted", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "% Discounted", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 122.12856971153846, "y": 104.98402521797622}}, "navigation_result": {"success": true, "url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1232", "method": "chartjs_event"}, "extracted_data": {"success": true, "chart_id": "1232", "point_data": {"canvasIndex": 4, "datasetIndex": 0, "pointIndex": 2, "value": "24.37", "xLabel": "2025-07-01", "screenX": 397.1285697115385, "screenY": 1025.1474856788411, "canvasX": 122.12856971153846, "canvasY": 62.14748567884121, "datasetLabel": "% Discounted", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-10-27T09:44:42.728321", "page_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1232", "success": true, "chart_id": "1232", "target_month_year": "2025-07-01", "dataset_label": "Overall Discount %", "extracted_value": "24.37%", "extracted_field": "discountpercentageperdiscountcpsale", "full_row_data": {"Overall Discount %": "24.37%"}, "mui_grid_data": [{"container_index": 0, "selector_used": "ag-grid-table", "items": [{"item_index": 0, "title": "Overall Discount %", "value": "24.37%", "html_structure": {"h5_html": "Overall Discount %", "h6_html": "24.37%"}}]}], "total_items_found": 1}, "error": null}, "timestamp": "2025-10-27T09:44:42.728496", "success": true, "legend_controlled": true, "drilldown_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1232", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1165_point_0", "chart_id": "chart_1165", "chart_title": "CP Total Disc $ Avg of Disc ROs", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 6, "datasetIndex": 0, "pointIndex": 2, "value": "125.01", "xLabel": "2025-07-01", "screenX": 399.2913551682692, "screenY": 1324.925680755679, "canvasX": 124.29135516826923, "canvasY": 59.92568075567887, "datasetLabel": "Total Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Total Discount", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 124.29135516826923, "y": 103.87312275639505}}, "navigation_result": {"success": true, "url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1165", "method": "chartjs_event"}, "extracted_data": {"success": true, "chart_id": "1165", "point_data": {"canvasIndex": 6, "datasetIndex": 0, "pointIndex": 2, "value": "125.01", "xLabel": "2025-07-01", "screenX": 399.2913551682692, "screenY": 1324.925680755679, "canvasX": 124.29135516826923, "canvasY": 59.92568075567887, "datasetLabel": "Total Discount", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-10-27T09:45:08.752427", "page_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1165", "success": true, "chart_id": "1165", "target_month_year": "2025-07-01", "dataset_label": "Overall Discount per CP RO", "extracted_value": "$125.01", "extracted_field": "totaldiscountsperdiscountedcpro", "full_row_data": {"Overall Discount per CP RO": "$125.01"}, "mui_grid_data": [{"container_index": 0, "selector_used": "ag-grid-table", "items": [{"item_index": 0, "title": "Overall Discount per CP RO", "value": "$125.01", "html_structure": {"h5_html": "Overall Discount per CP RO", "h6_html": "$125.01"}}]}], "total_items_found": 1}, "error": null}, "timestamp": "2025-10-27T09:45:08.753247", "success": true, "legend_controlled": true, "drilldown_url": "https://sheehyautostores.fixedops.cc/AnalyzeData?chartId=1165", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}]
{"tenant": "fopc_simt_prime_atm", "store": "<PERSON><PERSON><PERSON> of Glen Burnie", "role": "Admin", "generatedAt": "2025-10-27T10:41:06.828720", "total_comparisons": 21, "passed": 1, "failed": 20, "match_rate": 4.761904761904762, "results": [{"chart_name_with_id": "CP RO Count for Disc by Disc Level(1113)", "line_name_legend": "1113_ LOP (2025-07-01)", "drilldown_field": "Line Value", "match": true, "ui": {"line_value": 0, "drilldown_value": 0}, "db": {"line_value": 0.0, "drilldown_value": 0.0}}, {"chart_name_with_id": "CP RO Count for Disc by Disc Level(1113)", "line_name_legend": "1113_ Line (2025-07-01)", "drilldown_field": "Line - Discounts (2025-07-01)", "match": false, "ui": {"line_value": 281.0, "drilldown_value": 279.0}, "db": {"line_value": 268.0, "drilldown_value": 0}}, {"chart_name_with_id": "CP RO Count for Disc by Disc Level(1113)", "line_name_legend": "1113_ Line (2025-07-01)", "drilldown_field": "RO Count (2025-07-01)", "match": false, "ui": {"line_value": 281.0, "drilldown_value": 280.0}, "db": {"line_value": 268.0, "drilldown_value": 0}}, {"chart_name_with_id": "CP RO Count for Disc by Disc Level(1113)", "line_name_legend": "1113_RO (2025-07-01)", "drilldown_field": "Labor Sale - Customer Pay (2025-07-01)", "match": false, "ui": {"line_value": 19.0, "drilldown_value": 6645.28}, "db": {"line_value": 24.0, "drilldown_value": 0}}, {"chart_name_with_id": "CP RO Count for Disc by Disc Level(1113)", "line_name_legend": "1113_RO (2025-07-01)", "drilldown_field": "RO - Discounts (2025-07-01)", "match": false, "ui": {"line_value": 19.0, "drilldown_value": 15.0}, "db": {"line_value": 24.0, "drilldown_value": 0}}, {"chart_name_with_id": "CP RO Count for Disc by Disc Level(1113)", "line_name_legend": "1113_RO (2025-07-01)", "drilldown_field": "RO Count (2025-07-01)", "match": false, "ui": {"line_value": 19.0, "drilldown_value": 18.0}, "db": {"line_value": 24.0, "drilldown_value": 0}}, {"chart_name_with_id": "CP % Disc of Total $ Sold(1115)", "line_name_legend": "1115_Total CP Sale % (2025-07-01)", "drilldown_field": "Overall Discount Sale % (2025-07-01)", "match": false, "ui": {"line_value": 10.75, "drilldown_value": 10.75}, "db": {"line_value": 0, "drilldown_value": 0}}, {"chart_name_with_id": "CP Discounted RO %(1123)", "line_name_legend": "1123_Discounted RO % (2025-07-01)", "drilldown_field": "Line Value", "match": false, "ui": {"line_value": 22.61, "drilldown_value": 22.61}, "db": {"line_value": 22.7, "drilldown_value": 22.7}}, {"chart_name_with_id": "CP Total Disc $ Avg of Disc ROs(1165)", "line_name_legend": "1165_Total Discount (2025-07-01)", "drilldown_field": "Overall Discount per CP RO (2025-07-01)", "match": false, "ui": {"line_value": 125.01, "drilldown_value": 125.01}, "db": {"line_value": 0, "drilldown_value": 0}}, {"chart_name_with_id": "CP % Disc Per Discounted CP ROs test(1232)", "line_name_legend": "1232_% Discounted (2025-07-01)", "drilldown_field": "Overall Discount % (2025-07-01)", "match": false, "ui": {"line_value": 24.37, "drilldown_value": 24.37}, "db": {"line_value": 0, "drilldown_value": 0}}, {"chart_name_with_id": "CP Discounts - Labor & Parts(1234)", "line_name_legend": "1234_Total Discount (2025-07-01)", "drilldown_field": "Total Discount (2025-07-01)", "match": false, "ui": {"line_value": 41002.8, "drilldown_value": 41002.8}, "db": {"line_value": 39006.35, "drilldown_value": 0}}, {"chart_name_with_id": "CP Discounts - Labor & Parts(1234)", "line_name_legend": "1234_Total Labor Discount (2025-07-01)", "drilldown_field": "Total Labor Discount (2025-07-01)", "match": false, "ui": {"line_value": 23367.07, "drilldown_value": 23367.07}, "db": {"line_value": 22205.9, "drilldown_value": 0}}, {"chart_name_with_id": "CP Discounts - Labor & Parts(1234)", "line_name_legend": "1234_Total Parts Discount (2025-07-01)", "drilldown_field": "Total Parts Discount (2025-07-01)", "match": false, "ui": {"line_value": 17635.73, "drilldown_value": 17635.73}, "db": {"line_value": 16800.45, "drilldown_value": 0}}, {"chart_name_with_id": "Discounts Per Total CP ROs(1236)", "line_name_legend": "1236_Labor Discount (2025-07-01)", "drilldown_field": "$Discounted per Total CP ROs (2025-07-01)", "match": false, "ui": {"line_value": 18.74, "drilldown_value": 18.78}, "db": {"line_value": 18.74, "drilldown_value": 0}}, {"chart_name_with_id": "Discounts Per Total CP ROs(1236)", "line_name_legend": "1236_Labor Discount (2025-07-01)", "drilldown_field": "Labor Sale - Customer Pay (2025-07-01)", "match": false, "ui": {"line_value": 18.74, "drilldown_value": 91949.37}, "db": {"line_value": 18.74, "drilldown_value": 0}}, {"chart_name_with_id": "Discounts Per Total CP ROs(1236)", "line_name_legend": "1236_Labor Discount (2025-07-01)", "drilldown_field": "Overall RO Count (2025-07-01)", "match": false, "ui": {"line_value": 18.74, "drilldown_value": 1244.0}, "db": {"line_value": 18.74, "drilldown_value": 0}}, {"chart_name_with_id": "Discounts Per Total CP ROs(1236)", "line_name_legend": "1236_Labor Discount (2025-07-01)", "drilldown_field": "Total Labor Discount (2025-07-01)", "match": false, "ui": {"line_value": 18.74, "drilldown_value": 24752.96}, "db": {"line_value": 18.74, "drilldown_value": 0}}, {"chart_name_with_id": "Discounts Per Total CP ROs(1236)", "line_name_legend": "1236_Parts Discount (2025-07-01)", "drilldown_field": "$Discounted per Total CP ROs (2025-07-01)", "match": false, "ui": {"line_value": 14.14, "drilldown_value": 18.64}, "db": {"line_value": 14.18, "drilldown_value": 0}}, {"chart_name_with_id": "Discounts Per Total CP ROs(1236)", "line_name_legend": "1236_Parts Discount (2025-07-01)", "drilldown_field": "Overall RO Count (2025-07-01)", "match": false, "ui": {"line_value": 14.14, "drilldown_value": 946.0}, "db": {"line_value": 14.18, "drilldown_value": 0}}, {"chart_name_with_id": "Discounts Per Total CP ROs(1236)", "line_name_legend": "1236_Parts Discount (2025-07-01)", "drilldown_field": "Total Parts Discount (2025-07-01)", "match": false, "ui": {"line_value": 14.14, "drilldown_value": 17635.73}, "db": {"line_value": 14.18, "drilldown_value": 0}}, {"chart_name_with_id": "Discounts Per Total CP ROs(1236)", "line_name_legend": "1236_Total Discount (2025-07-01)", "drilldown_field": "$Discounted per Total CP ROs (2025-07-01)", "match": false, "ui": {"line_value": 32.88, "drilldown_value": 32.88}, "db": {"line_value": 32.92, "drilldown_value": 0}}]}

    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>UI vs DB Comparison Report - Discount Metrics</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body { padding: 20px; font-family: Arial, sans-serif; }
            .badge-pass { background-color: #28a745; color: white; }
            .badge-fail { background-color: #dc3545; color: white; }
            .card-header { cursor: pointer; background-color: #cfe2f3; }
            .comparison-section { display: flex; justify-content: space-between; margin-bottom: 15px; }
            .chart-info { background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin-right: 10px; }
            .match-status { background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 0 0 200px; }
            .value-comparison { display: flex; justify-content: space-between; margin-top: 15px; }
            .ui-extracted { background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin-right: 10px; }
            .db-calculated { background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 1; }
            .match-indicator { font-weight: bold; padding: 8px 15px; border-radius: 5px; display: inline-block; }
            .match-true { background-color: #d4edda; color: #155724; }
            .match-false { background-color: #f8d7da; color: #721c24; }
            .section-title { font-weight: bold; margin-bottom: 8px; color: #333; }
            .field-value { margin-bottom: 5px; }
            .badge-all-passed { background-color: #28a745; color: white; }
            .badge-has-failures { background-color: #dc3545; color: white; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="mb-4">UI vs DB Comparison Report - Discount Metrics</h1>
            <div class="mb-4">
                <strong>Tenant:</strong> fopc_simt_prime_atm<br>
                <strong>Store:</strong> Sheehy Nissan of Glen Burnie<br>
                <strong>Role:</strong> Admin<br>
                <strong>Generated At:</strong> 2025-10-27T10:41:06.829129<br>
                <strong>Report Timestamp:</strong> 20251027_104106<br>
            </div>

            <div class="d-flex gap-3 mb-4">
                <span class="badge bg-success">Passed: 1</span>
                <span class="badge bg-danger">Failed: 20</span>
                <span class="badge bg-secondary">Total: 21</span>
                <span class="badge bg-info">Match Rate: 4.8%</span>
            </div>

            <div class="accordion" id="reportAccordion">
    
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart0">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart0" aria-expanded="false" aria-controls="chart0">
                    CP RO Count for Disc by Disc Level(1113) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(6 comparisons)</small>
                </button>
            </h2>
            <div id="chart0" class="accordion-collapse collapse" aria-labelledby="heading-chart0" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Line Value</strong> (1113_ LOP (2025-07-01)) <span class="ms-2 badge badge-pass">Passed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1113_ LOP (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 0.0</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-true">
                                ✓ MATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Line Value</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Line Value</div>
                            <div class="field-value"><strong>Value:</strong> 0.0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Line - Discounts (2025-07-01)</strong> (1113_ Line (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1113_ Line (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 281.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 268.0</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Line - Discounts (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 279.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Line - Discounts (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>RO Count (2025-07-01)</strong> (1113_ Line (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1113_ Line (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 281.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 268.0</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> RO Count (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 280.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> RO Count (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale - Customer Pay (2025-07-01)</strong> (1113_RO (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1113_RO (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 19.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 24.0</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Customer Pay (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 6645.28</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Customer Pay (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>RO - Discounts (2025-07-01)</strong> (1113_RO (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1113_RO (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 19.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 24.0</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> RO - Discounts (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 15.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> RO - Discounts (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>RO Count (2025-07-01)</strong> (1113_RO (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1113_RO (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 19.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 24.0</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> RO Count (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 18.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> RO Count (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart1">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart1" aria-expanded="false" aria-controls="chart1">
                    CP % Disc of Total $ Sold(1115) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(1 comparisons)</small>
                </button>
            </h2>
            <div id="chart1" class="accordion-collapse collapse" aria-labelledby="heading-chart1" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Overall Discount Sale % (2025-07-01)</strong> (1115_Total CP Sale % (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1115_Total CP Sale % (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 10.75</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 0</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Overall Discount Sale % (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 10.75</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Overall Discount Sale % (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart2">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart2" aria-expanded="false" aria-controls="chart2">
                    CP Discounted RO %(1123) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(1 comparisons)</small>
                </button>
            </h2>
            <div id="chart2" class="accordion-collapse collapse" aria-labelledby="heading-chart2" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Line Value</strong> (1123_Discounted RO % (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1123_Discounted RO % (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 22.61</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 22.7</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Line Value</div>
                            <div class="field-value"><strong>Value:</strong> 22.61</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Line Value</div>
                            <div class="field-value"><strong>Value:</strong> 22.7</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart3">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart3" aria-expanded="false" aria-controls="chart3">
                    CP Total Disc $ Avg of Disc ROs(1165) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(1 comparisons)</small>
                </button>
            </h2>
            <div id="chart3" class="accordion-collapse collapse" aria-labelledby="heading-chart3" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Overall Discount per CP RO (2025-07-01)</strong> (1165_Total Discount (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1165_Total Discount (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 125.01</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 0</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Overall Discount per CP RO (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 125.01</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Overall Discount per CP RO (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart4">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart4" aria-expanded="false" aria-controls="chart4">
                    CP % Disc Per Discounted CP ROs test(1232) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(1 comparisons)</small>
                </button>
            </h2>
            <div id="chart4" class="accordion-collapse collapse" aria-labelledby="heading-chart4" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Overall Discount % (2025-07-01)</strong> (1232_% Discounted (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1232_% Discounted (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 24.37</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 0</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Overall Discount % (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 24.37</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Overall Discount % (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart5">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart5" aria-expanded="false" aria-controls="chart5">
                    CP Discounts - Labor & Parts(1234) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(3 comparisons)</small>
                </button>
            </h2>
            <div id="chart5" class="accordion-collapse collapse" aria-labelledby="heading-chart5" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total Discount (2025-07-01)</strong> (1234_Total Discount (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1234_Total Discount (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 41002.8</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 39006.35</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Discount (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 41002.8</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Discount (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total Labor Discount (2025-07-01)</strong> (1234_Total Labor Discount (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1234_Total Labor Discount (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 23367.07</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 22205.9</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Labor Discount (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 23367.07</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Labor Discount (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total Parts Discount (2025-07-01)</strong> (1234_Total Parts Discount (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1234_Total Parts Discount (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 17635.73</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 16800.45</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Parts Discount (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 17635.73</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Parts Discount (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart6">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart6" aria-expanded="false" aria-controls="chart6">
                    Discounts Per Total CP ROs(1236) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(8 comparisons)</small>
                </button>
            </h2>
            <div id="chart6" class="accordion-collapse collapse" aria-labelledby="heading-chart6" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>$Discounted per Total CP ROs (2025-07-01)</strong> (1236_Labor Discount (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1236_Labor Discount (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 18.74</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 18.74</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> $Discounted per Total CP ROs (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 18.78</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> $Discounted per Total CP ROs (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale - Customer Pay (2025-07-01)</strong> (1236_Labor Discount (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1236_Labor Discount (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 18.74</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 18.74</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Customer Pay (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 91949.37</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Customer Pay (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Overall RO Count (2025-07-01)</strong> (1236_Labor Discount (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1236_Labor Discount (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 18.74</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 18.74</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Overall RO Count (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 1244.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Overall RO Count (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total Labor Discount (2025-07-01)</strong> (1236_Labor Discount (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1236_Labor Discount (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 18.74</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 18.74</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Labor Discount (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 24752.96</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Labor Discount (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>$Discounted per Total CP ROs (2025-07-01)</strong> (1236_Parts Discount (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1236_Parts Discount (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 14.14</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 14.18</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> $Discounted per Total CP ROs (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 18.64</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> $Discounted per Total CP ROs (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Overall RO Count (2025-07-01)</strong> (1236_Parts Discount (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1236_Parts Discount (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 14.14</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 14.18</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Overall RO Count (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 946.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Overall RO Count (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total Parts Discount (2025-07-01)</strong> (1236_Parts Discount (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1236_Parts Discount (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 14.14</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 14.18</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Parts Discount (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 17635.73</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Parts Discount (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>$Discounted per Total CP ROs (2025-07-01)</strong> (1236_Total Discount (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> 1236_Total Discount (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 32.88</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> 32.92</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> $Discounted per Total CP ROs (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 32.88</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> $Discounted per Total CP ROs (2025-07-01)</div>
                            <div class="field-value"><strong>Value:</strong> 0</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    
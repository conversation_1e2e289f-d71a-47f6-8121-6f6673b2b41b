#!/usr/bin/env python3
"""
Test script to verify the custom database connector functions work correctly
"""

import sys
import os
sys.path.append('lib/pattern/qa_auto_core')

# Set up configuration
from lib.pattern.config import config
config.store_id = '121736308'
config.realm = 'sheehyautostores'
config.start_date = '2025-04-01'
config.end_date = '2025-07-30'
config.advisor = 'all'
config.technician = 'all'

# Import the custom database connector classes
from lib.pattern.qa_auto_core.db_handler.db_connector import (
    getCustomerPayTypeGroupsList, DiscountLaborDetails, DiscountPartsDetails
)

def test_custom_connectors():
    """Test the custom database connector implementations"""
    print("=" * 80)
    print("TESTING CUSTOM DATABASE CONNECTOR IMPLEMENTATIONS")
    print("=" * 80)
    
    # Test 1: getCustomerPayTypeGroupsList
    print("\n1. Testing getCustomerPayTypeGroupsList...")
    try:
        retail_flag_connector = getCustomerPayTypeGroupsList()
        retail_flag_df = retail_flag_connector.getCustomerPayTypeList()
        
        print(f"✅ getCustomerPayTypeGroupsList successful!")
        print(f"   Return type: {type(retail_flag_df)}")
        print(f"   Shape: {retail_flag_df.shape}")
        print(f"   Columns: {list(retail_flag_df.columns)}")
        
        if not retail_flag_df.empty and 'source_paytype' in retail_flag_df.columns:
            retail_flag = set(retail_flag_df['source_paytype'].tolist())
            print(f"   Retail flag values: {retail_flag}")
        else:
            print(f"   DataFrame is empty or missing 'source_paytype' column")
            
    except Exception as e:
        print(f"❌ getCustomerPayTypeGroupsList failed: {e}")
    
    # Test 2: DiscountLaborDetails
    print("\n2. Testing DiscountLaborDetails...")
    try:
        labor_connector = DiscountLaborDetails()
        labor_df = labor_connector.getTableResult()
        
        print(f"✅ DiscountLaborDetails successful!")
        print(f"   Return type: {type(labor_df)}")
        print(f"   Shape: {labor_df.shape}")
        print(f"   Columns: {list(labor_df.columns) if not labor_df.empty else 'No columns (empty)'}")
        
        # Test with advisor filter
        labor_df_filtered = labor_connector.getTableResult(advisor_filter={'test_advisor'})
        print(f"   With advisor filter - Shape: {labor_df_filtered.shape}")
        
    except Exception as e:
        print(f"❌ DiscountLaborDetails failed: {e}")
    
    # Test 3: DiscountPartsDetails
    print("\n3. Testing DiscountPartsDetails...")
    try:
        parts_connector = DiscountPartsDetails()
        parts_df = parts_connector.getTableResult()
        
        print(f"✅ DiscountPartsDetails successful!")
        print(f"   Return type: {type(parts_df)}")
        print(f"   Shape: {parts_df.shape}")
        print(f"   Columns: {list(parts_df.columns) if not parts_df.empty else 'No columns (empty)'}")
        
        # Test with advisor filter
        parts_df_filtered = parts_connector.getTableResult(advisor_filter={'test_advisor'})
        print(f"   With advisor filter - Shape: {parts_df_filtered.shape}")
        
    except Exception as e:
        print(f"❌ DiscountPartsDetails failed: {e}")
    
    print("\n" + "=" * 80)
    print("CUSTOM DATABASE CONNECTOR TESTING COMPLETED")
    print("=" * 80)

if __name__ == "__main__":
    test_custom_connectors()

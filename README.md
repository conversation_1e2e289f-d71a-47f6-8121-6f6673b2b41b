# FOPC UI Validation

This project validates the FOPC UI by comparing UI data with backend/calculated results and generates comprehensive reports. 
The project includes scripts for data extraction, transformation, comparison, and reporting.

---

## Omni AI Installation Requirements

Omni AI works with Python 3.12.0 or higher.
**Please upgrade your Python version if it's older.**

### To install the latest Python version:

1. Download the latest version from the official site:
   [https://www.python.org/downloads/](https://www.python.org/downloads/)
2. Run the installer and **check the box that says: "Add Python to PATH"** before installing.

---

## Poppler Installation (Required for PDF Processing)

Omni AI also requires Poppler for PDF processing.

### For Windows:

1. Download Poppler for Windows from:
   [https://github.com/oschwartz10612/poppler-windows/releases/](https://github.com/oschwartz10612/poppler-windows/releases/)
2. Unzip the downloaded folder.
3. Add the `bin` directory from the unzipped folder to your System PATH:

   * Search for "Edit the system environment variables"
   * Click **Environment Variables**
   * Under **System variables**, select **Path** > click **Edit** > **New**
   * Paste the full path to the `bin` folder (e.g., `C:\poppler\Library\bin`)
   * Click **OK** to save and close all dialogs.

### For Ubuntu/Debian:

Open your terminal and run:

```sh
sudo apt update
sudo apt install poppler-utils
```

---

## Project Structure

```
.
├── .env                     # Local environment variables (not committed)
├── .env_sample              # Sample env file to set up new environments
├── .gitignore               # Git ignore rules for unnecessary files
├── .vscode/                 # VSCode workspace configuration (not committed)
│   └── launch.json          # Debugger and run configurations (not committed)
├── README.md                # Project documentation (this file)
├── carriage-fopc_simt_prime/         # Generated reports and validation outputs based on realm or tenant with dbname as main folder  and store sub folder
│   └── Carriage_Kia_of_Woodstock/
│       ├── Final_Consolidated_Report/
│       │   └── Consolidated_Report.html   # Merged HTML report of all comparisons
│       ├── Individual_Reports/            # Detailed outputs for each module
│       │   ├── client_report_card_three_months.html
│       │   ├── client_report_card_three_months.json
│       │   ├── comparison_client_report_3_months_highlighted.xlsx
│       │   ├── comparison_result_3_months_client_report.csv
│       │   ├── cp_overview_comparison.html
│       │   ├── cp_overview_comparison.json
│       │   ├── cp_overview_comparison_highlighted.xlsx
│       │   └── cp_overview_comparison_results.csv
│       ├── Omni_Results/                  # Omni AI generated results (images, markdown, PDFs)
│       │   ├── client_report_card_1_month.jpg
│       │   ├── client_report_card_3_months*.{jpg,md,pdf}
│       │   ├── client_report_card_three_months.json
│       │   └── kpi_dashboard.jpg
│       └── chart_processing_results/      # Intermediate chart comparison results
│           ├── chart_processing_all_cp_summary.json
│           └── db_calculated_value_cp_summary_overview.json
├── lib/                     # Core Python validation and comparison logic
│   ├── README.md
│   ├── __init__.py
│   ├── pattern/             # Validation and comparison modules
│   │   ├── config.py               # Configuration and runtime parameters
│   │   ├── report_generator.py     # Generates consolidated HTML report
│   │   ├── run_all_tests.py        # Master script to execute all validations
│   │   ├── qa_auto_core            # Validation and comparison scripts for various charts
│   │   │   ├── __init__.py
│   │   │   ├── compare_advisor_metrics.py
│   │   │   ├── compare_cp_overview.py
│   │   │   ├── compare_cp_parts_overview.py
│   │   │   ├── compare_kpi_advisor.py
│   │   │   ├── compare_kpi_dashboard.py
│   │   │   ├── compare_parts_workmix_2_months.py
│   │   │   ├── compare_special_metrics.py
│   │   │   ├── compare_workmix_two_month_comparison.py
│   │   │   ├── db_handler  # common database handler
│   │   │   │   ├── __init__.py
│   │   │   │   ├── data_loader.py
│   │   │   │   ├── db_connector.py
│   │   │   │   ├── db_connector_old.py
│   │   │   │   ├── db_credential_handler.py
│   │   │   │   ├── db_query_handler.py
│   │   │   │   └── db_query_handler_old.py
│   │   │   ├── labor_overview_db_calculate.py
│   │   │   ├── labor_parallel_drilldown_legend_test.py
│   │   │   ├── validate-kpi-advisor.py
│   │   │   ├── validate_advisor_metrics.py
│   │   │   ├── validate_advisor_metrics_13month_db_calc.py
│   │   │   ├── validate_advisor_metrics_drilldown.py
│   │   │   ├── validate_advisor_metrics_old.py
│   │   │   ├── validate_cp_parts_overview.py
│   │   │   ├── validate_cp_summary_overview.py
│   │   │   ├── validate_kpi_scorecard.py
│   │   │   ├── validate_metrics.py
│   │   │   ├── validate_parts_workmix_2_months_comparison.py
│   │   │   └── validate_work_mix_two_months_comparison.py
│   └── std/                 # Standard utilities
│       └── universal/       
│           ├── authmanager.py       # Authentication manager for secure access
│           ├── chart_dict.py        # Chart mapping and definitions
|           └── constants.py         # Dynamic generation of custom prompt,kpiscorecard section text and headers any constants used throughout the project
│           ├── extract_image_data.py # Extracts data from charts/images
│           ├── image_to_pdf.py      # Converts image outputs into PDFs
│           ├── logger.py            # Logging utility
│           └── utils.py             # General-purpose helper functions

├── logs/                    # Application log directories
│   ├── application/         # General application logs
│   ├── error/               # Error logs
│   ├── litellm/             # Logs for LiteLLM integration
│   ├── omni/                # Omni-specific logs
│   └── warning/             # Warning logs
└── requirements.txt         # Python dependencies
```

---

## Setup

### 1. Clone the Repository

```sh
git clone <repository_url>
cd <repository_directory>
```

### 2. Create and Activate a Virtual Environment

It is recommended to use a Python virtual environment to manage dependencies:

On **Windows**:

```sh
python -m venv venv
venv\Scripts\activate
```

On **macOS/Linux**:

```sh
python3 -m venv venv
source venv/bin/activate
```

### 3. Install Dependencies

```sh
pip install -r requirements.txt
```

### 4. Set Up Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Site credentials
fopc_username=""
password=""

# Database credentials
db_user_name=""
db_password=""
host=""
port=""

# Gemini API details
GEMINI_API_KEY=""
GEMINI_MODEL=""
```

---

## Usage


### 1. Run the Validation Script

You can execute the main script from the command line with the required CLI arguments.

#### Example 1: Run validation for **all charts**

```sh
python -m lib.pattern.run_all_tests \
  --store_id 244284397 \
  --store_name "Carriage Kia of Woodstock" \
  --start_date 2025-04-01 \
  --end_date 2025-07-30 \
  --fopc_month 2025-04 \
  --pre_fopc_month 2025-01 \
  --database_name fopc_simt_prime \
  --working_days 73.8 \
  --advisor all \
  --technician all \
  --last_month 2025-07 \
  --site_url "https://carriageag-simt.fixedopspc.com" \
  --role Admin \
  --target_month_year 2025-08-01 \
  --realm carriageag \
  --charts all
```

#### Example 2: Run validation for **only KPI Scorecard**

```sh
python -m lib.pattern.run_all_tests \
  --store_id 244284397 \
  --store_name "Carriage Kia of Woodstock" \
  --start_date 2025-04-01 \
  --end_date 2025-07-30 \
  --fopc_month 2025-04 \
  --pre_fopc_month 2025-01 \
  --database_name fopc_simt_prime \
  --working_days 73.8 \
  --advisor all \
  --technician all \
  --last_month 2025-07 \
  --site_url "https://carriageag-simt.fixedopspc.com" \
  --role Admin \
  --target_month_year 2025-08-01 \
  --realm carriageag \
  --charts kpi_scorecard
```

#### Example 3: Run validation for **CP Summary Overview and KPI Score card**

```sh
python -m lib.pattern.run_all_tests \
  --store_id 244284397 \
  --store_name "Carriage Kia of Woodstock" \
  --start_date 2025-04-01 \
  --end_date 2025-07-30 \
  --fopc_month 2025-04 \
  --pre_fopc_month 2025-01 \
  --database_name fopc_simt_prime \
  --working_days 73.8 \
  --advisor all \
  --technician all \
  --last_month 2025-07 \
  --site_url "https://carriageag-simt.fixedopspc.com" \
  --role Admin \
  --target_month_year 2025-08-01 \
  --realm carriageag \
  --charts cp_summary_overview,kpi_scorecard
```

---

### Required CLI Arguments

* `--store_id`: Store ID
* `--store_name`: Store Name
* `--start_date`: Start date (YYYY-MM-DD)
* `--end_date`: End date (YYYY-MM-DD)
* `--fopc_month`: Current FOPC month (YYYY-MM)
* `--pre_fopc_month`: Previous FOPC month (YYYY-MM)
* `--database_name`: Database name (e.g., `fopc_simt_prime`)
* `--working_days`: Number of working days in the selected date range (e.g., 73.8)
* `--advisor`: Advisor name or `all`
* `--technician`: Technician name or `all`
* `--last_month`: Last month for client report card (YYYY-MM)
* `--site_url`: Base URL of the site (e.g., `https://carriageag-simt.fixedopspc.com`)
* `--role`: User role (e.g., `Admin`)
* `--target_month_year`: Target month year for charts (YYYY-MM-DD)
* `--realm`: Realm of the tenant (e.g., `carriageag`)
* `--charts`: Default is `all`, or specify a chart key (e.g., `kpi_scorecard`, `client_report_card_3_months`, `client_report_card_one_month`, `cp_summary_overview`)

---

## Output

Reports are generated dynamically based on the **tenant(with dbname)/store name** provided via CLI arguments. 
The folder structure is created automatically as follows:

```
<tenant_name-DB_name>/
└── <Store_Name>/
    ├── Individual_Reports/           # Individual chart-level reports
    │   ├── client_report_card_one_month.html
    │   ├── client_report_card_three_months.html
    │   ├── cp_summary_overview.html
    │   ├── kpi_scorecard.html
    │   └── ... (CSV, JSON, XLSX files for each chart)
    ├── Final_Consolidated_Report/    # Combined summary report
    │   └── Consolidated_Report.html
    ├── Omni_Results/                  # Omni AI generated outputs (images, markdown, PDFs)
    │   ├── client_report_card_1_month.jpg
    │   ├── client_report_card_3_months*.{jpg,md,pdf}
    │   ├── client_report_card_three_months.json
    │   └── kpi_dashboard.jpg
    └── chart_processing_results/     # Intermediate chart comparison results
        ├── chart_processing_all_cp_summary.json
        └── db_calculated_value_cp_summary_overview.json
```

### Key Points

* **Omni Results**
  All outputs generated by Omni AI—images, markdown files, PDFs, and JSON summaries—are saved under `Omni_Results/`. These are the raw outputs used for report generation.

* **Chart Processing Results**
  Intermediate comparison and calculated values are stored under `chart_processing_results/`. These files contain JSON summaries of processed charts and are used to generate the consolidated report.

* **Individual Reports**
  Each chart produces its own set of files (`.html`, `.json`, `.csv`, `.xlsx`) inside `Individual_Reports/`. Only successfully executed charts produce files here.

* **Final Consolidated Report**
  A single merged HTML file (`Consolidated_Report.html`) is generated inside `Final_Consolidated_Report/`, combining all charts that executed successfully.

* **Failure Handling**

  * If **any chart execution fails**, its files will **not** be included in the consolidated report.
  * If **database loading fails**, the entire automation process is stopped because all chart processing depends on the loaded data.

---

## Troubleshooting

* Ensure all required dependencies are installed using `pip install -r requirements.txt`.
* Verify that the `.env` credentials are correct.
* For Playwright/browser automation, ensure all required browsers are installed.
* For PDF processing, ensure Poppler is installed and added to your system PATH as described above.

---

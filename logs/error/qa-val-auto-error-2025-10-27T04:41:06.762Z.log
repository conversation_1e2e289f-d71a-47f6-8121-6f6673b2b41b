2025-10-27 10:11:24,482 [ERROR] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4510) ❌ Comparison failed: [Errno 2] No such file or directory: 'sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/chart_processing_results/db_calculated_value_discount_metrics_13_month_trend.json'
2025-10-27 10:11:24,483 [ERROR] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4510) Full traceback:
2025-10-27 10:11:24,483 [ERROR] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4510) Traceback (most recent call last):
  File "/home/<USER>/FOPC Python/fopc-test-automation/lib/pattern/qa_auto_core/compare_13_month_trend.py", line 524, in compare_discount_metrics_results
    with open(db_json_path, "r", encoding="utf-8") as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/chart_processing_results/db_calculated_value_discount_metrics_13_month_trend.json'


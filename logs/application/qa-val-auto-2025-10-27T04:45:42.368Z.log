2025-10-27 10:15:42,586 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Selected months for comparison: Apr-25, Jul-25
2025-10-27 10:15:42,586 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading database data
2025-10-27 10:15:42,586 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading critical data...
2025-10-27 10:15:42,586 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) 🔹 [allRevenueDetailsTable.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:42,587 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) 🔹 [getCustomerPayTypeGroupsList.getCustomerPayTypeList] Attempting to connect to DB...
2025-10-27 10:15:44,063 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) ✅ [allRevenueDetailsTable.getTableResult] DB connection established.
2025-10-27 10:15:44,210 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) ✅ [getCustomerPayTypeGroupsList.getCustomerPayTypeList] DB connection established.
2025-10-27 10:15:45,538 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) ✅ [getCustomerPayTypeGroupsList.getCustomerPayTypeList] Query executed successfully.
2025-10-27 10:15:45,539 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) 🔹 [getCustomerPayTypeGroupsList.getCustomerPayTypeList] DB connection closed.
2025-10-27 10:15:45,539 [INFO] [FOPC QA AUTOMATION] (data_loader.py:55) ✓ Completed retail_flag_all in 2.95s
2025-10-27 10:15:46,428 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) ✅ [allRevenueDetailsTable.getTableResult] Query executed successfully.
2025-10-27 10:15:46,666 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) 🔹 [allRevenueDetailsTable.getTableResult] DB connection closed.
2025-10-27 10:15:46,673 [INFO] [FOPC QA AUTOMATION] (data_loader.py:55) ✓ Completed all_revenue_details in 4.09s
2025-10-27 10:15:46,673 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading non-critical data...
2025-10-27 10:15:46,674 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) 🔹 [menuMasterTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:46,674 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) 🔹 [menuServiceTypeTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:46,674 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) 🔹 [assignedMenuModelsTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:46,674 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) 🔹 [assignedMenuOpcodesTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:46,675 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) 🔹 [MPISetupTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:48,456 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) ✅ [assignedMenuOpcodesTableResult.getTableResult] DB connection established.
2025-10-27 10:15:48,457 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) ✅ [assignedMenuModelsTableResult.getTableResult] DB connection established.
2025-10-27 10:15:48,601 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) ✅ [menuServiceTypeTableResult.getTableResult] DB connection established.
2025-10-27 10:15:48,601 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) ✅ [menuMasterTableResult.getTableResult] DB connection established.
2025-10-27 10:15:48,749 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) ✅ [MPISetupTableResult.getTableResult] DB connection established.
2025-10-27 10:15:49,044 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) ✅ [assignedMenuModelsTableResult.getTableResult] Query executed successfully.
2025-10-27 10:15:49,044 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) ✅ [assignedMenuOpcodesTableResult.getTableResult] Query executed successfully.
2025-10-27 10:15:49,045 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) 🔹 [assignedMenuOpcodesTableResult.getTableResult] DB connection closed.
2025-10-27 10:15:49,045 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) 🔹 [MPIOpcodesTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:49,045 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) 🔹 [assignedMenuModelsTableResult.getTableResult] DB connection closed.
2025-10-27 10:15:49,046 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed assigned_menu_opcodes_df in 2.37s
2025-10-27 10:15:49,046 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) 🔹 [allRevenueDetailsForClientReportCard3Month.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:49,046 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed assigned_menu_models_df in 2.37s
2025-10-27 10:15:49,191 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) ✅ [menuMasterTableResult.getTableResult] Query executed successfully.
2025-10-27 10:15:49,191 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) ✅ [menuServiceTypeTableResult.getTableResult] Query executed successfully.
2025-10-27 10:15:49,193 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) 🔹 [menuMasterTableResult.getTableResult] DB connection closed.
2025-10-27 10:15:49,193 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) 🔹 [menuServiceTypeTableResult.getTableResult] DB connection closed.
2025-10-27 10:15:49,193 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed menu_master_df in 2.52s
2025-10-27 10:15:49,193 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed menu_service_type_df in 2.52s
2025-10-27 10:15:49,344 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) ✅ [MPISetupTableResult.getTableResult] Query executed successfully.
2025-10-27 10:15:49,344 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) 🔹 [MPISetupTableResult.getTableResult] DB connection closed.
2025-10-27 10:15:49,344 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed mpi_setup_df in 2.67s
2025-10-27 10:15:50,680 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) ✅ [MPIOpcodesTableResult.getTableResult] DB connection established.
2025-10-27 10:15:50,829 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) ✅ [allRevenueDetailsForClientReportCard3Month.getTableResult] DB connection established.
2025-10-27 10:15:51,269 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) ✅ [MPIOpcodesTableResult.getTableResult] Query executed successfully.
2025-10-27 10:15:51,270 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) 🔹 [MPIOpcodesTableResult.getTableResult] DB connection closed.
2025-10-27 10:15:51,270 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed mpi_opcodes in 4.60s
2025-10-27 10:15:52,318 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) ✅ [allRevenueDetailsForClientReportCard3Month.getTableResult] Query executed successfully.
2025-10-27 10:15:52,578 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) 🔹 [allRevenueDetailsForClientReportCard3Month.getTableResult] DB connection closed.
2025-10-27 10:15:52,584 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed all_revenue_details_for_client_report_card_3_month in 5.91s
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Database loading completed in 10.00 seconds
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) 
==================================================
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) TASK TIMING SUMMARY
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ==================================================
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ all_revenue_details_for_client_report_card_3_month     5.91s ( 19.7%)
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ mpi_opcodes                                            4.60s ( 15.3%)
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ all_revenue_details                                    4.09s ( 13.6%)
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ retail_flag_all                                        2.95s (  9.8%)
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ mpi_setup_df                                           2.67s (  8.9%)
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ menu_master_df                                         2.52s (  8.4%)
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ menu_service_type_df                                   2.52s (  8.4%)
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ assigned_menu_models_df                                2.37s (  7.9%)
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ assigned_menu_opcodes_df                               2.37s (  7.9%)
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ----------------------------------------------------------------------
2025-10-27 10:15:52,585 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) Total execution time:                                 30.00s
2025-10-27 10:15:52,586 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ==================================================
2025-10-27 10:15:52,586 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Slowest task: ('all_revenue_details_for_client_report_card_3_month', 5.90958309173584)
2025-10-27 10:15:52,586 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Scanning folder: /home/<USER>/FOPC Python/fopc-test-automation/lib/pattern/qa_auto_core
2025-10-27 10:15:52,586 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Running validate_discount_metrics_13_month_trend.py with args: {'store_id': '121736308', 'store_name': 'Sheehy Nissan of Glen Burnie', 'start_date': '2025-04-01', 'end_date': '2025-07-30', 'fopc_month': '2025-04', 'pre_fopc_month': '2025-01', 'database_name': 'fopc_simt_prime_atm', 'working_days': '73.8', 'advisor': 'all', 'technician': 'all', 'site_url': 'https://sheehyautostores.fixedops.cc/', 'last_month': '2025-07', 'role': 'Admin', 'target_month_year': ['2025-07-01'], 'realm': 'sheehyautostores', 'charts': 'discount_metrics_13_month_trend'}
2025-10-27 10:15:52,709 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:149) Started running: validate_discount_metrics_13_month_trend
2025-10-27 10:15:52,710 [INFO] [FOPC QA AUTOMATION] (events.py:88) Start Time: 2025-10-27 10:15:52
2025-10-27 10:15:52,711 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:149) Completed: validate_discount_metrics_13_month_trend | Time taken: 0.00 seconds
2025-10-27 10:15:52,711 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) All validation scripts completed successfully.
2025-10-27 10:15:52,711 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:315) All validations completed in 10.13 seconds
2025-10-27 10:15:52,711 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:307) Combined HTML report created at: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/Final_Consolidated_Report/Consolidated_Report.html

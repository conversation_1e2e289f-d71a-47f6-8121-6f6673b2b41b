2025-10-27 10:11:06,994 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Selected months for comparison: Apr-25, Jul-25
2025-10-27 10:11:06,994 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading database data
2025-10-27 10:11:06,994 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading critical data...
2025-10-27 10:11:06,994 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) 🔹 [allRevenueDetailsTable.getTableResult] Attempting to connect to DB...
2025-10-27 10:11:06,994 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) 🔹 [getCustomerPayTypeGroupsList.getCustomerPayTypeList] Attempting to connect to DB...
2025-10-27 10:11:08,344 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) ✅ [allRevenueDetailsTable.getTableResult] DB connection established.
2025-10-27 10:11:08,490 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) ✅ [getCustomerPayTypeGroupsList.getCustomerPayTypeList] DB connection established.
2025-10-27 10:11:09,811 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) ✅ [getCustomerPayTypeGroupsList.getCustomerPayTypeList] Query executed successfully.
2025-10-27 10:11:09,811 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) 🔹 [getCustomerPayTypeGroupsList.getCustomerPayTypeList] DB connection closed.
2025-10-27 10:11:09,811 [INFO] [FOPC QA AUTOMATION] (data_loader.py:55) ✓ Completed retail_flag_all in 2.82s
2025-10-27 10:11:10,697 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) ✅ [allRevenueDetailsTable.getTableResult] Query executed successfully.
2025-10-27 10:11:10,968 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) 🔹 [allRevenueDetailsTable.getTableResult] DB connection closed.
2025-10-27 10:11:10,976 [INFO] [FOPC QA AUTOMATION] (data_loader.py:55) ✓ Completed all_revenue_details in 3.98s
2025-10-27 10:11:10,976 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading non-critical data...
2025-10-27 10:11:10,976 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) 🔹 [menuMasterTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:11:10,976 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) 🔹 [menuServiceTypeTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:11:10,977 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) 🔹 [assignedMenuModelsTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:11:10,977 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) 🔹 [assignedMenuOpcodesTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:11:10,977 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) 🔹 [MPISetupTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:11:12,737 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) ✅ [menuMasterTableResult.getTableResult] DB connection established.
2025-10-27 10:11:12,737 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) ✅ [menuServiceTypeTableResult.getTableResult] DB connection established.
2025-10-27 10:11:12,881 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) ✅ [assignedMenuModelsTableResult.getTableResult] DB connection established.
2025-10-27 10:11:12,882 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) ✅ [assignedMenuOpcodesTableResult.getTableResult] DB connection established.
2025-10-27 10:11:13,028 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) ✅ [MPISetupTableResult.getTableResult] DB connection established.
2025-10-27 10:11:13,177 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) ✅ [menuServiceTypeTableResult.getTableResult] Query executed successfully.
2025-10-27 10:11:13,178 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) 🔹 [menuServiceTypeTableResult.getTableResult] DB connection closed.
2025-10-27 10:11:13,179 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) 🔹 [MPIOpcodesTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:11:13,179 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed menu_service_type_df in 2.20s
2025-10-27 10:11:13,319 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) ✅ [menuMasterTableResult.getTableResult] Query executed successfully.
2025-10-27 10:11:13,320 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) 🔹 [menuMasterTableResult.getTableResult] DB connection closed.
2025-10-27 10:11:13,321 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) 🔹 [allRevenueDetailsForClientReportCard3Month.getTableResult] Attempting to connect to DB...
2025-10-27 10:11:13,321 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed menu_master_df in 2.34s
2025-10-27 10:11:13,466 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) ✅ [assignedMenuModelsTableResult.getTableResult] Query executed successfully.
2025-10-27 10:11:13,466 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) ✅ [MPISetupTableResult.getTableResult] Query executed successfully.
2025-10-27 10:11:13,466 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) ✅ [assignedMenuOpcodesTableResult.getTableResult] Query executed successfully.
2025-10-27 10:11:13,469 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) 🔹 [MPISetupTableResult.getTableResult] DB connection closed.
2025-10-27 10:11:13,469 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) 🔹 [assignedMenuOpcodesTableResult.getTableResult] DB connection closed.
2025-10-27 10:11:13,469 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed mpi_setup_df in 2.49s
2025-10-27 10:11:13,470 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) 🔹 [assignedMenuModelsTableResult.getTableResult] DB connection closed.
2025-10-27 10:11:13,470 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed assigned_menu_opcodes_df in 2.49s
2025-10-27 10:11:13,470 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed assigned_menu_models_df in 2.49s
2025-10-27 10:11:15,244 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) ✅ [MPIOpcodesTableResult.getTableResult] DB connection established.
2025-10-27 10:11:15,390 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) ✅ [allRevenueDetailsForClientReportCard3Month.getTableResult] DB connection established.
2025-10-27 10:11:15,827 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) ✅ [MPIOpcodesTableResult.getTableResult] Query executed successfully.
2025-10-27 10:11:15,828 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) 🔹 [MPIOpcodesTableResult.getTableResult] DB connection closed.
2025-10-27 10:11:15,828 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed mpi_opcodes in 4.85s
2025-10-27 10:11:17,202 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) ✅ [allRevenueDetailsForClientReportCard3Month.getTableResult] Query executed successfully.
2025-10-27 10:11:17,446 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) 🔹 [allRevenueDetailsForClientReportCard3Month.getTableResult] DB connection closed.
2025-10-27 10:11:17,456 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed all_revenue_details_for_client_report_card_3_month in 6.48s
2025-10-27 10:11:17,456 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Database loading completed in 10.46 seconds
2025-10-27 10:11:17,456 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) 
==================================================
2025-10-27 10:11:17,456 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) TASK TIMING SUMMARY
2025-10-27 10:11:17,456 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ==================================================
2025-10-27 10:11:17,456 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ all_revenue_details_for_client_report_card_3_month     6.48s ( 21.5%)
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ mpi_opcodes                                            4.85s ( 16.1%)
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ all_revenue_details                                    3.98s ( 13.2%)
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ retail_flag_all                                        2.82s (  9.3%)
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ assigned_menu_models_df                                2.49s (  8.3%)
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ assigned_menu_opcodes_df                               2.49s (  8.3%)
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ mpi_setup_df                                           2.49s (  8.3%)
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ menu_master_df                                         2.34s (  7.8%)
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ menu_service_type_df                                   2.20s (  7.3%)
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ----------------------------------------------------------------------
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) Total execution time:                                 30.16s
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ==================================================
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Slowest task: ('all_revenue_details_for_client_report_card_3_month', 6.****************)
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Scanning folder: /home/<USER>/FOPC Python/fopc-test-automation/lib/pattern/qa_auto_core
2025-10-27 10:11:17,457 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Running validate_discount_metrics_13_month_trend.py with args: {'store_id': '121736308', 'store_name': 'Sheehy Nissan of Glen Burnie', 'start_date': '2025-04-01', 'end_date': '2025-07-30', 'fopc_month': '2025-04', 'pre_fopc_month': '2025-01', 'database_name': 'fopc_simt_prime_atm', 'working_days': '73.8', 'advisor': 'all', 'technician': 'all', 'site_url': 'https://sheehyautostores.fixedops.cc/', 'last_month': '2025-07', 'role': 'Admin', 'target_month_year': ['2025-07-01'], 'realm': 'sheehyautostores', 'charts': 'discount_metrics_13_month_trend'}
2025-10-27 10:11:17,545 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:149) Started running: validate_discount_metrics_13_month_trend
2025-10-27 10:11:17,546 [INFO] [FOPC QA AUTOMATION] (events.py:88) Start Time: 2025-10-27 10:11:17
2025-10-27 10:11:17,546 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) 🔹 [allRevenueDetailsTable.getTableResult] Attempting to connect to DB...
2025-10-27 10:11:18,625 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) ✅ [allRevenueDetailsTable.getTableResult] DB connection established.
2025-10-27 10:11:20,554 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) ✅ [allRevenueDetailsTable.getTableResult] Query executed successfully.
2025-10-27 10:11:20,822 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) 🔹 [allRevenueDetailsTable.getTableResult] DB connection closed.
2025-10-27 10:11:20,832 [INFO] [FOPC QA AUTOMATION] (db_connector.py:377) 🔹 [DiscountLaborDetails.getTableResult] Attempting to connect to DB...
2025-10-27 10:11:22,040 [INFO] [FOPC QA AUTOMATION] (db_connector.py:377) ✅ [DiscountLaborDetails.getTableResult] DB connection established.
2025-10-27 10:11:22,490 [INFO] [FOPC QA AUTOMATION] (db_connector.py:377) ✅ [DiscountLaborDetails.getTableResult] Query executed successfully.
2025-10-27 10:11:22,502 [INFO] [FOPC QA AUTOMATION] (db_connector.py:377) 🔹 [DiscountLaborDetails.getTableResult] DB connection closed.
2025-10-27 10:11:22,504 [INFO] [FOPC QA AUTOMATION] (db_connector.py:389) 🔹 [DiscountPartsDetails.getTableResult] Attempting to connect to DB...
2025-10-27 10:11:23,732 [INFO] [FOPC QA AUTOMATION] (db_connector.py:389) ✅ [DiscountPartsDetails.getTableResult] DB connection established.
2025-10-27 10:11:24,178 [INFO] [FOPC QA AUTOMATION] (db_connector.py:389) ✅ [DiscountPartsDetails.getTableResult] Query executed successfully.
2025-10-27 10:11:24,223 [INFO] [FOPC QA AUTOMATION] (db_connector.py:389) 🔹 [DiscountPartsDetails.getTableResult] DB connection closed.
2025-10-27 10:11:24,482 [INFO] [FOPC QA AUTOMATION] (events.py:88) DB calculation completed successfully
2025-10-27 10:11:24,482 [INFO] [FOPC QA AUTOMATION] (events.py:88) UI JSON Path: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/chart_processing_results/chart_processing_all_discount_metrics_13_month_trend.json
2025-10-27 10:11:24,482 [INFO] [FOPC QA AUTOMATION] (events.py:88) DB JSON Path: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/chart_processing_results/db_calculated_value_discount_metrics_13_month_trend.json
2025-10-27 10:11:24,482 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4510) ================================================================================
2025-10-27 10:11:24,482 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4510) STARTING DISCOUNT METRICS COMPARISON
2025-10-27 10:11:24,482 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4510) ================================================================================
2025-10-27 10:11:24,483 [INFO] [FOPC QA AUTOMATION] (events.py:88) End Time: 6.93762469291687
2025-10-27 10:11:24,483 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:149) Completed: validate_discount_metrics_13_month_trend | Time taken: 6.94 seconds
2025-10-27 10:11:24,483 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) All validation scripts completed successfully.
2025-10-27 10:11:24,484 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:315) All validations completed in 17.49 seconds
2025-10-27 10:11:24,484 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:307) Combined HTML report created at: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/Final_Consolidated_Report/Consolidated_Report.html

2025-10-27 10:40:43,667 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Selected months for comparison: Apr-25, Jul-25
2025-10-27 10:40:43,667 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading database data
2025-10-27 10:40:43,667 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading critical data...
2025-10-27 10:40:43,668 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) 🔹 [allRevenueDetailsTable.getTableResult] Attempting to connect to DB...
2025-10-27 10:40:43,668 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) 🔹 [getCustomerPayTypeGroupsList.getCustomerPayTypeList] Attempting to connect to DB...
2025-10-27 10:40:45,137 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) ✅ [getCustomerPayTypeGroupsList.getCustomerPayTypeList] DB connection established.
2025-10-27 10:40:45,297 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) ✅ [allRevenueDetailsTable.getTableResult] DB connection established.
2025-10-27 10:40:45,772 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) ✅ [getCustomerPayTypeGroupsList.getCustomerPayTypeList] Query executed successfully.
2025-10-27 10:40:45,774 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) 🔹 [getCustomerPayTypeGroupsList.getCustomerPayTypeList] DB connection closed.
2025-10-27 10:40:45,774 [INFO] [FOPC QA AUTOMATION] (data_loader.py:55) ✓ Completed retail_flag_all in 2.11s
2025-10-27 10:40:49,729 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) ✅ [allRevenueDetailsTable.getTableResult] Query executed successfully.
2025-10-27 10:40:50,037 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) 🔹 [allRevenueDetailsTable.getTableResult] DB connection closed.
2025-10-27 10:40:50,047 [INFO] [FOPC QA AUTOMATION] (data_loader.py:55) ✓ Completed all_revenue_details in 6.38s
2025-10-27 10:40:50,048 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading non-critical data...
2025-10-27 10:40:50,048 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) 🔹 [menuMasterTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:40:50,048 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) 🔹 [menuServiceTypeTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:40:50,048 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) 🔹 [assignedMenuModelsTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:40:50,048 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) 🔹 [assignedMenuOpcodesTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:40:50,049 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) 🔹 [MPISetupTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:40:51,984 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) ✅ [MPISetupTableResult.getTableResult] DB connection established.
2025-10-27 10:40:51,984 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) ✅ [assignedMenuModelsTableResult.getTableResult] DB connection established.
2025-10-27 10:40:52,140 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) ✅ [menuServiceTypeTableResult.getTableResult] DB connection established.
2025-10-27 10:40:52,141 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) ✅ [menuMasterTableResult.getTableResult] DB connection established.
2025-10-27 10:40:52,299 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) ✅ [assignedMenuOpcodesTableResult.getTableResult] DB connection established.
2025-10-27 10:40:52,614 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) ✅ [assignedMenuModelsTableResult.getTableResult] Query executed successfully.
2025-10-27 10:40:52,614 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) ✅ [MPISetupTableResult.getTableResult] Query executed successfully.
2025-10-27 10:40:52,616 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) 🔹 [assignedMenuModelsTableResult.getTableResult] DB connection closed.
2025-10-27 10:40:52,616 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) 🔹 [MPISetupTableResult.getTableResult] DB connection closed.
2025-10-27 10:40:52,616 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) 🔹 [MPIOpcodesTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:40:52,616 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) 🔹 [allRevenueDetailsForClientReportCard3Month.getTableResult] Attempting to connect to DB...
2025-10-27 10:40:52,617 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed assigned_menu_models_df in 2.57s
2025-10-27 10:40:52,617 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed mpi_setup_df in 2.57s
2025-10-27 10:40:52,771 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) ✅ [menuServiceTypeTableResult.getTableResult] Query executed successfully.
2025-10-27 10:40:52,772 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) ✅ [menuMasterTableResult.getTableResult] Query executed successfully.
2025-10-27 10:40:52,773 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) 🔹 [menuServiceTypeTableResult.getTableResult] DB connection closed.
2025-10-27 10:40:52,776 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) 🔹 [menuMasterTableResult.getTableResult] DB connection closed.
2025-10-27 10:40:52,777 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed menu_service_type_df in 2.73s
2025-10-27 10:40:52,777 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed menu_master_df in 2.73s
2025-10-27 10:40:52,929 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) ✅ [assignedMenuOpcodesTableResult.getTableResult] Query executed successfully.
2025-10-27 10:40:52,931 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) 🔹 [assignedMenuOpcodesTableResult.getTableResult] DB connection closed.
2025-10-27 10:40:52,932 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed assigned_menu_opcodes_df in 2.88s
2025-10-27 10:40:54,539 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) ✅ [MPIOpcodesTableResult.getTableResult] DB connection established.
2025-10-27 10:40:54,697 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) ✅ [allRevenueDetailsForClientReportCard3Month.getTableResult] DB connection established.
2025-10-27 10:40:55,184 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) ✅ [MPIOpcodesTableResult.getTableResult] Query executed successfully.
2025-10-27 10:40:55,184 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) 🔹 [MPIOpcodesTableResult.getTableResult] DB connection closed.
2025-10-27 10:40:55,185 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed mpi_opcodes in 5.14s
2025-10-27 10:41:01,835 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) ✅ [allRevenueDetailsForClientReportCard3Month.getTableResult] Query executed successfully.
2025-10-27 10:41:02,098 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) 🔹 [allRevenueDetailsForClientReportCard3Month.getTableResult] DB connection closed.
2025-10-27 10:41:02,105 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed all_revenue_details_for_client_report_card_3_month in 12.06s
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Database loading completed in 18.44 seconds
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) 
==================================================
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) TASK TIMING SUMMARY
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ==================================================
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ all_revenue_details_for_client_report_card_3_month    12.06s ( 30.8%)
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ all_revenue_details                                    6.38s ( 16.3%)
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ mpi_opcodes                                            5.14s ( 13.1%)
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ assigned_menu_opcodes_df                               2.88s (  7.4%)
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ menu_master_df                                         2.73s (  7.0%)
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ menu_service_type_df                                   2.73s (  7.0%)
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ assigned_menu_models_df                                2.57s (  6.6%)
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ mpi_setup_df                                           2.57s (  6.6%)
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ retail_flag_all                                        2.11s (  5.4%)
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ----------------------------------------------------------------------
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) Total execution time:                                 39.16s
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ==================================================
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Slowest task: ('all_revenue_details_for_client_report_card_3_month', 12.056280612945557)
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Scanning folder: /home/<USER>/FOPC Python/fopc-test-automation/lib/pattern/qa_auto_core
2025-10-27 10:41:02,106 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Running validate_discount_metrics_13_month_trend.py with args: {'store_id': '121736308', 'store_name': 'Sheehy Nissan of Glen Burnie', 'start_date': '2025-04-01', 'end_date': '2025-07-30', 'fopc_month': '2025-04', 'pre_fopc_month': '2025-01', 'database_name': 'fopc_simt_prime_atm', 'working_days': '73.8', 'advisor': 'all', 'technician': 'all', 'site_url': 'https://sheehyautostores.fixedops.cc/', 'last_month': '2025-07', 'role': 'Admin', 'target_month_year': ['2025-07-01'], 'realm': 'sheehyautostores', 'charts': 'discount_metrics_13_month_trend'}
2025-10-27 10:41:02,194 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:149) Started running: validate_discount_metrics_13_month_trend
2025-10-27 10:41:02,194 [INFO] [FOPC QA AUTOMATION] (events.py:88) Start Time: 2025-10-27 10:41:02
2025-10-27 10:41:02,194 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4539) 
================================================================================
2025-10-27 10:41:02,194 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4539) DISCOUNT METRICS ANALYSIS - STARTING
2025-10-27 10:41:02,194 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4539) ================================================================================
2025-10-27 10:41:02,195 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4539) Retail flag from config: {'C', 'E', 'M'}
2025-10-27 10:41:02,195 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4539) Fetching discount-specific data...
2025-10-27 10:41:02,195 [INFO] [FOPC QA AUTOMATION] (db_connector.py:377) 🔹 [DiscountLaborDetails.getTableResult] Attempting to connect to DB...
2025-10-27 10:41:03,423 [INFO] [FOPC QA AUTOMATION] (db_connector.py:377) ✅ [DiscountLaborDetails.getTableResult] DB connection established.
2025-10-27 10:41:04,227 [INFO] [FOPC QA AUTOMATION] (db_connector.py:377) ✅ [DiscountLaborDetails.getTableResult] Query executed successfully.
2025-10-27 10:41:04,238 [INFO] [FOPC QA AUTOMATION] (db_connector.py:377) 🔹 [DiscountLaborDetails.getTableResult] DB connection closed.
2025-10-27 10:41:04,239 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4539) Labor discount records retrieved: 2758
2025-10-27 10:41:04,239 [INFO] [FOPC QA AUTOMATION] (db_connector.py:389) 🔹 [DiscountPartsDetails.getTableResult] Attempting to connect to DB...
2025-10-27 10:41:05,568 [INFO] [FOPC QA AUTOMATION] (db_connector.py:389) ✅ [DiscountPartsDetails.getTableResult] DB connection established.
2025-10-27 10:41:06,518 [INFO] [FOPC QA AUTOMATION] (db_connector.py:389) ✅ [DiscountPartsDetails.getTableResult] Query executed successfully.
2025-10-27 10:41:06,545 [INFO] [FOPC QA AUTOMATION] (db_connector.py:389) 🔹 [DiscountPartsDetails.getTableResult] DB connection closed.
2025-10-27 10:41:06,545 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4539) Parts discount records retrieved: 3708
2025-10-27 10:41:06,822 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4539) Target month discount metrics data written successfully to sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/chart_processing_results/db_calculated_value_discount_metrics_13_month_trend.json
2025-10-27 10:41:06,824 [INFO] [FOPC QA AUTOMATION] (events.py:88) DB calculation completed successfully
2025-10-27 10:41:06,824 [INFO] [FOPC QA AUTOMATION] (events.py:88) UI JSON Path: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/chart_processing_results/chart_processing_all_discount_metrics_13_month_trend.json
2025-10-27 10:41:06,824 [INFO] [FOPC QA AUTOMATION] (events.py:88) DB JSON Path: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/chart_processing_results/db_calculated_value_discount_metrics_13_month_trend.json
2025-10-27 10:41:06,825 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547) ================================================================================
2025-10-27 10:41:06,825 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547) STARTING DISCOUNT METRICS COMPARISON
2025-10-27 10:41:06,825 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547) ================================================================================
2025-10-27 10:41:06,825 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing 13 UI charts
2025-10-27 10:41:06,825 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1234_Total Labor Discount (2025-07-01) = 23367.07
2025-10-27 10:41:06,825 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Total Labor Discount (2025-07-01) = 23367.07
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1234_Total Parts Discount (2025-07-01) = 17635.73
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Total Parts Discount (2025-07-01) = 17635.73
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1234_Total Discount (2025-07-01) = 41002.80
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Total Discount (2025-07-01) = 41002.8
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1113_RO (2025-07-01) = 19
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: RO Count (2025-07-01) = 18.0
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Labor Sale - Customer Pay (2025-07-01) = 6645.28
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: RO - Discounts (2025-07-01) = 15.0
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1113_ Line (2025-07-01) = 281
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: RO Count (2025-07-01) = 280.0
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Line - Discounts (2025-07-01) = 279.0
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1236_Labor Discount (2025-07-01) = 18.74
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Labor Sale - Customer Pay (2025-07-01) = 91949.37
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Overall RO Count (2025-07-01) = 1244.0
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Total Labor Discount (2025-07-01) = 24752.96
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: $Discounted per Total CP ROs (2025-07-01) = 18.78
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1236_Parts Discount (2025-07-01) = 14.14
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Overall RO Count (2025-07-01) = 946.0
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Total Parts Discount (2025-07-01) = 17635.73
2025-10-27 10:41:06,826 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: $Discounted per Total CP ROs (2025-07-01) = 18.64
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1236_Total Discount (2025-07-01) = 32.88
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: $Discounted per Total CP ROs (2025-07-01) = 32.88
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1123_Discounted RO % (2025-07-01) = 22.6100
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1115_Total CP Sale % (2025-07-01) = 10.75
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Overall Discount Sale % (2025-07-01) = 10.75
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1232_% Discounted (2025-07-01) = 24.37
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Overall Discount % (2025-07-01) = 24.37
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1165_Total Discount (2025-07-01) = 125.01
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Overall Discount per CP RO (2025-07-01) = 125.01
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) ✅ Extracted 12 UI values
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:529) DB: 1234_Total Labor Discount (2025-07-01) = 22205.9
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:529) DB: 1234_Total Parts Discount (2025-07-01) = 16800.45
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:529) DB: 1234_Total Discount (2025-07-01) = 39006.35
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:529) DB: 1113_RO (2025-07-01) = 24.0
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:529) DB: 1113_ Line (2025-07-01) = 268.0
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:529) DB: 1113_ LOP (2025-07-01) = 0.0
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:529) DB: 1236_Labor Discount (2025-07-01) = 18.74
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:529) DB: 1236_Parts Discount (2025-07-01) = 14.18
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:529) DB: 1236_Total Discount (2025-07-01) = 32.92
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:529) DB: 1123_Discounted RO % (2025-07-01) = 22.7
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:529) ✅ Extracted 10 DB values
2025-10-27 10:41:06,827 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547) 
📊 Comparison Overview:
2025-10-27 10:41:06,828 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547)   UI keys: 12
2025-10-27 10:41:06,828 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547)   DB keys: 10
2025-10-27 10:41:06,828 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547)   Total unique keys: 13
2025-10-27 10:41:06,828 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547) 
📈 Results:
2025-10-27 10:41:06,828 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547)   Total comparisons: 21
2025-10-27 10:41:06,828 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547)   ✅ Passed: 1
2025-10-27 10:41:06,828 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547)   ❌ Failed: 20
2025-10-27 10:41:06,828 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547)   📊 Match rate: 4.8%
2025-10-27 10:41:06,828 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547) ✅ CSV saved: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/Individual_Reports/discount_metrics_13_month_trend_comparison_results.csv
2025-10-27 10:41:06,829 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547) ✅ JSON saved: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/Individual_Reports/discount_metrics_13_month_trend_comparison.json
2025-10-27 10:41:06,829 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:691) HTML report generated: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/Individual_Reports/discount_metrics_13_month_trend_comparison.html
2025-10-27 10:41:06,829 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547) ✅ HTML saved: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/Individual_Reports/discount_metrics_13_month_trend_comparison.html
2025-10-27 10:41:06,829 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547) ================================================================================
2025-10-27 10:41:06,829 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547) COMPARISON COMPLETED SUCCESSFULLY
2025-10-27 10:41:06,829 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4547) ================================================================================
2025-10-27 10:41:06,829 [INFO] [FOPC QA AUTOMATION] (events.py:88) End Time: 4.634875535964966
2025-10-27 10:41:06,829 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:149) Completed: validate_discount_metrics_13_month_trend | Time taken: 4.64 seconds
2025-10-27 10:41:06,829 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) All validation scripts completed successfully.
2025-10-27 10:41:06,830 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:315) All validations completed in 23.16 seconds
2025-10-27 10:41:06,906 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:307) Combined HTML report created at: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/Final_Consolidated_Report/Consolidated_Report.html

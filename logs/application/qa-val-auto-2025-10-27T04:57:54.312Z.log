2025-10-27 10:27:54,525 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Selected months for comparison: Apr-25, Jul-25
2025-10-27 10:27:54,525 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading database data
2025-10-27 10:27:54,525 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading critical data...
2025-10-27 10:27:54,525 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) 🔹 [allRevenueDetailsTable.getTableResult] Attempting to connect to DB...
2025-10-27 10:27:54,526 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) 🔹 [getCustomerPayTypeGroupsList.getCustomerPayTypeList] Attempting to connect to DB...
2025-10-27 10:27:56,304 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) ✅ [allRevenueDetailsTable.getTableResult] DB connection established.
2025-10-27 10:27:56,454 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) ✅ [getCustomerPayTypeGroupsList.getCustomerPayTypeList] DB connection established.
2025-10-27 10:27:57,797 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) ✅ [getCustomerPayTypeGroupsList.getCustomerPayTypeList] Query executed successfully.
2025-10-27 10:27:57,797 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) 🔹 [getCustomerPayTypeGroupsList.getCustomerPayTypeList] DB connection closed.
2025-10-27 10:27:57,797 [INFO] [FOPC QA AUTOMATION] (data_loader.py:55) ✓ Completed retail_flag_all in 3.27s
2025-10-27 10:28:00,536 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) ✅ [allRevenueDetailsTable.getTableResult] Query executed successfully.
2025-10-27 10:28:00,848 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) 🔹 [allRevenueDetailsTable.getTableResult] DB connection closed.
2025-10-27 10:28:00,856 [INFO] [FOPC QA AUTOMATION] (data_loader.py:55) ✓ Completed all_revenue_details in 6.33s
2025-10-27 10:28:00,856 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading non-critical data...
2025-10-27 10:28:00,857 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) 🔹 [menuMasterTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:28:00,857 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) 🔹 [menuServiceTypeTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:28:00,857 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) 🔹 [assignedMenuModelsTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:28:00,857 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) 🔹 [assignedMenuOpcodesTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:28:00,857 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) 🔹 [MPISetupTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:28:02,661 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) ✅ [menuServiceTypeTableResult.getTableResult] DB connection established.
2025-10-27 10:28:02,664 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) ✅ [menuMasterTableResult.getTableResult] DB connection established.
2025-10-27 10:28:02,664 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) ✅ [assignedMenuModelsTableResult.getTableResult] DB connection established.
2025-10-27 10:28:02,807 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) ✅ [assignedMenuOpcodesTableResult.getTableResult] DB connection established.
2025-10-27 10:28:02,955 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) ✅ [MPISetupTableResult.getTableResult] DB connection established.
2025-10-27 10:28:03,247 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) ✅ [assignedMenuModelsTableResult.getTableResult] Query executed successfully.
2025-10-27 10:28:03,247 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) ✅ [menuServiceTypeTableResult.getTableResult] Query executed successfully.
2025-10-27 10:28:03,247 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) ✅ [menuMasterTableResult.getTableResult] Query executed successfully.
2025-10-27 10:28:03,250 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) 🔹 [menuServiceTypeTableResult.getTableResult] DB connection closed.
2025-10-27 10:28:03,259 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) 🔹 [assignedMenuModelsTableResult.getTableResult] DB connection closed.
2025-10-27 10:28:03,260 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) 🔹 [MPIOpcodesTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:28:03,260 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) 🔹 [menuMasterTableResult.getTableResult] DB connection closed.
2025-10-27 10:28:03,260 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed menu_service_type_df in 2.40s
2025-10-27 10:28:03,261 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) 🔹 [allRevenueDetailsForClientReportCard3Month.getTableResult] Attempting to connect to DB...
2025-10-27 10:28:03,263 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed assigned_menu_models_df in 2.41s
2025-10-27 10:28:03,263 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed menu_master_df in 2.41s
2025-10-27 10:28:03,396 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) ✅ [assignedMenuOpcodesTableResult.getTableResult] Query executed successfully.
2025-10-27 10:28:03,397 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) ✅ [MPISetupTableResult.getTableResult] Query executed successfully.
2025-10-27 10:28:03,397 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) 🔹 [assignedMenuOpcodesTableResult.getTableResult] DB connection closed.
2025-10-27 10:28:03,397 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) 🔹 [MPISetupTableResult.getTableResult] DB connection closed.
2025-10-27 10:28:03,397 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed assigned_menu_opcodes_df in 2.54s
2025-10-27 10:28:03,397 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed mpi_setup_df in 2.54s
2025-10-27 10:28:04,589 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) ✅ [MPIOpcodesTableResult.getTableResult] DB connection established.
2025-10-27 10:28:04,736 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) ✅ [allRevenueDetailsForClientReportCard3Month.getTableResult] DB connection established.
2025-10-27 10:28:05,173 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) ✅ [MPIOpcodesTableResult.getTableResult] Query executed successfully.
2025-10-27 10:28:05,174 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) 🔹 [MPIOpcodesTableResult.getTableResult] DB connection closed.
2025-10-27 10:28:05,175 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed mpi_opcodes in 4.32s
2025-10-27 10:28:11,979 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) ✅ [allRevenueDetailsForClientReportCard3Month.getTableResult] Query executed successfully.
2025-10-27 10:28:12,246 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) 🔹 [allRevenueDetailsForClientReportCard3Month.getTableResult] DB connection closed.
2025-10-27 10:28:12,255 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed all_revenue_details_for_client_report_card_3_month in 11.40s
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Database loading completed in 17.73 seconds
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) 
==================================================
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) TASK TIMING SUMMARY
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ==================================================
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ all_revenue_details_for_client_report_card_3_month    11.40s ( 30.3%)
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ all_revenue_details                                    6.33s ( 16.8%)
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ mpi_opcodes                                            4.32s ( 11.5%)
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ retail_flag_all                                        3.27s (  8.7%)
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ assigned_menu_opcodes_df                               2.54s (  6.8%)
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ mpi_setup_df                                           2.54s (  6.8%)
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ menu_master_df                                         2.41s (  6.4%)
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ assigned_menu_models_df                                2.41s (  6.4%)
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ menu_service_type_df                                   2.40s (  6.4%)
2025-10-27 10:28:12,256 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ----------------------------------------------------------------------
2025-10-27 10:28:12,257 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) Total execution time:                                 37.61s
2025-10-27 10:28:12,257 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ==================================================
2025-10-27 10:28:12,257 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Slowest task: ('all_revenue_details_for_client_report_card_3_month', 11.397978782653809)
2025-10-27 10:28:12,257 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Scanning folder: /home/<USER>/FOPC Python/fopc-test-automation/lib/pattern/qa_auto_core
2025-10-27 10:28:12,257 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Running validate_discount_metrics_13_month_trend.py with args: {'store_id': '121736308', 'store_name': 'Sheehy Nissan of Glen Burnie', 'start_date': '2025-04-01', 'end_date': '2025-07-30', 'fopc_month': '2025-04', 'pre_fopc_month': '2025-01', 'database_name': 'fopc_simt_prime_atm', 'working_days': '73.8', 'advisor': 'all', 'technician': 'all', 'site_url': 'https://sheehyautostores.fixedops.cc/', 'last_month': '2025-07', 'role': 'Admin', 'target_month_year': ['2025-07-01'], 'realm': 'sheehyautostores', 'charts': 'discount_metrics_13_month_trend'}
2025-10-27 10:28:12,349 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:149) Started running: validate_discount_metrics_13_month_trend
2025-10-27 10:28:12,350 [INFO] [FOPC QA AUTOMATION] (events.py:88) Start Time: 2025-10-27 10:28:12
2025-10-27 10:28:12,350 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4500) 
================================================================================
2025-10-27 10:28:12,350 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4500) DISCOUNT METRICS ANALYSIS - STARTING
2025-10-27 10:28:12,350 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4500) ================================================================================
2025-10-27 10:28:12,350 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4500) Retail flag from config: {'M', 'C', 'E'}
2025-10-27 10:28:12,350 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4500) Fetching discount-specific data...
2025-10-27 10:28:12,350 [INFO] [FOPC QA AUTOMATION] (db_connector.py:377) 🔹 [DiscountLaborDetails.getTableResult] Attempting to connect to DB...
2025-10-27 10:28:13,486 [INFO] [FOPC QA AUTOMATION] (db_connector.py:377) ✅ [DiscountLaborDetails.getTableResult] DB connection established.
2025-10-27 10:28:14,093 [INFO] [FOPC QA AUTOMATION] (db_connector.py:377) ✅ [DiscountLaborDetails.getTableResult] Query executed successfully.
2025-10-27 10:28:14,104 [INFO] [FOPC QA AUTOMATION] (db_connector.py:377) 🔹 [DiscountLaborDetails.getTableResult] DB connection closed.
2025-10-27 10:28:14,104 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4500) Labor discount records retrieved: 2758
2025-10-27 10:28:14,104 [INFO] [FOPC QA AUTOMATION] (db_connector.py:389) 🔹 [DiscountPartsDetails.getTableResult] Attempting to connect to DB...
2025-10-27 10:28:15,346 [INFO] [FOPC QA AUTOMATION] (db_connector.py:389) ✅ [DiscountPartsDetails.getTableResult] DB connection established.
2025-10-27 10:28:16,093 [INFO] [FOPC QA AUTOMATION] (db_connector.py:389) ✅ [DiscountPartsDetails.getTableResult] Query executed successfully.
2025-10-27 10:28:16,111 [INFO] [FOPC QA AUTOMATION] (db_connector.py:389) 🔹 [DiscountPartsDetails.getTableResult] DB connection closed.
2025-10-27 10:28:16,112 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4500) Parts discount records retrieved: 3708
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4500) Target month discount metrics data written successfully to sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/chart_processing_results/db_calculated_value_discount_metrics_13_month_trend.json
2025-10-27 10:28:16,409 [INFO] [FOPC QA AUTOMATION] (events.py:88) DB calculation completed successfully
2025-10-27 10:28:16,409 [INFO] [FOPC QA AUTOMATION] (events.py:88) UI JSON Path: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/chart_processing_results/chart_processing_all_discount_metrics_13_month_trend.json
2025-10-27 10:28:16,409 [INFO] [FOPC QA AUTOMATION] (events.py:88) DB JSON Path: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/chart_processing_results/db_calculated_value_discount_metrics_13_month_trend.json
2025-10-27 10:28:16,409 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4508) ================================================================================
2025-10-27 10:28:16,409 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4508) STARTING DISCOUNT METRICS COMPARISON
2025-10-27 10:28:16,409 [INFO] [FOPC QA AUTOMATION] (validate_discount_metrics_13_month_trend.py:4508) ================================================================================
2025-10-27 10:28:16,409 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing 13 UI charts
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1234_Total Labor Discount (2025-07-01) = 23367.07
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Total Labor Discount (2025-07-01) = 23367.07
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1234_Total Parts Discount (2025-07-01) = 17635.73
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Total Parts Discount (2025-07-01) = 17635.73
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1234_Total Discount (2025-07-01) = 41002.80
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Total Discount (2025-07-01) = 41002.8
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1113_RO (2025-07-01) = 19
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: RO Count (2025-07-01) = 18.0
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Labor Sale - Customer Pay (2025-07-01) = 6645.28
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: RO - Discounts (2025-07-01) = 15.0
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1113_ Line (2025-07-01) = 281
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: RO Count (2025-07-01) = 280.0
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Line - Discounts (2025-07-01) = 279.0
2025-10-27 10:28:16,410 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1236_Labor Discount (2025-07-01) = 18.74
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Labor Sale - Customer Pay (2025-07-01) = 91949.37
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Overall RO Count (2025-07-01) = 1244.0
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Total Labor Discount (2025-07-01) = 24752.96
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: $Discounted per Total CP ROs (2025-07-01) = 18.78
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1236_Parts Discount (2025-07-01) = 14.14
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Overall RO Count (2025-07-01) = 946.0
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Total Parts Discount (2025-07-01) = 17635.73
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: $Discounted per Total CP ROs (2025-07-01) = 18.64
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1236_Total Discount (2025-07-01) = 32.88
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: $Discounted per Total CP ROs (2025-07-01) = 32.88
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1123_Discounted RO % (2025-07-01) = 22.6100
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1115_Total CP Sale % (2025-07-01) = 10.75
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Overall Discount Sale % (2025-07-01) = 10.75
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1232_% Discounted (2025-07-01) = 24.37
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Overall Discount % (2025-07-01) = 24.37
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) Processing UI: 1165_Total Discount (2025-07-01) = 125.01
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528)   Drilldown: Overall Discount per CP RO (2025-07-01) = 125.01
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:528) ✅ Extracted 12 UI values
2025-10-27 10:28:16,411 [INFO] [FOPC QA AUTOMATION] (compare_13_month_trend.py:529) ✅ Extracted 0 DB values
2025-10-27 10:28:16,412 [INFO] [FOPC QA AUTOMATION] (events.py:88) End Time: 4.061769962310791
2025-10-27 10:28:16,412 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:149) Completed: validate_discount_metrics_13_month_trend | Time taken: 4.06 seconds
2025-10-27 10:28:16,412 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) All validation scripts completed successfully.
2025-10-27 10:28:16,412 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:315) All validations completed in 21.89 seconds
2025-10-27 10:28:16,412 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:307) Combined HTML report created at: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/Final_Consolidated_Report/Consolidated_Report.html

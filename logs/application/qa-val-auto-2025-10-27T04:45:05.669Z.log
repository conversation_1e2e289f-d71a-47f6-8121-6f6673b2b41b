2025-10-27 10:15:05,881 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Selected months for comparison: Apr-25, Jul-25
2025-10-27 10:15:05,881 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading database data
2025-10-27 10:15:05,881 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading critical data...
2025-10-27 10:15:05,881 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) 🔹 [allRevenueDetailsTable.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:05,882 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) 🔹 [getCustomerPayTypeGroupsList.getCustomerPayTypeList] Attempting to connect to DB...
2025-10-27 10:15:07,531 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) ✅ [allRevenueDetailsTable.getTableResult] DB connection established.
2025-10-27 10:15:07,678 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) ✅ [getCustomerPayTypeGroupsList.getCustomerPayTypeList] DB connection established.
2025-10-27 10:15:09,012 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) ✅ [getCustomerPayTypeGroupsList.getCustomerPayTypeList] Query executed successfully.
2025-10-27 10:15:09,012 [INFO] [FOPC QA AUTOMATION] (db_connector.py:334) 🔹 [getCustomerPayTypeGroupsList.getCustomerPayTypeList] DB connection closed.
2025-10-27 10:15:09,012 [INFO] [FOPC QA AUTOMATION] (data_loader.py:55) ✓ Completed retail_flag_all in 3.13s
2025-10-27 10:15:10,034 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) ✅ [allRevenueDetailsTable.getTableResult] Query executed successfully.
2025-10-27 10:15:10,277 [INFO] [FOPC QA AUTOMATION] (db_connector.py:301) 🔹 [allRevenueDetailsTable.getTableResult] DB connection closed.
2025-10-27 10:15:10,283 [INFO] [FOPC QA AUTOMATION] (data_loader.py:55) ✓ Completed all_revenue_details in 4.40s
2025-10-27 10:15:10,285 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Loading non-critical data...
2025-10-27 10:15:10,285 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) 🔹 [menuMasterTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:10,285 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) 🔹 [menuServiceTypeTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:10,285 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) 🔹 [assignedMenuModelsTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:10,285 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) 🔹 [assignedMenuOpcodesTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:10,286 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) 🔹 [MPISetupTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:12,066 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) ✅ [assignedMenuOpcodesTableResult.getTableResult] DB connection established.
2025-10-27 10:15:12,067 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) ✅ [menuMasterTableResult.getTableResult] DB connection established.
2025-10-27 10:15:12,207 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) ✅ [assignedMenuModelsTableResult.getTableResult] DB connection established.
2025-10-27 10:15:12,207 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) ✅ [MPISetupTableResult.getTableResult] DB connection established.
2025-10-27 10:15:12,354 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) ✅ [menuServiceTypeTableResult.getTableResult] DB connection established.
2025-10-27 10:15:12,646 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) ✅ [assignedMenuOpcodesTableResult.getTableResult] Query executed successfully.
2025-10-27 10:15:12,647 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) ✅ [menuMasterTableResult.getTableResult] Query executed successfully.
2025-10-27 10:15:12,648 [INFO] [FOPC QA AUTOMATION] (db_connector.py:224) 🔹 [assignedMenuOpcodesTableResult.getTableResult] DB connection closed.
2025-10-27 10:15:12,648 [INFO] [FOPC QA AUTOMATION] (db_connector.py:191) 🔹 [menuMasterTableResult.getTableResult] DB connection closed.
2025-10-27 10:15:12,648 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) 🔹 [MPIOpcodesTableResult.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:12,648 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) 🔹 [allRevenueDetailsForClientReportCard3Month.getTableResult] Attempting to connect to DB...
2025-10-27 10:15:12,648 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed assigned_menu_opcodes_df in 2.36s
2025-10-27 10:15:12,649 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed menu_master_df in 2.36s
2025-10-27 10:15:12,794 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) ✅ [menuServiceTypeTableResult.getTableResult] Query executed successfully.
2025-10-27 10:15:12,794 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) ✅ [assignedMenuModelsTableResult.getTableResult] Query executed successfully.
2025-10-27 10:15:12,794 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) ✅ [MPISetupTableResult.getTableResult] Query executed successfully.
2025-10-27 10:15:12,795 [INFO] [FOPC QA AUTOMATION] (db_connector.py:235) 🔹 [MPISetupTableResult.getTableResult] DB connection closed.
2025-10-27 10:15:12,797 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed mpi_setup_df in 2.51s
2025-10-27 10:15:12,797 [INFO] [FOPC QA AUTOMATION] (db_connector.py:202) 🔹 [menuServiceTypeTableResult.getTableResult] DB connection closed.
2025-10-27 10:15:12,797 [INFO] [FOPC QA AUTOMATION] (db_connector.py:213) 🔹 [assignedMenuModelsTableResult.getTableResult] DB connection closed.
2025-10-27 10:15:12,797 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed menu_service_type_df in 2.51s
2025-10-27 10:15:12,797 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed assigned_menu_models_df in 2.51s
2025-10-27 10:15:14,433 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) ✅ [MPIOpcodesTableResult.getTableResult] DB connection established.
2025-10-27 10:15:14,582 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) ✅ [allRevenueDetailsForClientReportCard3Month.getTableResult] DB connection established.
2025-10-27 10:15:15,023 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) ✅ [MPIOpcodesTableResult.getTableResult] Query executed successfully.
2025-10-27 10:15:15,024 [INFO] [FOPC QA AUTOMATION] (db_connector.py:246) 🔹 [MPIOpcodesTableResult.getTableResult] DB connection closed.
2025-10-27 10:15:15,024 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed mpi_opcodes in 4.74s
2025-10-27 10:15:16,209 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) ✅ [allRevenueDetailsForClientReportCard3Month.getTableResult] Query executed successfully.
2025-10-27 10:15:16,438 [INFO] [FOPC QA AUTOMATION] (db_connector.py:323) 🔹 [allRevenueDetailsForClientReportCard3Month.getTableResult] DB connection closed.
2025-10-27 10:15:16,447 [INFO] [FOPC QA AUTOMATION] (data_loader.py:73) ✓ Completed all_revenue_details_for_client_report_card_3_month in 6.16s
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:110) Database loading completed in 10.57 seconds
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) 
==================================================
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) TASK TIMING SUMMARY
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ==================================================
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ all_revenue_details_for_client_report_card_3_month     6.16s ( 20.1%)
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ mpi_opcodes                                            4.74s ( 15.4%)
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ all_revenue_details                                    4.40s ( 14.3%)
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ retail_flag_all                                        3.13s ( 10.2%)
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ menu_service_type_df                                   2.51s (  8.2%)
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ assigned_menu_models_df                                2.51s (  8.2%)
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ mpi_setup_df                                           2.51s (  8.2%)
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ menu_master_df                                         2.36s (  7.7%)
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ✓ assigned_menu_opcodes_df                               2.36s (  7.7%)
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ----------------------------------------------------------------------
2025-10-27 10:15:16,448 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) Total execution time:                                 30.69s
2025-10-27 10:15:16,449 [INFO] [FOPC QA AUTOMATION] (data_loader.py:89) ==================================================
2025-10-27 10:15:16,449 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Slowest task: ('all_revenue_details_for_client_report_card_3_month', 6.161134243011475)
2025-10-27 10:15:16,449 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Scanning folder: /home/<USER>/FOPC Python/fopc-test-automation/lib/pattern/qa_auto_core
2025-10-27 10:15:16,449 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) Running validate_discount_metrics_13_month_trend.py with args: {'store_id': '121736308', 'store_name': 'Sheehy Nissan of Glen Burnie', 'start_date': '2025-04-01', 'end_date': '2025-07-30', 'fopc_month': '2025-04', 'pre_fopc_month': '2025-01', 'database_name': 'fopc_simt_prime_atm', 'working_days': '73.8', 'advisor': 'all', 'technician': 'all', 'site_url': 'https://sheehyautostores.fixedops.cc/', 'last_month': '2025-07', 'role': 'Admin', 'target_month_year': ['2025-07-01'], 'realm': 'sheehyautostores', 'charts': 'discount_metrics_13_month_trend'}
2025-10-27 10:15:16,568 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:149) Started running: validate_discount_metrics_13_month_trend
2025-10-27 10:15:16,569 [INFO] [FOPC QA AUTOMATION] (events.py:88) Start Time: 2025-10-27 10:15:16
2025-10-27 10:15:16,573 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:149) Completed: validate_discount_metrics_13_month_trend | Time taken: 0.00 seconds
2025-10-27 10:15:16,573 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:285) All validation scripts completed successfully.
2025-10-27 10:15:16,573 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:315) All validations completed in 10.69 seconds
2025-10-27 10:15:16,573 [INFO] [FOPC QA AUTOMATION] (run_all_tests.py:307) Combined HTML report created at: sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/Final_Consolidated_Report/Consolidated_Report.html

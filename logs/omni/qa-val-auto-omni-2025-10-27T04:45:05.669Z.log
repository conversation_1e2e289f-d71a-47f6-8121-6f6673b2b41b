2025-10-27 10:15:16,572 [ERROR] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Traceback (most recent call last):
2025-10-27 10:15:16,572 [ERROR] [FOPC QA AUTOMATION.OMNI] (logger.py:102) File "/home/<USER>/FOPC Python/fopc-test-automation/lib/pattern/qa_auto_core/validate_discount_metrics_13_month_trend.py", line 4493, in run_validation
    asyncio.run(main())
2025-10-27 10:15:16,572 [ERROR] [FOPC QA AUTOMATION.OMNI] (logger.py:102) File "/usr/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
2025-10-27 10:15:16,572 [ERROR] [FOPC QA AUTOMATION.OMNI] (logger.py:102) File "/usr/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-10-27 10:15:16,572 [ERROR] [FOPC QA AUTOMATION.OMNI] (logger.py:102) File "/usr/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
2025-10-27 10:15:16,573 [ERROR] [FOPC QA AUTOMATION.OMNI] (logger.py:102) File "/home/<USER>/FOPC Python/fopc-test-automation/lib/pattern/qa_auto_core/validate_discount_metrics_13_month_trend.py", line 4471, in main
    db_calculation()
    ^^^^^^^^^^^^^^
2025-10-27 10:15:16,573 [ERROR] [FOPC QA AUTOMATION.OMNI] (logger.py:102) NameError: name 'db_calculation' is not defined

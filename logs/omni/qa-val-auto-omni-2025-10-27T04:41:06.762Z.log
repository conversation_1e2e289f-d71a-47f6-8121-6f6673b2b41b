2025-10-27 10:11:17,546 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Loading data from database...
2025-10-27 10:11:20,829 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Loaded 27231 revenue records
2025-10-27 10:11:22,502 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Loaded 2758 labor discount records
2025-10-27 10:11:24,224 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Loaded 3708 parts discount records
2025-10-27 10:11:24,225 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Target month range: 2025-07-01 to 2025-07-31
2025-10-27 10:11:24,453 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ERROR in db_execution_discount_metrics: Invalid comparison between dtype=datetime64[ns] and date
2025-10-27 10:11:24,453 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ============================================================
2025-10-27 10:11:24,453 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) DISCOUNT METRICS DATABASE EXECUTION FAILED
2025-10-27 10:11:24,453 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ============================================================
2025-10-27 10:11:24,453 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ================================================================================
2025-10-27 10:11:24,453 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) NO DISCOUNT METRICS DATA FOUND
2025-10-27 10:11:24,453 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ================================================================================
2025-10-27 10:11:24,453 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) No data available for target month 2025-07-01
2025-10-27 10:11:24,453 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ================================================================================
2025-10-27 10:11:24,453 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) DISCOUNT METRICS ANALYSIS - MAIN EXECUTION COMPLETED
2025-10-27 10:11:24,453 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ================================================================================
2025-10-27 10:11:24,483 [ERROR] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Traceback (most recent call last):
2025-10-27 10:11:24,483 [ERROR] [FOPC QA AUTOMATION.OMNI] (logger.py:102) File "/home/<USER>/FOPC Python/fopc-test-automation/lib/pattern/qa_auto_core/compare_13_month_trend.py", line 524, in compare_discount_metrics_results
    with open(db_json_path, "r", encoding="utf-8") as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-10-27 10:11:24,483 [ERROR] [FOPC QA AUTOMATION.OMNI] (logger.py:102) FileNotFoundError: [Errno 2] No such file or directory: 'sheehyautostores-fopc_simt_prime_atm/Sheehy_Nissan_of_Glen_Burnie/chart_processing_results/db_calculated_value_discount_metrics_13_month_trend.json'

2025-10-27 10:28:16,112 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Target month range: 2025-07-01 to 2025-07-31
2025-10-27 10:28:16,406 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ================================================================================
2025-10-27 10:28:16,406 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) DISCOUNT METRICS RESULTS PROCESSING
2025-10-27 10:28:16,406 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ================================================================================
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Target Month Summary for July 2025:
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Total Discount: $-39,006.35
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - Labor Discount: $-22,205.90
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - Parts Discount: $-16,800.45
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Discounted RO %: 22.7%
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Total RO Count: 1185
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Discount Levels:
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - RO Level: 24
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - LINE Level: 268
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - LOP Level: 0
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ================================================================================
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) DISCOUNT METRICS ANALYSIS - MAIN EXECUTION COMPLETED
2025-10-27 10:28:16,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ================================================================================
